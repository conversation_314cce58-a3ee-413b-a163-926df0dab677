package main

import (
	"fmt"
	"os"
	"strings"
	"time"

	_ "github.com/go-sql-driver/mysql"
	"github.com/jmoiron/sqlx"
	"github.com/spf13/cobra"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"

	"wnapi/cmd/cli/commands"
	"wnapi/internal/core"
	"wnapi/internal/pkg/cache/memorycache"
	"wnapi/internal/pkg/config/viperconfig"
	"wnapi/internal/pkg/console"
	"wnapi/internal/pkg/database"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/permission"

	// Import modules để đăng ký với registry
	_ "wnapi/modules/auth"
	_ "wnapi/modules/blog"
	_ "wnapi/modules/hello"
	_ "wnapi/modules/marketing"
	_ "wnapi/modules/notification"
	_ "wnapi/modules/product"
	_ "wnapi/modules/rbac"
)

func main() {
	// Thiết lập CLI mode để tự động tắt các service không cần thiết
	os.Setenv("CLI_MODE", "true")

	// Initialize AppBootstrap with proper dependencies
	cfg, err := viperconfig.NewConfigLoader().Load("@.env")
	if err != nil {
		fmt.Printf("Failed to load config: %v\n", err)
		os.Exit(1)
	}

	// Parse log level
	logLevelStr := cfg.GetStringWithDefault("LOG_LEVEL", "debug")
	var logLevel logger.Level

	switch strings.ToLower(logLevelStr) {
	case "debug":
		logLevel = logger.LevelDebug
	case "info":
		logLevel = logger.LevelInfo
	case "warn", "warning":
		logLevel = logger.LevelWarn
	case "error":
		logLevel = logger.LevelError
	case "fatal":
		logLevel = logger.LevelFatal
	default:
		logLevel = logger.LevelDebug
	}

	// Create console logger
	log := logger.NewConsoleLogger("wnapi-cli", logLevel)

	// Initialize database connections
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?parseTime=true",
		cfg.GetString("db_username"),
		cfg.GetString("db_password"),
		cfg.GetString("db_host"),
		cfg.GetString("db_port"),
		cfg.GetString("db_database"),
	)

	// Initialize sqlx connection
	sqlxDB, err := sqlx.Connect("mysql", dsn)
	if err != nil {
		log.Error("Failed to connect to database with sqlx", "error", err)
		os.Exit(1)
	}

	// Configure connection pool
	sqlxDB.SetMaxOpenConns(25)
	sqlxDB.SetMaxIdleConns(5)
	sqlxDB.SetConnMaxLifetime(5 * time.Minute)

	// Initialize GORM connection from sqlx
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn: sqlxDB.DB,
	}), &gorm.Config{})
	if err != nil {
		log.Error("Failed to connect to database with GORM", "error", err)
		os.Exit(1)
	}

	// Create DBManager with both connections
	dbManager := database.NewDBManager(gormDB, gormDB, log)
	dbManager.SetSqlxDB(sqlxDB)

	// Initialize cache
	appCache := memorycache.NewMemoryCache()

	// Initialize middleware factory
	mwFactory := permission.NewMiddlewareFactory(nil, log)

	app := core.NewAppBootstrap(
		cfg,
		log,
		dbManager,
		appCache,
		mwFactory,
	)

	// Initialize modules
	if err := app.Initialize(); err != nil {
		log.Error("Failed to initialize modules", "error", err)
		os.Exit(1)
	}

	// Create root command
	rootCmd := &cobra.Command{
		Use:   "wnapi",
		Short: "WNAPI - Multi-tenant Web Application Platform",
		Long: `WNAPI is a modular, multi-tenant web application platform
that supports various content management and e-commerce features.`,
	}

	// Add global flags
	rootCmd.PersistentFlags().StringP("config", "c", "", "config file path")
	rootCmd.PersistentFlags().StringP("project", "p", "", "project name")
	console.AddGlobalFlags(rootCmd)

	// Initialize console
	noColor, _ := rootCmd.PersistentFlags().GetBool("no-color")
	if err := console.InitializeConsole(noColor); err != nil {
		logger.NewConsoleLogger("cli", logger.LevelError).
			Fatal("Failed to initialize console", "error", err)
	}

	// Register commands
	commands.RegisterCommands(rootCmd, app)

	// Execute the root command
	if err := rootCmd.Execute(); err != nil {
		console.Error("Error: " + err.Error())
		os.Exit(1)
	}
}
