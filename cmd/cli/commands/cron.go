package commands

import (
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"os"
	"text/tabwriter"

	"wnapi/internal/core"
	"wnapi/internal/pkg/console"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/queue/cron"
	"wnapi/internal/pkg/queue/types"

	"github.com/spf13/cobra"
)

// newCronCommand creates the main cron command
func newCronCommand(app *core.AppBootstrap) *cobra.Command {
	cmd := &cobra.Command{
		Use:   "cron",
		Short: "Manage cron jobs",
		Long:  `Manage cron jobs including listing, running, and monitoring scheduled tasks.`,
	}

	// Add subcommands
	cmd.AddCommand(newCronListCommand(app))
	cmd.AddCommand(newCronRunCommand(app))
	cmd.AddCommand(newCronStatusCommand(app))
	cmd.AddCommand(newCronStartCommand(app))

	return cmd
}

// newCronListCommand creates the cron list command
func newCronListCommand(app *core.AppBootstrap) *cobra.Command {
	cmd := &cobra.Command{
		Use:   "list",
		Short: "List all cron jobs",
		Long:  `List all registered cron jobs with their schedules and status.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return runCronList(cmd, args, app)
		},
	}

	cmd.Flags().BoolP("json", "j", false, "Output in JSON format")
	return cmd
}

// newCronRunCommand creates the cron run command
func newCronRunCommand(app *core.AppBootstrap) *cobra.Command {
	cmd := &cobra.Command{
		Use:   "run [job-id]",
		Short: "Run a specific cron job manually",
		Long:  `Run a specific cron job manually for testing purposes.`,
		Args:  cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			return runCronRun(cmd, args, app)
		},
	}

	return cmd
}

// newCronStatusCommand creates the cron status command
func newCronStatusCommand(app *core.AppBootstrap) *cobra.Command {
	cmd := &cobra.Command{
		Use:   "status",
		Short: "Show cron system status",
		Long:  `Show the status of the cron system including enabled jobs and last execution results.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return runCronStatus(cmd, args, app)
		},
	}

	cmd.Flags().BoolP("json", "j", false, "Output in JSON format")
	return cmd
}

// newCronStartCommand creates the cron start command
func newCronStartCommand(app *core.AppBootstrap) *cobra.Command {
	cmd := &cobra.Command{
		Use:   "start",
		Short: "Start the cron scheduler",
		Long:  `Start the cron scheduler to begin executing scheduled jobs.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return runCronStart(cmd, args, app)
		},
	}

	return cmd
}

// runCronList executes the cron list command
func runCronList(cmd *cobra.Command, _ []string, app *core.AppBootstrap) error {
	cronMgr, err := initializeCronManager(app)
	if err != nil {
		return fmt.Errorf("failed to initialize cron manager: %w", err)
	}

	// Get all jobs
	jobs := cronMgr.GetScheduler().GetJobs()

	// Check output format
	jsonOutput, _ := cmd.Flags().GetBool("json")
	if jsonOutput {
		return outputJobsJSON(jobs)
	}

	return outputJobsTable(jobs)
}

// runCronRun executes the cron run command
func runCronRun(_ *cobra.Command, args []string, app *core.AppBootstrap) error {
	jobID := args[0]

	cronMgr, err := initializeCronManager(app)
	if err != nil {
		return fmt.Errorf("failed to initialize cron manager: %w", err)
	}

	// Get the specific job
	job, err := cronMgr.GetScheduler().GetJob(jobID)
	if err != nil {
		return fmt.Errorf("job not found: %w", err)
	}

	console.Info(fmt.Sprintf("Running job: %s (%s)", job.Name, job.ID))

	// Create payload for the job
	payload, err := createPayloadForJob(job, app.GetConfig())
	if err != nil {
		return fmt.Errorf("failed to create payload: %w", err)
	}

	// Execute the job handler
	ctx := context.Background()
	result, err := job.Handler.Handle(ctx, payload)
	if err != nil {
		return fmt.Errorf("job execution failed: %w", err)
	}

	// Display result
	console.Success("Job completed successfully!")
	console.Info(fmt.Sprintf("Success: %t", result.Success))
	console.Info(fmt.Sprintf("Message: %s", result.Message))
	console.Info(fmt.Sprintf("Processed: %d", result.ProcessedCount))
	console.Info(fmt.Sprintf("Errors: %d", result.ErrorCount))
	console.Info(fmt.Sprintf("Duration: %v", result.Duration))

	return nil
}

// runCronStatus executes the cron status command
func runCronStatus(cmd *cobra.Command, _ []string, app *core.AppBootstrap) error {
	cronMgr, err := initializeCronManager(app)
	if err != nil {
		return fmt.Errorf("failed to initialize cron manager: %w", err)
	}

	cronConfig := app.GetConfig().GetCronConfig()
	scheduler := cronMgr.GetScheduler()

	// Check output format
	jsonOutput, _ := cmd.Flags().GetBool("json")
	if jsonOutput {
		status := map[string]any{
			"enabled":    cronConfig.Enabled,
			"timezone":   cronConfig.TimeZone,
			"started":    scheduler.IsStarted(),
			"jobs_count": len(scheduler.GetJobs()),
		}
		return json.NewEncoder(os.Stdout).Encode(status)
	}

	// Display status
	console.Info("Cron System Status")
	console.Info("==================")
	console.Info(fmt.Sprintf("Enabled: %t", cronConfig.Enabled))
	console.Info(fmt.Sprintf("Timezone: %s", cronConfig.TimeZone))
	console.Info(fmt.Sprintf("Started: %t", scheduler.IsStarted()))
	console.Info(fmt.Sprintf("Jobs Count: %d", len(scheduler.GetJobs())))

	return nil
}

// runCronStart executes the cron start command
func runCronStart(_ *cobra.Command, _ []string, app *core.AppBootstrap) error {
	cronMgr, err := initializeCronManager(app)
	if err != nil {
		return fmt.Errorf("failed to initialize cron manager: %w", err)
	}

	// Start the cron scheduler
	if err := cronMgr.Start(); err != nil {
		return fmt.Errorf("failed to start cron scheduler: %w", err)
	}

	console.Success("Cron scheduler started successfully!")
	console.Info("Press Ctrl+C to stop...")

	// Keep running until interrupted
	ctx := context.Background()
	<-ctx.Done()

	return cronMgr.Stop()
}

// Helper functions

// initializeCronManager initializes the cron manager
func initializeCronManager(app *core.AppBootstrap) (*cron.CronManager, error) {
	// For CLI usage, we'll create a simplified cron manager
	config := app.GetConfig()
	appLogger := app.GetLogger()

	// Get database from app
	dbManager := app.GetDBManager()
	if dbManager == nil {
		return nil, fmt.Errorf("database manager not available")
	}

	// Get GORM DB instance
	gormDB := dbManager.WriteDB()
	if gormDB == nil {
		return nil, fmt.Errorf("GORM database not available")
	}

	// Create a slog logger adapter from the app logger
	slogLogger := createSlogAdapter(appLogger)

	// Create a simple cron manager without queue manager for CLI usage
	// We'll pass nil for queue manager since we're just listing/running jobs manually
	cronMgr := cron.NewCronManager(nil, config, slogLogger, gormDB)

	if err := cronMgr.Initialize(); err != nil {
		return nil, fmt.Errorf("failed to initialize cron manager: %w", err)
	}

	return cronMgr, nil
}

// createSlogAdapter creates a slog.Logger adapter from the app logger
func createSlogAdapter(_ logger.Logger) *slog.Logger {
	// Create a simple slog logger that writes to stdout
	// In a production environment, you might want to create a proper adapter
	return slog.New(slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
		Level: slog.LevelInfo,
	}))
}

// outputJobsJSON outputs jobs in JSON format
func outputJobsJSON(jobs []*cron.CronJob) error {
	return json.NewEncoder(os.Stdout).Encode(jobs)
}

// outputJobsTable outputs jobs in table format
func outputJobsTable(jobs []*cron.CronJob) error {
	w := tabwriter.NewWriter(os.Stdout, 0, 0, 2, ' ', 0)
	defer w.Flush()

	fmt.Fprintln(w, "ID\tNAME\tSCHEDULE\tENABLED\tLAST RUN\tNEXT RUN\tSTATUS")
	fmt.Fprintln(w, "---\t----\t--------\t-------\t--------\t--------\t------")

	for _, job := range jobs {
		lastRun := "Never"
		if job.LastRun != nil {
			lastRun = job.LastRun.Format("2006-01-02 15:04:05")
		}

		nextRun := "Unknown"
		if job.NextRun != nil {
			nextRun = job.NextRun.Format("2006-01-02 15:04:05")
		}

		status := "Unknown"
		if job.LastResult != nil {
			if job.LastResult.Success {
				status = "Success"
			} else {
				status = "Failed"
			}
		}

		fmt.Fprintf(w, "%s\t%s\t%s\t%t\t%s\t%s\t%s\n",
			job.ID, job.Name, job.Schedule, job.Enabled, lastRun, nextRun, status)
	}

	return nil
}

// createPayloadForJob creates a payload for manual job execution
func createPayloadForJob(job *cron.CronJob, _ any) (types.CronTaskPayload, error) {
	switch job.TaskType {
	case types.CronTaskSystemCleanup:
		return types.NewSystemCleanupPayload(
			"24h",
			[]string{"logs", "temp_files", "cache"},
			false,
		), nil

	case types.CronTaskSystemHealthCheck:
		return types.NewSystemHealthCheckPayload(
			[]string{"database", "redis", "storage"},
			"30s",
			true,
		), nil

	case types.CronTaskSystemBackup:
		return types.NewSystemBackupPayload(
			[]string{"database", "config"},
			"/backup",
			true,
			"30d",
		), nil

	case types.CronTaskSystemDemo:
		return types.NewSystemDemoPayload(
			"Demo cron job executed manually",
			1,
		), nil

	case types.CronTaskAuthSessionCleanup:
		return types.NewAuthSessionCleanupPayload(
			"24h",
			[]string{"expired", "inactive"},
			100,
		), nil

	case types.CronTaskAuthPasswordExpiry:
		return types.NewAuthPasswordExpiryPayload(
			[]int{7, 3, 1},
			"password_expiry",
			50,
		), nil

	case types.CronTaskMediaImageOptimization:
		return types.NewMediaImageOptimizationPayload(
			85,
			100,
			10*1024*1024,
			[]string{"jpg", "jpeg", "png"},
		), nil

	case types.CronTaskMediaTempCleanup:
		return types.NewMediaTempCleanupPayload(
			"24h",
			[]string{"/tmp", "/uploads/temp"},
			[]string{"*.tmp", "*.temp"},
			false,
		), nil

	case types.CronTaskBlogProcessPendingSchedules:
		return types.NewBlogProcessPendingSchedulesPayload(
			0,  // All tenants
			50, // Batch size
		), nil

	case types.CronTaskBlogCleanupOldSchedules:
		return types.NewBlogCleanupOldSchedulesPayload(
			30,    // Retention days
			0,     // All tenants
			100,   // Batch size
			false, // Not dry run
		), nil

	case types.CronTaskBlogPublishScheduledPosts:
		return types.NewBlogPublishScheduledPostsPayload(
			0,  // All tenants
			20, // Batch size
		), nil

	default:
		return nil, fmt.Errorf("unknown task type: %s", job.TaskType)
	}
}
