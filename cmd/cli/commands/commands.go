package commands

import (
	"wnapi/internal/core"
	"wnapi/internal/pkg/console"

	"github.com/spf13/cobra"
)

// RegisterCommands registers all CLI commands
func RegisterCommands(rootCmd *cobra.Command, app *core.AppBootstrap) {
	// Create console registry
	registry := console.NewRegistry()

	// Register console built-in commands
	if err := console.RegisterConsoleCommands(registry); err != nil {
		console.Error("Failed to register console commands: " + err.Error())
	}

	// Register core commands
	registerCoreCommands(registry, app)

	// Register module commands
	registerModuleCommands(registry, app)

	// Add all registered commands to root
	for _, cmd := range registry.GetCommands() {
		rootCmd.AddCommand(cmd)
	}
}

// registerCoreCommands registers core CLI commands
func registerCoreCommands(registry *console.Registry, app *core.AppBootstrap) {
	// Version command
	registry.RegisterCommand(newVersionCommand())

	// Cron command
	registry.RegisterCommand(newCronCommand(app))

	// Seed command
	registry.RegisterCommand(newSeedCommand(app))

	// Add other core commands here
}

// registerModuleCommands registers commands from all modules
func registerModuleCommands(registry *console.Registry, app *core.AppBootstrap) {
	// Get all registered modules with CLI support
	for _, module := range app.GetModules() {
		// Check for new console module interface
		if consoleModule, ok := module.(console.ConsoleModule); ok {
			if err := consoleModule.RegisterConsoleCommands(registry); err != nil {
				console.Error("Failed to register commands from module: " + err.Error())
			}
			continue
		}

		// Fallback to legacy CLI module interface
		if cliModule, ok := module.(CLIModule); ok {
			commands := cliModule.GetCommands()
			for _, cmd := range commands {
				registry.RegisterCommand(cmd)
			}
		}
	}
}

// CLIModule defines the interface that modules must implement to provide CLI commands
type CLIModule interface {
	// GetCommands returns a list of CLI commands provided by the module
	GetCommands() []*cobra.Command
}
