package commands

import (
	"fmt"
	"runtime"

	"github.com/spf13/cobra"
)

var (
	// Version is the current version of the application
	Version = "dev"
	// BuildDate is the date when the binary was built
	BuildDate = "unknown"
	// GitCommit is the git commit hash
	GitCommit = ""
)

// newVersionCommand creates a new version command
func newVersionCommand() *cobra.Command {
	return &cobra.Command{
		Use:   "version",
		Short: "Print the version information",
		Long:  `Print the version information for the WNAPI CLI.`,
		Run: func(cmd *cobra.Command, args []string) {
			printVersion()
		},
	}
}

// printVersion prints the version information
func printVersion() {
	fmt.Printf("WNAPI CLI\n")
	fmt.Printf("Version:    %s\n", Version)
	if GitCommit != "" {
		fmt.Printf("Git commit: %s\n", GitCommit)
	}
	fmt.Printf("Build date: %s\n", BuildDate)
	fmt.Printf("Go version: %s\n", runtime.Version())
	fmt.Printf("OS/Arch:    %s/%s\n", runtime.GOOS, runtime.GOARCH)
}
