# Tổng Quan Module Media

## 1. <PERSON><PERSON><PERSON> và Trách Nhiệm

Module Media chịu trách nhiệm quản lý toàn bộ hệ thống file và media trong môi trường multi-tenant. Module này cung cấp khả năng upload, lư<PERSON> trữ, xử lý và phân phối các file media với nhiều storage backend và tính năng xử lý media tiên tiến.

### Chức Năng Chính:
- Upload và quản lý files (images, videos, audio, documents)
- Quản lý folders với cấu trúc phân cấp
- Multiple storage backends (S3, Local Storage)
- Media processing và optimization
- File access control và permissions
- Multi-tenant file isolation
- CDN integration và public URLs

## 2. Kiến Trúc Tổng Quan

Module Media tuân thủ kiến trúc multi-tenant với pluggable storage backends:

```
modules/media/
├── api/                    # API layer
│   ├── handler.go         # Main API handler
│   ├── handlers/          # Specific endpoint handlers
│   │   ├── user/          # User media handlers
│   │   └── admin/         # Admin media handlers
│   └── routes/           # Route definitions
├── service/               # Business logic layer
│   ├── media_service.go          # Core media service
│   ├── media_folder_service.go   # Folder management
│   ├── storage_repository.go     # Storage abstraction
│   ├── user/                    # User-specific services
│   └── common/                  # Shared services
├── repository/            # Data access layer
│   ├── repository.go      # Repository interfaces
│   ├── mysql/            # MySQL implementations
│   └── s3/               # S3 storage implementation
├── models/               # Data models
├── dto/                  # Request/Response DTOs
├── migrations/           # Database migrations
├── configs/              # Configuration
└── internal/            # Internal configurations
```

### Storage Architecture:
```
Storage Backends:
├── S3 Storage (AWS S3, MinIO, etc.)
├── Local Storage (filesystem)
├── Google Cloud Storage (future)
└── Azure Blob Storage (future)

Media Types:
├── Images (JPEG, PNG, GIF, WebP)
├── Videos (MP4, AVI, MOV, WMV)
├── Audio (MP3, WAV, OGG, M4A)
├── Documents (PDF, DOC, XLS, TXT)
└── Other (custom types)
```

## 3. Các Thành Phần Chính

### 3.1 Models

#### Media Model
```go
type Media struct {
    ID               uint            `gorm:"primaryKey" json:"id"`
    TenantID         uint            `gorm:"index;not null" json:"tenant_id"`
    MediaType        dto.MediaType   `gorm:"type:enum('image','video','audio','document','other');not null" json:"media_type"`
    Filename         string          `gorm:"type:varchar(255);not null" json:"filename"`
    OriginalFilename string          `gorm:"type:varchar(255);not null" json:"original_filename"`
    ObjectKey        string          `gorm:"type:varchar(512);not null" json:"object_key"`
    ContentType      string          `gorm:"type:varchar(100);not null" json:"content_type"`
    Size             int64           `gorm:"type:bigint unsigned;not null" json:"size"`
    Status           dto.MediaStatus `gorm:"type:enum('pending','processing','ready','failed');default:pending" json:"status"`
    PublicURL        string          `gorm:"type:varchar(1024)" json:"public_url,omitempty"`
    Description      string          `gorm:"type:text" json:"description,omitempty"`
    UploadedBy       uint            `gorm:"type:int unsigned" json:"uploaded_by,omitempty"`
    Checksum         string          `gorm:"type:varchar(64)" json:"checksum,omitempty"`
    IsPublic         bool            `gorm:"default:false" json:"is_public"`
    FolderID         *uint           `gorm:"type:int unsigned" json:"folder_id,omitempty"`
    CreatedAt        time.Time       `json:"created_at"`
    UpdatedAt        time.Time       `json:"updated_at"`
    DeletedAt        *time.Time      `gorm:"index" json:"deleted_at,omitempty"`
}
```

#### MediaFolder Model
```go
type MediaFolder struct {
    FolderID    int        `gorm:"primaryKey" json:"folder_id"`
    TenantID    int        `gorm:"index;not null" json:"tenant_id"`
    ParentID    *int       `json:"parent_id,omitempty"`
    Name        string     `gorm:"type:varchar(255);not null" json:"name"`
    Slug        string     `gorm:"type:varchar(255);not null" json:"slug"`
    Description string     `gorm:"type:text" json:"description,omitempty"`
    IsPublic    bool       `gorm:"default:false" json:"is_public"`
    CreatedAt   time.Time  `json:"created_at"`
    UpdatedAt   time.Time  `json:"updated_at"`
    DeletedAt   *time.Time `gorm:"index" json:"deleted_at,omitempty"`
    CreatedBy   *int       `json:"created_by,omitempty"`
    UpdatedBy   *int       `json:"updated_by,omitempty"`
}
```

### 3.2 Repository Layer

Repository interface với tenant ID parameters:

```go
type MediaRepository interface {
    // Basic CRUD operations
    Create(ctx context.Context, tenantID uint, media *Media) error
    GetByID(ctx context.Context, tenantID uint, id uint) (*Media, error)
    Update(ctx context.Context, tenantID uint, media *Media) error
    Delete(ctx context.Context, tenantID uint, id uint, permanent bool) error
    
    // List operations với cursor-based pagination
    List(ctx context.Context, tenantID uint, params ListMediaParams) ([]*Media, string, error)
    
    // Search operations
    Search(ctx context.Context, tenantID uint, query string, params ListMediaParams) ([]*Media, string, error)
}

type MediaFolderRepository interface {
    // Folder CRUD operations
    Create(ctx context.Context, tenantID uint, folder *MediaFolder) error
    GetByID(ctx context.Context, tenantID uint, id uint) (*MediaFolder, error)
    GetBySlug(ctx context.Context, tenantID uint, slug string) (*MediaFolder, error)
    Update(ctx context.Context, tenantID uint, folder *MediaFolder) error
    Delete(ctx context.Context, tenantID uint, id uint) error
    
    // List operations
    List(ctx context.Context, tenantID uint, params ListMediaFolderParams) ([]*MediaFolder, string, error)
}

type StorageRepository interface {
    // Storage operations
    Upload(ctx context.Context, objectKey string, data []byte, contentType string) error
    Download(ctx context.Context, objectKey string) ([]byte, error)
    Delete(ctx context.Context, objectKey string) error
    GetURL(ctx context.Context, objectKey string) (string, error)
}
```

### 3.3 Service Layer

#### MediaService Interface
```go
type MediaService interface {
    Upload(ctx context.Context, tenantID uint, userID uint, file *multipart.FileHeader, req dto.UploadMediaRequest) (*dto.UploadMediaResponse, error)
    GetByID(ctx context.Context, tenantID uint, id uint) (*dto.MediaResponse, error)
    List(ctx context.Context, tenantID uint, req dto.ListMediaRequest) (*dto.ListMediaResponse, error)
    Update(ctx context.Context, tenantID uint, id uint, req dto.UpdateMediaRequest) (*dto.MediaResponse, error)
    Delete(ctx context.Context, tenantID uint, id uint, permanent bool) error
    GetFile(ctx context.Context, tenantID uint, id uint) ([]byte, string, error)
    ProcessFile(ctx context.Context, mediaEntity *Media) error
}
```

#### MediaFolderService Interface
```go
type MediaFolderService interface {
    Create(ctx context.Context, tenantID uint, userID uint, req dto.CreateFolderRequest) (*dto.FolderResponse, error)
    GetByID(ctx context.Context, tenantID uint, id uint) (*dto.FolderResponse, error)
    List(ctx context.Context, tenantID uint, req dto.ListFolderRequest) (*dto.ListFolderResponse, error)
    Update(ctx context.Context, tenantID uint, id uint, req dto.UpdateFolderRequest) (*dto.FolderResponse, error)
    Delete(ctx context.Context, tenantID uint, id uint) error
}
```

## 4. Database Schema và Relationships

### 4.1 Bảng media_files
```sql
CREATE TABLE media_files (
    id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    tenant_id INT UNSIGNED NOT NULL,
    media_type ENUM('image','video','audio','document','other') NOT NULL,
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    object_key VARCHAR(512) NOT NULL,
    content_type VARCHAR(100) NOT NULL,
    size BIGINT UNSIGNED NOT NULL,
    status ENUM('pending','processing','ready','failed') DEFAULT 'pending',
    public_url VARCHAR(1024),
    description TEXT,
    uploaded_by INT UNSIGNED,
    checksum VARCHAR(64),
    is_public BOOLEAN DEFAULT FALSE,
    folder_id INT UNSIGNED,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_media_type (media_type),
    INDEX idx_status (status),
    INDEX idx_folder_id (folder_id),
    INDEX idx_uploaded_by (uploaded_by),
    INDEX idx_deleted_at (deleted_at)
);
```

### 4.2 Bảng media_folders
```sql
CREATE TABLE media_folders (
    folder_id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    tenant_id INT UNSIGNED NOT NULL,
    parent_id INT UNSIGNED,
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL,
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    created_by INT UNSIGNED,
    updated_by INT UNSIGNED,
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_parent_id (parent_id),
    INDEX idx_slug (slug),
    INDEX idx_deleted_at (deleted_at),
    UNIQUE KEY uk_tenant_slug (tenant_id, slug)
);
```

### 4.3 Relationships
- `media_files.tenant_id` → `tenant_tenants.tenant_id` (Many-to-One)
- `media_files.folder_id` → `media_folders.folder_id` (Many-to-One)
- `media_files.uploaded_by` → `users.user_id` (Many-to-One)
- `media_folders.tenant_id` → `tenant_tenants.tenant_id` (Many-to-One)
- `media_folders.parent_id` → `media_folders.folder_id` (Self-referencing)

## 5. Tích Hợp Multi-Tenant

### 5.1 Data Isolation
- Tất cả media files và folders đều có `tenant_id`
- Repository layer tự động filter theo tenant
- Object keys include tenant ID: `{tenant_id}/{media_type}/{date}/{uuid}.ext`

### 5.2 Storage Isolation
```go
// Object key generation với tenant isolation
func generateObjectKey(tenantID uint, mediaType string, filename string) string {
    fileExt := filepath.Ext(filename)
    return fmt.Sprintf(
        "%d/%s/%s/%s%s",
        tenantID,
        mediaType,
        time.Now().Format("2006-01-02"),
        uuid.New().String(),
        fileExt,
    )
}
```

## 6. Authentication và Authorization Integration

### 6.1 Permission Definitions
```go
// Permission constants cho media module
const (
    UploadPermission     = "media.upload"
    ReadPermission       = "media.read"
    UpdatePermission     = "media.update"
    DeletePermission     = "media.delete"
    DownloadPermission   = "media.download"
    FolderCreatePermission = "media.folders.create"
    FolderReadPermission   = "media.folders.read"
    FolderUpdatePermission = "media.folders.update"
    FolderDeletePermission = "media.folders.delete"
)
```

### 6.2 Middleware Integration
```go
// Protected routes với permission checking
func RegisterUserRoutes(router *gin.RouterGroup, ...) {
    mediaRoutes := router.Group("/media/file")
    {
        mediaRoutes.Use(tracing.GinMiddleware("media"))
        mediaRoutes.Use(jwtService.JWTAuthMiddleware())
        
        mediaRoutes.POST("", 
            pkgMiddleware.RequirePermission(permService, "media.upload"),
            mediaHandler.UploadFile)
        
        mediaRoutes.GET("", 
            pkgMiddleware.RequirePermission(permService, "media.read"),
            mediaHandler.GetMediaList)
        
        mediaRoutes.GET("/:id", 
            pkgMiddleware.RequirePermission(permService, "media.read"),
            mediaHandler.GetMediaByID)
        
        mediaRoutes.DELETE("/:id", 
            pkgMiddleware.RequirePermission(permService, "media.delete"),
            mediaHandler.DeleteMedia)
    }
}
```

## 7. API Endpoints

### 7.1 Media File Endpoints
- `POST /api/v1/media/file` - Upload file mới
- `GET /api/v1/media/file` - Danh sách files (với pagination và filters)
- `GET /api/v1/media/file/:id` - Chi tiết file
- `GET /api/v1/media/file/:id/url` - Lấy URL download
- `PUT /api/v1/media/file/:id` - Cập nhật metadata
- `DELETE /api/v1/media/file/:id` - Xóa file

### 7.2 Folder Management Endpoints
- `GET /api/v1/media/folder` - Danh sách folders
- `POST /api/v1/media/folder` - Tạo folder mới
- `GET /api/v1/media/folder/:id` - Chi tiết folder
- `PUT /api/v1/media/folder/:id` - Cập nhật folder
- `DELETE /api/v1/media/folder/:id` - Xóa folder

### 7.3 Admin Endpoints
- `GET /api/v1/admin/media/stats` - Thống kê media usage
- `POST /api/v1/admin/media/cleanup` - Cleanup unused files
- `GET /api/v1/admin/media/storage` - Storage usage information

## 8. Dependencies

### 8.1 Module Dependencies
- **Tenant Module**: Cần để xác định tenant context
- **Auth Module**: Cần để xác định user context
- **RBAC Module**: Cần để kiểm tra permissions

### 8.2 Package Dependencies
- `internal/pkg/response`: Standardized API responses
- `internal/pkg/errors`: Error handling
- `internal/pkg/tracing`: Database tracing
- `github.com/google/uuid`: UUID generation

### 8.3 External Dependencies
- AWS SDK (cho S3 storage)
- Image processing libraries
- Video processing tools (future)

## 9. Cấu Hình

### 9.1 Environment Variables
```env
# Storage Configuration
MEDIA_STORAGE_TYPE=s3
MEDIA_S3_BUCKET=my-media-bucket
MEDIA_S3_REGION=us-east-1
MEDIA_S3_ACCESS_KEY=your-access-key
MEDIA_S3_SECRET_KEY=your-secret-key

# File Limits
MEDIA_MAX_FILE_SIZE=104857600  # 100MB
MEDIA_IMAGE_QUALITY=85

# Allowed Types
MEDIA_ALLOWED_TYPES=image/jpeg,image/png,image/gif,video/mp4,application/pdf
```

### 9.2 Module Configuration
```go
type MediaConfig struct {
    StorageType      string   `yaml:"storage_type" env:"MEDIA_STORAGE_TYPE" envDefault:"s3"`
    S3Bucket         string   `yaml:"s3_bucket" env:"MEDIA_S3_BUCKET"`
    S3Region         string   `yaml:"s3_region" env:"MEDIA_S3_REGION"`
    S3AccessKey      string   `yaml:"s3_access_key" env:"MEDIA_S3_ACCESS_KEY"`
    S3SecretKey      string   `yaml:"s3_secret_key" env:"MEDIA_S3_SECRET_KEY"`
    MaxFileSize      int64    `yaml:"max_file_size" env:"MEDIA_MAX_FILE_SIZE" envDefault:"104857600"`
    AllowedTypes     []string `yaml:"allowed_types" env:"MEDIA_ALLOWED_TYPES"`
    ImageQuality     int      `yaml:"image_quality" env:"MEDIA_IMAGE_QUALITY" envDefault:"85"`
    EnableProcessing bool     `yaml:"enable_processing" env:"MEDIA_ENABLE_PROCESSING" envDefault:"true"`
}
```

## 10. Ví Dụ Sử Dụng

### 10.1 Upload File
```bash
curl -X POST http://localhost:8080/api/v1/media/file \
  -H "Authorization: Bearer jwt-token" \
  -H "X-Tenant-Domain: tenant1.example.com" \
  -F "file=@image.jpg" \
  -F "folder_id=5" \
  -F "description=Product image" \
  -F "is_public=true"
```

### 10.2 Lấy Danh Sách Files
```bash
curl -X GET "http://localhost:8080/api/v1/media/file?media_type=image&folder_id=5&limit=20&cursor=eyJpZCI6MTB9" \
  -H "Authorization: Bearer jwt-token" \
  -H "X-Tenant-Domain: tenant1.example.com"
```

### 10.3 Tạo Folder
```bash
curl -X POST http://localhost:8080/api/v1/media/folder \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer jwt-token" \
  -H "X-Tenant-Domain: tenant1.example.com" \
  -d '{
    "name": "Product Images",
    "description": "Images for products",
    "parent_id": null,
    "is_public": false
  }'
```

### 10.4 Lấy URL Download
```bash
curl -X GET http://localhost:8080/api/v1/media/file/123/url \
  -H "Authorization: Bearer jwt-token" \
  -H "X-Tenant-Domain: tenant1.example.com"
```

## 11. Storage Backends

### 11.1 S3 Storage
```go
type s3StorageRepository struct {
    client   *s3.Client
    bucket   string
    region   string
    logger   logger.Logger
}

func (r *s3StorageRepository) Upload(ctx context.Context, objectKey string, data []byte, contentType string) error {
    _, err := r.client.PutObject(ctx, &s3.PutObjectInput{
        Bucket:      aws.String(r.bucket),
        Key:         aws.String(objectKey),
        Body:        bytes.NewReader(data),
        ContentType: aws.String(contentType),
    })
    return err
}
```

### 11.2 Local Storage
```go
type localStorageRepository struct {
    basePath string
    logger   logger.Logger
}

func (r *localStorageRepository) Upload(ctx context.Context, objectKey string, data []byte, contentType string) error {
    filePath := filepath.Join(r.basePath, objectKey)
    
    // Create directory if not exists
    dir := filepath.Dir(filePath)
    if err := os.MkdirAll(dir, 0755); err != nil {
        return err
    }
    
    return ioutil.WriteFile(filePath, data, 0644)
}
```

## 12. Media Processing

### 12.1 Image Processing
- Automatic thumbnail generation
- Image optimization và compression
- Format conversion (JPEG, PNG, WebP)
- Resize và crop operations

### 12.2 Video Processing (Future)
- Video thumbnail extraction
- Format conversion
- Compression và optimization
- Streaming support

## 13. Cursor-Based Pagination

```go
type ListMediaResponse struct {
    Data       []*MediaResponse `json:"data"`
    NextCursor string           `json:"next_cursor,omitempty"`
    HasMore    bool             `json:"has_more"`
}

type ListMediaParams struct {
    MediaType *MediaType
    Status    *MediaStatus
    IsPublic  *bool
    FolderID  *uint
    Search    string
    Cursor    string
    Limit     int
}
```
