# Tổng Quan Module Notification

## 1. <PERSON><PERSON><PERSON>ch và Trách Nhiệm

Module Notification chịu trách nhiệm quản lý và gửi thông báo đa kênh trong hệ thống multi-tenant. Module này cung cấp khả năng gửi thông báo qua email, SMS, push notification, websocket và các kênh khác với template system và queue processing.

### Chức Năng Chính:
- G<PERSON><PERSON> thông báo qua nhiều kênh (email, SMS, push, websocket)
- Quản lý templates thông báo
- Queue-based notification processing
- Notification preferences và settings
- Delivery tracking và retry mechanism
- Multi-tenant notification isolation
- Event-driven notification system

## 2. Kiến Trúc Tổng Quan

Module Notification tuân thủ kiến trúc multi-tenant với event-driven và queue processing:

```
modules/notification/
├── api/                    # API layer
│   ├── handler.go         # Main API handler
│   ├── handlers/          # Specific endpoint handlers
│   └── middleware/        # Auth middleware
├── service/               # Business logic layer
│   ├── notification_service.go    # Core notification service
│   ├── email_service.go          # Email service
│   ├── channel_service.go        # Channel management
│   ├── queue_handlers.go         # Queue processing
│   └── delivery/              # Delivery services
├── repository/            # Data access layer
│   ├── repository.go      # Repository interfaces
│   ├── mysql/            # MySQL implementations
│   └── redis/            # Redis implementations
├── models/               # Data models
├── dto/                  # Request/Response DTOs
├── events/              # Event handling
├── migrations/          # Database migrations
├── configs/             # Configuration
└── internal/            # Internal configurations
```

### Event-Driven Architecture:
```
Event Publisher → Event Router → Notification Handlers → Queue → Delivery Services
```

## 3. Các Thành Phần Chính

### 3.1 Models

#### Notification Model
```go
type Notification struct {
    NotificationID uint                   `gorm:"primaryKey" json:"notification_id"`
    TenantID       uint                   `gorm:"index" json:"tenant_id"`
    UserID         *uint                  `gorm:"index" json:"user_id"`
    Type           string                 `json:"type"`
    Title          string                 `json:"title"`
    Content        string                 `gorm:"type:text" json:"content"`
    Data           string                 `gorm:"type:json" json:"data"`
    Channels       []string               `gorm:"type:json" json:"channels"`
    Status         NotificationStatus     `json:"status"`
    ScheduledAt    *time.Time            `json:"scheduled_at"`
    SentAt         *time.Time            `json:"sent_at"`
    CreatedAt      time.Time             `json:"created_at"`
    UpdatedAt      time.Time             `json:"updated_at"`
}
```

#### Channel Model
```go
type Channel struct {
    ChannelID   uint      `gorm:"primaryKey" json:"channel_id"`
    TenantID    uint      `gorm:"index" json:"tenant_id"`
    Name        string    `json:"name"`
    Type        string    `json:"type"` // email, sms, push, websocket
    Config      string    `gorm:"type:json" json:"config"`
    IsActive    bool      `gorm:"default:true" json:"is_active"`
    IsDefault   bool      `gorm:"default:false" json:"is_default"`
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
}
```

#### Template Model
```go
type Template struct {
    TemplateID  uint      `gorm:"primaryKey" json:"template_id"`
    TenantID    uint      `gorm:"index" json:"tenant_id"`
    Name        string    `json:"name"`
    Type        string    `json:"type"`
    Subject     string    `json:"subject"`
    Content     string    `gorm:"type:text" json:"content"`
    Variables   string    `gorm:"type:json" json:"variables"`
    IsActive    bool      `gorm:"default:true" json:"is_active"`
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
}
```

#### Delivery Model
```go
type Delivery struct {
    DeliveryID     uint           `gorm:"primaryKey" json:"delivery_id"`
    NotificationID uint           `gorm:"index" json:"notification_id"`
    ChannelID      uint           `gorm:"index" json:"channel_id"`
    Recipient      string         `json:"recipient"`
    Status         DeliveryStatus `json:"status"`
    AttemptCount   int            `gorm:"default:0" json:"attempt_count"`
    LastAttemptAt  *time.Time     `json:"last_attempt_at"`
    DeliveredAt    *time.Time     `json:"delivered_at"`
    ErrorMessage   string         `json:"error_message"`
    CreatedAt      time.Time      `json:"created_at"`
    UpdatedAt      time.Time      `json:"updated_at"`
}
```

### 3.2 Repository Layer

Repository interface với tenant ID parameters:

```go
type Repository interface {
    Notification() NotificationRepository
    Template() TemplateRepository
    Channel() ChannelRepository
    Preference() PreferenceRepository
    Delivery() DeliveryRepository
    Telegram() TelegramRepository
    Websocket() WebsocketRepository
}

type NotificationRepository interface {
    Create(ctx context.Context, tenantID uint, notification *models.Notification) error
    GetByID(ctx context.Context, tenantID uint, notificationID uint) (*models.Notification, error)
    Update(ctx context.Context, tenantID uint, notification *models.Notification) error
    List(ctx context.Context, tenantID uint, params dto.ListNotificationsParams) ([]models.Notification, int64, error)
    GetPendingNotifications(ctx context.Context, limit int) ([]models.Notification, error)
}
```

### 3.3 Service Layer

#### NotificationService Interface
```go
type NotificationService interface {
    SendNotification(ctx context.Context, tenantID uint, req dto.SendNotificationRequest) (*dto.NotificationResponse, error)
    SendEmail(ctx context.Context, tenantID uint, req dto.SendEmailRequest) error
    SendSMS(ctx context.Context, tenantID uint, req dto.SendSMSRequest) error
    ScheduleNotification(ctx context.Context, tenantID uint, req dto.ScheduleNotificationRequest) (*dto.NotificationResponse, error)
    GetNotification(ctx context.Context, tenantID uint, notificationID uint) (*dto.NotificationResponse, error)
    ListNotifications(ctx context.Context, tenantID uint, params dto.ListNotificationsParams) (*dto.ListNotificationsResponse, error)
}
```

#### EmailService Interface
```go
type EmailService interface {
    SendEmail(ctx context.Context, to, subject, body string) error
    SendEmailWithTemplate(ctx context.Context, to string, templateID uint, data map[string]interface{}) error
    SendVerificationEmail(ctx context.Context, to, token, webURL string) error
    SendPasswordResetEmail(ctx context.Context, to, token, webURL string) error
}
```

## 4. Database Schema và Relationships

### 4.1 Bảng notification_notifications
```sql
CREATE TABLE notification_notifications (
    notification_id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    tenant_id INT UNSIGNED NOT NULL,
    user_id INT UNSIGNED,
    type VARCHAR(50) NOT NULL,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    data JSON,
    channels JSON,
    status ENUM('pending', 'processing', 'sent', 'failed', 'cancelled') DEFAULT 'pending',
    scheduled_at TIMESTAMP NULL,
    sent_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_scheduled_at (scheduled_at)
);
```

### 4.2 Bảng notification_channels
```sql
CREATE TABLE notification_channels (
    channel_id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    tenant_id INT UNSIGNED NOT NULL,
    name VARCHAR(100) NOT NULL,
    type ENUM('email', 'sms', 'push', 'websocket', 'telegram') NOT NULL,
    config JSON NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    is_default BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_type (type),
    INDEX idx_is_active (is_active)
);
```

### 4.3 Bảng notification_templates
```sql
CREATE TABLE notification_templates (
    template_id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    tenant_id INT UNSIGNED NOT NULL,
    name VARCHAR(100) NOT NULL,
    type VARCHAR(50) NOT NULL,
    subject VARCHAR(255),
    content TEXT NOT NULL,
    variables JSON,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_type (type),
    INDEX idx_is_active (is_active)
);
```

### 4.4 Bảng notification_deliveries
```sql
CREATE TABLE notification_deliveries (
    delivery_id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    notification_id INT UNSIGNED NOT NULL,
    channel_id INT UNSIGNED NOT NULL,
    recipient VARCHAR(255) NOT NULL,
    status ENUM('pending', 'processing', 'delivered', 'failed', 'bounced') DEFAULT 'pending',
    attempt_count INT DEFAULT 0,
    last_attempt_at TIMESTAMP NULL,
    delivered_at TIMESTAMP NULL,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_notification_id (notification_id),
    INDEX idx_channel_id (channel_id),
    INDEX idx_status (status),
    INDEX idx_recipient (recipient)
);
```

### 4.5 Relationships
- `notification_notifications.tenant_id` → `tenant_tenants.tenant_id` (Many-to-One)
- `notification_notifications.user_id` → `users.user_id` (Many-to-One)
- `notification_channels.tenant_id` → `tenant_tenants.tenant_id` (Many-to-One)
- `notification_templates.tenant_id` → `tenant_tenants.tenant_id` (Many-to-One)
- `notification_deliveries.notification_id` → `notification_notifications.notification_id` (Many-to-One)
- `notification_deliveries.channel_id` → `notification_channels.channel_id` (Many-to-One)

## 5. Tích Hợp Multi-Tenant

### 5.1 Data Isolation
- Tất cả notifications, channels, templates đều có `tenant_id`
- Repository layer tự động filter theo tenant
- Queue processing maintain tenant context

### 5.2 Tenant-Specific Configuration
```go
// Channel configuration per tenant
type ChannelConfig struct {
    SMTP struct {
        Host     string `json:"host"`
        Port     int    `json:"port"`
        Username string `json:"username"`
        Password string `json:"password"`
        From     string `json:"from"`
    } `json:"smtp"`
    
    SMS struct {
        Provider string `json:"provider"`
        APIKey   string `json:"api_key"`
        From     string `json:"from"`
    } `json:"sms"`
}
```

## 6. Authentication và Authorization Integration

### 6.1 Middleware Integration
```go
// Protected routes với permission checking
func (h *Handler) RegisterRoutes(server *core.Server) error {
    apiGroup := server.Group("/api/v1/notifications")
    
    // Apply middleware order: Tenant → Auth → RBAC
    protected := apiGroup.Group("/")
    protected.Use(tenantMiddleware())
    protected.Use(authMiddleware())
    protected.Use(permissionMiddleware())
    
    protected.POST("/send", h.SendNotification)
    protected.GET("/", h.ListNotifications)
    protected.GET("/:id", h.GetNotification)
    
    return nil
}
```

### 6.2 Permission Requirements
- `notifications.send`: Gửi thông báo
- `notifications.read`: Đọc thông báo
- `notifications.templates.manage`: Quản lý templates
- `notifications.channels.manage`: Quản lý channels

## 7. API Endpoints

### 7.1 Notification Endpoints
- `POST /api/v1/notifications/send` - Gửi thông báo
- `POST /api/v1/notifications/send-email` - Gửi email
- `POST /api/v1/notifications/schedule` - Lên lịch thông báo
- `GET /api/v1/notifications` - Danh sách thông báo
- `GET /api/v1/notifications/:id` - Chi tiết thông báo

### 7.2 Template Endpoints
- `GET /api/v1/notifications/templates` - Danh sách templates
- `POST /api/v1/notifications/templates` - Tạo template mới
- `PUT /api/v1/notifications/templates/:id` - Cập nhật template

### 7.3 Channel Endpoints
- `GET /api/v1/notifications/channels` - Danh sách channels
- `POST /api/v1/notifications/channels` - Tạo channel mới
- `PUT /api/v1/notifications/channels/:id` - Cập nhật channel

## 8. Dependencies

### 8.1 Module Dependencies
- **Tenant Module**: Cần để xác định tenant context
- **Auth Module**: Cần để xác định user context
- **RBAC Module**: Cần để kiểm tra permissions

### 8.2 Package Dependencies
- `internal/pkg/queue`: Queue processing
- `internal/pkg/events`: Event handling
- `internal/pkg/response`: Standardized API responses
- `internal/pkg/errors`: Error handling

### 8.3 External Dependencies
- SMTP servers cho email
- SMS providers (Twilio, etc.)
- Push notification services
- Redis cho queue và caching

## 9. Cấu Hình

### 9.1 Environment Variables
```env
# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-password
SMTP_FROM=<EMAIL>

# Queue Configuration
REDIS_URL=redis://localhost:6379
QUEUE_WORKERS=5
QUEUE_MAX_RETRIES=3

# Notification Settings
NOTIFICATION_BATCH_SIZE=100
NOTIFICATION_RETRY_DELAY=300s
```

### 9.2 Module Configuration
```go
type NotificationConfig struct {
    SMTPHost     string `env:"SMTP_HOST"`
    SMTPPort     int    `env:"SMTP_PORT"`
    SMTPUsername string `env:"SMTP_USERNAME"`
    SMTPPassword string `env:"SMTP_PASSWORD"`
    SMTPFrom     string `env:"SMTP_FROM"`
    
    QueueWorkers    int           `env:"QUEUE_WORKERS" envDefault:"5"`
    MaxRetries      int           `env:"QUEUE_MAX_RETRIES" envDefault:"3"`
    RetryDelay      time.Duration `env:"NOTIFICATION_RETRY_DELAY" envDefault:"300s"`
    BatchSize       int           `env:"NOTIFICATION_BATCH_SIZE" envDefault:"100"`
}
```

## 10. Ví Dụ Sử Dụng

### 10.1 Gửi Email Đơn Giản
```bash
curl -X POST http://localhost:8080/api/v1/notifications/send-email \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer jwt-token" \
  -H "X-Tenant-Domain: tenant1.example.com" \
  -d '{
    "to": "<EMAIL>",
    "subject": "Welcome to our platform",
    "body": "Thank you for joining us!"
  }'
```

### 10.2 Gửi Thông Báo Đa Kênh
```bash
curl -X POST http://localhost:8080/api/v1/notifications/send \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer jwt-token" \
  -H "X-Tenant-Domain: tenant1.example.com" \
  -d '{
    "user_id": 123,
    "type": "order_confirmation",
    "title": "Order Confirmed",
    "content": "Your order #12345 has been confirmed",
    "channels": ["email", "sms", "push"],
    "data": {
      "order_id": "12345",
      "amount": "100.00"
    }
  }'
```

### 10.3 Sử Dụng Template
```bash
curl -X POST http://localhost:8080/api/v1/notifications/send \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer jwt-token" \
  -H "X-Tenant-Domain: tenant1.example.com" \
  -d '{
    "user_id": 123,
    "template_id": 5,
    "channels": ["email"],
    "data": {
      "user_name": "John Doe",
      "verification_link": "https://example.com/verify/token123"
    }
  }'
```

## 11. Event Integration

### 11.1 Event Handling
Module Notification lắng nghe các events:
- `user.registered` → Gửi welcome email
- `user.password.reset.requested` → Gửi reset password email
- `order.created` → Gửi order confirmation
- `payment.completed` → Gửi payment receipt

### 11.2 Event Publishing
Module Notification publish các events:
- `notification.sent`: Khi gửi thông báo thành công
- `notification.failed`: Khi gửi thông báo thất bại
- `email.delivered`: Khi email được gửi thành công
- `sms.delivered`: Khi SMS được gửi thành công

## 12. Queue Processing

### 12.1 Queue Handlers
```go
// Queue handler cho email processing
func (h *QueueHandlers) ProcessEmailNotification(ctx context.Context, payload []byte) error {
    var emailTask EmailTask
    if err := json.Unmarshal(payload, &emailTask); err != nil {
        return err
    }
    
    return h.emailService.SendEmail(ctx, emailTask.To, emailTask.Subject, emailTask.Body)
}
```

### 12.2 Retry Mechanism
- Automatic retry với exponential backoff
- Maximum retry attempts configurable
- Dead letter queue cho failed notifications
- Delivery status tracking
