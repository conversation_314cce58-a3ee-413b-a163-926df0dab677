# Quy Trình <PERSON>

## Tổng Quan
Quy trình đăng ký cho phép người dùng mới tạo tài khoản trong hệ thống. Tài liệu này mô tả chi tiết toàn bộ luồng từ đầu vào của người dùng đến khi tạo tài khoản thành công.

## Sơ Đồ Quy Trình

```mermaid
flowchart LR
    A[Nhập thông tin] --> B[Xác thực dữ liệu]
    B --> C{Kiểm tra người dùng}
    C -->|Chưa tồn tại| D[Tạo người dùng]
    C -->|Đã tồn tại| G[Trả lỗi]
    D --> E[Gửi xác minh email]
    E --> I[Tạo Tenant]
    I --> F[Phản hồi thành công]
    G --> H[<PERSON>ết thúc với lỗi]
```

## C<PERSON><PERSON>uan

### 1. <PERSON><PERSON>le Controller (AuthController)

- T<PERSON><PERSON><PERSON> nhận request từ client
- <PERSON>y<PERSON><PERSON> dữ liệu sang service xử lý
- Trả về response cho client

### 2. Module Service (AuthService)

- Xử lý logic nghiệp vụ
- Gọi repository để thao tác với database
- Xử lý mã hóa mật khẩu
- Khởi tạo token xác thực

### 3. Module Repository (UserRepository)

- Thực hiện các thao tác CRUD với database
- Kiểm tra sự tồn tại của email/username
- Lưu trữ thông tin người dùng mới

### 4. Module Validation (ValidationPipe)

- Kiểm tra định dạng email
- Xác thực độ mạnh của mật khẩu
- Kiểm tra các ràng buộc về username

### 5. Module Notification (EmailService)

- Tạo mẫu email xác thực
- Gửi email với token xác minh
- Xử lý các lỗi gửi email

## Các Bước Chi Tiết

1. **Thu thập thông tin người dùng**
   - Địa chỉ email
   - Mật khẩu (kèm xác nhận)
   - Tên người dùng (tùy chọn)
   - Thông tin hồ sơ bổ sung (tùy theo yêu cầu)

2. **Xác thực đầu vào**
   - Kiểm tra định dạng email
   - Kiểm tra yêu cầu về độ mạnh mật khẩu
   - Kiểm tra các ràng buộc về tên người dùng (nếu có)

3. **Kiểm tra trùng lặp**
   - Xác minh email chưa được đăng ký
   - Xác minh tên người dùng chưa được sử dụng (nếu có)

4. **Tạo người dùng**
   - Mã hóa mật khẩu
   - Tạo bản ghi người dùng trong cơ sở dữ liệu
   - Đặt trạng thái tài khoản là chưa xác minh

5. **Quy trình xác minh**
   - Tạo mã thông báo xác minh
   - Lưu trữ mã thông báo với thời gian hết hạn
   - Gửi email xác minh cho người dùng

6. **Tạo Tenant**
   - Sau khi người dùng được tạo và email đã được gửi, hệ thống sẽ tiến hành tạo một tenant mới liên kết với người dùng này.
   - Tenant đại diện cho một không gian làm việc riêng biệt cho người dùng hoặc tổ chức của họ.

7. **Xử lý phản hồi**
   - Phản hồi thành công với hướng dẫn
   - Xử lý lỗi với các thông báo phù hợp

## API Endpoint

### POST /api/auth/register

**Request Body:**

```json
{
  "email": "<EMAIL>",
  "password": "securePassword123",
  "username": "username123"
}
```

**Phản hồi thành công (200):**

```json
{
  "success": true,
  "message": "Đăng ký thành công. Vui lòng xác minh email của bạn.",
  "userId": "user-uuid"
}
```

**Phản hồi lỗi:**

- 400: Bad Request (lỗi xác thực)
- 409: Conflict (email/username đã tồn tại)
- 500: Lỗi máy chủ

## Vấn đề bảo mật

- Mật khẩu phải được mã hóa bằng bcrypt hoặc tương tự
- Mã thông báo xác minh nên hết hạn trong vòng 24 giờ
- Giới hạn tốc độ đăng ký từ cùng một IP
- CAPTCHA cho các mẫu đăng ký đáng ngờ
- Ghi nhật ký các nỗ lực đăng ký để giám sát bảo mật

## Ghi chú triển khai

- Sử dụng middleware xác thực mạnh
- Triển khai xử lý lỗi phù hợp
- Lưu trữ thông tin nhạy cảm một cách an toàn
- Tuân thủ các quy định bảo vệ dữ liệu (GDPR, v.v.)

## Yêu cầu kiểm thử

- Kiểm tra đầu vào hợp lệ
- Kiểm tra các đầu vào không hợp lệ khác nhau
- Kiểm tra các nỗ lực đăng ký trùng lặp
- Kiểm tra hoàn thành luồng xác minh
