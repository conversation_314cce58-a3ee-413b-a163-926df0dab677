# Tổng Quan Module Auth

## 1. <PERSON><PERSON><PERSON> và Trách Nhiệm

Module Auth chịu trách nhiệm xử lý tất cả các chức năng liên quan đến xác thực và quản lý người dùng trong hệ thống multi-tenant. Module này cung cấp các tính năng cốt lõi cho việc đăng ký, đăng nhập, quản lý session và xác thực email.

### Chức Năng Chính:
- Đăng ký và đăng nhập người dùng
- Quản lý JWT tokens (access và refresh tokens)
- Reset mật khẩu qua email
- X<PERSON><PERSON> thực email người dùng
- Quản lý session và logout
- Quản lý profile người dùng
- Tích hợp với notification service để gửi email

## 2. Kiến Trúc Tổng Quan

Module Auth tuân thủ kiến trúc multi-tenant với strict data isolation và middleware ordering pattern:

```
modules/auth/
├── api/                    # API layer
│   ├── handlers/          # HTTP request handlers
│   └── routes.go          # Route definitions
├── service/               # Business logic layer
│   ├── auth_service.go    # Core authentication service
│   └── password_reset_service.go
├── repository/            # Data access layer
│   ├── repository.go      # Repository interfaces
│   └── mysql/            # MySQL implementations
├── models/               # Data models
├── dto/                  # Request/Response DTOs
├── migrations/           # Database migrations
├── events/              # Event publishing
├── queue/               # Queue handlers
└── internal/            # Internal configurations
```

### Middleware Ordering:
```
Tenant → Auth → RBAC
```

## 3. Các Thành Phần Chính

### 3.1 Models

#### User Model
```go
type User struct {
    UserID          uint       `gorm:"column:user_id;primaryKey;autoIncrement"`
    TenantID        uint       `gorm:"column:tenant_id;index"`
    Username        string     `gorm:"column:username;uniqueIndex"`
    Email           string     `gorm:"column:email;uniqueIndex"`
    PasswordHash    string     `gorm:"column:password_hash"`
    FullName        string     `gorm:"column:full_name"`
    Status          UserStatus `gorm:"column:status"`
    IsEmailVerified bool       `gorm:"column:is_email_verified"`
    UserType        UserType   `gorm:"column:user_type"`
    CreatedAt       time.Time
    UpdatedAt       time.Time
    LastLogin       *time.Time
}
```

#### AuthSession Model
```go
type AuthSession struct {
    ID           string    `gorm:"column:id;primaryKey"`
    UserID       uint      `gorm:"column:user_id;index"`
    AccessToken  string    `gorm:"column:access_token"`
    RefreshToken string    `gorm:"column:refresh_token"`
    ExpiresAt    time.Time `gorm:"column:expires_at;index"`
    CreatedAt    time.Time
    UpdatedAt    time.Time
}
```

#### EmailVerification Model
```go
type EmailVerification struct {
    ID        int64     `gorm:"primaryKey"`
    UserID    int64     `gorm:"index"`
    Email     string    `gorm:"index"`
    Token     string    `gorm:"index"`
    Verified  bool      `gorm:"default:false"`
    ExpiresAt time.Time
    CreatedAt time.Time
    UpdatedAt time.Time
}
```

### 3.2 Repository Layer

Repository interface với tenant ID parameters:

```go
type Repository interface {
    // User operations với tenant isolation
    CreateUser(ctx context.Context, tenantID uint, user *models.User) error
    GetUserByEmail(ctx context.Context, tenantID uint, email string) (*models.User, error)
    GetUserByID(ctx context.Context, tenantID uint, userID uint) (*models.User, error)
    UpdateUser(ctx context.Context, tenantID uint, user *models.User) error
    
    // Session operations
    CreateSession(ctx context.Context, session *models.AuthSession) error
    GetSessionByToken(ctx context.Context, token string) (*models.AuthSession, error)
    DeleteSession(ctx context.Context, sessionID string) error
    
    // Email verification
    CreateEmailVerification(ctx context.Context, verification *models.EmailVerification) error
    GetEmailVerificationByToken(ctx context.Context, token string) (*models.EmailVerification, error)
}
```

### 3.3 Service Layer

#### AuthService Interface
```go
type AuthService interface {
    Register(ctx context.Context, tenantID uint, req dto.RegisterRequest) (*dto.RegisterResponse, error)
    Login(ctx context.Context, tenantID uint, req dto.LoginRequest) (*dto.LoginResponse, error)
    Logout(ctx context.Context, sessionID string) error
    RefreshToken(ctx context.Context, refreshToken string) (*dto.RefreshTokenResponse, error)
    ChangePassword(ctx context.Context, userID uint, req dto.ChangePasswordRequest) error
    GetProfile(ctx context.Context, userID uint) (*dto.ProfileResponse, error)
    VerifyEmail(ctx context.Context, token string) error
}
```

## 4. Database Schema và Relationships

### 4.1 Bảng users
```sql
CREATE TABLE users (
  user_id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
  tenant_id INT UNSIGNED NOT NULL,
  username VARCHAR(50) NOT NULL UNIQUE,
  email VARCHAR(100) NOT NULL UNIQUE,
  password_hash VARCHAR(255) NOT NULL,
  full_name VARCHAR(100),
  user_type ENUM('admin', 'tenant', 'customer') NOT NULL,
  status ENUM('active', 'inactive', 'suspended', 'pending_verification', 
              'email_verification_required', 'banned', 'locked', 'deleted') 
         DEFAULT 'pending_verification',
  is_email_verified TINYINT(1) NOT NULL DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  last_login TIMESTAMP NULL,
  INDEX idx_users_email (email),
  INDEX idx_users_tenant_id (tenant_id)
);
```

### 4.2 Bảng auth_sessions
```sql
CREATE TABLE auth_sessions (
    id VARCHAR(36) PRIMARY KEY,
    user_id INT NOT NULL,
    access_token TEXT NOT NULL,
    refresh_token TEXT NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_expires_at (expires_at)
);
```

### 4.3 Bảng auth_email_verifications
```sql
CREATE TABLE auth_email_verifications (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    email VARCHAR(255) NOT NULL,
    token VARCHAR(255) NOT NULL,
    verified BOOLEAN NOT NULL DEFAULT FALSE,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_token (token),
    INDEX idx_user_id (user_id),
    INDEX idx_email (email)
);
```

### 4.4 Relationships
- `users.tenant_id` → `tenants.tenant_id` (Many-to-One)
- `auth_sessions.user_id` → `users.user_id` (Many-to-One)
- `auth_email_verifications.user_id` → `users.user_id` (One-to-Many)

## 5. Tích Hợp Multi-Tenant

### 5.1 Data Isolation
- Tất cả operations đều yêu cầu `tenantID` parameter
- Repository layer tự động filter theo tenant
- Middleware xác định tenant từ domain/subdomain

### 5.2 Tenant Context
```go
// Middleware order: Tenant → Auth → RBAC
func (h *Handler) RegisterRoutes(server *core.Server) error {
    apiGroup := server.Group("/api/v1/auth")
    
    // Public routes (không cần auth)
    apiGroup.POST("/login", h.adminAuthHandler.Login)
    apiGroup.POST("/register", h.adminAuthHandler.Register)
    
    // Protected routes (cần auth)
    authenticated := apiGroup.Group("/")
    authenticated.Use(h.jwtService.JWTAuthMiddleware())
    {
        authenticated.POST("/logout", h.adminAuthHandler.Logout)
        authenticated.GET("/profile", h.adminAuthHandler.GetProfile)
    }
    
    return nil
}
```

## 6. Authentication và Authorization Integration

### 6.1 JWT Configuration
```go
type JWTConfig struct {
    AccessSigningKey       string
    RefreshSigningKey      string
    AccessTokenExpiration  time.Duration
    RefreshTokenExpiration time.Duration
    Issuer                 string
}
```

### 6.2 Middleware Integration
- JWT middleware xác thực access tokens
- Tự động extract user context
- Integration với RBAC module cho permission checking

## 7. API Endpoints

### 7.1 Public Endpoints
- `POST /api/v1/auth/login` - Đăng nhập
- `POST /api/v1/auth/register` - Đăng ký
- `POST /api/v1/auth/refresh-token` - Refresh token
- `POST /api/v1/auth/forgot-password` - Quên mật khẩu
- `POST /api/v1/auth/reset-password` - Reset mật khẩu
- `POST /api/v1/auth/verify-email` - Xác thực email

### 7.2 Protected Endpoints
- `POST /api/v1/auth/logout` - Đăng xuất
- `PUT /api/v1/auth/change-password` - Đổi mật khẩu
- `GET /api/v1/auth/profile` - Lấy thông tin profile

## 8. Dependencies

### 8.1 Module Dependencies
- **Tenant Module**: Cần để xác định tenant context
- **Notification Module**: Để gửi email verification và password reset

### 8.2 Package Dependencies
- `internal/pkg/auth`: JWT service và middleware
- `internal/pkg/response`: Standardized API responses
- `internal/pkg/errors`: Error handling
- `internal/pkg/events`: Event publishing

## 9. Cấu Hình

### 9.1 Environment Variables
```env
# JWT Configuration
JWT_SECRET=your-secret-key
JWT_ACCESS_TOKEN_EXPIRY=15m
JWT_REFRESH_TOKEN_EXPIRY=7d
JWT_ISSUER=wnapi

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-password

# Web URL for email links
WEB_URL=http://localhost:3000
```

### 9.2 Module Configuration
```go
type AuthConfig struct {
    JWTSecret            string
    AccessTokenExpiry    time.Duration
    RefreshTokenExpiry   time.Duration
    Issuer               string
    PasswordMinLength    int
    EmailVerificationURL string
    PasswordResetURL     string
}
```

## 10. Ví Dụ Sử Dụng

### 10.1 Đăng Ký Người Dùng
```bash
curl -X POST http://localhost:8080/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -H "X-Tenant-Domain: tenant1.example.com" \
  -d '{
    "username": "john_doe",
    "email": "<EMAIL>",
    "password": "SecurePassword123",
    "full_name": "John Doe"
  }'
```

### 10.2 Đăng Nhập
```bash
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -H "X-Tenant-Domain: tenant1.example.com" \
  -d '{
    "email": "<EMAIL>",
    "password": "SecurePassword123"
  }'
```

### 10.3 Lấy Profile (với JWT token)
```bash
curl -X GET http://localhost:8080/api/v1/auth/profile \
  -H "Authorization: Bearer your-jwt-token" \
  -H "X-Tenant-Domain: tenant1.example.com"
```

## 11. Event Integration

Module Auth publish các events sau:
- `user.registered`: Khi user đăng ký thành công
- `user.login`: Khi user đăng nhập
- `user.logout`: Khi user đăng xuất
- `email.verification.requested`: Khi yêu cầu xác thực email

## 12. Queue Integration

Module Auth sử dụng queue để xử lý:
- Gửi email verification
- Gửi password reset email
- Cleanup expired sessions
- Cleanup expired verification tokens
