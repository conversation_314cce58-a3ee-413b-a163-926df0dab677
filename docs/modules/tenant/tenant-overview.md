# Tổng Quan Module Tenant

## 1. <PERSON><PERSON><PERSON>ch và Trách Nhiệm

Module Tenant là module cốt lõi chịu trách nhiệm quản lý multi-tenancy trong toàn bộ hệ thống. Module này cung cấp khả năng cô lập dữ liệu hoàn toàn giữa các tenant, quản lý cấu hình tenant-specific và xử lý tenant resolution từ domain/subdomain.

### Chức Năng Chính:
- Quản lý thông tin tenant (tạo, cập nhật, xóa)
- Tenant resolution từ domain/subdomain
- Quản lý subscription và plan của tenant
- Cấu hình tenant-specific settings
- Data isolation và security
- Tenant-based routing và middleware

## 2. Kiến Trúc Tổng Quan

Module Tenant là foundation module được khởi tạo đầu tiên và cung cấp context cho tất cả các module khác:

```
modules/tenant/
├── api/                    # API layer
│   ├── handlers/          # HTTP request handlers
│   │   ├── admin/         # Admin tenant management
│   │   └── user/          # User tenant operations
│   ├── middleware/        # Tenant-specific middleware
│   └── routes/           # Route definitions
├── service/               # Business logic layer
│   ├── admin/            # Admin services
│   ├── user/             # User services
│   └── common/           # Shared services
├── repository/           # Data access layer
├── dto/                  # Request/Response DTOs
├── migrations/           # Database migrations
├── events/              # Event publishing
├── event-handlers/      # Event handling
└── internal/            # Internal configurations
```

### Module Priority:
```
1. Tenant (khởi tạo đầu tiên)
2. Auth (cần tenant context)
3. RBAC (cần auth context)
4. Business Modules (cần tất cả context trên)
```

## 3. Các Thành Phần Chính

### 3.1 Models

#### Tenant Model
```go
type Tenant struct {
    TenantID     uint      `gorm:"primaryKey" json:"tenant_id"`
    Name         string    `gorm:"uniqueIndex" json:"name"`
    DisplayName  string    `json:"display_name"`
    Domain       string    `gorm:"uniqueIndex" json:"domain"`
    Subdomain    string    `gorm:"uniqueIndex" json:"subdomain"`
    PlanID       uint      `json:"plan_id"`
    IsActive     bool      `gorm:"default:true" json:"is_active"`
    Settings     string    `gorm:"type:json" json:"settings"`
    Description  string    `json:"description"`
    TimeZone     string    `gorm:"default:'Asia/Ho_Chi_Minh'" json:"timezone"`
    CreatedAt    time.Time `json:"created_at"`
    UpdatedAt    time.Time `json:"updated_at"`
}
```

#### TenantPlan Model
```go
type TenantPlan struct {
    PlanID      uint      `gorm:"primaryKey" json:"plan_id"`
    Name        string    `json:"name"`
    Description string    `json:"description"`
    MaxUsers    int       `json:"max_users"`
    MaxStorage  int64     `json:"max_storage"`
    Features    string    `gorm:"type:json" json:"features"`
    Price       decimal.Decimal `json:"price"`
    IsActive    bool      `gorm:"default:true" json:"is_active"`
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
}
```

### 3.2 Repository Layer

Repository interface với GORM cho CRUD và raw queries cho lists:

```go
type Repository interface {
    // Tenant CRUD operations
    CreateTenant(ctx context.Context, tenant *Tenant) error
    GetTenantByID(ctx context.Context, tenantID uint) (*Tenant, error)
    GetTenantByDomain(ctx context.Context, domain string) (*Tenant, error)
    GetTenantBySubdomain(ctx context.Context, subdomain string) (*Tenant, error)
    UpdateTenant(ctx context.Context, tenant *Tenant) error
    DeleteTenant(ctx context.Context, tenantID uint) error
    
    // List operations với cursor-based pagination
    ListTenants(ctx context.Context, params dto.ListTenantsParams) ([]Tenant, int64, error)
    
    // Plan operations
    GetPlanByID(ctx context.Context, planID uint) (*TenantPlan, error)
    ListPlans(ctx context.Context) ([]TenantPlan, error)
}
```

### 3.3 Service Layer

#### Admin Service
```go
type AdminService interface {
    CreateTenant(ctx context.Context, req dto.CreateTenantRequest) (*dto.TenantResponse, error)
    GetTenant(ctx context.Context, tenantID uint) (*dto.TenantResponse, error)
    UpdateTenant(ctx context.Context, tenantID uint, req dto.UpdateTenantRequest) (*dto.TenantResponse, error)
    DeleteTenant(ctx context.Context, tenantID uint) error
    ListTenants(ctx context.Context, params dto.ListTenantsParams) (*dto.ListTenantsResponse, error)
}
```

#### User Service
```go
type UserService interface {
    GetCurrentTenant(ctx context.Context, tenantID uint) (*dto.TenantResponse, error)
    ListAvailableTenants(ctx context.Context, userID uint) (*dto.ListTenantsResponse, error)
    GetTenantByCode(ctx context.Context, tenantCode string) (*dto.TenantResponse, error)
}
```

## 4. Database Schema và Relationships

### 4.1 Bảng tenant_tenants
```sql
CREATE TABLE tenant_tenants (
    tenant_id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL UNIQUE,
    display_name VARCHAR(200) NOT NULL,
    domain VARCHAR(255) UNIQUE,
    subdomain VARCHAR(100) UNIQUE,
    plan_id INT UNSIGNED NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    settings JSON,
    description TEXT,
    timezone VARCHAR(50) DEFAULT 'Asia/Ho_Chi_Minh',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_domain (domain),
    INDEX idx_subdomain (subdomain),
    INDEX idx_plan_id (plan_id),
    INDEX idx_is_active (is_active)
);
```

### 4.2 Bảng tenant_plans
```sql
CREATE TABLE tenant_plans (
    plan_id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    max_users INT DEFAULT 100,
    max_storage BIGINT DEFAULT 1073741824, -- 1GB in bytes
    features JSON,
    price DECIMAL(10,2) DEFAULT 0.00,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 4.3 Relationships
- `tenant_tenants.plan_id` → `tenant_plans.plan_id` (Many-to-One)
- Tất cả tables khác có `tenant_id` → `tenant_tenants.tenant_id` (Many-to-One)

## 5. Tích Hợp Multi-Tenant

### 5.1 Tenant Resolution
```go
// Middleware xác định tenant từ request
func TenantResolutionMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        var tenantID uint
        
        // 1. Từ subdomain
        if subdomain := extractSubdomain(c.Request.Host); subdomain != "" {
            tenant, err := tenantService.GetTenantBySubdomain(c, subdomain)
            if err == nil {
                tenantID = tenant.TenantID
            }
        }
        
        // 2. Từ custom domain
        if tenantID == 0 {
            tenant, err := tenantService.GetTenantByDomain(c, c.Request.Host)
            if err == nil {
                tenantID = tenant.TenantID
            }
        }
        
        // 3. Từ header (fallback)
        if tenantID == 0 {
            if headerTenant := c.GetHeader("X-Tenant-ID"); headerTenant != "" {
                // Parse và validate tenant ID
            }
        }
        
        c.Set("tenantID", tenantID)
        c.Next()
    }
}
```

### 5.2 Data Isolation Strategy
- Tất cả database operations phải include tenant_id
- Repository layer tự động filter theo tenant
- Middleware validation đảm bảo tenant context

## 6. Authentication và Authorization Integration

### 6.1 Middleware Ordering
```go
// Thứ tự middleware: Tenant → Auth → RBAC
protectedRoutes := apiGroup.Group("")
protectedRoutes.Use(tenantMiddleware())     // 1. Xác định tenant
protectedRoutes.Use(jwtAuthMiddleware())    // 2. Xác thực người dùng  
protectedRoutes.Use(permissionMiddleware()) // 3. Kiểm tra quyền
```

### 6.2 Permission Integration
```go
// Admin routes với permission checking
tenants := router.Group("/admin/tenants")
{
    tenants.GET("", middleware.RequirePermission(permService, "tenants.read"), handler.List)
    tenants.POST("", middleware.RequirePermission(permService, "tenants.create"), handler.Create)
    tenants.PUT("/:tenant_id", middleware.RequirePermission(permService, "tenants.update"), handler.Update)
    tenants.DELETE("/:tenant_id", middleware.RequirePermission(permService, "tenants.delete"), handler.Delete)
}
```

## 7. API Endpoints

### 7.1 Admin Endpoints
- `GET /api/v1/admin/tenants` - Danh sách tenants (với pagination)
- `POST /api/v1/admin/tenants` - Tạo tenant mới
- `GET /api/v1/admin/tenants/:tenant_id` - Chi tiết tenant
- `PUT /api/v1/admin/tenants/:tenant_id` - Cập nhật tenant
- `DELETE /api/v1/admin/tenants/:tenant_id` - Xóa tenant

### 7.2 User Endpoints
- `GET /api/v1/tenants` - Danh sách tenants user có quyền truy cập
- `GET /api/v1/tenants/:tenant_code` - Thông tin tenant theo code
- `GET /api/v1/tenants/current` - Thông tin tenant hiện tại

### 7.3 Plan Endpoints
- `GET /api/v1/admin/plans` - Danh sách plans
- `GET /api/v1/plans` - Danh sách plans công khai

## 8. Dependencies

### 8.1 Module Dependencies
- **Không có dependencies**: Tenant module được khởi tạo đầu tiên
- **Được sử dụng bởi**: Tất cả modules khác cần tenant context

### 8.2 Package Dependencies
- `internal/pkg/response`: Standardized API responses
- `internal/pkg/errors`: Error handling
- `internal/pkg/tracing`: Database tracing
- `internal/database`: Database manager

## 9. Cấu Hình

### 9.1 Environment Variables
```env
# Tenant Configuration
MAX_USERS_PER_TENANT=100
MAX_TENANTS_PER_ACCOUNT=5
DEFAULT_PLAN_ID=1
ENABLE_MULTI_TENANCY=true

# Pagination
DEFAULT_PAGE_SIZE=10
MAX_PAGE_SIZE=100
DEFAULT_SORT_FIELD=created_at
DEFAULT_SORT_ORDER=desc

# Default Settings
DEFAULT_TENANT_TIMEZONE=Asia/Ho_Chi_Minh
```

### 9.2 Module Configuration
```go
type TenantConfig struct {
    MaxUsersPerTenant     int    `env:"MAX_USERS_PER_TENANT" envDefault:"100"`
    MaxTenantsPerAccount  int    `env:"MAX_TENANTS_PER_ACCOUNT" envDefault:"5"`
    DefaultPlanID         int    `env:"DEFAULT_PLAN_ID" envDefault:"1"`
    EnableMultiTenancy    bool   `env:"ENABLE_MULTI_TENANCY" envDefault:"true"`
    DefaultPageSize       int    `env:"DEFAULT_PAGE_SIZE" envDefault:"10"`
    MaxPageSize           int    `env:"MAX_PAGE_SIZE" envDefault:"100"`
    DefaultSortField      string `env:"DEFAULT_SORT_FIELD" envDefault:"created_at"`
    DefaultSortOrder      string `env:"DEFAULT_SORT_ORDER" envDefault:"desc"`
    DefaultTenantTimeZone string `env:"DEFAULT_TENANT_TIMEZONE" envDefault:"Asia/Ho_Chi_Minh"`
}
```

## 10. Ví Dụ Sử Dụng

### 10.1 Tạo Tenant Mới (Admin)
```bash
curl -X POST http://localhost:8080/api/v1/admin/tenants \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer admin-jwt-token" \
  -d '{
    "name": "company-abc",
    "display_name": "Company ABC Ltd",
    "domain": "abc.example.com",
    "subdomain": "abc",
    "plan_id": 1,
    "description": "Company ABC tenant",
    "settings": {
      "theme": "blue",
      "language": "vi"
    }
  }'
```

### 10.2 Lấy Thông Tin Tenant Hiện Tại
```bash
curl -X GET http://localhost:8080/api/v1/tenants/current \
  -H "Authorization: Bearer user-jwt-token" \
  -H "X-Tenant-Domain: abc.example.com"
```

### 10.3 Danh Sách Tenants (với pagination)
```bash
curl -X GET "http://localhost:8080/api/v1/admin/tenants?page_size=20&cursor=eyJ0ZW5hbnRfaWQiOjEwfQ==" \
  -H "Authorization: Bearer admin-jwt-token"
```

## 11. Event Integration

Module Tenant publish các events sau:
- `tenant.created`: Khi tạo tenant mới
- `tenant.updated`: Khi cập nhật tenant
- `tenant.deleted`: Khi xóa tenant
- `tenant.activated`: Khi kích hoạt tenant
- `tenant.deactivated`: Khi vô hiệu hóa tenant

## 12. Cursor-Based Pagination

```go
type ListTenantsParams struct {
    PageSize   int    `json:"page_size" form:"page_size"`
    Cursor     string `json:"cursor" form:"cursor"`
    Search     string `json:"search" form:"search"`
    PlanID     *uint  `json:"plan_id" form:"plan_id"`
    IsActive   *bool  `json:"is_active" form:"is_active"`
    SortField  string `json:"sort_field" form:"sort_field"`
    SortOrder  string `json:"sort_order" form:"sort_order"`
}

type ListTenantsResponse struct {
    Data       []TenantResponse `json:"data"`
    Pagination PaginationInfo   `json:"pagination"`
}

type PaginationInfo struct {
    HasNext    bool   `json:"has_next"`
    NextCursor string `json:"next_cursor,omitempty"`
    Total      int64  `json:"total"`
}
```
