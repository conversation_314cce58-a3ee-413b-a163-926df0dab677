# Tổng Quan Module RBAC

## 1. <PERSON><PERSON><PERSON> và Trách Nhiệm

Module RBAC (Role-Based Access Control) chịu trách nhiệm quản lý hệ thống phân quyền dựa trên vai trò trong môi trường multi-tenant. Module này cung cấp khả năng kiểm soát truy cập chi tiết, quản lý roles và permissions, và tích hợp với tất cả các module khác để đảm bảo security.

### Chức Năng Chính:
- Quản lý roles và permissions
- Gán roles cho users
- Ki<PERSON>m tra quyền truy cập (permission checking)
- Quản lý permission groups
- Multi-tenant permission isolation
- Permission middleware và factories

## 2. Kiến Trúc Tổng Quan

Module RBAC tuân thủ kiến trúc multi-tenant với middleware ordering và permission checking:

```
modules/rbac/
├── api/                    # API layer
│   ├── handler.go         # Main API handler
│   └── handlers/          # Specific endpoint handlers
├── service/               # Business logic layer
│   ├── permission_checker.go    # Permission validation
│   ├── role_service.go         # Role management
│   ├── user_role_service.go    # User-role assignments
│   ├── permission_service.go   # Permission management
│   └── permission_group_service.go
├── repository/            # Data access layer
│   ├── repository.go      # Repository interfaces
│   └── mysql/            # MySQL implementations
├── models/               # Data models
├── dto/                  # Request/Response DTOs
├── migrations/           # Database migrations
├── events/              # Event publishing
└── internal/            # Internal configurations
```

### Permission System Architecture:
```
User → UserRole → Role → RolePermission → Permission
     ↓
   Tenant Isolation (tenant_id in roles và permission_groups)
```

## 3. Các Thành Phần Chính

### 3.1 Models

#### Role Model
```go
type Role struct {
    RoleID      uint      `gorm:"primaryKey" json:"role_id"`
    TenantID    *uint     `json:"tenant_id"`  // NULL cho system roles
    RoleCode    string    `json:"role_code"`
    Name        string    `json:"name"`
    Description string    `json:"description"`
    Status      string    `json:"status"`
    CreatedBy   *uint     `json:"created_by"`
    CreatedAt   time.Time `json:"created_at"`
    UpdatedBy   *uint     `json:"updated_by"`
    UpdatedAt   time.Time `json:"updated_at"`
    
    // Virtual fields
    Permissions []uint `db:"-" json:"permissions,omitempty"` // Permission IDs
}
```

#### Permission Model
```go
type Permission struct {
    PermissionID   uint      `gorm:"primaryKey" json:"permission_id"`
    GroupID        *uint     `json:"group_id"`
    PermissionCode string    `json:"permission_code"`
    Name           string    `json:"name"`
    Description    string    `json:"description"`
    CreatedAt      time.Time `json:"created_at"`
    UpdatedAt      time.Time `json:"updated_at"`
}
```

#### PermissionGroup Model
```go
type PermissionGroup struct {
    GroupID     uint      `gorm:"primaryKey" json:"group_id"`
    TenantID    *uint     `json:"tenant_id"`  // NULL cho system groups
    Name        string    `json:"name"`
    Description string    `json:"description"`
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
}
```

#### UserRole Model
```go
type UserRole struct {
    UserID    uint      `gorm:"primaryKey" json:"user_id"`
    RoleID    uint      `gorm:"primaryKey" json:"role_id"`
    CreatedBy *uint     `json:"created_by"`
    CreatedAt time.Time `json:"created_at"`
    UpdatedBy *uint     `json:"updated_by"`
    UpdatedAt time.Time `json:"updated_at"`
}
```

#### RolePermission Model
```go
type RolePermission struct {
    RoleID       uint      `gorm:"primaryKey" json:"role_id"`
    PermissionID uint      `gorm:"primaryKey" json:"permission_id"`
    CreatedBy    *uint     `json:"created_by"`
    CreatedAt    time.Time `json:"created_at"`
    UpdatedBy    *uint     `json:"updated_by"`
    UpdatedAt    time.Time `json:"updated_at"`
}
```

### 3.2 Repository Layer

Repository interface với tenant ID parameters:

```go
type Repository interface {
    // Role operations
    CreateRole(ctx context.Context, tenantID *uint, role *models.Role) error
    GetRoleByID(ctx context.Context, tenantID *uint, roleID uint) (*models.Role, error)
    UpdateRole(ctx context.Context, tenantID *uint, role *models.Role) error
    DeleteRole(ctx context.Context, tenantID *uint, roleID uint) error
    ListRoles(ctx context.Context, tenantID *uint, params dto.ListRolesParams) ([]models.Role, int64, error)
    
    // Permission operations
    CreatePermission(ctx context.Context, permission *models.Permission) error
    GetPermissionByID(ctx context.Context, permissionID uint) (*models.Permission, error)
    ListPermissions(ctx context.Context, params dto.ListPermissionsParams) ([]models.Permission, int64, error)
    
    // User-Role operations
    AssignRoleToUser(ctx context.Context, userID, roleID uint) error
    RemoveRoleFromUser(ctx context.Context, userID, roleID uint) error
    GetUserRoles(ctx context.Context, userID uint) ([]models.Role, error)
    
    // Permission checking
    CheckUserPermission(ctx context.Context, userID uint, permissionCode string) (bool, error)
    GetUserPermissions(ctx context.Context, userID uint) ([]models.Permission, error)
}
```

### 3.3 Service Layer

#### PermissionChecker Interface
```go
type PermissionChecker interface {
    CheckUserPermission(ctx context.Context, userID uint, permissionCode string) (bool, error)
    CheckUserPermissions(ctx context.Context, userID uint, permissionCodes []string) (map[string]bool, error)
    GetUserPermissions(ctx context.Context, userID uint) ([]models.Permission, error)
    HasRole(ctx context.Context, userID uint, roleCode string) (bool, error)
}
```

#### RoleService Interface
```go
type RoleService interface {
    CreateRole(ctx context.Context, tenantID *uint, req dto.CreateRoleRequest) (*dto.RoleResponse, error)
    GetRole(ctx context.Context, tenantID *uint, roleID uint) (*dto.RoleResponse, error)
    UpdateRole(ctx context.Context, tenantID *uint, roleID uint, req dto.UpdateRoleRequest) (*dto.RoleResponse, error)
    DeleteRole(ctx context.Context, tenantID *uint, roleID uint) error
    ListRoles(ctx context.Context, tenantID *uint, params dto.ListRolesParams) (*dto.ListRolesResponse, error)
    AssignPermissionsToRole(ctx context.Context, roleID uint, permissionIDs []uint) error
}
```

## 4. Database Schema và Relationships

### 4.1 Bảng rbac_roles
```sql
CREATE TABLE rbac_roles (
    role_id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    tenant_id INT UNSIGNED NULL,  -- NULL cho system roles
    role_code VARCHAR(100) NOT NULL,
    role_name VARCHAR(200) NOT NULL,
    role_description TEXT,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_by INT UNSIGNED,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by INT UNSIGNED,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_tenant_role_code (tenant_id, role_code),
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_status (status)
);
```

### 4.2 Bảng rbac_permissions
```sql
CREATE TABLE rbac_permissions (
    permission_id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    group_id INT UNSIGNED,
    permission_code VARCHAR(100) NOT NULL UNIQUE,
    permission_name VARCHAR(200) NOT NULL,
    permission_description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_group_id (group_id),
    INDEX idx_permission_code (permission_code)
);
```

### 4.3 Bảng rbac_permission_groups
```sql
CREATE TABLE rbac_permission_groups (
    group_id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    tenant_id INT UNSIGNED NULL,  -- NULL cho system groups
    group_name VARCHAR(200) NOT NULL,
    group_description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_tenant_id (tenant_id)
);
```

### 4.4 Bảng rbac_user_roles
```sql
CREATE TABLE rbac_user_roles (
    user_id INT UNSIGNED,
    role_id INT UNSIGNED,
    created_by INT UNSIGNED,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by INT UNSIGNED,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (user_id, role_id),
    INDEX idx_user_id (user_id),
    INDEX idx_role_id (role_id)
);
```

### 4.5 Bảng rbac_role_permissions
```sql
CREATE TABLE rbac_role_permissions (
    role_id INT UNSIGNED,
    permission_id INT UNSIGNED,
    created_by INT UNSIGNED,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by INT UNSIGNED,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (role_id, permission_id),
    INDEX idx_role_id (role_id),
    INDEX idx_permission_id (permission_id)
);
```

### 4.6 Relationships
- `rbac_roles.tenant_id` → `tenant_tenants.tenant_id` (Many-to-One, nullable)
- `rbac_permission_groups.tenant_id` → `tenant_tenants.tenant_id` (Many-to-One, nullable)
- `rbac_permissions.group_id` → `rbac_permission_groups.group_id` (Many-to-One)
- `rbac_user_roles.user_id` → `users.user_id` (Many-to-One)
- `rbac_user_roles.role_id` → `rbac_roles.role_id` (Many-to-One)
- `rbac_role_permissions.role_id` → `rbac_roles.role_id` (Many-to-One)
- `rbac_role_permissions.permission_id` → `rbac_permissions.permission_id` (Many-to-One)

## 5. Tích Hợp Multi-Tenant

### 5.1 Tenant Isolation Strategy
- **System Roles/Permissions**: `tenant_id = NULL` (shared across all tenants)
- **Tenant-Specific Roles**: `tenant_id = specific_tenant_id`
- **Permission Groups**: Có thể là system-wide hoặc tenant-specific

### 5.2 Permission Naming Convention
```
Format: module.action.resource
Examples:
- users.create.admin
- products.read.catalog
- orders.update.status
- tenants.delete.all
```

### 5.3 Middleware Integration
```go
// Permission middleware với tenant context
func RequirePermission(permissionCode string) gin.HandlerFunc {
    return func(c *gin.Context) {
        userID := auth.GetUserID(c)
        tenantID := tenant.GetTenantID(c)
        
        hasPermission, err := rbacService.CheckUserPermission(c, userID, permissionCode)
        if err != nil || !hasPermission {
            response.ErrorResponse(c, http.StatusForbidden, "Insufficient permissions")
            c.Abort()
            return
        }
        
        c.Next()
    }
}
```

## 6. Authentication và Authorization Integration

### 6.1 Middleware Ordering
```go
// Thứ tự middleware: Tenant → Auth → RBAC
protectedRoutes := apiGroup.Group("")
protectedRoutes.Use(tenantMiddleware())     // 1. Xác định tenant
protectedRoutes.Use(jwtAuthMiddleware())    // 2. Xác thực người dùng
protectedRoutes.Use(permissionMiddleware()) // 3. Kiểm tra quyền
```

### 6.2 Permission Factory
```go
// Shared permission package
type MiddlewareFactory struct {
    permissionChecker PermissionChecker
}

func (mf *MiddlewareFactory) RequirePermission(permission string) gin.HandlerFunc {
    return func(c *gin.Context) {
        userID := auth.GetUserID(c)
        hasPermission, err := mf.permissionChecker.CheckUserPermission(c, userID, permission)
        
        if err != nil || !hasPermission {
            response.ErrorResponse(c, http.StatusForbidden, "Insufficient permissions")
            c.Abort()
            return
        }
        
        c.Next()
    }
}
```

## 7. API Endpoints

### 7.1 Role Management
- `GET /api/v1/rbac/roles` - Danh sách roles
- `POST /api/v1/rbac/roles` - Tạo role mới
- `GET /api/v1/rbac/roles/:role_id` - Chi tiết role
- `PUT /api/v1/rbac/roles/:role_id` - Cập nhật role
- `DELETE /api/v1/rbac/roles/:role_id` - Xóa role

### 7.2 Permission Management
- `GET /api/v1/rbac/permissions` - Danh sách permissions
- `POST /api/v1/rbac/permissions` - Tạo permission mới
- `GET /api/v1/rbac/permissions/:permission_id` - Chi tiết permission

### 7.3 User-Role Management
- `POST /api/v1/rbac/user-roles` - Gán role cho user
- `DELETE /api/v1/rbac/user-roles` - Gỡ role khỏi user
- `GET /api/v1/rbac/users/:user_id/roles` - Danh sách roles của user
- `GET /api/v1/rbac/users/:user_id/permissions` - Danh sách permissions của user

### 7.4 Permission Groups
- `GET /api/v1/rbac/permission-groups` - Danh sách permission groups
- `POST /api/v1/rbac/permission-groups` - Tạo permission group mới

## 8. Dependencies

### 8.1 Module Dependencies
- **Tenant Module**: Cần để xác định tenant context
- **Auth Module**: Cần để xác định user context

### 8.2 Package Dependencies
- `internal/pkg/response`: Standardized API responses
- `internal/pkg/errors`: Error handling
- `internal/pkg/permission`: Shared permission interfaces

## 9. Cấu Hình

### 9.1 Environment Variables
```env
# RBAC Configuration
RBAC_CACHE_TTL=300s
RBAC_ENABLE_CACHING=true
RBAC_DEFAULT_ROLE=user
RBAC_ADMIN_ROLE=admin
```

### 9.2 Default Permissions
```go
var DefaultPermissions = []Permission{
    {Code: "users.read.own", Name: "Read Own Profile"},
    {Code: "users.update.own", Name: "Update Own Profile"},
    {Code: "users.read.all", Name: "Read All Users"},
    {Code: "users.create.admin", Name: "Create Admin Users"},
    {Code: "tenants.read.own", Name: "Read Own Tenant"},
    {Code: "tenants.update.own", Name: "Update Own Tenant"},
    {Code: "products.read.catalog", Name: "Read Product Catalog"},
    {Code: "products.create.admin", Name: "Create Products"},
}
```

## 10. Ví Dụ Sử Dụng

### 10.1 Tạo Role Mới
```bash
curl -X POST http://localhost:8080/api/v1/rbac/roles \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer admin-jwt-token" \
  -H "X-Tenant-Domain: tenant1.example.com" \
  -d '{
    "role_code": "product_manager",
    "name": "Product Manager",
    "description": "Manages products and categories",
    "permissions": [1, 2, 3, 4]
  }'
```

### 10.2 Gán Role Cho User
```bash
curl -X POST http://localhost:8080/api/v1/rbac/user-roles \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer admin-jwt-token" \
  -d '{
    "user_id": 123,
    "role_id": 5
  }'
```

### 10.3 Kiểm Tra Permission (trong code)
```go
// Trong handler
hasPermission, err := rbacService.CheckUserPermission(ctx, userID, "products.create.admin")
if err != nil || !hasPermission {
    return response.ErrorResponse(c, http.StatusForbidden, "Insufficient permissions")
}
```

## 11. Event Integration

Module RBAC publish các events sau:
- `role.created`: Khi tạo role mới
- `role.updated`: Khi cập nhật role
- `role.deleted`: Khi xóa role
- `user.role.assigned`: Khi gán role cho user
- `user.role.removed`: Khi gỡ role khỏi user
- `permission.granted`: Khi cấp permission
- `permission.revoked`: Khi thu hồi permission

## 12. Current Implementation Status

### 12.1 Working Features
- ✅ Database schema và migrations
- ✅ GORM models
- ✅ Basic CRUD operations cho roles/permissions
- ✅ API endpoints
- ✅ Multi-tenant support

### 12.2 Stub Implementations
```go
// Permission checking hiện tại là stub
func (s *permissionService) CheckUserPermission(ctx context.Context, userID int, permissionName string) (bool, error) {
    // TODO: Triển khai kiểm tra quyền người dùng
    // Tạm thời luôn trả về true
    s.logger.Info("Kiểm tra quyền %s cho người dùng ID %d", permissionName, userID)
    return true, nil
}
```
