# Hệ Thống Phân Quyền WNAPI

## Tổng Quan

WNAPI sử dụng hệ thống phân quyền RBAC (Role-Based Access Control) với kiến trúc multi-tenant. Hệ thống hiện tại đang trong giai đoạn phát triển với cấu trúc database hoàn chỉnh và logic cơ bản.

## Trạng Thái Hiện Tại

### Đã Triển Khai
- ✅ Database schema hoàn chỉnh cho RBAC
- ✅ Models và repositories cơ bản
- ✅ API endpoints cho quản lý roles/permissions
- ✅ Multi-tenant support
- ✅ Cấu trúc package `internal/pkg/permission`

### Đang Phát Triển
- 🔄 Permission checking logic (hiện tại return true)
- 🔄 Middleware integration
- 🔄 Caching layer
- 🔄 Advanced permission rules

## Kiến Trúc Hệ Thống

### Database Schema

```
User ←→ UserRole ←→ Role ←→ RolePermission ←→ Permission
  ↓                                              ↓
Tenant                                    PermissionGroup
```

### Core Tables

#### rbac_roles
```sql
CREATE TABLE rbac_roles (
  role_id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
  role_code VARCHAR(255) NOT NULL UNIQUE,
  tenant_id INT UNSIGNED REFERENCES tenants(tenant_id),
  role_name VARCHAR(255) NOT NULL,
  role_description TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### rbac_permissions
```sql
CREATE TABLE rbac_permissions (
  permission_id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
  permission_code VARCHAR(255) NOT NULL UNIQUE,
  tenant_id INT UNSIGNED REFERENCES tenants(tenant_id),
  group_id INT UNSIGNED REFERENCES rbac_permission_groups(group_id),
  permission_name VARCHAR(255) NOT NULL,
  permission_description TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## Permission Naming Convention

### Format Chuẩn
```
{module}.{action}.{resource}
```

### Ví Dụ
```go
// Module Product
"products.create"           // Tạo sản phẩm
"products.read"             // Xem sản phẩm
"products.update"           // Cập nhật sản phẩm
"products.delete"           // Xóa sản phẩm
"products.list"             // Liệt kê sản phẩm

// Module User
"users.create"              // Tạo người dùng
"users.read"                // Xem thông tin người dùng
"users.update"              // Cập nhật người dùng
"users.delete"              // Xóa người dùng
"users.list"                // Liệt kê người dùng

// Module RBAC
"rbac.roles.create"         // Tạo vai trò
"rbac.permissions.read"     // Xem quyền
"rbac.user_roles.assign"    // Gán vai trò cho người dùng
```

## API Endpoints

### Role Management
```
GET    /rbac/roles              # Danh sách roles
POST   /rbac/roles              # Tạo role mới
GET    /rbac/roles/{id}         # Chi tiết role
PUT    /rbac/roles/{id}         # Cập nhật role
DELETE /rbac/roles/{id}         # Xóa role
```

### Permission Management
```
GET    /rbac/permissions        # Danh sách permissions
POST   /rbac/permissions        # Tạo permission mới
GET    /rbac/permissions/{id}   # Chi tiết permission
PUT    /rbac/permissions/{id}   # Cập nhật permission
DELETE /rbac/permissions/{id}   # Xóa permission
```

### User Role Assignment
```
GET    /rbac/user-roles         # Danh sách user-role assignments
POST   /rbac/user-roles         # Gán role cho user
DELETE /rbac/user-roles         # Hủy gán role
```

### Permission Groups
```
GET    /rbac/permission-groups  # Danh sách permission groups
POST   /rbac/permission-groups  # Tạo permission group
```

## Default Roles và Permissions

### System Roles
```sql
-- Super Admin: Quyền cao nhất trong hệ thống
INSERT INTO rbac_roles (role_code, tenant_id, role_name, role_description) 
VALUES ('super_admin', NULL, 'Super Admin', 'Quyền cao nhất trong hệ thống');

-- Tenant Admin: Quản trị một tenant cụ thể
INSERT INTO rbac_roles (role_code, tenant_id, role_name, role_description) 
VALUES ('tenant_admin', NULL, 'Tenant Admin', 'Quyền quản trị một tenant cụ thể');

-- Content Manager: Quản lý nội dung
INSERT INTO rbac_roles (role_code, tenant_id, role_name, role_description) 
VALUES ('content_manager', NULL, 'Content Manager', 'Quản lý nội dung trong hệ thống');
```

### Permission Groups
```sql
INSERT INTO rbac_permission_groups (permission_group_name, permission_group_description) VALUES
('Quản lý người dùng', 'Quyền liên quan đến quản lý người dùng trong hệ thống'),
('Quản lý vai trò', 'Quyền liên quan đến quản lý vai trò và phân quyền'),
('Quản lý nội dung', 'Quyền liên quan đến quản lý nội dung trong hệ thống'),
('Quản lý sản phẩm', 'Quyền liên quan đến quản lý sản phẩm'),
('Quản lý media', 'Quyền liên quan đến quản lý tệp và media');
```

## Middleware Integration (Đang Phát Triển)

### Thứ Tự Middleware
```go
// Thứ tự quan trọng: Tenant → Auth → Permission
protectedRoutes := apiGroup.Group("")
protectedRoutes.Use(tenantMiddleware())     // 1. Xác định tenant
protectedRoutes.Use(jwtAuthMiddleware())    // 2. Xác thực người dùng
protectedRoutes.Use(permissionMiddleware()) // 3. Kiểm tra quyền
```

### Permission Package Structure
```
internal/pkg/permission/
├── interfaces.go          # PermissionChecker interface
├── constants.go          # Permission constants và builder
├── errors.go             # Error handling
├── factory.go            # Middleware factory (stub)
└── cache.go              # Caching layer (stub)
```

## Current Implementation Status

### Working Features
- ✅ Database schema và migrations
- ✅ GORM models
- ✅ Basic CRUD operations cho roles/permissions
- ✅ API endpoints
- ✅ Multi-tenant support

### Stub Implementations
```go
// modules/rbac/service/permission_service.go
func (s *permissionService) CheckUserPermission(ctx context.Context, userID int, permissionName string) (bool, error) {
    // TODO: Triển khai kiểm tra quyền người dùng
    // Tạm thời luôn trả về true
    s.logger.Info("Kiểm tra quyền %s cho người dùng ID %d", permissionName, userID)
    return true, nil
}
```

```go
// internal/pkg/permission/factory.go
func (mf *MiddlewareFactory) RequirePermission(permission string) gin.HandlerFunc {
    return func(c *gin.Context) {
        // Stub - sẽ được triển khai đầy đủ trong tương lai
        c.Next()
    }
}
```

## Development Roadmap

### Phase 1: Core Permission Logic
- [ ] Implement actual permission checking logic
- [ ] Complete middleware factory implementation
- [ ] Add proper error handling

### Phase 2: Caching Layer
- [ ] Implement permission caching
- [ ] Cache invalidation strategies
- [ ] Performance optimization

### Phase 3: Advanced Features
- [ ] Hierarchical roles
- [ ] Dynamic permissions
- [ ] Audit logging
- [ ] Permission inheritance

## Usage Examples

### Checking Permissions (Current API)
```go
// Sử dụng service để kiểm tra quyền
hasPermission, err := permissionService.CheckUserPermission(ctx, userID, "products.create")
if err != nil {
    return err
}
if !hasPermission {
    return errors.New("permission denied")
}
```

### Future Middleware Usage
```go
// Khi middleware được hoàn thiện
router.POST("/products", 
    middlewareFactory.RequirePermission("products.create"),
    productHandler.CreateProduct)

router.GET("/admin/users",
    middlewareFactory.RequireAnyPermission("users.list", "users.manage"),
    userHandler.ListUsers)
```

## Multi-Tenant Considerations

### Tenant Isolation
- Tất cả permission checks phải include `tenant_id`
- Roles có thể là global (tenant_id = NULL) hoặc tenant-specific
- Permissions được scope theo tenant

### Global vs Tenant Permissions
```sql
-- Global permission (áp dụng cho tất cả tenant)
INSERT INTO rbac_permissions (permission_code, tenant_id, permission_name) 
VALUES ('system.admin', NULL, 'System Administration');

-- Tenant-specific permission
INSERT INTO rbac_permissions (permission_code, tenant_id, permission_name) 
VALUES ('products.create', 1, 'Create Products for Tenant 1');
```

## Best Practices

### Permission Design
1. Sử dụng naming convention nhất quán
2. Tạo permissions granular nhưng không quá chi tiết
3. Group permissions logically
4. Document tất cả permissions

### Security
1. Luôn validate tenant context
2. Implement proper error handling
3. Log permission checks cho audit
4. Use principle of least privilege

### Performance
1. Cache permission results
2. Optimize database queries
3. Minimize permission checks
4. Use efficient data structures
