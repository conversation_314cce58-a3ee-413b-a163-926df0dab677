# Tổng Quan Module Product

## 1. <PERSON><PERSON><PERSON>ch và Trách Nhiệm

Module Product chịu trách nhiệm quản lý toàn bộ hệ thống sản phẩm trong môi trường multi-tenant e-commerce. Module này cung cấp khả năng quản lý sản phẩm phức tạp với nhiều loại sản phẩm, variants, attributes và categories.

### Chức Năng Chính:
- Quản lý sản phẩm đa dạng (simple, configurable, bundle, virtual, downloadable)
- Quản lý categories với nested set model
- Quản lý product attributes và attribute groups
- Quản lý product variants và pricing
- Search và filtering sản phẩm
- Multi-tenant product isolation
- Permission-based access control

## 2. Kiến Trúc Tổng Quan

Module Product tuân thủ kiến trúc multi-tenant với complex product management:

```
modules/product/
├── api/                    # API layer
│   ├── handler.go         # Main API handler
│   ├── handlers/          # Specific endpoint handlers
│   ├── middleware/        # Product-specific middleware
│   └── routes.go          # Route definitions
├── service/               # Business logic layer
│   ├── product_service.go           # Core product service
│   ├── category_service.go         # Category management
│   ├── product_variant_service.go  # Variant management
│   ├── product_attribute_service.go # Attribute management
│   └── utils.go                    # Utility functions
├── repository/            # Data access layer
│   ├── product_repository.go       # Product repository interface
│   ├── category_repository.go      # Category repository interface
│   └── mysql/                     # MySQL implementations
├── models/               # Data models
├── dto/                  # Request/Response DTOs
├── migrations/           # Database migrations
├── permission_defs.go    # Permission definitions
└── internal/            # Internal configurations
```

### Product Type Architecture:
```
Product Types:
├── SIMPLE (sản phẩm đơn giản)
├── CONFIGURABLE (sản phẩm có variants)
├── BUNDLE (gói sản phẩm)
├── VIRTUAL (sản phẩm ảo)
├── DOWNLOADABLE (sản phẩm tải xuống)
├── COMPOSITE (sản phẩm tổng hợp)
├── GIFT_CARD (thẻ quà tặng)
└── GROUPED (nhóm sản phẩm)
```

## 3. Các Thành Phần Chính

### 3.1 Models

#### Product Model
```go
type Product struct {
    ProductID      uint      `gorm:"primaryKey" json:"product_id"`
    TenantID       uint      `gorm:"index" json:"tenant_id"`
    CategoryID     *uint     `json:"category_id"`
    Name           string    `json:"name"`
    Description    string    `json:"description"`
    Content        string    `gorm:"type:text" json:"content"`
    Slug           string    `gorm:"uniqueIndex:idx_tenant_slug" json:"slug"`
    ImageURL       string    `json:"image_url"`
    BasePrice      float64   `json:"base_price"`
    CostPrice      *float64  `json:"cost_price"`
    ProductType    string    `gorm:"type:enum('SIMPLE','CONFIGURABLE','BUNDLE','VIRTUAL','DOWNLOADABLE','COMPOSITE','GIFT_CARD','GROUPED');default:'SIMPLE'" json:"product_type"`
    Status         string    `gorm:"type:enum('DRAFT','PUBLISHED','ARCHIVED','PENDING_APPROVAL');default:'DRAFT'" json:"status"`
    ProductCode    string    `gorm:"uniqueIndex:idx_tenant_code" json:"product_code"`
    IsTaxable      bool      `gorm:"default:true" json:"is_taxable"`
    IsVirtual      bool      `gorm:"default:false" json:"is_virtual"`
    IsDownloadable bool      `gorm:"default:false" json:"is_downloadable"`
    CreatedAt      time.Time `json:"created_at"`
    UpdatedAt      time.Time `json:"updated_at"`
    CreatedBy      *uint     `json:"created_by"`
    UpdatedBy      *uint     `json:"updated_by"`
}
```

#### Category Model (Nested Set)
```go
type Category struct {
    CategoryID    uint      `gorm:"primaryKey" json:"category_id"`
    TenantID      uint      `gorm:"index" json:"tenant_id"`
    ParentID      *uint     `json:"parent_id"`
    Name          string    `json:"name"`
    Slug          string    `gorm:"uniqueIndex:idx_tenant_slug" json:"slug"`
    Description   string    `json:"description"`
    FeaturedImage string    `json:"image"`
    Left          int       `json:"lft"`        // Nested Set left value
    Right         int       `json:"rgt"`        // Nested Set right value
    Depth         int       `json:"depth"`      // Tree depth
    Position      int       `json:"position"`   // Sort order
    IsActive      bool      `gorm:"default:true" json:"is_active"`
    IsFeatured    bool      `gorm:"default:false" json:"is_featured"`
    MetaTitle     string    `json:"meta_title"`
    MetaDescription string  `json:"meta_description"`
    CreatedAt     time.Time `json:"created_at"`
    UpdatedAt     time.Time `json:"updated_at"`
    CreatedBy     uint      `json:"created_by"`
    UpdatedBy     uint      `json:"updated_by"`
    
    // Virtual fields
    Children     []*Category `gorm:"-" json:"children,omitempty"`
    ProductCount int         `gorm:"-" json:"product_count,omitempty"`
}
```

#### ProductAttribute Model
```go
type ProductAttribute struct {
    AttributeID     uint      `gorm:"primaryKey" json:"attribute_id"`
    TenantID        uint      `gorm:"index" json:"tenant_id"`
    GroupID         *uint     `json:"group_id"`
    Name            string    `json:"name"`
    Code            string    `gorm:"uniqueIndex:idx_tenant_code" json:"code"`
    Type            string    `gorm:"type:enum('TEXT','NUMBER','BOOLEAN','SELECT','MULTISELECT','DATE','TEXTAREA','FILE')" json:"type"`
    Unit            *string   `json:"unit"`
    ValidationRules *string   `gorm:"type:json" json:"validation_rules"`
    IsConfigurable  bool      `gorm:"default:false" json:"is_configurable"`
    IsFilterable    bool      `gorm:"default:false" json:"is_filterable"`
    IsRequired      bool      `gorm:"default:false" json:"is_required"`
    Position        int       `gorm:"default:0" json:"position"`
    CreatedAt       time.Time `json:"created_at"`
    UpdatedAt       time.Time `json:"updated_at"`
}
```

#### ProductVariant Model
```go
type ProductVariant struct {
    VariantID uint      `gorm:"primaryKey" json:"variant_id"`
    TenantID  uint      `gorm:"index" json:"tenant_id"`
    ProductID uint      `gorm:"index" json:"product_id"`
    SKU       string    `gorm:"uniqueIndex:idx_tenant_sku" json:"sku"`
    Price     *float64  `json:"price"`
    CostPrice *float64  `json:"cost_price"`
    ImageURL  *string   `json:"image_url"`
    IsActive  bool      `gorm:"default:true" json:"is_active"`
    CreatedAt time.Time `json:"created_at"`
    UpdatedAt time.Time `json:"updated_at"`
}
```

### 3.2 Repository Layer

Repository interface với tenant ID parameters:

```go
type ProductRepository interface {
    // Basic CRUD operations
    Create(ctx context.Context, product *models.Product) error
    GetByID(ctx context.Context, tenantID, productID int) (*models.Product, error)
    Update(ctx context.Context, product *models.Product) error
    Delete(ctx context.Context, tenantID, productID int) error
    
    // List and search operations với cursor-based pagination
    List(ctx context.Context, tenantID int, cursor string, limit int, filters map[string]interface{}) ([]*models.Product, string, bool, error)
    Search(ctx context.Context, tenantID int, keyword string, cursor string, limit int, filters map[string]interface{}) ([]*models.Product, string, bool, error)
    GetAll(ctx context.Context, tenantID int) ([]*models.Product, error)
    
    // Category-related operations
    GetProductCategories(ctx context.Context, tenantID int) ([]map[string]interface{}, error)
    
    // Product attributes and variants
    GetProductAttributesAndVariants(ctx context.Context, tenantID, productID int) ([]common.AttributeDTO, []common.VariantDTO, bool, error)
}
```

### 3.3 Service Layer

#### ProductService Interface
```go
type ProductService interface {
    CreateProduct(ctx context.Context, tenantID int, req *request.CreateProductRequest) (*response.ProductResponse, error)
    GetProduct(ctx context.Context, tenantID int, productID int) (*response.ProductResponse, error)
    UpdateProduct(ctx context.Context, tenantID int, productID int, req *request.UpdateProductRequest) (*response.ProductResponse, error)
    DeleteProduct(ctx context.Context, tenantID int, productID int) error
    ListProducts(ctx context.Context, tenantID int, cursor string, limit int, filters map[string]interface{}) (*response.ProductListResponse, error)
    SearchProducts(ctx context.Context, tenantID int, keyword string, cursor string, limit int, filters map[string]interface{}) (*response.ProductListResponse, error)
    GetAllProducts(ctx context.Context, tenantID int) ([]*response.ProductResponse, error)
    GetProductCategories(ctx context.Context, tenantID int) ([]map[string]interface{}, error)
}
```

## 4. Database Schema và Relationships

### 4.1 Bảng ecom_products
```sql
CREATE TABLE ecom_products (
    product_id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    tenant_id INT UNSIGNED NOT NULL,
    category_id INT UNSIGNED,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    content LONGTEXT,
    slug VARCHAR(255) NOT NULL,
    image_url VARCHAR(500),
    base_price DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    cost_price DECIMAL(15,2),
    product_type ENUM('SIMPLE','CONFIGURABLE','BUNDLE','VIRTUAL','DOWNLOADABLE','COMPOSITE','GIFT_CARD','GROUPED') DEFAULT 'SIMPLE',
    status ENUM('DRAFT','PUBLISHED','ARCHIVED','PENDING_APPROVAL') DEFAULT 'DRAFT',
    product_code VARCHAR(100) NOT NULL,
    is_taxable BOOLEAN DEFAULT TRUE,
    is_virtual BOOLEAN DEFAULT FALSE,
    is_downloadable BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT UNSIGNED,
    updated_by INT UNSIGNED,
    UNIQUE KEY uk_tenant_slug (tenant_id, slug),
    UNIQUE KEY uk_tenant_code (tenant_id, product_code),
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_category_id (category_id),
    INDEX idx_status (status),
    INDEX idx_product_type (product_type)
);
```

### 4.2 Bảng ecom_product_categories (Nested Set)
```sql
CREATE TABLE ecom_product_categories (
    category_id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    tenant_id INT UNSIGNED NOT NULL,
    parent_id INT UNSIGNED,
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL,
    description TEXT,
    image VARCHAR(500),
    lft INT NOT NULL,
    rgt INT NOT NULL,
    depth INT NOT NULL DEFAULT 0,
    position INT NOT NULL DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    is_featured BOOLEAN DEFAULT FALSE,
    meta_title VARCHAR(255),
    meta_description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT UNSIGNED NOT NULL,
    updated_by INT UNSIGNED NOT NULL,
    UNIQUE KEY uk_tenant_slug (tenant_id, slug),
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_parent_id (parent_id),
    INDEX idx_lft_rgt (lft, rgt),
    INDEX idx_is_active (is_active)
);
```

### 4.3 Bảng ecom_product_attributes
```sql
CREATE TABLE ecom_product_attributes (
    attribute_id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    tenant_id INT UNSIGNED NOT NULL,
    group_id INT UNSIGNED,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(100) NOT NULL,
    type ENUM('TEXT','NUMBER','BOOLEAN','SELECT','MULTISELECT','DATE','TEXTAREA','FILE') NOT NULL,
    unit VARCHAR(50),
    validation_rules JSON,
    is_configurable BOOLEAN DEFAULT FALSE,
    is_filterable BOOLEAN DEFAULT FALSE,
    is_required BOOLEAN DEFAULT FALSE,
    position INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_tenant_code (tenant_id, code),
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_group_id (group_id),
    INDEX idx_type (type)
);
```

### 4.4 Bảng ecom_product_variants
```sql
CREATE TABLE ecom_product_variants (
    variant_id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    tenant_id INT UNSIGNED NOT NULL,
    product_id INT UNSIGNED NOT NULL,
    sku VARCHAR(100) NOT NULL,
    price DECIMAL(15,2),
    cost_price DECIMAL(15,2),
    image_url VARCHAR(500),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_tenant_sku (tenant_id, sku),
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_product_id (product_id),
    INDEX idx_is_active (is_active)
);
```

### 4.5 Relationships
- `ecom_products.tenant_id` → `tenant_tenants.tenant_id` (Many-to-One)
- `ecom_products.category_id` → `ecom_product_categories.category_id` (Many-to-One)
- `ecom_product_categories.tenant_id` → `tenant_tenants.tenant_id` (Many-to-One)
- `ecom_product_categories.parent_id` → `ecom_product_categories.category_id` (Self-referencing)
- `ecom_product_variants.product_id` → `ecom_products.product_id` (Many-to-One)
- `ecom_product_attributes.tenant_id` → `tenant_tenants.tenant_id` (Many-to-One)

## 5. Tích Hợp Multi-Tenant

### 5.1 Data Isolation
- Tất cả products, categories, attributes đều có `tenant_id`
- Repository layer tự động filter theo tenant
- Unique constraints bao gồm tenant_id (tenant_id, slug), (tenant_id, product_code)

### 5.2 Tenant Context Extraction
```go
// Utility function để lấy tenant ID từ context
func getTenantIDInt64FromContext(c *gin.Context) (int64, error) {
    // TODO: Implement when auth package is available
    // tenantID := auth.GetTenantID(c)
    // For now, return a default tenant ID for compilation
    return 1, nil
}
```

## 6. Authentication và Authorization Integration

### 6.1 Permission Definitions
```go
// Permission constants cho product module
const (
    CreatePermission  = "products.create"
    ReadPermission    = "products.read"
    UpdatePermission  = "products.update"
    DeletePermission  = "products.delete"
    ListPermission    = "products.list"
    PublishPermission = "products.publish"
)
```

### 6.2 Middleware Integration
```go
// Protected routes với permission checking
func (h *ProductHandler) RegisterRoutes(router *gin.RouterGroup) {
    productGroup := router.Group("/products")
    
    // Public endpoints
    productGroup.GET("/public", h.ListPublicProducts)
    
    // Protected endpoints với permission requirements
    productGroup.GET("", 
        h.middlewareFactory.RequirePermission(product.ListPermission),
        h.ListProducts)
    
    productGroup.POST("", 
        h.middlewareFactory.RequirePermission(product.CreatePermission),
        h.CreateProduct)
    
    productGroup.PUT("/:id", 
        h.middlewareFactory.RequirePermission(product.UpdatePermission),
        h.UpdateProduct)
    
    productGroup.DELETE("/:id", 
        h.middlewareFactory.RequirePermission(product.DeletePermission),
        h.DeleteProduct)
}
```

## 7. API Endpoints

### 7.1 Product Management
- `GET /api/v1/product/products` - Danh sách sản phẩm (với pagination và filters)
- `POST /api/v1/product/products` - Tạo sản phẩm mới
- `GET /api/v1/product/products/:id` - Chi tiết sản phẩm
- `PUT /api/v1/product/products/:id` - Cập nhật sản phẩm
- `DELETE /api/v1/product/products/:id` - Xóa sản phẩm
- `GET /api/v1/product/products/search` - Tìm kiếm sản phẩm
- `GET /api/v1/product/products/all` - Tất cả sản phẩm
- `GET /api/v1/product/products/categories` - Danh sách categories

### 7.2 Category Management
- `GET /api/v1/product/categories` - Danh sách categories (tree structure)
- `POST /api/v1/product/categories` - Tạo category mới
- `GET /api/v1/product/categories/:id` - Chi tiết category
- `PUT /api/v1/product/categories/:id` - Cập nhật category
- `DELETE /api/v1/product/categories/:id` - Xóa category

### 7.3 Attribute Management
- `GET /api/v1/product/attribute-groups` - Danh sách attribute groups
- `POST /api/v1/product/attribute-groups` - Tạo attribute group mới
- `GET /api/v1/product/attributes` - Danh sách attributes
- `POST /api/v1/product/attributes` - Tạo attribute mới

## 8. Dependencies

### 8.1 Module Dependencies
- **Tenant Module**: Cần để xác định tenant context
- **Auth Module**: Cần để xác định user context
- **RBAC Module**: Cần để kiểm tra permissions
- **Media Module**: Để quản lý product images

### 8.2 Package Dependencies
- `internal/pkg/response`: Standardized API responses
- `internal/pkg/errors`: Error handling
- `internal/pkg/tracing`: Database tracing
- `internal/database`: Database manager

## 9. Cấu Hình

### 9.1 Environment Variables
```env
# Product Configuration
PRODUCT_DEFAULT_PAGE_SIZE=20
PRODUCT_MAX_PAGE_SIZE=100
PRODUCT_IMAGE_MAX_SIZE=5MB
PRODUCT_SLUG_AUTO_GENERATE=true

# Search Configuration
PRODUCT_SEARCH_MIN_LENGTH=3
PRODUCT_SEARCH_MAX_RESULTS=1000
```

### 9.2 Product Types Configuration
```go
var ProductTypes = []string{
    "SIMPLE",        // Sản phẩm đơn giản
    "CONFIGURABLE",  // Sản phẩm có variants
    "BUNDLE",        // Gói sản phẩm
    "VIRTUAL",       // Sản phẩm ảo
    "DOWNLOADABLE",  // Sản phẩm tải xuống
    "COMPOSITE",     // Sản phẩm tổng hợp
    "GIFT_CARD",     // Thẻ quà tặng
    "GROUPED",       // Nhóm sản phẩm
}
```

## 10. Ví Dụ Sử Dụng

### 10.1 Tạo Sản Phẩm Configurable
```bash
curl -X POST http://localhost:8080/api/v1/product/products \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer jwt-token" \
  -H "X-Tenant-Domain: tenant1.example.com" \
  -d '{
    "name": "Áo thun nam cổ tròn",
    "description": "Áo thun nam chất liệu cotton",
    "content": "<p>Áo thun nam cổ tròn chất liệu cotton 100%</p>",
    "category_id": 5,
    "base_price": 250000,
    "product_type": "CONFIGURABLE",
    "status": "PUBLISHED",
    "product_code": "ATN001",
    "attributes": [
      {
        "attribute_id": 1,
        "name": "Màu sắc",
        "type": "SELECT",
        "options": ["Đỏ", "Xanh", "Vàng"]
      },
      {
        "attribute_id": 2,
        "name": "Kích thước",
        "type": "SELECT", 
        "options": ["S", "M", "L", "XL"]
      }
    ],
    "variants": [
      {
        "sku": "ATN001-RED-S",
        "price": 250000,
        "attributes": {"color": "Đỏ", "size": "S"}
      },
      {
        "sku": "ATN001-BLUE-M", 
        "price": 250000,
        "attributes": {"color": "Xanh", "size": "M"}
      }
    ]
  }'
```

### 10.2 Tìm Kiếm Sản Phẩm
```bash
curl -X GET "http://localhost:8080/api/v1/product/products/search?keyword=áo thun&category_id=5&min_price=100000&max_price=500000&cursor=eyJwcm9kdWN0X2lkIjoxMH0%3D&limit=20" \
  -H "Authorization: Bearer jwt-token" \
  -H "X-Tenant-Domain: tenant1.example.com"
```

### 10.3 Lấy Danh Sách Categories (Tree Structure)
```bash
curl -X GET http://localhost:8080/api/v1/product/categories \
  -H "Authorization: Bearer jwt-token" \
  -H "X-Tenant-Domain: tenant1.example.com"
```

## 11. Cursor-Based Pagination

```go
type ProductListResponse struct {
    Data       []ProductResponse `json:"data"`
    Pagination PaginationInfo    `json:"pagination"`
}

type PaginationInfo struct {
    HasNext    bool   `json:"has_next"`
    NextCursor string `json:"next_cursor,omitempty"`
    Total      int64  `json:"total"`
}
```

## 12. Search và Filtering

### 12.1 Search Parameters
- `keyword`: Tìm kiếm theo tên, mô tả
- `category_id`: Filter theo category
- `product_type`: Filter theo loại sản phẩm
- `status`: Filter theo trạng thái
- `min_price`, `max_price`: Filter theo giá
- `is_featured`: Filter sản phẩm nổi bật

### 12.2 Advanced Filtering
- Attribute-based filtering
- Multi-category filtering
- Date range filtering
- Inventory status filtering
