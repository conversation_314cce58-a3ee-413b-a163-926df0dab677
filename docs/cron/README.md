# Cron System Documentation

## Overview

The cron system provides scheduled task execution capabilities for the WNAPI application. It supports both system-level maintenance tasks and module-specific scheduled operations.

## Features

- **Flexible Scheduling**: Uses standard cron expressions for scheduling
- **Module Integration**: Seamless integration with existing modules
- **Error Handling**: Comprehensive error handling and logging
- **Monitoring**: Built-in monitoring and health checks
- **CLI Management**: Command-line tools for managing cron jobs
- **Configuration**: Environment-based configuration
- **Testing**: Comprehensive test coverage

## Architecture

### Core Components

1. **CronScheduler**: Main scheduler that manages job execution
2. **HandlerRegistry**: Registry for cron job handlers
3. **CronManager**: High-level manager that coordinates all components
4. **CronJob**: Represents a scheduled job
5. **CronHandler**: Interface for job execution logic

### Task Types

#### System Tasks
- `system:cleanup` - Clean up logs, temp files, and cache
- `system:health_check` - Monitor system health
- `system:backup` - Backup system data

#### Auth Module Tasks
- `auth:session_cleanup` - Clean up expired sessions
- `auth:password_expiry` - Send password expiry notifications

#### Media Module Tasks
- `media:image_optimization` - Optimize images
- `media:temp_cleanup` - Clean up temporary media files

## Configuration

### Environment Variables

```bash
# Enable/disable cron system
CRON_ENABLED=true
CRON_TIME_ZONE=Asia/Ho_Chi_Minh

# System jobs
CRON_SYSTEM_CLEANUP_ENABLED=true
CRON_SYSTEM_CLEANUP_SCHEDULE="0 2 * * *"
CRON_SYSTEM_HEALTH_CHECK_ENABLED=true
CRON_SYSTEM_HEALTH_CHECK_SCHEDULE="*/15 * * * *"
CRON_SYSTEM_BACKUP_ENABLED=false
CRON_SYSTEM_BACKUP_SCHEDULE="0 3 * * 0"

# Auth module jobs
CRON_AUTH_SESSION_CLEANUP_ENABLED=true
CRON_AUTH_SESSION_CLEANUP_SCHEDULE="0 2 * * *"
CRON_AUTH_SESSION_CLEANUP_MAX_AGE=24h
CRON_AUTH_PASSWORD_EXPIRY_ENABLED=true
CRON_AUTH_PASSWORD_EXPIRY_SCHEDULE="0 9 * * *"
CRON_AUTH_PASSWORD_EXPIRY_NOTIFY_DAYS="7,3,1"
CRON_AUTH_PASSWORD_EXPIRY_TEMPLATE_ID=password_expiry

# Media module jobs
CRON_MEDIA_IMAGE_OPTIMIZATION_ENABLED=true
CRON_MEDIA_IMAGE_OPTIMIZATION_SCHEDULE="0 3 * * *"
CRON_MEDIA_IMAGE_OPTIMIZATION_QUALITY=85
CRON_MEDIA_IMAGE_OPTIMIZATION_BATCH_SIZE=100
CRON_MEDIA_TEMP_CLEANUP_ENABLED=true
CRON_MEDIA_TEMP_CLEANUP_SCHEDULE="0 1 * * *"
CRON_MEDIA_TEMP_CLEANUP_MAX_AGE=24h
```

### Cron Schedule Format

The system uses standard cron expressions:

```
┌───────────── minute (0 - 59)
│ ┌───────────── hour (0 - 23)
│ │ ┌───────────── day of the month (1 - 31)
│ │ │ ┌───────────── month (1 - 12)
│ │ │ │ ┌───────────── day of the week (0 - 6) (Sunday to Saturday)
│ │ │ │ │
│ │ │ │ │
* * * * *
```

Examples:
- `0 2 * * *` - Daily at 2:00 AM
- `*/15 * * * *` - Every 15 minutes
- `0 3 * * 0` - Weekly on Sunday at 3:00 AM
- `0 9 * * 1-5` - Weekdays at 9:00 AM

## Usage

### CLI Commands

#### List all cron jobs
```bash
./wnapi cron list
./wnapi cron list --json
```

#### Show cron system status
```bash
./wnapi cron status
./wnapi cron status --json
```

#### Run a specific job manually
```bash
./wnapi cron run system_cleanup
./wnapi cron run auth_session_cleanup
```

#### Start the cron scheduler
```bash
./wnapi cron start
```

### Programmatic Usage

#### Initialize Cron Manager
```go
import (
    "wnapi/internal/pkg/queue/cron"
)

// Create cron manager
cronMgr := cron.NewCronManager(queueMgr, config, logger, db)

// Initialize with handlers and jobs
if err := cronMgr.Initialize(); err != nil {
    log.Fatal("Failed to initialize cron manager:", err)
}

// Start the scheduler
if err := cronMgr.Start(); err != nil {
    log.Fatal("Failed to start cron scheduler:", err)
}
```

#### Register Custom Handler
```go
// Implement CronHandler interface
type MyCustomHandler struct {
    db     *gorm.DB
    logger *slog.Logger
}

func (h *MyCustomHandler) GetTaskType() types.CronTaskType {
    return "custom:my_task"
}

func (h *MyCustomHandler) GetDescription() string {
    return "My custom cron task"
}

func (h *MyCustomHandler) Handle(ctx context.Context, payload types.CronTaskPayload) (*types.CronTaskResult, error) {
    // Your task logic here
    return &types.CronTaskResult{
        TaskType:    "custom:my_task",
        Success:     true,
        Message:     "Task completed successfully",
        ExecutedAt:  time.Now(),
        CompletedAt: time.Now(),
    }, nil
}

// Register the handler
handler := &MyCustomHandler{db: db, logger: logger}
if err := cronMgr.RegisterCustomHandler(handler); err != nil {
    log.Fatal("Failed to register custom handler:", err)
}
```

#### Register Custom Job
```go
job := &cron.CronJob{
    ID:          "my_custom_job",
    Name:        "My Custom Job",
    TaskType:    "custom:my_task",
    Schedule:    "0 4 * * *", // Daily at 4 AM
    Enabled:     true,
    Handler:     handler,
    Description: "My custom scheduled task",
}

if err := cronMgr.RegisterCustomJob(job); err != nil {
    log.Fatal("Failed to register custom job:", err)
}
```

## Module Integration

### Implementing CronModule Interface

```go
package mymodule

import (
    "wnapi/internal/pkg/module"
    "wnapi/internal/pkg/queue/cron"
)

type MyModule struct {
    module.CronModuleBase
    // ... other fields
}

func (m *MyModule) RegisterCronHandlers(registry *cron.HandlerRegistry) error {
    // Register your module's cron handlers
    handler := &MyModuleHandler{...}
    return registry.RegisterHandler(handler)
}

func (m *MyModule) GetCronJobs() ([]*cron.CronJob, error) {
    // Return your module's cron jobs
    jobs := []*cron.CronJob{
        {
            ID:       "mymodule_task",
            Name:     "My Module Task",
            TaskType: "mymodule:task",
            Schedule: "0 5 * * *",
            Enabled:  true,
            Handler:  handler,
        },
    }
    return jobs, nil
}
```

## Monitoring and Logging

### Job Execution Results

Each job execution produces a `CronTaskResult` with:
- Success status
- Error messages
- Processed item count
- Error count
- Execution duration
- Additional details

### Health Checks

The system includes health checks for:
- Database connectivity
- Redis connectivity
- Storage accessibility
- External API availability

### Logging

All cron activities are logged with structured logging:
- Job registration
- Job execution start/completion
- Errors and warnings
- Performance metrics

## Error Handling

### Retry Logic

Failed jobs can be configured with retry policies:
- Maximum retry attempts
- Retry intervals
- Exponential backoff

### Error Notifications

Critical errors can trigger notifications:
- Email alerts
- Slack notifications
- System monitoring integration

## Testing

### Unit Tests

```bash
go test ./internal/pkg/queue/cron/...
```

### Integration Tests

```bash
go test -tags=integration ./internal/pkg/queue/cron/...
```

### Manual Testing

```bash
# Test individual jobs
./wnapi cron run system_cleanup

# Test with dry run (where supported)
CRON_SYSTEM_CLEANUP_DRY_RUN=true ./wnapi cron run system_cleanup
```

## Performance Considerations

### Batch Processing

Large operations are processed in batches to:
- Prevent memory issues
- Allow for interruption
- Provide progress feedback

### Resource Management

- Database connections are properly managed
- File operations include proper cleanup
- Memory usage is monitored

### Scheduling Optimization

- Jobs are distributed across different times
- Resource-intensive jobs run during off-peak hours
- Dependencies between jobs are considered

## Security

### Access Control

- Cron operations require appropriate permissions
- Sensitive operations are logged and audited
- Configuration is validated for security

### Data Protection

- Backup operations include encryption
- Cleanup operations preserve important data
- Access to sensitive files is restricted

## Troubleshooting

### Common Issues

1. **Jobs not running**: Check if cron system is enabled and started
2. **Schedule not working**: Validate cron expression syntax
3. **Handler errors**: Check logs for specific error messages
4. **Performance issues**: Review batch sizes and resource usage

### Debug Mode

Enable debug logging:
```bash
LOG_LEVEL=debug ./wnapi cron start
```

### Manual Execution

Test jobs manually:
```bash
./wnapi cron run <job-id>
```

## Local Development

Để phát triển và test cron jobs ở môi trường local, tham khảo [Local Development Guide](./LOCAL_DEVELOPMENT.md).

### Quick Start cho Local

```bash
# 1. Setup môi trường
make cron-setup

# 2. Khởi động services
docker-compose up -d mysql redis

# 3. Build ứng dụng
make build

# 4. Test cron jobs
make cron-test

# 5. Xem logs
make cron-logs
```

### Quick Commands

```bash
# Xem danh sách jobs
make cron-list

# Chạy job cụ thể
make cron-run JOB=system_cleanup

# Khởi động scheduler
make cron-start

# Xem status
make cron-status
```

## Production Deployment (Triển khai Production)

### 1. Docker Production Setup

#### Cập nhật Docker Compose cho Production
Thêm Redis service và cấu hình cron trong `docker-compose.prod.yml`:

```yaml
services:
  # API service với cron support
  api:
    image: wnapi:latest
    container_name: wnapi
    restart: always
    ports:
      - "8080:8080"
    volumes:
      - ./logs:/app/logs
      - ./config:/app/config
      - ./projects:/app/projects
      - cron-data:/app/cron-data
    environment:
      - CRON_ENABLED=true
      - CRON_TIME_ZONE=Asia/Ho_Chi_Minh
      - REDIS_URL=redis://redis:6379
    depends_on:
      - mysql
      - redis
    networks:
      - wnapi-network
    command: ["--project=production"]

  # Cron Worker Service (riêng biệt)
  cron-worker:
    image: wnapi:latest
    container_name: wnapi-cron-worker
    restart: always
    volumes:
      - ./logs:/app/logs
      - ./config:/app/config
      - ./projects:/app/projects
      - cron-data:/app/cron-data
    environment:
      - CRON_ENABLED=true
      - CRON_TIME_ZONE=Asia/Ho_Chi_Minh
      - REDIS_URL=redis://redis:6379
      - APP_MODE=cron-only
    depends_on:
      - mysql
      - redis
    networks:
      - wnapi-network
    command: ["cron", "start", "--project=production"]

  # Redis for queue system
  redis:
    image: redis:7.2-alpine
    container_name: wnapi-redis
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    command: redis-server --appendonly yes
    networks:
      - wnapi-network

volumes:
  cron-data:
    driver: local
  redis-data:
    driver: local
```

#### Dockerfile cho Production
Cập nhật Dockerfile để hỗ trợ cron:

```dockerfile
# Stage 2: Runtime
FROM alpine:3.20

# Cài đặt các gói cần thiết + cron tools
RUN apk add --no-cache ca-certificates tzdata busybox-extras

# Tạo user non-root
RUN addgroup -g 1001 -S wnapi && \
    adduser -S wnapi -u 1001 -G wnapi

# Thư mục làm việc
WORKDIR /app

# Copy binary từ stage 1
COPY --from=builder /app/build/wnapi /app/wnapi

# Tạo thư mục cần thiết
RUN mkdir -p /app/logs /app/cron-data /app/config && \
    chown -R wnapi:wnapi /app

# Switch to non-root user
USER wnapi

# Thiết lập timezone
ENV TZ=Asia/Ho_Chi_Minh

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD /app/wnapi health || exit 1

# Command để chạy ứng dụng
ENTRYPOINT ["/app/wnapi"]
```

### 2. Environment Configuration

#### Production Environment Variables
Tạo file `.env.production`:

```bash
# Database Configuration
DB_HOST=mysql
DB_PORT=3306
DB_NAME=wnapi_prod
DB_USER=wnapi
DB_PASSWORD=secure_password_here
DB_MAX_CONNECTIONS=25
DB_MAX_IDLE_CONNECTIONS=5
DB_CONNECTION_LIFETIME=5m

# Redis Configuration
REDIS_URL=redis://redis:6379
REDIS_PASSWORD=
REDIS_DB=0

# Cron System Configuration
CRON_ENABLED=true
CRON_TIME_ZONE=Asia/Ho_Chi_Minh

# System Jobs
CRON_SYSTEM_CLEANUP_ENABLED=true
CRON_SYSTEM_CLEANUP_SCHEDULE="0 2 * * *"
CRON_SYSTEM_CLEANUP_RETENTION_DAYS=30
CRON_SYSTEM_HEALTH_CHECK_ENABLED=true
CRON_SYSTEM_HEALTH_CHECK_SCHEDULE="*/5 * * * *"
CRON_SYSTEM_BACKUP_ENABLED=true
CRON_SYSTEM_BACKUP_SCHEDULE="0 3 * * 0"
CRON_SYSTEM_BACKUP_S3_BUCKET=wnapi-backups

# Auth Module Jobs
CRON_AUTH_SESSION_CLEANUP_ENABLED=true
CRON_AUTH_SESSION_CLEANUP_SCHEDULE="0 2 * * *"
CRON_AUTH_SESSION_CLEANUP_MAX_AGE=168h
CRON_AUTH_PASSWORD_EXPIRY_ENABLED=true
CRON_AUTH_PASSWORD_EXPIRY_SCHEDULE="0 9 * * *"
CRON_AUTH_PASSWORD_EXPIRY_NOTIFY_DAYS="14,7,3,1"

# Media Module Jobs
CRON_MEDIA_IMAGE_OPTIMIZATION_ENABLED=true
CRON_MEDIA_IMAGE_OPTIMIZATION_SCHEDULE="0 3 * * *"
CRON_MEDIA_IMAGE_OPTIMIZATION_QUALITY=85
CRON_MEDIA_IMAGE_OPTIMIZATION_BATCH_SIZE=50
CRON_MEDIA_TEMP_CLEANUP_ENABLED=true
CRON_MEDIA_TEMP_CLEANUP_SCHEDULE="0 1 * * *"
CRON_MEDIA_TEMP_CLEANUP_MAX_AGE=48h

# Blog Module Jobs
CRON_BLOG_AUTO_PUBLISH_ENABLED=true
CRON_BLOG_AUTO_PUBLISH_SCHEDULE="*/10 * * * *"
CRON_BLOG_SITEMAP_UPDATE_ENABLED=true
CRON_BLOG_SITEMAP_UPDATE_SCHEDULE="0 4 * * *"

# Notification Jobs
CRON_NOTIFICATION_DIGEST_ENABLED=true
CRON_NOTIFICATION_DIGEST_SCHEDULE="0 8 * * 1"
CRON_NOTIFICATION_CLEANUP_ENABLED=true
CRON_NOTIFICATION_CLEANUP_SCHEDULE="0 5 * * 0"
CRON_NOTIFICATION_CLEANUP_RETENTION_DAYS=90

# Monitoring và Logging
LOG_LEVEL=info
LOG_FORMAT=json
CRON_LOG_LEVEL=info
SENTRY_DSN=your_sentry_dsn_here
```

### 3. Kubernetes Deployment

#### Cron CronJob Resource
Tạo file `k8s/cron-jobs.yaml`:

```yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  name: wnapi-system-cleanup
spec:
  schedule: "0 2 * * *"
  timeZone: "Asia/Ho_Chi_Minh"
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: wnapi-cron
            image: wnapi:latest
            command: ["/app/wnapi", "cron", "run", "system_cleanup"]
            env:
            - name: DB_HOST
              valueFrom:
                secretKeyRef:
                  name: wnapi-secrets
                  key: db-host
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: wnapi-secrets
                  key: db-password
            volumeMounts:
            - name: logs
              mountPath: /app/logs
          volumes:
          - name: logs
            persistentVolumeClaim:
              claimName: wnapi-logs
          restartPolicy: OnFailure
---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: wnapi-auth-cleanup
spec:
  schedule: "0 2 * * *"
  timeZone: "Asia/Ho_Chi_Minh"
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: wnapi-cron
            image: wnapi:latest
            command: ["/app/wnapi", "cron", "run", "auth_session_cleanup"]
          restartPolicy: OnFailure
```

#### Deployment với Cron Worker
Tạo file `k8s/cron-worker-deployment.yaml`:

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: wnapi-cron-worker
spec:
  replicas: 1
  selector:
    matchLabels:
      app: wnapi-cron-worker
  template:
    metadata:
      labels:
        app: wnapi-cron-worker
    spec:
      containers:
      - name: cron-worker
        image: wnapi:latest
        command: ["/app/wnapi", "cron", "start"]
        env:
        - name: CRON_ENABLED
          value: "true"
        - name: APP_MODE
          value: "cron-only"
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        volumeMounts:
        - name: logs
          mountPath: /app/logs
        - name: config
          mountPath: /app/config
      volumes:
      - name: logs
        persistentVolumeClaim:
          claimName: wnapi-logs
      - name: config
        configMap:
          name: wnapi-config
```

### 4. Monitoring và Alerting

#### Prometheus Metrics
Thêm metrics cho cron jobs trong code:

```go
// internal/pkg/monitoring/cron_metrics.go
var (
    cronJobsTotal = prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "cron_jobs_total",
            Help: "Total number of cron jobs executed",
        },
        []string{"job_name", "status"},
    )
    
    cronJobDuration = prometheus.NewHistogramVec(
        prometheus.HistogramOpts{
            Name: "cron_job_duration_seconds",
            Help: "Duration of cron job execution",
        },
        []string{"job_name"},
    )
)
```

#### Grafana Dashboard
Tạo dashboard monitoring cho cron jobs:
- Job execution frequency
- Success/failure rates
- Execution duration
- Queue depth
- Error patterns

#### Alerting Rules
Tạo file `monitoring/alerts/cron-alerts.yaml`:

```yaml
groups:
- name: cron-jobs
  rules:
  - alert: CronJobFailed
    expr: rate(cron_jobs_total{status="failed"}[5m]) > 0
    for: 0m
    labels:
      severity: warning
    annotations:
      summary: "Cron job {{ $labels.job_name }} failed"
      
  - alert: CronJobNotRunning
    expr: time() - cron_last_execution_timestamp > 86400
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "Cron job {{ $labels.job_name }} hasn't run for 24 hours"
```

### 5. Backup và Recovery

#### Database Backup Job
```bash
#!/bin/bash
# scripts/backup-cron.sh
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/app/backups"
DB_NAME="wnapi_prod"

# Tạo backup
mysqldump -h $DB_HOST -u $DB_USER -p$DB_PASSWORD $DB_NAME | gzip > $BACKUP_DIR/backup_$DATE.sql.gz

# Upload to S3
aws s3 cp $BACKUP_DIR/backup_$DATE.sql.gz s3://$S3_BUCKET/database/

# Cleanup old local backups (keep 7 days)
find $BACKUP_DIR -name "backup_*.sql.gz" -mtime +7 -delete
```

#### Cron Data Backup
```bash
#!/bin/bash
# scripts/backup-cron-data.sh
tar -czf /app/backups/cron-data_$(date +%Y%m%d).tar.gz /app/cron-data
aws s3 cp /app/backups/cron-data_$(date +%Y%m%d).tar.gz s3://$S3_BUCKET/cron-data/
```

### 6. Security Best Practices

#### Network Security
- Cron worker containers chỉ cần truy cập database và Redis
- Không expose ports không cần thiết
- Sử dụng network policies trong Kubernetes

#### Access Control
- Separate service accounts cho cron jobs
- Minimal permissions principle
- Audit logging cho tất cả cron operations

#### Secrets Management
- Sử dụng Kubernetes secrets hoặc external secret managers
- Rotate credentials định kỳ
- Encrypt sensitive data trong cron configs

### 7. Troubleshooting Production

#### Common Issues và Solutions

**Cron jobs không chạy:**
```bash
# Check cron service status
kubectl get pods -l app=wnapi-cron-worker
kubectl logs -l app=wnapi-cron-worker

# Check cron jobs trong cluster
kubectl get cronjobs
kubectl describe cronjob wnapi-system-cleanup
```

**Performance Issues:**
```bash
# Monitor resource usage
kubectl top pods -l app=wnapi-cron-worker

# Check job execution times
./wnapi cron status --json | jq '.jobs[] | select(.last_duration > 300)'
```

**Database Connection Issues:**
```bash
# Test database connectivity
./wnapi cron run system_health_check

# Check connection pool
kubectl exec -it wnapi-cron-worker -- ./wnapi health db
```

### 8. Scaling và High Availability

#### Horizontal Scaling
- Sử dụng distributed cron với Redis locks
- Multiple cron workers với job distribution
- Leader election cho critical jobs

#### Failover Strategy
- Multiple availability zones
- Automatic job retry với exponential backoff
- Dead letter queues cho failed jobs

#### Load Balancing
- Distribute jobs across multiple workers
- Priority-based job scheduling
- Resource-based job assignment

## Future Enhancements

- Web-based cron job management interface
- Advanced scheduling options (holidays, business days)
- Job dependency management
- Distributed cron execution across multiple regions
- Real-time monitoring dashboard with WebSocket updates
- AI-powered job scheduling optimization
- Integration với CI/CD pipelines
- Multi-cluster cron job synchronization
