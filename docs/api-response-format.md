# API Response Format Standards

## Overview

This document defines the standardized response format for all API endpoints in the WNAPI application, with special attention to list endpoints and pagination.

## Standard Response Structure

All API responses follow this consistent structure:

```json
{
  "status": {
    "code": 200,
    "message": "Operation completed successfully",
    "success": true,
    "error_code": "",
    "path": "/api/v1/endpoint",
    "timestamp": "2024-01-15T10:30:00Z",
    "details": null
  },
  "data": <response_data>,
  "meta": <pagination_metadata>
}
```

### Status Object

- **code**: HTTP status code
- **message**: Human-readable message
- **success**: <PERSON>olean indicating operation success
- **error_code**: Application-specific error code (empty for success)
- **path**: Request path
- **timestamp**: ISO 8601 timestamp
- **details**: Additional error details (null for success)

## List Endpoints Response Format

### ✅ Correct Format

For list endpoints (e.g., GET /api/admin/v1/users), the response should be:

```json
{
  "status": { ... },
  "data": [
    {
      "user_id": 1,
      "username": "user1",
      "email": "<EMAIL>"
    },
    {
      "user_id": 2,
      "username": "user2", 
      "email": "<EMAIL>"
    }
  ],
  "meta": {
    "next_cursor": "eyJpZCI6Mn0=",
    "has_more": true
  }
}
```

### ❌ Incorrect Format (Avoid)

Do NOT nest the array and metadata under a data object:

```json
{
  "status": { ... },
  "data": {
    "users": [...],
    "meta": {
      "next_cursor": "...",
      "has_more": true
    }
  }
}
```

## Pagination Standards

### Cursor-Based Pagination

All list endpoints use cursor-based pagination:

- **next_cursor**: Opaque cursor string for the next page
- **has_more**: Boolean indicating if more results are available
- **limit**: Maximum number of items per page (query parameter)

### Query Parameters

- `cursor`: Cursor for pagination (optional)
- `limit`: Number of items per page (default: 10, max: 100)
- Additional filters as needed

### Example Request

```
GET /api/admin/v1/users?limit=20&cursor=eyJpZCI6Mn0=&status=active
```

## Implementation Guidelines

### DTO Structure

List response DTOs should follow this pattern:

```go
type ListUsersResponse struct {
    Data []UserResponse `json:"data"`
    Meta struct {
        NextCursor string `json:"next_cursor"`
        HasMore    bool   `json:"has_more"`
    } `json:"meta"`
}
```

### Handler Implementation

Handlers should extract data and meta separately:

```go
func (h *Handler) ListUsers(c *gin.Context) {
    resp, err := h.service.ListUsers(ctx, req)
    if err != nil {
        // handle error
        return
    }

    // Extract meta for proper response structure
    meta := &response.Meta{
        NextCursor: resp.Meta.NextCursor,
        HasMore:    resp.Meta.HasMore,
    }

    response.Success(c, resp.Data, meta)
}
```

### Service Layer

Services return structured responses:

```go
func (s *Service) ListUsers(ctx context.Context, req ListUsersRequest) (*ListUsersResponse, error) {
    // ... business logic ...
    
    return &ListUsersResponse{
        Data: userResponses,
        Meta: struct {
            NextCursor string `json:"next_cursor"`
            HasMore    bool   `json:"has_more"`
        }{
            NextCursor: nextCursor,
            HasMore:    hasMore,
        },
    }, nil
}
```

## Error Response Format

Error responses follow the same structure with additional details:

```json
{
  "status": {
    "code": 400,
    "message": "Validation failed",
    "success": false,
    "error_code": "VALIDATION_ERROR",
    "path": "/api/admin/v1/users",
    "timestamp": "2024-01-15T10:30:00Z",
    "details": [
      {
        "field": "email",
        "message": "Invalid email format"
      }
    ]
  },
  "data": null,
  "meta": null
}
```

## Consistency Across Modules

All modules should follow these patterns:

- **Auth Module**: ✅ Fixed to use correct format
- **RBAC Module**: ✅ Already follows correct format
- **Tenant Module**: ✅ Already follows correct format
- **Product Module**: ✅ Already follows correct format
- **Notification Module**: Should follow same pattern

## Testing

Always test response format:

```go
func TestListEndpoint_ResponseFormat(t *testing.T) {
    // ... setup ...
    
    var response response.Response
    err := json.Unmarshal(w.Body.Bytes(), &response)
    assert.NoError(t, err)
    
    // Verify data is array at top level
    dataBytes, _ := json.Marshal(response.Data)
    var items []ItemResponse
    err = json.Unmarshal(dataBytes, &items)
    assert.NoError(t, err)
    
    // Verify meta at top level
    assert.NotNil(t, response.Meta)
    assert.Equal(t, expectedCursor, response.Meta.NextCursor)
    assert.Equal(t, expectedHasMore, response.Meta.HasMore)
}
```

## Migration Checklist

When updating existing endpoints:

1. ✅ Update DTO structure (Data field instead of nested array)
2. ✅ Update service implementation
3. ✅ Update handler to extract meta separately
4. ✅ Update tests to verify correct format
5. ✅ Update API documentation
6. ✅ Verify consistency with other modules

## Benefits

This standardized format provides:

- **Consistency**: All endpoints follow the same pattern
- **Predictability**: Clients know what to expect
- **Extensibility**: Easy to add new metadata fields
- **Performance**: Efficient cursor-based pagination
- **Debugging**: Clear error information with context
