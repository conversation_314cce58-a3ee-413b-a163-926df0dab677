# Hướng dẫn Format File Bruno cho AI

## Tổng quan
<PERSON> l<PERSON> một API client mã nguồn mở sử dụng format file `.bru` (Bru Markup Language) để lưu trữ các API request. <PERSON><PERSON><PERSON> c<PERSON> request được lưu dưới dạng plain text files, dễ đọc và version control friendly.

## Cấu trúc Collection

### 1. C<PERSON>u trúc thư mục cơ bản
```
my-bruno-collection/
├── bruno.json              # File cấu hình collection
├── package.json            # Dependencies cho scripts (tùy chọn)
├── environments/           # Thư mục chứa environment variables
│   ├── development.bru
│   ├── staging.bru
│   └── production.bru
├── api/                    # Thư mục chứa các request
│   ├── users/
│   │   ├── get-users.bru
│   │   ├── create-user.bru
│   │   └── update-user.bru
│   └── auth/
│       ├── login.bru
│       └── refresh-token.bru
├── .gitignore             # Ignore file cho Git
└── .env                   # Environment variables (secret)
```

### 2. File bruno.json
```json
{
  "version": "1",
  "name": "My API Collection",
  "type": "collection",
  "ignore": [
    "node_modules",
    ".env"
  ]
}
```

## Format File .bru

### Cấu trúc cơ bản
Mỗi file `.bru` có các phần chính:
- `meta` - Metadata của request
- HTTP method block - `get`, `post`, `put`, `delete`, etc.
- `headers` - HTTP headers
- `body` - Request body (nếu có)
- `auth` - Authentication
- `vars` - Variables
- `assert` - Assertions
- `script` - Scripts (pre-request và post-response)
- `tests` - Test scripts

### Ví dụ GET Request
```bru
meta {
  name: Get Users
  type: http
  seq: 1
}

get {
  url: {{BASE_URL}}/api/users
  body: none
  auth: none
}

headers {
  Content-Type: application/json
  Accept: application/json
  User-Agent: Bruno/1.0
}

vars {
  limit: 10
  offset: 0
}

assert {
  res.status: eq 200
  res.body.data: isDefined
  res.body.data.length: lte {{limit}}
}

tests {
  test("should return users list", function() {
    const body = res.getBody();
    expect(body.data).to.be.an('array');
    expect(body.data.length).to.be.at.most({{limit}});
  });
}
```

### Ví dụ POST Request với JSON Body
```bru
meta {
  name: Create User
  type: http
  seq: 2
}

post {
  url: {{BASE_URL}}/api/users
  body: json
  auth: bearer
}

headers {
  Content-Type: application/json
  Accept: application/json
}

auth:bearer {
  token: {{ACCESS_TOKEN}}
}

body:json {
  {
    "name": "John Doe",
    "email": "<EMAIL>",
    "role": "user"
  }
}

vars {
  user_id: ""
}

script:pre-request {
  // Generate random email
  const timestamp = Date.now();
  bru.setVar("email", `user_${timestamp}@example.com`);
}

script:post-response {
  // Save user ID for later use
  if (res.status === 201) {
    const body = res.getBody();
    bru.setVar("user_id", body.data.id);
    bru.setEnvVar("LAST_CREATED_USER_ID", body.data.id);
  }
}

assert {
  res.status: eq 201
  res.body.data.id: isDefined
  res.body.data.email: eq {{email}}
}

tests {
  test("should create user successfully", function() {
    expect(res.getStatus()).to.equal(201);
    const body = res.getBody();
    expect(body.data).to.have.property('id');
    expect(body.data.email).to.equal(bru.getVar('email'));
  });
}
```

### Ví dụ PUT Request với Form Data
```bru
meta {
  name: Update User Avatar
  type: http
  seq: 3
}

put {
  url: {{BASE_URL}}/api/users/{{user_id}}
  body: multipartForm
  auth: bearer
}

auth:bearer {
  token: {{ACCESS_TOKEN}}
}

body:multipart-form {
  avatar: @file(./assets/avatar.jpg)
  name: Updated Name
}

assert {
  res.status: eq 200
}
```

## Environment Files

### File environment .bru
```bru
vars {
  BASE_URL: https://api.example.com
  API_VERSION: v1
  TIMEOUT: 30000
}

vars:secret [
  ACCESS_TOKEN,
  API_KEY,
  DB_PASSWORD
]
```

### File .env (cho secrets)
```env
ACCESS_TOKEN=your_secret_token_here
API_KEY=your_api_key_here
DB_PASSWORD=your_db_password_here
```

## Các loại Body

### 1. JSON Body
```bru
body:json {
  {
    "key": "value",
    "nested": {
      "data": "{{variable}}"
    }
  }
}
```

### 2. Form URL Encoded
```bru
body:form-urlencoded {
  username: john_doe
  password: {{PASSWORD}}
  grant_type: password
}
```

### 3. Multipart Form
```bru
body:multipart-form {
  file: @file(./path/to/file.jpg)
  description: File upload
  category: image
}
```

### 4. Raw Text
```bru
body:text {
  This is raw text content
  with multiple lines
  and {{variables}}
}
```

### 5. XML Body
```bru
body:xml {
  <?xml version="1.0" encoding="UTF-8"?>
  <user>
    <name>{{username}}</name>
    <email>{{email}}</email>
  </user>
}
```

### 6. GraphQL
```bru
body:graphql {
  query GetUser($id: ID!) {
    user(id: $id) {
      id
      name
      email
    }
  }
}

body:graphql:vars {
  {
    "id": "{{user_id}}"
  }
}
```

## Authentication Types

### 1. Bearer Token
```bru
auth:bearer {
  token: {{ACCESS_TOKEN}}
}
```

### 2. Basic Auth
```bru
auth:basic {
  username: {{USERNAME}}
  password: {{PASSWORD}}
}
```

### 3. API Key
```bru
auth:apikey {
  key: X-API-Key
  value: {{API_KEY}}
  placement: header
}
```

### 4. OAuth 2.0
```bru
auth:oauth2 {
  grant_type: authorization_code
  callback_url: http://localhost:3000/callback
  authorization_url: https://auth.example.com/oauth/authorize
  access_token_url: https://auth.example.com/oauth/token
  client_id: {{CLIENT_ID}}
  client_secret: {{CLIENT_SECRET}}
  scope: read write
}
```

### 5. AWS Signature V4
```bru
auth:awsv4 {
  accessKeyId: {{AWS_ACCESS_KEY_ID}}
  secretAccessKey: {{AWS_SECRET_ACCESS_KEY}}
  sessionToken: {{AWS_SESSION_TOKEN}}
  service: s3
  region: us-east-1
}
```

### 6. Digest Auth
```bru
auth:digest {
  username: {{USERNAME}}
  password: {{PASSWORD}}
}
```

## Scripts và Variables

### Pre-request Scripts
```bru
script:pre-request {
  // Set dynamic variables
  bru.setVar("timestamp", Date.now());
  bru.setVar("uuid", require('uuid').v4());
  
  // Generate auth token
  const token = await getAuthToken();
  bru.setVar("auth_token", token);
  
  // Set headers dynamically
  req.setHeader("X-Request-ID", bru.getVar("uuid"));
}
```

### Post-response Scripts
```bru
script:post-response {
  // Save response data
  if (res.status === 200) {
    const body = res.getBody();
    bru.setEnvVar("LAST_RESPONSE_ID", body.id);
    
    // Log response for debugging
    console.log("Response received:", body);
  }
  
  // Handle errors
  if (res.status >= 400) {
    console.error("Request failed:", res.getBody());
  }
}
```

### Các hàm Script có sẵn

#### Request functions
- `req.getUrl()` - Get request URL
- `req.setUrl(url)` - Set request URL
- `req.getMethod()` - Get HTTP method
- `req.setMethod(method)` - Set HTTP method
- `req.getHeader(name)` - Get header value
- `req.setHeader(name, value)` - Set header
- `req.getHeaders()` - Get all headers
- `req.setHeaders(headers)` - Set multiple headers
- `req.getBody()` - Get request body
- `req.setBody(body)` - Set request body

#### Response functions
- `res.getStatus()` - Get status code
- `res.getHeader(name)` - Get response header
- `res.getHeaders()` - Get all response headers
- `res.getBody()` - Get response body
- `res.getResponseTime()` - Get response time

#### Variable functions
- `bru.getVar(name)` - Get variable
- `bru.setVar(name, value)` - Set variable
- `bru.getEnvVar(name)` - Get environment variable
- `bru.setEnvVar(name, value)` - Set environment variable
- `bru.getProcessEnv(name)` - Get process environment variable

#### Utility functions
- `bru.cwd()` - Get current working directory

## Assertions

### Simple Assertions
```bru
assert {
  res.status: eq 200
  res.body.success: eq true
  res.body.data: isDefined
  res.body.data.id: isNumber
  res.body.data.email: contains @
  res.body.data.created_at: matches ^\d{4}-\d{2}-\d{2}
  res.body.items.length: gt 0
  res.body.items.length: lte 50
  res.responseTime: lt 1000
}
```

### Assertion Operators
- `eq` - Equal
- `neq` - Not equal
- `gt` - Greater than
- `gte` - Greater than or equal
- `lt` - Less than
- `lte` - Less than or equal
- `contains` - Contains substring
- `notContains` - Does not contain substring
- `startsWith` - Starts with
- `endsWith` - Ends with
- `matches` - Matches regex pattern
- `isDefined` - Is defined
- `isUndefined` - Is undefined
- `isNull` - Is null
- `isNotNull` - Is not null
- `isNumber` - Is number
- `isString` - Is string
- `isBoolean` - Is boolean
- `isArray` - Is array
- `isObject` - Is object

## Tests với Chai.js

### Basic Tests
```bru
tests {
  test("should return success status", function() {
    expect(res.getStatus()).to.equal(200);
  });

  test("should return valid data structure", function() {
    const body = res.getBody();
    expect(body).to.be.an('object');
    expect(body).to.have.property('data');
    expect(body.data).to.be.an('array');
  });

  test("should have required fields", function() {
    const body = res.getBody();
    body.data.forEach(item => {
      expect(item).to.have.property('id');
      expect(item).to.have.property('name');
      expect(item).to.have.property('email');
      expect(item.id).to.be.a('number');
      expect(item.name).to.be.a('string');
      expect(item.email).to.match(/^.+@.+\..+$/);
    });
  });
}
```

### Advanced Tests
```bru
tests {
  test("should handle pagination correctly", function() {
    const body = res.getBody();
    expect(body.pagination).to.exist;
    expect(body.pagination.page).to.be.a('number');
    expect(body.pagination.limit).to.be.a('number');
    expect(body.pagination.total).to.be.a('number');
    expect(body.data.length).to.be.at.most(body.pagination.limit);
  });

  test("should validate response schema", function() {
    const body = res.getBody();
    const expectedSchema = {
      success: 'boolean',
      data: 'array',
      message: 'string'
    };
    
    Object.keys(expectedSchema).forEach(key => {
      expect(body).to.have.property(key);
      expect(body[key]).to.be.a(expectedSchema[key]);
    });
  });
}
```

## Ví dụ Workflows

### Authentication Flow
```bru
# 1. login.bru
meta {
  name: Login
  type: http
  seq: 1
}

post {
  url: {{BASE_URL}}/auth/login
  body: json
  auth: none
}

body:json {
  {
    "email": "{{EMAIL}}",
    "password": "{{PASSWORD}}"
  }
}

script:post-response {
  if (res.status === 200) {
    const body = res.getBody();
    bru.setEnvVar("ACCESS_TOKEN", body.access_token);
    bru.setEnvVar("REFRESH_TOKEN", body.refresh_token);
  }
}

assert {
  res.status: eq 200
  res.body.access_token: isDefined
}
```

### CRUD Operations
```bru
# create-user.bru
meta {
  name: Create User
  type: http
  seq: 1
}

post {
  url: {{BASE_URL}}/users
  body: json
  auth: bearer
}

auth:bearer {
  token: {{ACCESS_TOKEN}}
}

body:json {
  {
    "name": "Test User",
    "email": "<EMAIL>"
  }
}

script:post-response {
  if (res.status === 201) {
    bru.setVar("created_user_id", res.getBody().id);
  }
}

# get-user.bru
meta {
  name: Get User
  type: http
  seq: 2
}

get {
  url: {{BASE_URL}}/users/{{created_user_id}}
  body: none
  auth: bearer
}

auth:bearer {
  token: {{ACCESS_TOKEN}}
}

# update-user.bru
meta {
  name: Update User
  type: http
  seq: 3
}

put {
  url: {{BASE_URL}}/users/{{created_user_id}}
  body: json
  auth: bearer
}

auth:bearer {
  token: {{ACCESS_TOKEN}}
}

body:json {
  {
    "name": "Updated User Name"
  }
}

# delete-user.bru
meta {
  name: Delete User
  type: http
  seq: 4
}

delete {
  url: {{BASE_URL}}/users/{{created_user_id}}
  body: none
  auth: bearer
}

auth:bearer {
  token: {{ACCESS_TOKEN}}
}
```

## Best Practices

### 1. Naming Conventions
- Collection names: `kebab-case`
- Request names: `Descriptive Title Case`
- Variable names: `UPPER_SNAKE_CASE` cho environments, `camelCase` cho runtime
- File names: `kebab-case.bru`

### 2. Organization
- Sắp xếp requests theo modules/features
- Sử dụng folders để group related requests
- Đặt sequence number (`seq`) hợp lý

### 3. Variables Management
- Sử dụng environment variables cho configs
- Sử dụng runtime variables cho data giữa requests
- Đặt secrets trong file `.env`
- Sử dụng `vars:secret` để mark sensitive data

### 4. Testing Strategy
- Viết assertions cho happy path
- Test error cases
- Validate data types và structure
- Check performance (response time)

### 5. Scripts Usage
- Keep scripts simple và focused
- Use pre-request scripts cho data preparation
- Use post-response scripts cho data extraction
- Handle errors gracefully

## Troubleshooting

### Common Issues
1. **File encoding**: Ensure UTF-8 encoding
2. **Variable scope**: Check variable availability
3. **JSON syntax**: Validate JSON bodies
4. **Path references**: Use correct file paths for `@file()`
5. **Environment selection**: Ensure correct environment is active

### Debugging Tips
- Use `console.log()` trong scripts
- Check variable values in Bruno UI
- Validate JSON syntax externally
- Test requests individually before chaining
- Check network connectivity và proxy settings

## Tích hợp CI/CD

### CLI Usage
```bash
# Install Bruno CLI
npm install -g @usebruno/cli

# Run all requests in collection
bru run

# Run specific request
bru run api/users/get-users.bru

# Run with specific environment
bru run --env production

# Generate reports
bru run --output results.json --format json
bru run --output results.xml --format junit
```

### GitHub Actions Example
```yaml
name: API Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm install -g @usebruno/cli
      - run: bru run --env ci --output results.junit --format junit
        working-directory: ./api-tests
      - uses: dorny/test-reporter@v1
        if: always()
        with:
          name: API Test Results
          path: ./api-tests/results.junit
          reporter: java-junit
```

Tài liệu này cung cấp đầy đủ thông tin để AI có thể hiểu và tạo ra các file Bruno đúng format, bao gồm tất cả các thành phần cần thiết và best practices.