# Tài Liệu Event System với Watermill Redis Streams

## 1. Tổng Quan

Event System trong WNAPI được xây dựng dựa trên thư viện [Watermill](https://watermill.io/) và sử dụng Redis Streams làm message broker. Watermill cung cấp một interface đơn giản và mạnh mẽ cho event-driven architecture với khả năng xử lý hàng trăm nghìn messages mỗi giây.

### Đặc điểm chính:
- **Universal Interface**: Sử dụng Publisher/Subscriber interfaces chuẩn của Watermill
- **Redis Streams**: Tận dụng Redis Streams để có hiệu suất cao và độ tin cậy
- **At-Least-Once Delivery**: Đảm bảo messages được delivery ít nhất một lần
- **Consumer Groups**: Hỗ trợ parallel processing và load balancing
- **Middleware Support**: Router với middleware cho logging, retry, metrics
- **Multi-tenant Ready**: Hỗ trợ isolation theo tenant và website

## 2. Dependencies và Installation

### 2.1 Go Dependencies

```bash
# Core Watermill
go get github.com/ThreeDotsLabs/watermill

# Redis Streams Pub/Sub
go get github.com/ThreeDotsLabs/watermill-redisstream

# Redis client
go get github.com/redis/go-redis/v9
```

### 2.2 Docker Setup cho Development

```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    image: golang:1.23
    restart: unless-stopped
    depends_on:
      - redis
    volumes:
      - .:/app
      - $GOPATH/pkg/mod:/go/pkg/mod
    working_dir: /app
    command: go run main.go
    environment:
      - REDIS_URL=redis://redis:6379/0

  redis:
    image: redis:7-alpine
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  redis_data:
```

## 3. Cấu Trúc Thư Mục

```
internal/pkg/events/
├── config.go                  # Event system configuration
├── publisher.go               # Redis streams publisher wrapper
├── subscriber.go              # Redis streams subscriber wrapper
├── router.go                  # Watermill router setup
├── middleware.go              # Custom middlewares
├── message.go                 # Message creation utilities
├── types/
│   ├── auth_events.go          # Auth domain events
│   ├── blog_events.go          # Blog domain events
│   ├── ecommerce_events.go     # E-commerce domain events
│   └── system_events.go        # System events
└── handlers/
    ├── auth_handlers.go        # Auth event handlers
    ├── blog_handlers.go        # Blog event handlers
    ├── ecommerce_handlers.go   # E-commerce event handlers
    └── notification_handlers.go # Notification handlers

modules/*/events/               # Module-specific event integration
├── auth/events/
│   ├── publisher.go            # Auth module event publishing
│   └── handlers.go             # Auth module event handling
├── blog/events/
│   ├── publisher.go            # Blog module event publishing
│   └── handlers.go             # Blog module event handling
└── product/events/
    ├── publisher.go            # Product module event publishing
    └── handlers.go             # Product module event handling
```

## 4. Cấu Hình Environment Variables

```bash
# Redis Configuration
REDIS_URL=redis://localhost:6379/0
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# Event System Configuration
EVENT_ENABLED=true
EVENT_CONSUMER_GROUP=wnapi_consumers
EVENT_CONSUMER_ID=wnapi_consumer_1

# Router Configuration
EVENT_ROUTER_CLOSE_TIMEOUT=30s
EVENT_ROUTER_MIDDLEWARE_TIMEOUT=30s

# Publisher Configuration
EVENT_PUBLISHER_MAX_LEN=10000

# Retry Configuration
EVENT_RETRY_MAX_ATTEMPTS=3
EVENT_RETRY_INITIAL_INTERVAL=1s
EVENT_RETRY_MAX_INTERVAL=30s

# Monitoring
EVENT_LOGGING_ENABLED=true
EVENT_METRICS_ENABLED=true
EVENT_DEBUG=false
```

## 5. Core Implementation

### 5.1 Configuration Structure

```go
package events

import (
    "time"
    "github.com/redis/go-redis/v9"
)

type Config struct {
    // Redis configuration
    RedisURL      string `env:"REDIS_URL" envDefault:"redis://localhost:6379/0"`
    RedisHost     string `env:"REDIS_HOST" envDefault:"localhost"`
    RedisPort     int    `env:"REDIS_PORT" envDefault:"6379"`
    RedisDB       int    `env:"REDIS_DB" envDefault:"0"`
    RedisPassword string `env:"REDIS_PASSWORD"`

    // Event system configuration
    Enabled       bool   `env:"EVENT_ENABLED" envDefault:"true"`
    ConsumerGroup string `env:"EVENT_CONSUMER_GROUP" envDefault:"wnapi_consumers"`
    ConsumerID    string `env:"EVENT_CONSUMER_ID" envDefault:"wnapi_consumer_1"`

    // Router configuration
    RouterCloseTimeout      time.Duration `env:"EVENT_ROUTER_CLOSE_TIMEOUT" envDefault:"30s"`
    RouterMiddlewareTimeout time.Duration `env:"EVENT_ROUTER_MIDDLEWARE_TIMEOUT" envDefault:"30s"`

    // Publisher configuration
    PublisherMaxLen int64 `env:"EVENT_PUBLISHER_MAX_LEN" envDefault:"10000"`

    // Retry configuration
    RetryMaxAttempts     int           `env:"EVENT_RETRY_MAX_ATTEMPTS" envDefault:"3"`
    RetryInitialInterval time.Duration `env:"EVENT_RETRY_INITIAL_INTERVAL" envDefault:"1s"`
    RetryMaxInterval     time.Duration `env:"EVENT_RETRY_MAX_INTERVAL" envDefault:"30s"`

    // Monitoring
    LoggingEnabled bool `env:"EVENT_LOGGING_ENABLED" envDefault:"true"`
    MetricsEnabled bool `env:"EVENT_METRICS_ENABLED" envDefault:"true"`
    Debug          bool `env:"EVENT_DEBUG" envDefault:"false"`
}

func (c *Config) GetRedisOptions() *redis.Options {
    return &redis.Options{
        Addr:     fmt.Sprintf("%s:%d", c.RedisHost, c.RedisPort),
        Password: c.RedisPassword,
        DB:       c.RedisDB,
    }
}
```

### 5.2 Publisher Implementation

```go
package events

import (
    "context"
    "encoding/json"
    "fmt"
    "time"

    "github.com/ThreeDotsLabs/watermill"
    "github.com/ThreeDotsLabs/watermill-redisstream/pkg/redisstream"
    "github.com/ThreeDotsLabs/watermill/message"
    "github.com/redis/go-redis/v9"
)

type Publisher struct {
    publisher *redisstream.Publisher
    logger    watermill.LoggerAdapter
}

func NewPublisher(config Config) (*Publisher, error) {
    logger := watermill.NewStdLogger(config.Debug, config.Debug)
    
    redisClient := redis.NewClient(config.GetRedisOptions())
    
    publisher, err := redisstream.NewPublisher(
        redisstream.PublisherConfig{
            Client:        redisClient,
            Marshaller:    redisstream.DefaultMarshallerUnmarshaller{},
            DefaultMaxlen: config.PublisherMaxLen,
        },
        logger,
    )
    if err != nil {
        return nil, fmt.Errorf("failed to create redis publisher: %w", err)
    }
    
    return &Publisher{
        publisher: publisher,
        logger:    logger,
    }, nil
}

func (p *Publisher) Publish(ctx context.Context, topic string, payload interface{}) error {
    payloadBytes, err := json.Marshal(payload)
    if err != nil {
        return fmt.Errorf("failed to marshal payload: %w", err)
    }
    
    msg := message.NewMessage(watermill.NewUUID(), payloadBytes)
    
    // Add metadata
    msg.Metadata.Set("published_at", time.Now().Format(time.RFC3339))
    msg.Metadata.Set("topic", topic)
    
    return p.publisher.Publish(topic, msg)
}

func (p *Publisher) PublishEvent(ctx context.Context, event Event) error {
    return p.Publish(ctx, event.Type(), event)
}

func (p *Publisher) Close() error {
    return p.publisher.Close()
}
```

### 5.3 Subscriber Implementation

```go
package events

import (
    "context"
    "fmt"
    "time"

    "github.com/ThreeDotsLabs/watermill"
    "github.com/ThreeDotsLabs/watermill-redisstream/pkg/redisstream"
    "github.com/ThreeDotsLabs/watermill/message"
    "github.com/redis/go-redis/v9"
)

type Subscriber struct {
    subscriber *redisstream.Subscriber
    logger     watermill.LoggerAdapter
}

func NewSubscriber(config Config) (*Subscriber, error) {
    logger := watermill.NewStdLogger(config.Debug, config.Debug)
    
    redisClient := redis.NewClient(config.GetRedisOptions())
    
    subscriber, err := redisstream.NewSubscriber(
        redisstream.SubscriberConfig{
            Client:        redisClient,
            Unmarshaller:  redisstream.DefaultMarshallerUnmarshaller{},
            ConsumerGroup: config.ConsumerGroup,
            Consumer:      config.ConsumerID,
            
            // Performance settings
            BlockTime:               100 * time.Millisecond,
            ClaimInterval:           5 * time.Second,
            ClaimBatchSize:          100,
            MaxIdleTime:             60 * time.Second,
            CheckConsumersInterval:  5 * time.Minute,
            ConsumerTimeout:         10 * time.Minute,
        },
        logger,
    )
    if err != nil {
        return nil, fmt.Errorf("failed to create redis subscriber: %w", err)
    }
    
    return &Subscriber{
        subscriber: subscriber,
        logger:     logger,
    }, nil
}

func (s *Subscriber) Subscribe(ctx context.Context, topic string) (<-chan *message.Message, error) {
    return s.subscriber.Subscribe(ctx, topic)
}

func (s *Subscriber) Close() error {
    return s.subscriber.Close()
}
```

### 5.4 Event Router Implementation

```go
package events

import (
    "context"
    "time"

    "github.com/ThreeDotsLabs/watermill"
    "github.com/ThreeDotsLabs/watermill/message"
    "github.com/ThreeDotsLabs/watermill/message/router/middleware"
    "github.com/ThreeDotsLabs/watermill/message/router/plugin"
)

type EventRouter struct {
    router    *message.Router
    publisher *Publisher
    config    Config
    logger    watermill.LoggerAdapter
}

func NewEventRouter(config Config, publisher *Publisher) (*EventRouter, error) {
    logger := watermill.NewStdLogger(config.Debug, config.Debug)
    
    routerConfig := message.RouterConfig{
        CloseTimeout: config.RouterCloseTimeout,
    }
    
    router, err := message.NewRouter(routerConfig, logger)
    if err != nil {
        return nil, err
    }
    
    // Add plugins
    router.AddPlugin(plugin.SignalsHandler)
    
    // Add middlewares
    router.AddMiddleware(
        // Correlation ID middleware
        middleware.CorrelationID,
        
        // Retry middleware
        middleware.Retry{
            MaxRetries:      config.RetryMaxAttempts,
            InitialInterval: config.RetryInitialInterval,
            MaxInterval:     config.RetryMaxInterval,
            Multiplier:      2.0,
        }.Middleware,
        
        // Timeout middleware
        middleware.Timeout(config.RouterMiddlewareTimeout),
        
        // Recovery middleware
        middleware.Recoverer,
    )
    
    if config.MetricsEnabled {
        // Add metrics middleware if enabled
        // router.AddMiddleware(middleware.NewMetrics().Middleware)
    }
    
    return &EventRouter{
        router:    router,
        publisher: publisher,
        config:    config,
        logger:    logger,
    }, nil
}

func (r *EventRouter) AddHandler(
    handlerName string,
    topic string,
    subscriber message.Subscriber,
    handlerFunc message.HandlerFunc,
) {
    r.router.AddHandler(
        handlerName,
        topic,
        subscriber,
        topic, // For now, publish to same topic
        r.publisher.publisher,
        handlerFunc,
    )
}

func (r *EventRouter) AddNoPublisherHandler(
    handlerName string,
    topic string,
    subscriber message.Subscriber,
    handlerFunc message.NoPublishHandlerFunc,
) {
    r.router.AddNoPublisherHandler(
        handlerName,
        topic,
        subscriber,
        handlerFunc,
    )
}

func (r *EventRouter) Run(ctx context.Context) error {
    return r.router.Run(ctx)
}

func (r *EventRouter) Close() error {
    return r.router.Close()
}
```

## 6. Event Types và Message Structure

### 6.1 Event Interface

```go
package events

import (
    "time"
)

type Event interface {
    Type() string
    TenantID() uint
    WebsiteID() uint
    UserID() *uint
    Payload() interface{}
    Metadata() map[string]interface{}
    Timestamp() time.Time
}

type BaseEvent struct {
    EventType   string                 `json:"event_type"`
    TenantId    uint                   `json:"tenant_id"`
    WebsiteId   uint                   `json:"website_id"`
    UserId      *uint                  `json:"user_id,omitempty"`
    EventPayload interface{}           `json:"payload"`
    EventMetadata map[string]interface{} `json:"metadata"`
    CreatedAt   time.Time              `json:"created_at"`
}

func (e *BaseEvent) Type() string { return e.EventType }
func (e *BaseEvent) TenantID() uint { return e.TenantId }
func (e *BaseEvent) WebsiteID() uint { return e.WebsiteId }
func (e *BaseEvent) UserID() *uint { return e.UserId }
func (e *BaseEvent) Payload() interface{} { return e.EventPayload }
func (e *BaseEvent) Metadata() map[string]interface{} { return e.EventMetadata }
func (e *BaseEvent) Timestamp() time.Time { return e.CreatedAt }

func NewEvent(eventType string, tenantID, websiteID uint, userID *uint, payload interface{}) Event {
    return &BaseEvent{
        EventType:     eventType,
        TenantId:      tenantID,
        WebsiteId:     websiteID,
        UserId:        userID,
        EventPayload:  payload,
        EventMetadata: make(map[string]interface{}),
        CreatedAt:     time.Now(),
    }
}
```

### 6.2 Event Constants

```go
package events

// Auth Events
const (
    AuthUserCreated      = "auth.user.created"
    AuthUserUpdated      = "auth.user.updated"
    AuthUserDeleted      = "auth.user.deleted"
    AuthUserLoginSuccess = "auth.user.login_success"
    AuthUserLoginFailed  = "auth.user.login_failed"
    AuthUserLogout       = "auth.user.logout"
    AuthPasswordReset    = "auth.user.password_reset"
    AuthEmailVerified    = "auth.user.email_verified"
)

// Blog Events
const (
    BlogPostCreated   = "blog.post.created"
    BlogPostPublished = "blog.post.published"
    BlogPostUpdated   = "blog.post.updated"
    BlogPostDeleted   = "blog.post.deleted"
    BlogPostViewed    = "blog.post.viewed"
    
    BlogCommentCreated  = "blog.comment.created"
    BlogCommentApproved = "blog.comment.approved"
    BlogCommentRejected = "blog.comment.rejected"
    BlogCommentDeleted  = "blog.comment.deleted"
)

// E-commerce Events
const (
    ProductCreated        = "product.created"
    ProductUpdated        = "product.updated"
    ProductDeleted        = "product.deleted"
    ProductInventoryChanged = "product.inventory_changed"
    ProductPriceChanged   = "product.price_changed"
    
    OrderCreated         = "order.created"
    OrderPaymentReceived = "order.payment_received"
    OrderPaymentFailed   = "order.payment_failed"
    OrderFulfilled       = "order.fulfilled"
    OrderShipped         = "order.shipped"
    OrderDelivered       = "order.delivered"
    OrderCancelled       = "order.cancelled"
    OrderRefunded        = "order.refunded"
)

// System Events
const (
    SystemStarted         = "system.started"
    SystemStopped         = "system.stopped"
    SystemHealthCheck     = "system.health_check"
    SystemMaintenanceMode = "system.maintenance_mode"
)
```

### 6.3 Event Payloads

```go
package events

import (
    "time"
    "github.com/shopspring/decimal"
)

// Auth Event Payloads
type UserCreatedPayload struct {
    UserID    uint      `json:"user_id"`
    Email     string    `json:"email"`
    Username  string    `json:"username"`
    FullName  string    `json:"full_name"`
    Status    string    `json:"status"`
    CreatedAt time.Time `json:"created_at"`
}

type UserLoginPayload struct {
    UserID    uint   `json:"user_id"`
    Email     string `json:"email"`
    IPAddress string `json:"ip_address"`
    UserAgent string `json:"user_agent"`
    Success   bool   `json:"success"`
    Reason    string `json:"reason,omitempty"`
}

// Blog Event Payloads
type PostCreatedPayload struct {
    PostID     uint      `json:"post_id"`
    Title      string    `json:"title"`
    Slug       string    `json:"slug"`
    AuthorID   uint      `json:"author_id"`
    Status     string    `json:"status"`
    CreatedAt  time.Time `json:"created_at"`
}

type CommentCreatedPayload struct {
    CommentID uint      `json:"comment_id"`
    PostID    uint      `json:"post_id"`
    AuthorID  *uint     `json:"author_id,omitempty"`
    Content   string    `json:"content"`
    Status    string    `json:"status"`
    CreatedAt time.Time `json:"created_at"`
}

// E-commerce Event Payloads
type ProductCreatedPayload struct {
    ProductID   uint            `json:"product_id"`
    Name        string          `json:"name"`
    SKU         string          `json:"sku"`
    Price       decimal.Decimal `json:"price"`
    Inventory   int             `json:"inventory"`
    Status      string          `json:"status"`
    CreatedAt   time.Time       `json:"created_at"`
}

type OrderCreatedPayload struct {
    OrderID     uint                `json:"order_id"`
    CustomerID  uint                `json:"customer_id"`
    TotalAmount decimal.Decimal     `json:"total_amount"`
    Currency    string              `json:"currency"`
    Status      string              `json:"status"`
    Items       []OrderItemPayload  `json:"items"`
    CreatedAt   time.Time           `json:"created_at"`
}

type OrderItemPayload struct {
    ProductID uint            `json:"product_id"`
    Quantity  int             `json:"quantity"`
    Price     decimal.Decimal `json:"price"`
    Total     decimal.Decimal `json:"total"`
}
```

## 7. Event Handlers

### 7.1 Handler Implementation Pattern

```go
package handlers

import (
    "context"
    "encoding/json"
    "fmt"
    "log"

    "github.com/ThreeDotsLabs/watermill/message"
    "your-project/internal/pkg/events"
)

// Email notification handler
func SendWelcomeEmailHandler(msg *message.Message) error {
    var event events.BaseEvent
    if err := json.Unmarshal(msg.Payload, &event); err != nil {
        return fmt.Errorf("failed to unmarshal event: %w", err)
    }
    
    if event.Type() != events.AuthUserCreated {
        return nil // Skip non-relevant events
    }
    
    var payload events.UserCreatedPayload
    payloadBytes, err := json.Marshal(event.Payload())
    if err != nil {
        return fmt.Errorf("failed to marshal payload: %w", err)
    }
    
    if err := json.Unmarshal(payloadBytes, &payload); err != nil {
        return fmt.Errorf("failed to unmarshal payload: %w", err)
    }
    
    // Send welcome email logic
    log.Printf("Sending welcome email to user %s (%s)", payload.FullName, payload.Email)
    
    // Simulate email sending
    // emailService.SendWelcomeEmail(payload.Email, payload.FullName)
    
    return nil
}

// Analytics tracking handler
func TrackUserEventHandler(msg *message.Message) error {
    var event events.BaseEvent
    if err := json.Unmarshal(msg.Payload, &event); err != nil {
        return fmt.Errorf("failed to unmarshal event: %w", err)
    }
    
    // Track all user-related events
    if event.UserID() != nil {
        log.Printf("Tracking event %s for user %d", event.Type(), *event.UserID())
        
        // Send to analytics service
        // analyticsService.Track(event.Type(), event.UserID(), event.Payload())
    }
    
    return nil
}

// Blog search indexing handler  
func IndexBlogContentHandler(msg *message.Message) error {
    var event events.BaseEvent
    if err := json.Unmarshal(msg.Payload, &event); err != nil {
        return fmt.Errorf("failed to unmarshal event: %w", err)
    }
    
    if event.Type() != events.BlogPostPublished {
        return nil
    }
    
    var payload events.PostCreatedPayload
    payloadBytes, err := json.Marshal(event.Payload())
    if err != nil {
        return fmt.Errorf("failed to marshal payload: %w", err)
    }
    
    if err := json.Unmarshal(payloadBytes, &payload); err != nil {
        return fmt.Errorf("failed to unmarshal payload: %w", err)
    }
    
    // Index blog post for search
    log.Printf("Indexing blog post %s for search", payload.Title)
    
    // searchService.IndexDocument("blog_posts", payload.PostID, payload)
    
    return nil
}
```

### 7.2 Handler Registration

```go
package events

func SetupEventHandlers(router *EventRouter, subscriber *Subscriber) error {
    // Email notifications
    router.AddNoPublisherHandler(
        "send_welcome_email",
        AuthUserCreated,
        subscriber.subscriber,
        handlers.SendWelcomeEmailHandler,
    )
    
    // Analytics tracking
    router.AddNoPublisherHandler(
        "track_user_events",
        "auth.*", // Subscribe to all auth events
        subscriber.subscriber,
        handlers.TrackUserEventHandler,
    )
    
    // Blog search indexing
    router.AddNoPublisherHandler(
        "index_blog_content",
        BlogPostPublished,
        subscriber.subscriber,
        handlers.IndexBlogContentHandler,
    )
    
    // Order processing workflow
    router.AddNoPublisherHandler(
        "process_order",
        OrderCreated,
        subscriber.subscriber,
        handlers.ProcessOrderHandler,
    )
    
    return nil
}
```

## 8. Integration Example

### 8.1 Main Application Setup

```go
package main

import (
    "context"
    "log"
    "os"
    "os/signal"
    "syscall"

    "your-project/internal/pkg/events"
    "your-project/internal/pkg/events/handlers"
)

func main() {
    ctx, cancel := context.WithCancel(context.Background())
    defer cancel()
    
    // Load configuration
    config := events.Config{
        RedisHost:     "localhost",
        RedisPort:     6379,
        RedisDB:       0,
        Enabled:       true,
        ConsumerGroup: "wnapi_consumers",
        ConsumerID:    "wnapi_consumer_1",
        Debug:         false,
    }
    
    // Create publisher
    publisher, err := events.NewPublisher(config)
    if err != nil {
        log.Fatal("Failed to create publisher:", err)
    }
    defer publisher.Close()
    
    // Create subscriber
    subscriber, err := events.NewSubscriber(config)
    if err != nil {
        log.Fatal("Failed to create subscriber:", err)
    }
    defer subscriber.Close()
    
    // Create router
    router, err := events.NewEventRouter(config, publisher)
    if err != nil {
        log.Fatal("Failed to create router:", err)
    }
    defer router.Close()
    
    // Setup event handlers
    if err := events.SetupEventHandlers(router, subscriber); err != nil {
        log.Fatal("Failed to setup handlers:", err)
    }
    
    // Start router in background
    go func() {
        if err := router.Run(ctx); err != nil {
            log.Fatal("Router failed:", err)
        }
    }()
    
    // Example: Publish some events
    go func() {
        userEvent := events.NewEvent(
            events.AuthUserCreated,
            1, // tenant ID
            1, // website ID
            uintPtr(123), // user ID
            events.UserCreatedPayload{
                UserID:   123,
                Email:    "<EMAIL>",
                Username: "newuser",
                FullName: "New User",
                Status:   "active",
            },
        )
        
        if err := publisher.PublishEvent(ctx, userEvent); err != nil {
            log.Printf("Failed to publish event: %v", err)
        }
    }()
    
    // Wait for interrupt signal
    sigChan := make(chan os.Signal, 1)
    signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
    
    log.Println("Event system started. Press Ctrl+C to stop...")
    <-sigChan
    
    log.Println("Shutting down event system...")
    cancel()
}

func uintPtr(i uint) *uint {
    return &i
}
```

### 8.2 Module Integration Example (Auth Module)

```go
package auth

import (
    "context"
    "your-project/internal/pkg/events"
)

type AuthService struct {
    eventPublisher *events.Publisher
    // ... other dependencies
}

func NewAuthService(eventPublisher *events.Publisher) *AuthService {
    return &AuthService{
        eventPublisher: eventPublisher,
    }
}

func (s *AuthService) CreateUser(ctx context.Context, req CreateUserRequest) (*User, error) {
    // Create user logic
    user := &User{
        ID:       123,
        Email:    req.Email,
        Username: req.Username,
        FullName: req.FullName,
        Status:   "active",
    }
    
    // Save to database
    if err := s.userRepo.Create(ctx, user); err != nil {
        return nil, err
    }
    
    // Publish event
    event := events.NewEvent(
        events.AuthUserCreated,
        req.TenantID,
        req.WebsiteID,
        &user.ID,
        events.UserCreatedPayload{
            UserID:   user.ID,
            Email:    user.Email,
            Username: user.Username,
            FullName: user.FullName,
            Status:   user.Status,
        },
    )
    
    if err := s.eventPublisher.PublishEvent(ctx, event); err != nil {
        // Log error but don't fail the operation
        log.Printf("Failed to publish user created event: %v", err)
    }
    
    return user, nil
}
```

## 9. Testing

### 9.1 Unit Testing với Mock

```go
package events_test

import (
    "context"
    "testing"
    "time"

    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/require"
    "your-project/internal/pkg/events"
)

func TestEventPublishing(t *testing.T) {
    // Setup test Redis (you might want to use miniredis for true unit tests)
    config := events.Config{
        RedisHost:     "localhost",
        RedisPort:     6379,
        RedisDB:       1, // Use different DB for testing
        ConsumerGroup: "test_consumers",
        ConsumerID:    "test_consumer",
    }
    
    publisher, err := events.NewPublisher(config)
    require.NoError(t, err)
    defer publisher.Close()
    
    subscriber, err := events.NewSubscriber(config)
    require.NoError(t, err)
    defer subscriber.Close()
    
    ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
    defer cancel()
    
    // Subscribe to test topic
    messages, err := subscriber.Subscribe(ctx, "test.topic")
    require.NoError(t, err)
    
    // Publish test event
    testPayload := map[string]interface{}{
        "test_key": "test_value",
        "number":   42,
    }
    
    err = publisher.Publish(ctx, "test.topic", testPayload)
    require.NoError(t, err)
    
    // Wait for message
    select {
    case msg := <-messages:
        assert.NotNil(t, msg)
        msg.Ack()
        
        // Verify message content
        assert.Equal(t, "test.topic", msg.Metadata.Get("topic"))
        
    case <-ctx.Done():
        t.Fatal("Timeout waiting for message")
    }
}

func TestEventHandlerIntegration(t *testing.T) {
    config := events.Config{
        RedisHost:     "localhost",
        RedisPort:     6379,
        RedisDB:       1,
        ConsumerGroup: "test_consumers",
        ConsumerID:    "test_consumer",
    }
    
    publisher, err := events.NewPublisher(config)
    require.NoError(t, err)
    defer publisher.Close()
    
    subscriber, err := events.NewSubscriber(config)
    require.NoError(t, err)
    defer subscriber.Close()
    
    router, err := events.NewEventRouter(config, publisher)
    require.NoError(t, err)
    defer router.Close()
    
    // Track if handler was called
    handlerCalled := make(chan bool, 1)
    
    testHandler := func(msg *message.Message) error {
        handlerCalled <- true
        return nil
    }
    
    // Add test handler
    router.AddNoPublisherHandler(
        "test_handler",
        events.AuthUserCreated,
        subscriber.subscriber,
        testHandler,
    )
    
    ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
    defer cancel()
    
    // Start router
    go router.Run(ctx)
    
    // Wait a moment for router to start
    time.Sleep(100 * time.Millisecond)
    
    // Publish test event
    event := events.NewEvent(
        events.AuthUserCreated,
        1, 1, uintPtr(123),
        events.UserCreatedPayload{
            UserID:   123,
            Email:    "<EMAIL>",
            Username: "testuser",
            FullName: "Test User",
            Status:   "active",
        },
    )
    
    err = publisher.PublishEvent(ctx, event)
    require.NoError(t, err)
    
    // Wait for handler to be called
    select {
    case <-handlerCalled:
        // Success
    case <-ctx.Done():
        t.Fatal("Handler was not called")
    }
}
```

### 9.2 Integration Testing

```go
package events_test

import (
    "context"
    "testing"
    "time"
    
    "github.com/stretchr/testify/suite"
    "your-project/internal/pkg/events"
    "your-project/internal/pkg/events/handlers"
)

type EventSystemIntegrationSuite struct {
    suite.Suite
    publisher  *events.Publisher
    subscriber *events.Subscriber
    router     *events.EventRouter
    config     events.Config
}

func (suite *EventSystemIntegrationSuite) SetupSuite() {
    suite.config = events.Config{
        RedisHost:     "localhost",
        RedisPort:     6379,
        RedisDB:       2, // Different DB for integration tests
        ConsumerGroup: "integration_test_consumers",
        ConsumerID:    "integration_test_consumer",
        Debug:         true,
    }
    
    var err error
    
    suite.publisher, err = events.NewPublisher(suite.config)
    suite.Require().NoError(err)
    
    suite.subscriber, err = events.NewSubscriber(suite.config)
    suite.Require().NoError(err)
    
    suite.router, err = events.NewEventRouter(suite.config, suite.publisher)
    suite.Require().NoError(err)
}

func (suite *EventSystemIntegrationSuite) TearDownSuite() {
    if suite.publisher != nil {
        suite.publisher.Close()
    }
    if suite.subscriber != nil {
        suite.subscriber.Close()
    }
    if suite.router != nil {
        suite.router.Close()
    }
}

func (suite *EventSystemIntegrationSuite) TestUserCreationWorkflow() {
    ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
    defer cancel()
    
    // Track handler calls
    emailSent := make(chan bool, 1)
    analyticsTracked := make(chan bool, 1)
    
    // Mock email handler
    emailHandler := func(msg *message.Message) error {
        emailSent <- true
        return nil
    }
    
    // Mock analytics handler
    analyticsHandler := func(msg *message.Message) error {
        analyticsTracked <- true
        return nil
    }
    
    // Register handlers
    suite.router.AddNoPublisherHandler(
        "test_email_handler",
        events.AuthUserCreated,
        suite.subscriber.subscriber,
        emailHandler,
    )
    
    suite.router.AddNoPublisherHandler(
        "test_analytics_handler",
        events.AuthUserCreated,
        suite.subscriber.subscriber,
        analyticsHandler,
    )
    
    // Start router
    go suite.router.Run(ctx)
    
    // Wait for router to start
    time.Sleep(200 * time.Millisecond)
    
    // Publish user created event
    event := events.NewEvent(
        events.AuthUserCreated,
        1, 1, uintPtr(123),
        events.UserCreatedPayload{
            UserID:   123,
            Email:    "<EMAIL>",
            Username: "integrationuser",
            FullName: "Integration User",
            Status:   "active",
        },
    )
    
    err := suite.publisher.PublishEvent(ctx, event)
    suite.Require().NoError(err)
    
    // Verify both handlers were called
    select {
    case <-emailSent:
        // Success
    case <-ctx.Done():
        suite.Fail("Email handler was not called")
    }
    
    select {
    case <-analyticsTracked:
        // Success  
    case <-ctx.Done():
        suite.Fail("Analytics handler was not called")
    }
}

func TestEventSystemIntegrationSuite(t *testing.T) {
    suite.Run(t, new(EventSystemIntegrationSuite))
}
```

### 9.3 Mock Implementation cho Unit Tests

```go
package mocks

import (
    "context"
    "sync"
    
    "github.com/ThreeDotsLabs/watermill/message"
    "your-project/internal/pkg/events"
)

type MockPublisher struct {
    mu             sync.RWMutex
    publishedEvents []PublishedEvent
}

type PublishedEvent struct {
    Topic   string
    Payload interface{}
}

func NewMockPublisher() *MockPublisher {
    return &MockPublisher{
        publishedEvents: make([]PublishedEvent, 0),
    }
}

func (m *MockPublisher) Publish(ctx context.Context, topic string, payload interface{}) error {
    m.mu.Lock()
    defer m.mu.Unlock()
    
    m.publishedEvents = append(m.publishedEvents, PublishedEvent{
        Topic:   topic,
        Payload: payload,
    })
    
    return nil
}

func (m *MockPublisher) PublishEvent(ctx context.Context, event events.Event) error {
    return m.Publish(ctx, event.Type(), event)
}

func (m *MockPublisher) GetPublishedEvents() []PublishedEvent {
    m.mu.RLock()
    defer m.mu.RUnlock()
    
    result := make([]PublishedEvent, len(m.publishedEvents))
    copy(result, m.publishedEvents)
    return result
}

func (m *MockPublisher) GetEventsByType(eventType string) []PublishedEvent {
    m.mu.RLock()
    defer m.mu.RUnlock()
    
    var result []PublishedEvent
    for _, event := range m.publishedEvents {
        if event.Topic == eventType {
            result = append(result, event)
        }
    }
    return result
}

func (m *MockPublisher) Clear() {
    m.mu.Lock()
    defer m.mu.Unlock()
    
    m.publishedEvents = m.publishedEvents[:0]
}

func (m *MockPublisher) Close() error {
    return nil
}

// Test helper functions
func AssertEventPublished(t *testing.T, publisher *MockPublisher, eventType string) {
    events := publisher.GetEventsByType(eventType)
    if len(events) == 0 {
        t.Errorf("Expected event %s to be published, but it was not found", eventType)
    }
}

func AssertEventNotPublished(t *testing.T, publisher *MockPublisher, eventType string) {
    events := publisher.GetEventsByType(eventType)
    if len(events) > 0 {
        t.Errorf("Expected event %s NOT to be published, but %d were found", eventType, len(events))
    }
}

func AssertEventCount(t *testing.T, publisher *MockPublisher, eventType string, expectedCount int) {
    events := publisher.GetEventsByType(eventType)
    if len(events) != expectedCount {
        t.Errorf("Expected %d events of type %s, but found %d", expectedCount, eventType, len(events))
    }
}
```

## 10. Monitoring và Health Checks

### 10.1 Health Check Implementation

```go
package events

import (
    "context"
    "fmt"
    "time"
    
    "github.com/redis/go-redis/v9"
)

type HealthChecker struct {
    redisClient *redis.Client
    publisher   *Publisher
    config      Config
}

func NewHealthChecker(config Config, publisher *Publisher) *HealthChecker {
    return &HealthChecker{
        redisClient: redis.NewClient(config.GetRedisOptions()),
        publisher:   publisher,
        config:      config,
    }
}

func (h *HealthChecker) CheckHealth(ctx context.Context) error {
    // Check Redis connectivity
    if err := h.checkRedis(ctx); err != nil {
        return fmt.Errorf("redis health check failed: %w", err)
    }
    
    // Check publisher functionality
    if err := h.checkPublisher(ctx); err != nil {
        return fmt.Errorf("publisher health check failed: %w", err)
    }
    
    return nil
}

func (h *HealthChecker) checkRedis(ctx context.Context) error {
    ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
    defer cancel()
    
    return h.redisClient.Ping(ctx).Err()
}

func (h *HealthChecker) checkPublisher(ctx context.Context) error {
    ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
    defer cancel()
    
    healthEvent := events.NewEvent(
        events.SystemHealthCheck,
        0, 0, nil,
        map[string]interface{}{
            "check_time": time.Now(),
            "component":  "event_system",
        },
    )
    
    return h.publisher.PublishEvent(ctx, healthEvent)
}

func (h *HealthChecker) GetStats(ctx context.Context) (map[string]interface{}, error) {
    info := h.redisClient.Info(ctx, "memory", "clients", "stats")
    if info.Err() != nil {
        return nil, info.Err()
    }
    
    return map[string]interface{}{
        "redis_info":     info.Val(),
        "consumer_group": h.config.ConsumerGroup,
        "consumer_id":    h.config.ConsumerID,
        "timestamp":      time.Now(),
    }, nil
}
```

### 10.2 Metrics Implementation

```go
package events

import (
    "context"
    "time"
    
    "github.com/prometheus/client_golang/prometheus"
    "github.com/prometheus/client_golang/prometheus/promauto"
)

type Metrics struct {
    eventsPublished   *prometheus.CounterVec
    eventsProcessed   *prometheus.CounterVec
    eventsFailed      *prometheus.CounterVec
    processingTime    *prometheus.HistogramVec
    activeConsumers   prometheus.Gauge
}

func NewMetrics() *Metrics {
    return &Metrics{
        eventsPublished: promauto.NewCounterVec(
            prometheus.CounterOpts{
                Name: "watermill_events_published_total",
                Help: "Total number of events published",
            },
            []string{"topic", "tenant_id"},
        ),
        eventsProcessed: promauto.NewCounterVec(
            prometheus.CounterOpts{
                Name: "watermill_events_processed_total",
                Help: "Total number of events processed successfully",
            },
            []string{"topic", "handler"},
        ),
        eventsFailed: promauto.NewCounterVec(
            prometheus.CounterOpts{
                Name: "watermill_events_failed_total",
                Help: "Total number of events that failed processing",
            },
            []string{"topic", "handler", "error_type"},
        ),
        processingTime: promauto.NewHistogramVec(
            prometheus.HistogramOpts{
                Name:    "watermill_event_processing_duration_seconds",
                Help:    "Time spent processing events",
                Buckets: prometheus.DefBuckets,
            },
            []string{"topic", "handler"},
        ),
        activeConsumers: promauto.NewGauge(
            prometheus.GaugeOpts{
                Name: "watermill_active_consumers",
                Help: "Number of active consumers",
            },
        ),
    }
}

func (m *Metrics) RecordEventPublished(topic string, tenantID uint) {
    m.eventsPublished.WithLabelValues(topic, fmt.Sprintf("%d", tenantID)).Inc()
}

func (m *Metrics) RecordEventProcessed(topic, handler string, duration time.Duration) {
    m.eventsProcessed.WithLabelValues(topic, handler).Inc()
    m.processingTime.WithLabelValues(topic, handler).Observe(duration.Seconds())
}

func (m *Metrics) RecordEventFailed(topic, handler, errorType string) {
    m.eventsFailed.WithLabelValues(topic, handler, errorType).Inc()
}

func (m *Metrics) SetActiveConsumers(count float64) {
    m.activeConsumers.Set(count)
}

// Middleware for metrics collection
func (m *Metrics) Middleware(h message.HandlerFunc) message.HandlerFunc {
    return func(msg *message.Message) ([]*message.Message, error) {
        start := time.Now()
        topic := msg.Metadata.Get("topic")
        
        result, err := h(msg)
        
        duration := time.Since(start)
        
        if err != nil {
            m.RecordEventFailed(topic, "handler", "processing_error")
        } else {
            m.RecordEventProcessed(topic, "handler", duration)
        }
        
        return result, err
    }
}
```

## 11. Deployment và Production Setup

### 11.1 Docker Configuration

```dockerfile
# Dockerfile
FROM golang:1.23-alpine AS builder

WORKDIR /app

# Copy go mod files
COPY go.mod go.sum ./
RUN go mod download

# Copy source code
COPY . .

# Build the application
RUN CGO_ENABLED=0 GOOS=linux go build -o wnapi ./cmd/server

FROM alpine:latest

RUN apk --no-cache add ca-certificates
WORKDIR /root/

# Copy the binary
COPY --from=builder /app/wnapi .

# Copy config files
COPY --from=builder /app/config ./config

CMD ["./wnapi"]
```

```yaml
# docker-compose.production.yml
version: '3.8'

services:
  app:
    build: .
    restart: unless-stopped
    depends_on:
      - redis
      - postgres
    environment:
      - REDIS_URL=redis://redis:6379/0
      - DATABASE_URL=**********************************/wnapi
      - EVENT_ENABLED=true
      - EVENT_CONSUMER_GROUP=wnapi_consumers
      - EVENT_CONSUMER_ID=wnapi_consumer_${HOSTNAME}
      - EVENT_DEBUG=false
      - EVENT_METRICS_ENABLED=true
    networks:
      - wnapi_network

  redis:
    image: redis:7-alpine
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - wnapi_network

  redis_exporter:
    image: oliver006/redis_exporter
    restart: unless-stopped
    environment:
      - REDIS_ADDR=redis://redis:6379
    depends_on:
      - redis
    networks:
      - wnapi_network

  postgres:
    image: postgres:15-alpine
    restart: unless-stopped
    environment:
      - POSTGRES_DB=wnapi
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=pass
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - wnapi_network

volumes:
  redis_data:
  postgres_data:

networks:
  wnapi_network:
    driver: bridge
```

### 11.2 Kubernetes Deployment

```yaml
# k8s/redis.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        command: ["redis-server", "--appendonly", "yes"]
        ports:
        - containerPort: 6379
        volumeMounts:
        - name: redis-data
          mountPath: /data
      volumes:
      - name: redis-data
        persistentVolumeClaim:
          claimName: redis-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: redis
spec:
  selector:
    app: redis
  ports:
  - port: 6379
    targetPort: 6379
```

```yaml
# k8s/wnapi.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: wnapi
spec:
  replicas: 3
  selector:
    matchLabels:
      app: wnapi
  template:
    metadata:
      labels:
        app: wnapi
    spec:
      containers:
      - name: wnapi
        image: wnapi:latest
        env:
        - name: REDIS_URL
          value: "redis://redis:6379/0"
        - name: EVENT_ENABLED
          value: "true"
        - name: EVENT_CONSUMER_GROUP
          value: "wnapi_consumers"
        - name: EVENT_CONSUMER_ID
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: EVENT_METRICS_ENABLED
          value: "true"
        ports:
        - containerPort: 8080
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
```

### 11.3 Graceful Shutdown

```go
package main

import (
    "context"
    "log"
    "net/http"
    "os"
    "os/signal"
    "syscall"
    "time"
)

type Application struct {
    server         *http.Server
    eventPublisher *events.Publisher
    eventRouter    *events.EventRouter
}

func (app *Application) Start() error {
    // Start HTTP server
    go func() {
        log.Printf("Starting server on %s", app.server.Addr)
        if err := app.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
            log.Fatal("Server failed to start:", err)
        }
    }()
    
    // Start event router
    go func() {
        if err := app.eventRouter.Run(context.Background()); err != nil {
            log.Printf("Event router error: %v", err)
        }
    }()
    
    return nil
}

func (app *Application) Shutdown(ctx context.Context) error {
    log.Println("Initiating graceful shutdown...")
    
    // Create a context with timeout for shutdown
    shutdownCtx, cancel := context.WithTimeout(ctx, 30*time.Second)
    defer cancel()
    
    // Shutdown HTTP server
    if err := app.server.Shutdown(shutdownCtx); err != nil {
        log.Printf("HTTP server shutdown error: %v", err)
    }
    
    // Close event system components
    if err := app.eventRouter.Close(); err != nil {
        log.Printf("Event router close error: %v", err)
    }
    
    if err := app.eventPublisher.Close(); err != nil {
        log.Printf("Event publisher close error: %v", err)
    }
    
    log.Println("Graceful shutdown completed")
    return nil
}

func main() {
    // Initialize application
    app := &Application{
        // ... initialize components
    }
    
    // Start application
    if err := app.Start(); err != nil {
        log.Fatal("Failed to start application:", err)
    }
    
    // Wait for interrupt signal
    sigChan := make(chan os.Signal, 1)
    signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
    
    <-sigChan
    
    // Graceful shutdown
    ctx, cancel := context.WithTimeout(context.Background(), 45*time.Second)
    defer cancel()
    
    if err := app.Shutdown(ctx); err != nil {
        log.Printf("Shutdown error: %v", err)
        os.Exit(1)
    }
}
```

## 12. Best Practices và Recommendations

### 12.1 Event Design Best Practices

#### ✅ DO's:
- **Small Events**: Giữ event payload nhỏ gọn, chỉ chứa thông tin cần thiết
- **Immutable Events**: Events không được thay đổi sau khi published
- **Clear Naming**: Sử dụng naming convention `{domain}.{entity}.{action}`
- **Include Context**: Luôn include tenant_id, website_id khi cần thiết
- **Backward Compatibility**: Ensure schema evolution không break consumers

#### ❌ DON'Ts:
- **Large Payloads**: Tránh events với payload > 1MB
- **Sensitive Data**: Không include passwords, tokens trong events
- **Synchronous Dependencies**: Event handlers không nên block nhau
- **Database Transactions**: Tránh long-running transactions trong handlers

### 12.2 Performance Optimization

```go
// Good: Batch processing cho high-throughput
func (s *OrderService) ProcessOrdersBatch(ctx context.Context, orders []Order) error {
    for _, order := range orders {
        event := events.NewEvent(events.OrderCreated, order.TenantID, order.WebsiteID, &order.UserID, order)
        
        // Publish asynchronously
        go func(e events.Event) {
            if err := s.eventPublisher.PublishEvent(context.Background(), e); err != nil {
                log.Printf("Failed to publish order event: %v", err)
            }
        }(event)
    }
    
    return nil
}

// Good: Connection pooling
func NewEventSystem(config Config) (*EventSystem, error) {
    // Reuse Redis connections
    redisOptions := config.GetRedisOptions()
    redisOptions.PoolSize = 20
    redisOptions.MinIdleConns = 5
    
    return &EventSystem{
        redisClient: redis.NewClient(redisOptions),
    }, nil
}

// Good: Handler timeout
func TimeoutHandler(timeout time.Duration, handler message.HandlerFunc) message.HandlerFunc {
    return func(msg *message.Message) ([]*message.Message, error) {
        ctx, cancel := context.WithTimeout(context.Background(), timeout)
        defer cancel()
        
        done := make(chan struct{})
        var result []*message.Message
        var err error
        
        go func() {
            result, err = handler(msg)
            close(done)
        }()
        
        select {
        case <-done:
            return result, err
        case <-ctx.Done():
            return nil, fmt.Errorf("handler timeout after %v", timeout)
        }
    }
}
```

### 12.3 Error Handling Patterns

```go
// Idempotent handler pattern
func IdempotentHandler(handler message.HandlerFunc) message.HandlerFunc {
    processedMessages := sync.Map{}
    
    return func(msg *message.Message) ([]*message.Message, error) {
        messageID := msg.UUID
        
        // Check if already processed
        if _, exists := processedMessages.Load(messageID); exists {
            log.Printf("Message %s already processed, skipping", messageID)
            return nil, nil
        }
        
        result, err := handler(msg)
        if err == nil {
            // Mark as processed only on success
            processedMessages.Store(messageID, true)
        }
        
        return result, err
    }
}

// Dead letter queue pattern
func DeadLetterHandler(maxRetries int, handler message.HandlerFunc) message.HandlerFunc {
    return func(msg *message.Message) ([]*message.Message, error) {
        retryCount := getRetryCount(msg)
        
        result, err := handler(msg)
        if err != nil {
            if retryCount >= maxRetries {
                // Send to dead letter queue
                log.Printf("Message %s exceeded max retries, sending to DLQ", msg.UUID)
                return nil, sendToDeadLetterQueue(msg, err)
            }
            
            // Increment retry count
            incrementRetryCount(msg)
            return nil, err
        }
        
        return result, nil
    }
}
```

## 13. Troubleshooting Guide

### 13.1 Common Issues

#### Redis Connection Issues
```bash
# Check Redis connectivity
redis-cli -h localhost -p 6379 ping

# Check consumer groups
redis-cli -h localhost -p 6379 XINFO GROUPS your_stream_name

# Check pending messages
redis-cli -h localhost -p 6379 XPENDING your_stream_name your_consumer_group
```

#### Message Processing Issues
```go
// Debug handler để trace messages
func DebugHandler(handler message.HandlerFunc) message.HandlerFunc {
    return func(msg *message.Message) ([]*message.Message, error) {
        start := time.Now()
        
        log.Printf("Processing message %s on topic %s", 
            msg.UUID, msg.Metadata.Get("topic"))
        
        result, err := handler(msg)
        
        duration := time.Since(start)
        if err != nil {
            log.Printf("Message %s failed after %v: %v", msg.UUID, duration, err)
        } else {
            log.Printf("Message %s processed successfully in %v", msg.UUID, duration)
        }
        
        return result, err
    }
}
```

### 13.2 Monitoring Commands

```bash
# Monitor Redis streams
redis-cli -h localhost -p 6379 MONITOR

# Check stream info
redis-cli -h localhost -p 6379 XINFO STREAM your_stream_name

# List consumer groups
redis-cli -h localhost -p 6379 XINFO GROUPS your_stream_name

# Check consumer group info
redis-cli -h localhost -p 6379 XINFO CONSUMERS your_stream_name your_consumer_group
```

## 14. Kết Luận

Event System với Watermill và Redis Streams cung cấp một giải pháp robust và scalable cho event-driven architecture trong WNAPI. Hệ thống này cho phép:

- **High Performance**: Xử lý hàng trăm nghìn messages/giây với Redis Streams
- **Reliability**: At-least-once delivery với consumer groups và automatic retries  
- **Scalability**: Horizontal scaling thông qua multiple consumers
- **Flexibility**: Middleware pattern cho cross-cutting concerns
- **Observability**: Built-in metrics, logging và health checks
- **Production Ready**: Comprehensive error handling và graceful shutdown

Việc tuân thủ các patterns và best practices trong tài liệu này sẽ đảm bảo event system hoạt động hiệu quả và ổn định trong production environment.

### Next Steps

1. **Implement Core Components**: Bắt đầu với Publisher, Subscriber và basic Router
2. **Add Event Types**: Định nghĩa events cho từng domain (auth, blog, ecommerce)
3. **Setup Handlers**: Implement event handlers cho business logic
4. **Add Monitoring**: Setup metrics và health checks
5. **Deploy và Test**: Deploy trong staging environment và load testing
6. **Production Rollout**: Gradual rollout với monitoring và alerting