# WNAPI CLI Structure Design

## Tổng quan CLI Architecture

CLI được thiết kế theo mô hình modular, mỗi module có thể đăng ký các commands riêng thông qua console folder. Hệ thống CLI sử dụng Cobra library và tích hợp với core framework hiện có.

## Cấu trúc thư mục CLI

```
├── cmd/
│   ├── server/
│   │   └── main.go                         # HTTP Server entry point
│   │
│   └── cli/                                # CLI Application
│       ├── main.go                         # CLI entry point
│       ├── root.go                         # Root command definition
│       ├── version.go                      # Version command
│       └── commands/                       # Global CLI commands
│           ├── migrate.go                  # Database migration commands
│           ├── project.go                  # Project management commands
│           └── generate.go                 # Code generation commands
│
├── modules/                                # Modules với CLI support
│   │
│   ├── core/
│   │   ├── auth/                           # Auth module
│   │   │   ├── module.go
│   │   │   ├── console/                    # Auth CLI commands
│   │   │   │   ├── user_cmd.go            # User management commands
│   │   │   │   ├── token_cmd.go           # Token operations
│   │   │   │   └── permission_cmd.go      # Permission management
│   │   │   ├── api/
│   │   │   ├── domain/
│   │   │   └── repository/
│   │   │
│   │   ├── tenant/                         # Tenant module
│   │   │   ├── module.go
│   │   │   ├── console/                    # Tenant CLI commands
│   │   │   │   ├── tenant_cmd.go          # Tenant CRUD operations
│   │   │   │   ├── plan_cmd.go            # Tenant plan management
│   │   │   │   └── settings_cmd.go        # Tenant settings
│   │   │   ├── api/
│   │   │   └── domain/
│   │   │
│   │   └── website/                        # Website module
│   │       ├── module.go
│   │       ├── console/                    # Website CLI commands
│   │       │   ├── website_cmd.go         # Website management
│   │       │   ├── menu_cmd.go            # Menu management
│   │       │   └── theme_cmd.go           # Theme operations
│   │       ├── api/
│   │       └── domain/
│   │
│   ├── content/
│   │   ├── blog/                           # Blog module
│   │   │   ├── module.go
│   │   │   ├── console/                    # Blog CLI commands
│   │   │   │   ├── post_cmd.go            # Post management
│   │   │   │   ├── category_cmd.go        # Category management
│   │   │   │   └── import_cmd.go          # Content import/export
│   │   │   ├── api/
│   │   │   └── domain/
│   │   │
│   │   └── media/                          # Media module
│   │       ├── module.go
│   │       ├── console/                    # Media CLI commands
│   │       │   ├── media_cmd.go           # Media file operations
│   │       │   ├── optimize_cmd.go        # Image optimization
│   │       │   └── cleanup_cmd.go         # Cleanup unused files
│   │       ├── api/
│   │       └── domain/
│   │
│   └── ecommerce/
│       ├── product/                        # Product module
│       │   ├── module.go
│       │   ├── console/                    # Product CLI commands
│       │   │   ├── product_cmd.go         # Product CRUD
│       │   │   ├── category_cmd.go        # Category management
│       │   │   ├── import_cmd.go          # Product import
│       │   │   └── sync_cmd.go            # Product sync
│       │   ├── api/
│       │   └── domain/
│       │
│       └── order/                          # Order module
│           ├── module.go
│           ├── console/                    # Order CLI commands
│           │   ├── order_cmd.go           # Order management
│           │   ├── export_cmd.go          # Order export
│           │   └── report_cmd.go          # Order reports
│           ├── api/
│           └── domain/
│
├── internal/
│   ├── core/
│   │   ├── app.go
│   │   ├── module.go
│   │   └── cli.go                          # CLI registry and management
│   │
│   └── pkg/
│       ├── console/                        # CLI utilities
│       │   ├── registry.go                 # Command registry
│       │   ├── interface.go                # CLI interfaces
│       │   ├── output.go                   # Output formatting
│       │   ├── input.go                    # Input handling
│       │   └── progress.go                 # Progress bars
│       │
│       └── logger/
│           └── cli_logger.go               # CLI-specific logger
```

## Core CLI Components

### 1. CLI Entry Point (`cmd/cli/main.go`)

```go
package main

import (
	"context"
	"os"

	"wnapi/cmd/cli/commands"
	"wnapi/internal/core"
	"wnapi/internal/pkg/console"
	"wnapi/internal/pkg/logger"

	"github.com/spf13/cobra"
)

func main() {
	// Initialize application
	app, err := core.NewApp()
	if err != nil {
		logger.NewConsoleLogger("cli", logger.LevelError).
			Fatal("Failed to initialize application", "error", err)
	}
	defer app.Cleanup(context.Background())

	// Create root command
	rootCmd := &cobra.Command{
		Use:   "wnapi",
		Short: "WNAPI - Multi-tenant Web Application Platform",
		Long: `WNAPI is a modular, multi-tenant web application platform
that supports various content management and e-commerce features.`,
	}

	// Add global flags
	rootCmd.PersistentFlags().StringP("config", "c", "", "config file path")
	rootCmd.PersistentFlags().StringP("project", "p", "", "project name")
	rootCmd.PersistentFlags().BoolP("verbose", "v", false, "verbose output")
	rootCmd.PersistentFlags().Bool("no-color", false, "disable colored output")

	// Register global commands
	rootCmd.AddCommand(commands.NewVersionCommand())
	rootCmd.AddCommand(commands.NewMigrateCommand(app))
	rootCmd.AddCommand(commands.NewProjectCommand(app))
	rootCmd.AddCommand(commands.NewGenerateCommand(app))

	// Register module console commands
	registry := console.NewRegistry()
	if err := registerModuleCommands(app, registry); err != nil {
		logger.NewConsoleLogger("cli", logger.LevelError).
			Fatal("Failed to register module commands", "error", err)
	}

	// Add registered commands to root
	for _, cmd := range registry.GetCommands() {
		rootCmd.AddCommand(cmd)
	}

	// Execute CLI
	if err := rootCmd.Execute(); err != nil {
		os.Exit(1)
	}
}

// registerModuleCommands tự động đăng ký tất cả console commands từ modules
func registerModuleCommands(app *core.App, registry *console.Registry) error {
	modules := app.GetModules()
	
	for _, module := range modules {
		if consoleModule, ok := module.(console.ConsoleModule); ok {
			if err := consoleModule.RegisterConsoleCommands(registry); err != nil {
				return err
			}
		}
	}
	
	return nil
}
```

### 2. CLI Registry Interface (`internal/pkg/console/interface.go`)

```go
package console

import (
	"github.com/spf13/cobra"
)

// ConsoleModule interface cho modules có CLI commands
type ConsoleModule interface {
	// RegisterConsoleCommands đăng ký các CLI commands của module
	RegisterConsoleCommands(registry *Registry) error
}

// CommandBuilder interface để build commands
type CommandBuilder interface {
	// BuildCommand tạo cobra command
	BuildCommand() *cobra.Command
}

// CommandGroup đại diện cho một nhóm commands
type CommandGroup struct {
	Name        string
	Description string
	Commands    []CommandBuilder
}

// Registry quản lý tất cả CLI commands
type Registry struct {
	commands []*cobra.Command
	groups   map[string]*CommandGroup
}

// NewRegistry tạo registry mới
func NewRegistry() *Registry {
	return &Registry{
		commands: make([]*cobra.Command, 0),
		groups:   make(map[string]*CommandGroup),
	}
}

// RegisterCommand đăng ký một command
func (r *Registry) RegisterCommand(cmd *cobra.Command) {
	r.commands = append(r.commands, cmd)
}

// RegisterCommandBuilder đăng ký command builder
func (r *Registry) RegisterCommandBuilder(builder CommandBuilder) {
	cmd := builder.BuildCommand()
	r.RegisterCommand(cmd)
}

// RegisterGroup đăng ký một nhóm commands
func (r *Registry) RegisterGroup(name, description string, builders []CommandBuilder) {
	group := &CommandGroup{
		Name:        name,
		Description: description,
		Commands:    builders,
	}
	r.groups[name] = group

	// Tạo parent command cho group
	parentCmd := &cobra.Command{
		Use:   name,
		Short: description,
	}

	// Thêm tất cả commands vào parent
	for _, builder := range builders {
		parentCmd.AddCommand(builder.BuildCommand())
	}

	r.RegisterCommand(parentCmd)
}

// GetCommands trả về tất cả commands đã đăng ký
func (r *Registry) GetCommands() []*cobra.Command {
	return r.commands
}

// GetGroups trả về tất cả command groups
func (r *Registry) GetGroups() map[string]*CommandGroup {
	return r.groups
}
```

### 3. Console Utilities (`internal/pkg/console/output.go`)

```go
package console

import (
	"fmt"
	"os"
	"text/tabwriter"

	"github.com/fatih/color"
	"github.com/spf13/cobra"
)

// OutputFormat định nghĩa format output
type OutputFormat string

const (
	OutputFormatTable OutputFormat = "table"
	OutputFormatJSON  OutputFormat = "json"
	OutputFormatYAML  OutputFormat = "yaml"
)

// Printer interface cho output formatting
type Printer interface {
	Print(data interface{}) error
	Printf(format string, args ...interface{})
	Println(args ...interface{})
	Success(message string)
	Error(message string)
	Warning(message string)
	Info(message string)
}

// ConsolePrinter implementation
type ConsolePrinter struct {
	noColor bool
	verbose bool
}

// NewConsolePrinter tạo console printer mới
func NewConsolePrinter(noColor, verbose bool) *ConsolePrinter {
	return &ConsolePrinter{
		noColor: noColor,
		verbose: verbose,
	}
}

// Success in màu xanh lá
func (p *ConsolePrinter) Success(message string) {
	if p.noColor {
		fmt.Printf("✓ %s\n", message)
	} else {
		color.Green("✓ %s", message)
	}
}

// Error in màu đỏ
func (p *ConsolePrinter) Error(message string) {
	if p.noColor {
		fmt.Printf("✗ %s\n", message)
	} else {
		color.Red("✗ %s", message)
	}
}

// Warning in màu vàng
func (p *ConsolePrinter) Warning(message string) {
	if p.noColor {
		fmt.Printf("⚠ %s\n", message)
	} else {
		color.Yellow("⚠ %s", message)
	}
}

// Info in màu xanh dương
func (p *ConsolePrinter) Info(message string) {
	if p.noColor {
		fmt.Printf("ℹ %s\n", message)
	} else {
		color.Cyan("ℹ %s", message)
	}
}

// Printf formatted output
func (p *ConsolePrinter) Printf(format string, args ...interface{}) {
	fmt.Printf(format, args...)
}

// Println standard output
func (p *ConsolePrinter) Println(args ...interface{}) {
	fmt.Println(args...)
}

// Print interface{} data
func (p *ConsolePrinter) Print(data interface{}) error {
	fmt.Printf("%+v\n", data)
	return nil
}

// Table helper để in bảng
func (p *ConsolePrinter) Table(headers []string, rows [][]string) {
	w := tabwriter.NewWriter(os.Stdout, 0, 0, 2, ' ', 0)
	
	// Print headers
	for i, header := range headers {
		if i > 0 {
			fmt.Fprint(w, "\t")
		}
		if p.noColor {
			fmt.Fprint(w, header)
		} else {
			color.New(color.Bold).Fprint(w, header)
		}
	}
	fmt.Fprintln(w)
	
	// Print separator
	for i := range headers {
		if i > 0 {
			fmt.Fprint(w, "\t")
		}
		fmt.Fprint(w, "---")
	}
	fmt.Fprintln(w)
	
	// Print rows
	for _, row := range rows {
		for i, cell := range row {
			if i > 0 {
				fmt.Fprint(w, "\t")
			}
			fmt.Fprint(w, cell)
		}
		fmt.Fprintln(w)
	}
	
	w.Flush()
}

// GetPrinterFromCmd extract printer từ cobra command
func GetPrinterFromCmd(cmd *cobra.Command) *ConsolePrinter {
	noColor, _ := cmd.Flags().GetBool("no-color")
	verbose, _ := cmd.Flags().GetBool("verbose")
	return NewConsolePrinter(noColor, verbose)
}
```

## Module Console Commands Examples

### 1. Auth Module Console (`modules/core/auth/console/user_cmd.go`)

```go
package console

import (
	"context"
	"strconv"

	"wnapi/internal/pkg/console"
	"wnapi/modules/core/auth/domain"

	"github.com/spf13/cobra"
)

// UserCommandBuilder xây dựng user management commands
type UserCommandBuilder struct {
	service domain.AuthService
}

// NewUserCommandBuilder tạo user command builder mới
func NewUserCommandBuilder(service domain.AuthService) *UserCommandBuilder {
	return &UserCommandBuilder{
		service: service,
	}
}

// BuildCommand tạo user command với sub-commands
func (b *UserCommandBuilder) BuildCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "user",
		Short: "User management commands",
		Long:  "Manage users: create, update, delete, and list operations",
	}

	// Add sub-commands
	cmd.AddCommand(b.createUserCommand())
	cmd.AddCommand(b.listUsersCommand())
	cmd.AddCommand(b.getUserCommand())
	cmd.AddCommand(b.updateUserCommand())
	cmd.AddCommand(b.deleteUserCommand())
	cmd.AddCommand(b.resetPasswordCommand())

	return cmd
}

// createUserCommand tạo user mới
func (b *UserCommandBuilder) createUserCommand() *cobra.Command {
	return &cobra.Command{
		Use:   "create",
		Short: "Create a new user",
		Long:  "Create a new user with email and password",
		Example: `  wnapi auth user create --email <EMAIL> --password secret123 --name "John Doe"
  wnapi auth user create -e <EMAIL> -p admin123 -n "Admin User" --admin`,
		RunE: func(cmd *cobra.Command, args []string) error {
			printer := console.GetPrinterFromCmd(cmd)
			
			email, _ := cmd.Flags().GetString("email")
			password, _ := cmd.Flags().GetString("password")
			name, _ := cmd.Flags().GetString("name")
			isAdmin, _ := cmd.Flags().GetBool("admin")
			tenantID, _ := cmd.Flags().GetUint("tenant-id")

			if email == "" || password == "" {
				printer.Error("Email and password are required")
				return nil
			}

			req := &domain.CreateUserRequest{
				Email:    email,
				Password: password,
				Name:     name,
				IsAdmin:  isAdmin,
				TenantID: tenantID,
			}

			user, err := b.service.CreateUser(context.Background(), req)
			if err != nil {
				printer.Error("Failed to create user: " + err.Error())
				return nil
			}

			printer.Success("User created successfully")
			printer.Printf("ID: %d\n", user.ID)
			printer.Printf("Email: %s\n", user.Email)
			printer.Printf("Name: %s\n", user.Name)
			
			return nil
		},
	}
}

// listUsersCommand liệt kê users
func (b *UserCommandBuilder) listUsersCommand() *cobra.Command {
	return &cobra.Command{
		Use:   "list",
		Short: "List all users",
		Long:  "Display a list of all users with pagination support",
		Example: `  wnapi auth user list
  wnapi auth user list --limit 50
  wnapi auth user list --tenant-id 1`,
		RunE: func(cmd *cobra.Command, args []string) error {
			printer := console.GetPrinterFromCmd(cmd)
			
			limit, _ := cmd.Flags().GetInt("limit")
			offset, _ := cmd.Flags().GetInt("offset")
			tenantID, _ := cmd.Flags().GetUint("tenant-id")

			filter := &domain.UserFilter{
				Limit:    limit,
				Offset:   offset,
				TenantID: tenantID,
			}

			users, total, err := b.service.GetUsers(context.Background(), filter)
			if err != nil {
				printer.Error("Failed to get users: " + err.Error())
				return nil
			}

			if len(users) == 0 {
				printer.Info("No users found")
				return nil
			}

			// Display as table
			headers := []string{"ID", "Email", "Name", "Status", "Created"}
			rows := make([][]string, len(users))
			
			for i, user := range users {
				status := "Active"
				if !user.IsActive {
					status = "Inactive"
				}
				
				rows[i] = []string{
					strconv.Itoa(int(user.ID)),
					user.Email,
					user.Name,
					status,
					user.CreatedAt.Format("2006-01-02 15:04"),
				}
			}

			printer.Table(headers, rows)
			printer.Printf("\nTotal: %d users\n", total)
			
			return nil
		},
	}
}

// resetPasswordCommand reset password cho user
func (b *UserCommandBuilder) resetPasswordCommand() *cobra.Command {
	return &cobra.Command{
		Use:   "reset-password",
		Short: "Reset user password",
		Long:  "Reset password for a specific user",
		Example: `  wnapi auth user reset-password --user-id 123 --password newpassword123
  wnapi auth user reset-password --email <EMAIL> --password newpass`,
		RunE: func(cmd *cobra.Command, args []string) error {
			printer := console.GetPrinterFromCmd(cmd)
			
			userID, _ := cmd.Flags().GetUint("user-id")
			email, _ := cmd.Flags().GetString("email")
			password, _ := cmd.Flags().GetString("password")

			if password == "" {
				printer.Error("Password is required")
				return nil
			}

			if userID == 0 && email == "" {
				printer.Error("Either user-id or email is required")
				return nil
			}

			req := &domain.ResetPasswordRequest{
				UserID:   userID,
				Email:    email,
				Password: password,
			}

			err := b.service.ResetPassword(context.Background(), req)
			if err != nil {
				printer.Error("Failed to reset password: " + err.Error())
				return nil
			}

			printer.Success("Password reset successfully")
			return nil
		},
	}
}

// Thêm flags cho các commands
func init() {
	// Flags sẽ được add trong BuildCommand method của mỗi command
}
```

### 2. Auth Module Registration (`modules/core/auth/module.go`)

```go
package auth

import (
	"context"
	"path/filepath"

	"wnapi/internal/core"
	"wnapi/internal/pkg/console"
	"wnapi/internal/pkg/logger"
	"wnapi/modules/core/auth/api"
	authConsole "wnapi/modules/core/auth/console"
	"wnapi/modules/core/auth/internal"
	"wnapi/modules/core/auth/repository"
	"wnapi/modules/core/auth/service"
)

// Module triển khai auth module với CLI support
type Module struct {
	name    string
	logger  logger.Logger
	config  map[string]interface{}
	app     *core.App
	handler *api.Handler
	service internal.AuthService
}

// NewModule tạo module mới
func NewModule(app *core.App, config map[string]interface{}) (core.Module, error) {
	logger := app.GetLogger()

	// Khởi tạo repository
	repo, err := repository.NewMySQLRepository(app.GetDBManager(), logger)
	if err != nil {
		return nil, err
	}

	// Đọc cấu hình
	authConfig, err := internal.LoadAuthConfig()
	if err != nil {
		logger.Warn("Could not load auth config, using defaults: %v", err)
	}

	// Khởi tạo service và handler
	authService := service.NewService(repo, *authConfig, logger)
	handler := api.NewHandler(authService)

	return &Module{
		name:    "auth",
		logger:  logger,
		config:  config,
		app:     app,
		handler: handler,
		service: authService,
	}, nil
}

// Name trả về tên module
func (m *Module) Name() string {
	return m.name
}

// Init khởi tạo module
func (m *Module) Init(ctx context.Context) error {
	m.logger.Info("Initializing auth module")
	return nil
}

// RegisterRoutes đăng ký HTTP routes
func (m *Module) RegisterRoutes(server *core.Server) error {
	return m.handler.RegisterRoutes(server)
}

// RegisterConsoleCommands đăng ký CLI commands
func (m *Module) RegisterConsoleCommands(registry *console.Registry) error {
	// Đăng ký auth command group
	builders := []console.CommandBuilder{
		authConsole.NewUserCommandBuilder(m.service),
		authConsole.NewTokenCommandBuilder(m.service),
		authConsole.NewPermissionCommandBuilder(m.service),
	}

	registry.RegisterGroup("auth", "Authentication and authorization commands", builders)
	
	m.logger.Info("Auth console commands registered")
	return nil
}

// Cleanup dọn dẹp tài nguyên
func (m *Module) Cleanup(ctx context.Context) error {
	m.logger.Info("Cleaning up auth module")
	return nil
}

// GetMigrationPath trả về đường dẫn migrations
func (m *Module) GetMigrationPath() string {
	return filepath.Join("modules", "core", "auth", "migrations")
}

// GetMigrationOrder trả về thứ tự migration
func (m *Module) GetMigrationOrder() int {
	return 1
}
```

### 3. Global CLI Commands (`cmd/cli/commands/migrate.go`)

```go
package commands

import (
	"context"
	"fmt"

	"wnapi/internal/core"
	"wnapi/internal/pkg/console"

	"github.com/spf13/cobra"
)

// NewMigrateCommand tạo migration command
func NewMigrateCommand(app *core.App) *cobra.Command {
	cmd := &cobra.Command{
		Use:   "migrate",
		Short: "Database migration commands",
		Long:  "Run database migrations for all modules or specific modules",
	}

	// Add sub-commands
	cmd.AddCommand(newMigrateUpCommand(app))
	cmd.AddCommand(newMigrateDownCommand(app))
	cmd.AddCommand(newMigrateStatusCommand(app))
	cmd.AddCommand(newMigrateResetCommand(app))

	return cmd
}

// newMigrateUpCommand chạy migrations
func newMigrateUpCommand(app *core.App) *cobra.Command {
	return &cobra.Command{
		Use:   "up",
		Short: "Run pending migrations",
		Long:  "Execute all pending database migrations",
		Example: `  wnapi migrate up
  wnapi migrate up --module auth
  wnapi migrate up --steps 5`,
		RunE: func(cmd *cobra.Command, args []string) error {
			printer := console.GetPrinterFromCmd(cmd)
			
			module, _ := cmd.Flags().GetString("module")
			steps, _ := cmd.Flags().GetInt("steps")
			dryRun, _ := cmd.Flags().GetBool("dry-run")

			if dryRun {
				printer.Info("Running in dry-run mode")
			}

			printer.Info("Starting database migrations...")

			migrator := app.GetMigrator()
			if migrator == nil {
				printer.Error("Migrator not available")
				return nil
			}

			var err error
			if module != "" {
				err = migrator.MigrateModule(context.Background(), module, steps, dryRun)
			} else {
				err = migrator.MigrateUp(context.Background(), steps, dryRun)
			}

			if err != nil {
				printer.Error("Migration failed: " + err.Error())
				return nil
			}

			printer.Success("Migrations completed successfully")
			return nil
		},
	}
}

// newMigrateStatusCommand hiển thị trạng thái migration
func newMigrateStatusCommand(app *core.App) *cobra.Command {
	return &cobra.Command{
		Use:   "status",
		Short: "Show migration status",
		Long:  "Display the current status of all migrations",
		Example: `  wnapi migrate status
  wnapi migrate status --module auth`,
		RunE: func(cmd *cobra.Command, args []string) error {
			printer := console.GetPrinterFromCmd(cmd)
			
			module, _ := cmd.Flags().GetString("module")

			migrator := app.GetMigrator()
			if migrator == nil {
				printer.Error("Migrator not available")
				return nil
			}

			status, err := migrator.GetStatus(context.Background(), module)
			if err != nil {
				printer.Error("Failed to get migration status: " + err.Error())
				return nil
			}

			if len(status) == 0 {
				printer.Info("No migrations found")
				return nil
			}

			// Display status table
			headers := []string{"Module", "Version", "Name", "Status", "Applied At"}
			rows := make([][]string, len(status))

			for i, s := range status {
				appliedAt := "Not applied"
				if s.AppliedAt != nil {
					appliedAt = s.AppliedAt.Format("2006-01-02 15:04:05")
				}

				rows[i] = []string{
					s.Module,
					fmt.Sprintf("%03d", s.Version),
					s.Name,
					s.Status,
					appliedAt,
				}
			}

			printer.Table(headers, rows)
			return nil
		},
	}
}

func init() {
	// Add common flags
	// Flags will be added in each command function
}
```

## CLI Usage Examples

### Basic Usage
```bash
# Show help
wnapi --help

# Version info
wnapi version

# Database migrations
wnapi migrate up
wnapi migrate status
wnapi migrate up --module auth --steps 1

# User management
wnapi auth user create --email <EMAIL> --password secret123 --name "Admin"
wnapi auth user list --limit 10
wnapi auth user reset-password --user-id 1 --password newpass123

# Tenant management
wnapi tenant create --name "Company ABC" --domain "abc.example.com"
wnapi tenant list --limit 20

# Product management
wnapi product import --file products.csv --tenant-id 1
wnapi product sync --external-api shopify --tenant-id 1

# Blog management
wnapi blog post create --title "New Post" --content "Content here" --author-id 1
wnapi blog import --format wordpress --file export.xml
```

### Advanced Usage
```bash
# Project-specific commands
wnapi --project ecommerce migrate up
wnapi --project blog auth user create --email <EMAIL>

# Verbose output
wnapi --verbose product sync --external-api shopify

# JSON output
wnapi auth user list --output json

# No color output
wnapi --no-color tenant list
```

## Integration với Core Framework

### 1. Core CLI Support (`internal/core/cli.go`)

```go
package core

import (
	"wnapi/internal/pkg/console"
)

// CLI interface for core framework
type CLI interface {
	RegisterConsoleCommands(registry *console.Registry) error
}

// CLIModule interface combines Module and Console capabilities
type CLIModule interface {
	Module
	console.ConsoleModule
}
```

### 2. Auto Registration

Khi khởi tạo CLI, hệ thống sẽ tự động:
1. Scan tất cả modules đã được load
2. Kiểm tra xem module có implement `ConsoleModule` interface không
3. Đăng ký các console commands từ module đó
4. Tổ chức commands theo nhóm (auth, tenant, product, v.v.)

## Input Handling và Validation

### 1. Input Validation (`internal/pkg/console/input.go`)

```go
package console

import (
	"bufio"
	"fmt"
	"os"
	"strconv"
	"strings"
	"syscall"

	"golang.org/x/term"
)

// InputValidator interface for input validation
type InputValidator interface {
	Validate(input string) error
}

// EmailValidator validates email format
type EmailValidator struct{}

func (v EmailValidator) Validate(input string) error {
	if !strings.Contains(input, "@") {
		return fmt.Errorf("invalid email format")
	}
	return nil
}

// RequiredValidator ensures input is not empty
type RequiredValidator struct {
	Message string
}

func (v RequiredValidator) Validate(input string) error {
	if strings.TrimSpace(input) == "" {
		if v.Message != "" {
			return fmt.Errorf(v.Message)
		}
		return fmt.Errorf("this field is required")
	}
	return nil
}

// InputPrompt handles interactive input from user
type InputPrompt struct {
	printer *ConsolePrinter
}

// NewInputPrompt creates new input prompt
func NewInputPrompt(printer *ConsolePrinter) *InputPrompt {
	return &InputPrompt{printer: printer}
}

// Ask prompts user for input with validation
func (p *InputPrompt) Ask(question string, validators ...InputValidator) (string, error) {
	reader := bufio.NewReader(os.Stdin)
	
	for {
		p.printer.Printf("%s: ", question)
		input, err := reader.ReadString('\n')
		if err != nil {
			return "", err
		}
		
		input = strings.TrimSpace(input)
		
		// Validate input
		valid := true
		for _, validator := range validators {
			if err := validator.Validate(input); err != nil {
				p.printer.Error(err.Error())
				valid = false
				break
			}
		}
		
		if valid {
			return input, nil
		}
	}
}

// AskPassword prompts for password without echo
func (p *InputPrompt) AskPassword(question string) (string, error) {
	p.printer.Printf("%s: ", question)
	
	password, err := term.ReadPassword(int(syscall.Stdin))
	if err != nil {
		return "", err
	}
	
	fmt.Println() // New line after password input
	return string(password), nil
}

// Confirm asks for yes/no confirmation
func (p *InputPrompt) Confirm(question string, defaultYes bool) bool {
	reader := bufio.NewReader(os.Stdin)
	
	prompt := question
	if defaultYes {
		prompt += " [Y/n]: "
	} else {
		prompt += " [y/N]: "
	}
	
	p.printer.Printf(prompt)
	input, err := reader.ReadString('\n')
	if err != nil {
		return defaultYes
	}
	
	input = strings.TrimSpace(strings.ToLower(input))
	
	if input == "" {
		return defaultYes
	}
	
	return input == "y" || input == "yes"
}

// Select shows a list of options for selection
func (p *InputPrompt) Select(question string, options []string) (int, string, error) {
	p.printer.Println(question)
	
	for i, option := range options {
		p.printer.Printf("  %d) %s\n", i+1, option)
	}
	
	reader := bufio.NewReader(os.Stdin)
	
	for {
		p.printer.Printf("Enter your choice (1-%d): ", len(options))
		input, err := reader.ReadString('\n')
		if err != nil {
			return 0, "", err
		}
		
		input = strings.TrimSpace(input)
		choice, err := strconv.Atoi(input)
		if err != nil || choice < 1 || choice > len(options) {
			p.printer.Error("Invalid choice. Please try again.")
			continue
		}
		
		return choice - 1, options[choice-1], nil
	}
}
```

### 2. Progress Indicators (`internal/pkg/console/progress.go`)

```go
package console

import (
	"fmt"
	"strings"
	"time"

	"github.com/schollz/progressbar/v3"
)

// ProgressBar wrapper around progressbar library
type ProgressBar struct {
	bar     *progressbar.ProgressBar
	printer *ConsolePrinter
}

// NewProgressBar creates a new progress bar
func NewProgressBar(max int, description string, printer *ConsolePrinter) *ProgressBar {
	bar := progressbar.NewOptions(max,
		progressbar.OptionSetDescription(description),
		progressbar.OptionSetWidth(50),
		progressbar.OptionShowCount(),
		progressbar.OptionShowIts(),
		progressbar.OptionSetTheme(progressbar.Theme{
			Saucer:        "=",
			SaucerHead:    ">",
			SaucerPadding: " ",
			BarStart:      "[",
			BarEnd:        "]",
		}),
	)
	
	return &ProgressBar{
		bar:     bar,
		printer: printer,
	}
}

// Add increments the progress bar
func (p *ProgressBar) Add(num int) error {
	return p.bar.Add(num)
}

// Set sets the current progress
func (p *ProgressBar) Set(num int) error {
	return p.bar.Set(num)
}

// Finish completes the progress bar
func (p *ProgressBar) Finish() error {
	return p.bar.Finish()
}

// Spinner for indefinite progress
type Spinner struct {
	chars   []string
	current int
	message string
	printer *ConsolePrinter
	done    chan bool
}

// NewSpinner creates a new spinner
func NewSpinner(message string, printer *ConsolePrinter) *Spinner {
	return &Spinner{
		chars:   []string{"|", "/", "-", "\\"},
		message: message,
		printer: printer,
		done:    make(chan bool),
	}
}

// Start begins the spinner animation
func (s *Spinner) Start() {
	go func() {
		for {
			select {
			case <-s.done:
				return
			default:
				fmt.Printf("\r%s %s", s.chars[s.current], s.message)
				s.current = (s.current + 1) % len(s.chars)
				time.Sleep(100 * time.Millisecond)
			}
		}
	}()
}

// Stop stops the spinner
func (s *Spinner) Stop() {
	s.done <- true
	fmt.Print("\r" + strings.Repeat(" ", len(s.message)+2) + "\r")
}

// UpdateMessage updates spinner message
func (s *Spinner) UpdateMessage(message string) {
	s.message = message
}
```

## Advanced Console Examples

### 1. Interactive Commands (`modules/core/tenant/console/tenant_cmd.go`)

```go
package console

import (
	"context"
	"strconv"

	"wnapi/internal/pkg/console"
	"wnapi/modules/core/tenant/domain"

	"github.com/spf13/cobra"
)

// TenantCommandBuilder builds tenant management commands
type TenantCommandBuilder struct {
	service domain.TenantService
}

// NewTenantCommandBuilder creates new tenant command builder
func NewTenantCommandBuilder(service domain.TenantService) *TenantCommandBuilder {
	return &TenantCommandBuilder{
		service: service,
	}
}

// BuildCommand creates tenant command with sub-commands
func (b *TenantCommandBuilder) BuildCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "tenant",
		Short: "Tenant management commands",
		Long:  "Create, manage and configure tenants",
	}

	cmd.AddCommand(b.createTenantCommand())
	cmd.AddCommand(b.listTenantsCommand())
	cmd.AddCommand(b.updateTenantCommand())
	cmd.AddCommand(b.deleteTenantCommand())
	cmd.AddCommand(b.configureTenantCommand())

	return cmd
}

// createTenantCommand with interactive mode
func (b *TenantCommandBuilder) createTenantCommand() *cobra.Command {
	return &cobra.Command{
		Use:   "create",
		Short: "Create a new tenant",
		Long:  "Create a new tenant with interactive or flag-based input",
		Example: `  wnapi tenant create --name "Company ABC" --domain "abc.example.com"
  wnapi tenant create --interactive`,
		RunE: func(cmd *cobra.Command, args []string) error {
			printer := console.GetPrinterFromCmd(cmd)
			input := console.NewInputPrompt(printer)
			
			interactive, _ := cmd.Flags().GetBool("interactive")
			
			var name, domain, plan string
			var err error
			
			if interactive {
				// Interactive mode
				printer.Info("Creating new tenant - Interactive mode")
				
				name, err = input.Ask("Tenant name", 
					console.RequiredValidator{Message: "Tenant name is required"})
				if err != nil {
					return err
				}
				
				domain, err = input.Ask("Domain (optional)", 
					console.EmailValidator{}) // Basic domain validation
				if err != nil {
					return err
				}
				
				// Select plan
				plans := []string{"free", "basic", "premium", "enterprise"}
				_, plan, err = input.Select("Select tenant plan", plans)
				if err != nil {
					return err
				}
				
			} else {
				// Flag-based mode
				name, _ = cmd.Flags().GetString("name")
				domain, _ = cmd.Flags().GetString("domain")
				plan, _ = cmd.Flags().GetString("plan")
				
				if name == "" {
					printer.Error("Tenant name is required")
					return nil
				}
			}
			
			// Create tenant
			req := &domain.CreateTenantRequest{
				Name:   name,
				Domain: domain,
				Plan:   plan,
			}
			
			spinner := console.NewSpinner("Creating tenant...", printer)
			spinner.Start()
			
			tenant, err := b.service.CreateTenant(context.Background(), req)
			
			spinner.Stop()
			
			if err != nil {
				printer.Error("Failed to create tenant: " + err.Error())
				return nil
			}
			
			printer.Success("Tenant created successfully")
			printer.Printf("ID: %d\n", tenant.ID)
			printer.Printf("Name: %s\n", tenant.Name)
			printer.Printf("Domain: %s\n", tenant.Domain)
			printer.Printf("Plan: %s\n", tenant.Plan)
			
			return nil
		},
	}
}

// configureTenantCommand for tenant configuration
func (b *TenantCommandBuilder) configureTenantCommand() *cobra.Command {
	return &cobra.Command{
		Use:   "configure",
		Short: "Configure tenant settings",
		Long:  "Interactive configuration of tenant settings",
		Args:  cobra.ExactArgs(1),
		Example: `  wnapi tenant configure 123
  wnapi tenant configure abc-tenant-slug`,
		RunE: func(cmd *cobra.Command, args []string) error {
			printer := console.GetPrinterFromCmd(cmd)
			input := console.NewInputPrompt(printer)
			
			tenantIdentifier := args[0]
			
			// Get tenant
			tenant, err := b.service.GetTenantByIdentifier(context.Background(), tenantIdentifier)
			if err != nil {
				printer.Error("Tenant not found: " + err.Error())
				return nil
			}
			
			printer.Info("Configuring tenant: " + tenant.Name)
			
			// Configuration options
			options := []string{
				"Update basic info",
				"Configure modules",
				"Set limits",
				"Manage API keys",
				"View current settings",
			}
			
			for {
				_, choice, err := input.Select("What would you like to configure?", options)
				if err != nil {
					break
				}
				
				switch choice {
				case "Update basic info":
					err = b.configureBasicInfo(tenant, input, printer)
				case "Configure modules":
					err = b.configureModules(tenant, input, printer)
				case "Set limits":
					err = b.configureLimits(tenant, input, printer)
				case "Manage API keys":
					err = b.configureAPIKeys(tenant, input, printer)
				case "View current settings":
					err = b.showCurrentSettings(tenant, printer)
				}
				
				if err != nil {
					printer.Error("Configuration error: " + err.Error())
				}
				
				if !input.Confirm("Continue configuring?", false) {
					break
				}
			}
			
			printer.Success("Configuration completed")
			return nil
		},
	}
}

// Helper methods for configuration
func (b *TenantCommandBuilder) configureBasicInfo(tenant *domain.Tenant, input *console.InputPrompt, printer *console.ConsolePrinter) error {
	printer.Info("Current tenant info:")
	printer.Printf("Name: %s\n", tenant.Name)
	printer.Printf("Domain: %s\n", tenant.Domain)
	
	newName, err := input.Ask("New name (leave empty to keep current)")
	if err != nil {
		return err
	}
	
	newDomain, err := input.Ask("New domain (leave empty to keep current)")
	if err != nil {
		return err
	}
	
	req := &domain.UpdateTenantRequest{
		ID: tenant.ID,
	}
	
	if newName != "" {
		req.Name = &newName
	}
	if newDomain != "" {
		req.Domain = &newDomain
	}
	
	_, err = b.service.UpdateTenant(context.Background(), req)
	if err != nil {
		return err
	}
	
	printer.Success("Basic info updated")
	return nil
}

func (b *TenantCommandBuilder) configureModules(tenant *domain.Tenant, input *console.InputPrompt, printer *console.ConsolePrinter) error {
	// Get available modules
	availableModules := []string{"auth", "blog", "ecommerce", "analytics"}
	
	printer.Info("Available modules:")
	for _, module := range availableModules {
		enabled := "❌"
		if b.isModuleEnabled(tenant, module) {
			enabled = "✅"
		}
		printer.Printf("  %s %s\n", enabled, module)
	}
	
	_, selectedModule, err := input.Select("Select module to toggle", availableModules)
	if err != nil {
		return err
	}
	
	currentStatus := b.isModuleEnabled(tenant, selectedModule)
	action := "enable"
	if currentStatus {
		action = "disable"
	}
	
	if input.Confirm("Do you want to "+action+" "+selectedModule+"?", true) {
		err = b.service.ToggleModule(context.Background(), tenant.ID, selectedModule, !currentStatus)
		if err != nil {
			return err
		}
		printer.Success("Module " + selectedModule + " " + action + "d")
	}
	
	return nil
}

func (b *TenantCommandBuilder) isModuleEnabled(tenant *domain.Tenant, module string) bool {
	// Implementation to check if module is enabled
	// This would check tenant settings or database
	return true // Placeholder
}
```

### 2. Batch Operations (`modules/content/blog/console/import_cmd.go`)

```go
package console

import (
	"context"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"os"
	"strconv"

	"wnapi/internal/pkg/console"
	"wnapi/modules/content/blog/domain"

	"github.com/spf13/cobra"
)

// ImportCommandBuilder builds import commands
type ImportCommandBuilder struct {
	service domain.BlogService
}

// NewImportCommandBuilder creates new import command builder
func NewImportCommandBuilder(service domain.BlogService) *ImportCommandBuilder {
	return &ImportCommandBuilder{
		service: service,
	}
}

// BuildCommand creates import command
func (b *ImportCommandBuilder) BuildCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "import",
		Short: "Import blog content from various sources",
		Long:  "Import posts, categories, and other blog content from files or external sources",
	}

	cmd.AddCommand(b.importPostsCommand())
	cmd.AddCommand(b.importCategoriesCommand())
	cmd.AddCommand(b.importFromWordPressCommand())

	return cmd
}

// importPostsCommand imports posts from CSV/JSON
func (b *ImportCommandBuilder) importPostsCommand() *cobra.Command {
	return &cobra.Command{
		Use:   "posts",
		Short: "Import posts from file",
		Long:  "Import blog posts from CSV or JSON file",
		Example: `  wnapi blog import posts --file posts.csv --tenant-id 1
  wnapi blog import posts --file posts.json --format json --dry-run`,
		RunE: func(cmd *cobra.Command, args []string) error {
			printer := console.GetPrinterFromCmd(cmd)
			
			file, _ := cmd.Flags().GetString("file")
			format, _ := cmd.Flags().GetString("format")
			tenantID, _ := cmd.Flags().GetUint("tenant-id")
			dryRun, _ := cmd.Flags().GetBool("dry-run")
			batchSize, _ := cmd.Flags().GetInt("batch-size")
			
			if file == "" {
				printer.Error("File path is required")
				return nil
			}
			
			if tenantID == 0 {
				printer.Error("Tenant ID is required")
				return nil
			}
			
			// Check file exists
			if _, err := os.Stat(file); os.IsNotExist(err) {
				printer.Error("File not found: " + file)
				return nil
			}
			
			printer.Info("Starting import process...")
			printer.Printf("File: %s\n", file)
			printer.Printf("Format: %s\n", format)
			printer.Printf("Tenant ID: %d\n", tenantID)
			printer.Printf("Batch size: %d\n", batchSize)
			
			if dryRun {
				printer.Warning("DRY RUN MODE - No data will be saved")
			}
			
			var posts []*domain.ImportPost
			var err error
			
			// Parse file based on format
			switch format {
			case "csv":
				posts, err = b.parseCSVFile(file)
			case "json":
				posts, err = b.parseJSONFile(file)
			default:
				printer.Error("Unsupported format: " + format)
				return nil
			}
			
			if err != nil {
				printer.Error("Failed to parse file: " + err.Error())
				return nil
			}
			
			if len(posts) == 0 {
				printer.Warning("No posts found in file")
				return nil
			}
			
			printer.Info(fmt.Sprintf("Found %d posts to import", len(posts)))
			
			// Confirm import
			input := console.NewInputPrompt(printer)
			if !dryRun && !input.Confirm("Continue with import?", true) {
				printer.Info("Import cancelled")
				return nil
			}
			
			// Progress bar
			progress := console.NewProgressBar(len(posts), "Importing posts", printer)
			
			// Import in batches
			imported := 0
			failed := 0
			
			for i := 0; i < len(posts); i += batchSize {
				end := i + batchSize
				if end > len(posts) {
					end = len(posts)
				}
				
				batch := posts[i:end]
				
				if dryRun {
					// Dry run - just validate
					for _, post := range batch {
						if err := b.validatePost(post); err != nil {
							printer.Error(fmt.Sprintf("Validation error for post '%s': %s", post.Title, err.Error()))
							failed++
						} else {
							imported++
						}
						progress.Add(1)
					}
				} else {
					// Actual import
					result, err := b.service.ImportPosts(context.Background(), tenantID, batch)
					if err != nil {
						printer.Error("Batch import failed: " + err.Error())
						failed += len(batch)
					} else {
						imported += result.Imported
						failed += result.Failed
					}
					progress.Add(len(batch))
				}
			}
			
			progress.Finish()
			
			// Summary
			printer.Success("Import completed")
			printer.Printf("Successfully imported: %d posts\n", imported)
			if failed > 0 {
				printer.Warning(fmt.Sprintf("Failed to import: %d posts", failed))
			}
			
			return nil
		},
	}
}

// parseCSVFile parses CSV file and returns posts
func (b *ImportCommandBuilder) parseCSVFile(filename string) ([]*domain.ImportPost, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()
	
	reader := csv.NewReader(file)
	records, err := reader.ReadAll()
	if err != nil {
		return nil, err
	}
	
	if len(records) == 0 {
		return nil, fmt.Errorf("empty CSV file")
	}
	
	// Assume first row is header
	headers := records[0]
	posts := make([]*domain.ImportPost, 0, len(records)-1)
	
	for i, record := range records[1:] {
		if len(record) != len(headers) {
			return nil, fmt.Errorf("row %d has different number of columns than header", i+2)
		}
		
		post := &domain.ImportPost{}
		
		// Map CSV columns to post fields
		for j, value := range record {
			switch headers[j] {
			case "title":
				post.Title = value
			case "content":
				post.Content = value
			case "excerpt":
				post.Excerpt = value
			case "author_id":
				if authorID, err := strconv.Atoi(value); err == nil {
					post.AuthorID = uint(authorID)
				}
			case "category":
				post.Category = value
			case "tags":
				post.Tags = value
			case "status":
				post.Status = value
			case "image":
				post.FeaturedImage = value
			}
		}
		
		posts = append(posts, post)
	}
	
	return posts, nil
}

// parseJSONFile parses JSON file and returns posts
func (b *ImportCommandBuilder) parseJSONFile(filename string) ([]*domain.ImportPost, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()
	
	var posts []*domain.ImportPost
	decoder := json.NewDecoder(file)
	
	if err := decoder.Decode(&posts); err != nil {
		return nil, err
	}
	
	return posts, nil
}

// validatePost validates post data
func (b *ImportCommandBuilder) validatePost(post *domain.ImportPost) error {
	if post.Title == "" {
		return fmt.Errorf("title is required")
	}
	
	if post.Content == "" {
		return fmt.Errorf("content is required")
	}
	
	// Add more validation rules as needed
	
	return nil
}
```

## Configuration và Environment

### 1. CLI Configuration (`internal/pkg/console/config.go`)

```go
package console

import (
	"os"
	"path/filepath"

	"github.com/spf13/viper"
)

// CLIConfig holds CLI-specific configuration
type CLIConfig struct {
	DefaultProject string            `mapstructure:"default_project"`
	OutputFormat   string            `mapstructure:"output_format"`
	NoColor        bool              `mapstructure:"no_color"`
	Verbose        bool              `mapstructure:"verbose"`
	BatchSize      int               `mapstructure:"batch_size"`
	Aliases        map[string]string `mapstructure:"aliases"`
}

// LoadCLIConfig loads CLI configuration from various sources
func LoadCLIConfig() (*CLIConfig, error) {
	v := viper.New()
	
	// Set defaults
	v.SetDefault("output_format", "table")
	v.SetDefault("no_color", false)
	v.SetDefault("verbose", false)
	v.SetDefault("batch_size", 100)
	
	// Configuration file paths
	v.SetConfigName("wnapi-cli")
	v.SetConfigType("yaml")
	
	// Add configuration paths
	v.AddConfigPath(".")
	v.AddConfigPath("$HOME/.wnapi")
	v.AddConfigPath("/etc/wnapi")
	
	// Environment variables
	v.SetEnvPrefix("WNAPI_CLI")
	v.AutomaticEnv()
	
	// Try to read config file
	if err := v.ReadInConfig(); err != nil {
		// Config file not found is not an error
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return nil, err
		}
	}
	
	var config CLIConfig
	if err := v.Unmarshal(&config); err != nil {
		return nil, err
	}
	
	return &config, nil
}

// SaveCLIConfig saves CLI configuration to file
func SaveCLIConfig(config *CLIConfig) error {
	v := viper.New()
	
	// Set all values
	v.Set("default_project", config.DefaultProject)
	v.Set("output_format", config.OutputFormat)
	v.Set("no_color", config.NoColor)
	v.Set("verbose", config.Verbose)
	v.Set("batch_size", config.BatchSize)
	v.Set("aliases", config.Aliases)
	
	// Ensure config directory exists
	configDir := filepath.Join(os.Getenv("HOME"), ".wnapi")
	if err := os.MkdirAll(configDir, 0755); err != nil {
		return err
	}
	
	// Write config file
	configFile := filepath.Join(configDir, "wnapi-cli.yaml")
	return v.WriteConfigAs(configFile)
}
```

### 2. CLI Configuration Commands (`cmd/cli/commands/config.go`)

```go
package commands

import (
	"fmt"

	"wnapi/internal/pkg/console"

	"github.com/spf13/cobra"
)

// NewConfigCommand creates configuration management command
func NewConfigCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "config",
		Short: "CLI configuration management",
		Long:  "Manage CLI configuration settings",
	}

	cmd.AddCommand(newConfigGetCommand())
	cmd.AddCommand(newConfigSetCommand())
	cmd.AddCommand(newConfigListCommand())
	cmd.AddCommand(newConfigResetCommand())

	return cmd
}

// newConfigGetCommand gets configuration value
func newConfigGetCommand() *cobra.Command {
	return &cobra.Command{
		Use:   "get [key]",
		Short: "Get configuration value",
		Long:  "Get the value of a configuration key",
		Args:  cobra.ExactArgs(1),
		Example: `  wnapi config get default_project
  wnapi config get output_format`,
		RunE: func(cmd *cobra.Command, args []string) error {
			printer := console.GetPrinterFromCmd(cmd)
			
			config, err := console.LoadCLIConfig()
			if err != nil {
				printer.Error("Failed to load config: " + err.Error())
				return nil
			}
			
			key := args[0]
			
			switch key {
			case "default_project":
				printer.Printf("%s\n", config.DefaultProject)
			case "output_format":
				printer.Printf("%s\n", config.OutputFormat)
			case "no_color":
				printer.Printf("%t\n", config.NoColor)
			case "verbose":
				printer.Printf("%t\n", config.Verbose)
			case "batch_size":
				printer.Printf("%d\n", config.BatchSize)
			default:
				printer.Error("Unknown configuration key: " + key)
			}
			
			return nil
		},
	}
}

// newConfigSetCommand sets configuration value
func newConfigSetCommand() *cobra.Command {
	return &cobra.Command{
		Use:   "set [key] [value]",
		Short: "Set configuration value",
		Long:  "Set the value of a configuration key",
		Args:  cobra.ExactArgs(2),
		Example: `  wnapi config set default_project ecommerce
  wnapi config set output_format json
  wnapi config set batch_size 50`,
		RunE: func(cmd *cobra.Command, args []string) error {
			printer := console.GetPrinterFromCmd(cmd)
			
			config, err := console.LoadCLIConfig()
			if err != nil {
				printer.Error("Failed to load config: " + err.Error())
				return nil
			}
			
			key := args[0]
			value := args[1]
			
			switch key {
			case "default_project":
				config.DefaultProject = value
			case "output_format":
				if value != "table" && value != "json" && value != "yaml" {
					printer.Error("Invalid output format. Use: table, json, or yaml")
					return nil
				}
				config.OutputFormat = value
			case "no_color":
				config.NoColor = value == "true"
			case "verbose":
				config.Verbose = value == "true"
			case "batch_size":
				// Parse and validate batch size
				// Implementation here...
			default:
				printer.Error("Unknown configuration key: " + key)
				return nil
			}
			
			if err := console.SaveCLIConfig(config); err != nil {
				printer.Error("Failed to save config: " + err.Error())
				return nil
			}
			
			printer.Success(fmt.Sprintf("Configuration updated: %s = %s", key, value))
			return nil
		},
	}
}
```

## Testing CLI Commands

### 1. CLI Testing Utilities (`