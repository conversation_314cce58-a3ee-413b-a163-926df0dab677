# Tracing Documentation

## Overview

This document explains how to use the tracing functionality provided by the `internal/pkg/tracing` package within your modules. Proper tracing enables better observability, performance monitoring, and debugging capabilities across the application.

## Tracing Package Structure

The `internal/pkg/tracing` package provides:

- Middleware for HTTP request tracing
- Helper functions for span creation and management
- Context propagation utilities
- Integration with external tracing systems

## How to Implement Tracing in Your Module

### 1. Import Required Packages

```go
import (
    "context"
    
    "go.opentelemetry.io/otel"
    "go.opentelemetry.io/otel/attribute"
    "go.opentelemetry.io/otel/trace"
    
    "github.com/your-org/wnapi2/internal/pkg/tracing"
)
```

### 2. Add Tracing to Your Handler Functions

Example from the auth module:

```go
func (h *Handler) Login(ctx context.Context, req *LoginRequest) (*LoginResponse, error) {
    ctx, span := tracing.StartSpan(ctx, "auth.Login")
    defer span.End()
    
    // Add relevant attributes to the span
    span.SetAttributes(
        attribute.String("user_id", req.UserID),
        attribute.String("auth_method", req.Method),
    )
    
    // If an error occurs, record it in the span
    if err := validateCredentials(ctx, req); err != nil {
        tracing.RecordError(span, err, "credential validation failed")
        return nil, err
    }
    
    // Continue with the login process
    // ...
    
    return response, nil
}
```

### 3. Create Child Spans for Subtasks

For operations that involve multiple steps or external service calls:

```go
func (s *Service) RefreshToken(ctx context.Context, token string) (string, error) {
    ctx, span := tracing.StartSpan(ctx, "auth.RefreshToken")
    defer span.End()
    
    // Create a child span for token validation
    validateCtx, validateSpan := tracing.StartSpan(ctx, "auth.ValidateToken")
    valid, err := s.validateToken(validateCtx, token)
    validateSpan.End()
    
    if err != nil {
        tracing.RecordError(span, err, "token validation failed")
        return "", err
    }
    
    // Create another child span for token generation
    generateCtx, generateSpan := tracing.StartSpan(ctx, "auth.GenerateNewToken")
    newToken, err := s.generateToken(generateCtx, valid.UserID)
    generateSpan.End()
    
    if err != nil {
        tracing.RecordError(span, err, "token generation failed")
        return "", err
    }
    
    return newToken, nil
}
```

### 4. Add Tracing Middleware to HTTP Routes

If your module exposes HTTP endpoints:

```go
func RegisterRoutes(router *gin.Engine) {
    authGroup := router.Group("/auth")
    
    // Apply tracing middleware
    authGroup.Use(tracing.Middleware("auth-service"))
    
    // Register your handlers
    authGroup.POST("/login", LoginHandler)
    authGroup.POST("/refresh", RefreshHandler)
    // ...
}
```

## Best Practices

1. **Use Descriptive Span Names**: Name spans according to the operation they represent (e.g., `auth.VerifyPassword`, `auth.GenerateJWT`).

2. **Add Relevant Attributes**: Include attributes that would be useful for filtering and analyzing traces.

3. **Record Errors Properly**: Use `tracing.RecordError()` to mark spans as failed and include error details.

4. **Propagate Context**: Always pass the context with trace information to downstream functions and services.

5. **Create Child Spans for Expensive Operations**: Any database query, external API call, or computationally expensive operation should have its own span.

6. **Clean Up Resources**: Always call `span.End()` when the operation is complete, typically using `defer`.

## Integration with Other Systems

The tracing package integrates with:

- Logging system: Trace IDs are automatically added to log entries
- Metrics: Key spans automatically generate metrics
- Error reporting: Errors recorded in spans can be automatically reported

## Example: Tracing Database Operations

```go
func (r *Repository) GetUserByID(ctx context.Context, userID string) (*User, error) {
    ctx, span := tracing.StartSpan(ctx, "auth.repository.GetUserByID")
    defer span.End()
    
    span.SetAttributes(attribute.String("user_id", userID))
    
    // Execute database query
    user, err := r.db.QueryContext(ctx, "SELECT * FROM users WHERE id = ?", userID)
    if err != nil {
        tracing.RecordError(span, err, "database query failed")
        return nil, err
    }
    
    return user, nil
}
```

## Viewing Traces

Traces can be viewed in:

1. The local development UI at `http://localhost:16686` (if using Jaeger)
2. The production observability platform
3. Through the CLI using the tracing tools

## Troubleshooting

If traces aren't appearing:

1. Verify that tracing is enabled in your environment
2. Check that the context is properly propagated
3. Ensure spans are being ended properly
4. Confirm the tracer provider is correctly configured

## References

- [OpenTelemetry Documentation](https://opentelemetry.io/docs/)
- [Jaeger Documentation](https://www.jaegertracing.io/docs/)
- Internal Architecture Guide
