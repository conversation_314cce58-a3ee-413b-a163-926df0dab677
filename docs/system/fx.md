# FX Framework - Hướng dẫn Module Development

## Tổng quan
FX Framework là dependency injection container được sử dụng trong hệ thống để quản lý lifecycle và dependencies của các modules. Tài liệu này hướng dẫn cách phát triển modules tuân thủ chuẩn FX.

## Cấu trúc Module

### 1. Interface Module

Mỗi module phải implement interface `Module`:

```go
type Module interface {
    Name() string
    Priority() int
    Dependencies() []string
    Providers() []interface{}
    Invokes() []interface{}
}
```

### 2. C<PERSON>u trúc thư mục chuẩn

```text
FX Framework là một dependency injection container mạnh mẽ (`uber-go/fx`) được sử dụng trong hệ thống để quản lý vòng đời (lifecycle) và các phụ thuộc (dependencies) của các module. Tài liệu này hướng dẫn cách phát triển các module tuân thủ chuẩn FX, đảm bảo t<PERSON>h nhất quán, kh<PERSON> năng mở rộng và dễ bảo trì.

## Cấu trúc Module Chuẩn

Mỗi module trong hệ thống hiện được định nghĩa như một `fx.Module`, tuân thủ các nguyên tắc của `uber-go/fx` để quản lý dependencies và lifecycle.

```text
modules/
├── {module_name}/
│   ├── fx.go                 # Module definition
│   ├── providers.go          # Provider functions
│   ├── internal/             # Internal types
│   ├── repository/           # Data access layer
│   ├── service/              # Business logic layer
│   ├── api/                  # HTTP handlers
│   └── dto/                  # Data transfer objects
```

## Quy tắc Dependencies

### 1. Thứ tự ưu tiên (Priority)

```go
// Thứ tự loading modules theo priority (số nhỏ load trước)
const (
    TenantModulePriority      = 10  // Core tenant functionality
    NotificationModulePriority = 20  // Infrastructure services
    AuthModulePriority        = 30  // Business modules
    RBACModulePriority        = 40  // Permission system
    MediaModulePriority       = 50  // Feature modules
)
```

### 2. Dependency Rules

**QUAN TRỌNG**: Tránh circular dependencies!

✅ **Đúng**:
```go
// notification module
func (m *NotificationModule) Dependencies() []string {
    return []string{"tenant"}  // Chỉ phụ thuộc vào tenant
}

// auth module
func (m *MyModule) Dependencies() []string {
    return []string{"tenant", "notification"}  // Phụ thuộc vào notification
}
```

❌ **Sai**:
```go
// notification module
func (m *NotificationModule) Dependencies() []string {
    return []string{"tenant", "auth"}  // Circular dependency!
}

// auth module
func (m *MyModule) Dependencies() []string {
    return []string{"tenant", "notification"}  // Circular dependency!
}
```

### 3. Dependency Graph

```text
tenant (priority: 10)
  ↓
notification (priority: 20)
  ↓
auth (priority: 30)
  ↓
rbac (priority: 40)
  ↓
media (priority: 50)
```

## Template Module

### fx.go

```go
package mymodule

import (
    "go.uber.org/fx"
    "wnapi/internal/fx/module"
)

type MyModule struct{}

func NewMyModule() module.Module {
    return &MyModule{}
}

// Name trả về tên module
func (m *MyModule) Name() string {
    return "mymodule"
}

// Priority trả về thứ tự loading (số nhỏ load trước)
func (m *MyModule) Priority() int {
    return 50 // Adjust based on dependencies
}

// Dependencies trả về danh sách modules phụ thuộc
func (m *MyModule) Dependencies() []string {
    return []string{"tenant", "notification"} // List required modules
}

// Providers trả về danh sách provider functions
func (m *MyModule) Providers() []interface{} {
    return []interface{}{
        // Config
        NewMyModuleConfig,

        // Repositories
        fx.Annotate(
            repository.NewMyRepository,
            fx.As(new(repository.MyRepositoryInterface)),
        ),

        // Services
        fx.Annotate(
            NewMyService,
            fx.As(new(service.MyServiceInterface)),
        ),

        // Handlers
        NewMyHandler,
    }
}

// Invokes trả về danh sách invoke functions
func (m *MyModule) Invokes() []interface{} {
    return []interface{}{
        RegisterMyRoutes,
    }
}
```

### providers.go

```go
package mymodule

import (
    "wnapi/modules/mymodule/internal"
    "wnapi/modules/mymodule/service"
    "wnapi/modules/mymodule/repository"
    "wnapi/modules/mymodule/api"
)

// NewMyModuleConfig tạo config cho module
func NewMyModuleConfig(cfg config.Config) *internal.MyModuleConfig {
    return &internal.MyModuleConfig{
        // Initialize from config
    }
}

// NewMyService tạo service với dependencies
func NewMyService(
    repo repository.MyRepositoryInterface,
    logger logger.Logger,
    // Other dependencies...
) service.MyServiceInterface {
    return service.NewMyService(repo, logger)
}

// NewMyHandler tạo HTTP handler
func NewMyHandler(
    svc service.MyServiceInterface,
    logger logger.Logger,
) *api.Handler {
    return api.NewHandler(svc, logger)
}

// RegisterMyRoutes đăng ký routes
func RegisterMyRoutes(
    engine *gin.Engine,
    handler *api.Handler,
) {
    handler.RegisterRoutes(engine)
}
```

## Best Practices

### 1. Naming Conventions

```go
// ✅ Đúng - Tên provider rõ ràng
func NewUserService() service.UserService { }
func NewUserRepository() repository.UserRepository { }
func NewUserHandler() *api.UserHandler { }

// ❌ Sai - Tên không rõ ràng
func NewService() interface{} { }
func NewRepo() interface{} { }
```

### 2. Interface Usage

```go
// ✅ Đúng - Sử dụng interface cho loose coupling
fx.Annotate(
    repository.NewUserRepository,
    fx.As(new(repository.UserRepositoryInterface)),
)

// ❌ Sai - Sử dụng concrete type
repository.NewUserRepository, // Không có interface
```

### 3. Error Handling

```go
// ✅ Đúng - Provider có error handling
func NewMyService(repo repository.MyRepo) (service.MyService, error) {
    if repo == nil {
        return nil, fmt.Errorf("repository is required")
    }
    return service.NewMyService(repo), nil
}

// ❌ Sai - Không có error handling
func NewMyService(repo repository.MyRepo) service.MyService {
    return service.NewMyService(repo) // Có thể panic nếu repo nil
}
```

## Troubleshooting

### 1. Circular Dependency Error

**Lỗi**: `cycle detected in dependency graph`

**Nguyên nhân**: Module A phụ thuộc vào Module B, và Module B cũng phụ thuộc vào Module A.

**Giải pháp**:

1. Xem xét lại dependency graph
2. Tách shared functionality ra module riêng
3. Sử dụng event system thay vì direct dependency

### 2. Provider Not Found Error

**Lỗi**: `missing type: *service.MyService`

**Nguyên nhân**: Provider function không được đăng ký hoặc return type không đúng.

**Giải pháp**:

1. Kiểm tra provider function có trong `Providers()` không
2. Kiểm tra return type của provider function
3. Kiểm tra import statements

### 3. Module Loading Order

**Lỗi**: Service không available khi module khởi tạo

**Nguyên nhân**: Module dependency load sau module hiện tại.

**Giải pháp**:

1. Kiểm tra `Priority()` của các modules
2. Kiểm tra `Dependencies()` list
3. Đảm bảo dependency modules có priority thấp hơn

## Examples

### Module với Database Repository

```go
// Providers
func (m *MyModule) Providers() []interface{} {
    return []interface{}{
        // Repository với GORM
        fx.Annotate(
            mysql.NewMyRepository,
            fx.As(new(repository.MyRepositoryInterface)),
        ),

        // Service với repository dependency
        fx.Annotate(
            NewMyService,
            fx.As(new(service.MyServiceInterface)),
        ),
    }
}

// Provider function
func NewMyService(
    repo repository.MyRepositoryInterface,
    db *gorm.DB,
    logger logger.Logger,
) service.MyServiceInterface {
    return service.NewMyService(repo, db, logger)
}
```

### Module với External Service

```go
// Module phụ thuộc vào notification service
func (m *MyModule) Dependencies() []string {
    return []string{"tenant", "notification"}
}

// Provider với external service dependency
func NewMyService(
    repo repository.MyRepositoryInterface,
    emailService *notificationService.EmailService,
    logger logger.Logger,
) service.MyServiceInterface {
    return service.NewMyService(repo, emailService, logger)
}
```

## Checklist

Khi tạo module mới, kiểm tra:

- [ ] Module implement đúng interface `Module`
- [ ] Priority được set phù hợp với dependencies
- [ ] Dependencies list không tạo circular dependency
- [ ] Tất cả providers có return type rõ ràng
- [ ] Sử dụng `fx.Annotate` cho interfaces
- [ ] Provider functions có error handling
- [ ] Routes được đăng ký trong `Invokes()`
- [ ] Module được register trong main application
- [ ] Test module loading thành công

## Tham khảo

- FX Documentation
- Dependency Injection Patterns
- Module Examples
FX Framework là một dependency injection container mạnh mẽ (`uber-go/fx`) được sử dụng trong hệ thống để quản lý vòng đời (lifecycle) và các phụ thuộc (dependencies) của các module. Tài liệu này hướng dẫn cách phát triển các module tuân thủ chuẩn FX, đảm bảo tính nhất quán, khả năng mở rộng và dễ bảo trì.

## Cấu trúc Module Chuẩn

Mỗi module trong hệ thống hiện được định nghĩa như một `fx.Module`, tuân thủ các nguyên tắc của `uber-go/fx` để quản lý dependencies và lifecycle.

```text
modules/
├── {module_name}/
│   ├── fx.go                        # FX module definition và registration
│   ├── providers.go                 # FX providers (constructors)
│   ├── routes.go                    # FX-compatible route registration
│   ├── internal/                    # Định nghĩa cấu hình và các kiểu dữ liệu nội bộ
│   │   ├── config.go                # Cấu hình module
│   │   └── types.go                 # Định nghĩa types, interfaces và xử lý lỗi
│   ├── dto/                         # Data Transfer Objects
│   ├── repository/                  # Repository implementations
│   │   ├── interfaces.go            # Repository interfaces
│   │   └── mysql/                   # MySQL implementations (hoặc các DB khác)
│   │       └── my_repository.go     # Ví dụ: MySQL repository
│   ├── service/                     # Service implementations
│   │   ├── interfaces.go            # Service interfaces
│   │   └── my_service.go            # Ví dụ: Service implementation
│   ├── api/                         # API handlers
│   │   ├── handler.go               # Main API handler
│   │   └── handlers/                # Specific handlers
│   │       └── my_handler.go        # Ví dụ: Specific handler
│   ├── migrations/                  # SQL migrations
│   │   ├── 001_create_tables.up.sql # Migration tạo bảng
│   │   └── 001_create_tables.down.sql # Migration rollback
│   ├── queue/                       # Queue handlers (optional)
│   │   └── handlers.go              # Queue task handlers
│   └── events/                      # Event handlers (optional)
│       └── handlers.go              # Event handlers
```

## FX System - Dependency Injection

Hệ thống hiện tại sử dụng **Uber FX** để quản lý dependency injection và module lifecycle. FX cung cấp:

-   `fx.go`                        # FX module definition và registration
-   `providers.go`                 # FX providers (constructors)
-   `routes.go`                    # FX-compatible route registration
-   `internal/`                    # Định nghĩa cấu hình và các kiểu dữ liệu nội bộ
    -   `config.go`                # Cấu hình module
    -   `types.go`                 # Định nghĩa types, interfaces và xử lý lỗi
-   `dto/`                         # Data Transfer Objects
-   `repository/`                  # Repository implementations
    -   `interfaces.go`            # Repository interfaces
    -   `mysql/`                   # MySQL implementations (hoặc các DB khác)
        -   `my_repository.go`     # Ví dụ: MySQL repository
-   `service/`                     # Service implementations
    -   `interfaces.go`            # Service interfaces
    -   `my_service.go`            # Ví dụ: Service implementation
-   `api/`                         # API handlers
    -   `handler.go`               # Main API handler
    -   `handlers/`                # Specific handlers
        -   `my_handler.go`        # Ví dụ: Specific handler
-   `migrations/`                  # SQL migrations
    -   `001_create_tables.up.sql` # Migration tạo bảng
    -   `001_create_tables.down.sql` # Migration rollback
-   `queue/`                       # Queue handlers (optional)
    -   `handlers.go`              # Queue task handlers
-   `events/`                      # Event handlers (optional)
    -   `handlers.go`              # Event handlers
```

## FX System - Dependency Injection

Hệ thống hiện tại sử dụng **Uber FX** để quản lý dependency injection và module lifecycle. FX cung cấp:

-   **Dependency Injection**: Tự động resolve dependencies
-   **Lifecycle Management**: Quản lý startup/shutdown
-   **Module System**: Tổ chức code theo modules
-   **Type Safety**: Compile-time dependency checking

### Cách FX hoạt động

1.  **Providers (`fx.Provide`)**: Các hàm constructor tạo ra các dependencies (services, repositories, configurations, v.v.).
2.  **Invokes (`fx.Invoke`)**: Các hàm sẽ được gọi sau khi tất cả các dependencies cần thiết đã được resolved. Thường dùng để đăng ký routes, khởi tạo các background process, hoặc chạy các tác vụ khởi động khác.
3.  **Modules (`fx.Module`)**: Nhóm các providers và invokes lại với nhau thành một đơn vị chức năng.
4.  **App (`fx.App`)**: Container chính chứa tất cả các modules và quản lý toàn bộ vòng đời ứng dụng.

### FX Module Definition (`fx.go`)

Đây là file quan trọng nhất định nghĩa module FX của bạn.

```go
package mymodule

import (
	"go.uber.org/fx"
	"wnapi/internal/config" // Giả sử có global config provider
	"wnapi/internal/database" // Giả sử có global database manager provider
	"wnapi/internal/pkg/logger"
	"wnapi/modules/mymodule/api"
	"wnapi/modules/mymodule/internal" // Cho cấu hình và kiểu dữ liệu nội bộ
	"wnapi/modules/mymodule/repository/mysql" // Cho cài đặt repository cụ thể
	"wnapi/modules/mymodule/service"
)

// Module returns the FX module for 'mymodule'
func Module() fx.Option {
	return fx.Module("mymodule",
		// fx.Provide: Đăng ký các constructor functions (providers)
		fx.Provide(
			// Cấu hình module
			NewMyModuleConfig,

			// Repository (sử dụng fx.Annotate để bind với interface)
			fx.Annotate(
				mysql.NewMyRepository, // Constructor của cài đặt cụ thể
				fx.As(new(internal.MyRepositoryInterface)), // Bind với interface
			),

			// Service
			fx.Annotate(
				NewMyService, // Constructor của service
				fx.As(new(internal.MyServiceInterface)), // Bind với interface
			),

			// API Handler
			NewMyHandler,
		),

		// fx.Invoke: Đăng ký các hàm sẽ được gọi khi ứng dụng khởi động
		// Thường dùng để đăng ký routes hoặc khởi tạo các background process
		fx.Invoke(
			RegisterMyRoutes, // Hàm đăng ký routes
		),
	)
}
```

### FX Providers (`providers.go`)

File này chứa các hàm constructor (providers) cho FX dependency injection. Mỗi hàm này sẽ nhận các dependencies từ FX container và trả về một instance của service/component.

```go
package mymodule

import (
	"wnapi/internal/config"
	"wnapi/internal/database"
	"wnapi/internal/pkg/logger"
	"wnapi/modules/mymodule/api"
	"wnapi/modules/mymodule/internal"
	"wnapi/modules/mymodule/repository/mysql"
	"wnapi/modules/mymodule/service"
	"github.com/gin-gonic/gin" // Cần cho việc đăng ký routes
)

// NewMyModuleConfig tạo cấu hình cho module.
// Nó nhận global config.Config từ FX container.
func NewMyModuleConfig(cfg config.Config) *internal.MyModuleConfig {
	// Ví dụ: Load các cài đặt cụ thể của module từ global config
	return &internal.MyModuleConfig{
		Setting1: cfg.GetString("MYMODULE_SETTING_1"),
		Enabled:  cfg.GetBool("MYMODULE_ENABLED"),
	}
}

// NewMyRepository tạo cài đặt repository.
// Nó nhận database.Manager và logger từ FX container.
func NewMyRepository(dbManager *database.Manager, log logger.Logger) (internal.MyRepositoryInterface, error) {
	// Giả sử database.Manager được cung cấp globally bởi FX
	return mysql.NewRepository(dbManager, log)
}

// NewMyService tạo service với các dependencies của nó.
// Nó nhận repository, cấu hình module và logger từ FX container.
func NewMyService(
	repo internal.MyRepositoryInterface,
	config *internal.MyModuleConfig,
	log logger.Logger,
) internal.MyServiceInterface {
	return service.New(repo, *config, log)
}

// NewMyHandler tạo API handler.
// Nó nhận service từ FX container.
func NewMyHandler(svc internal.MyServiceInterface) *api.Handler {
	return api.NewHandler(svc)
}
```

### FX-Compatible Route Registration (`routes.go`)

File này chứa hàm `fx.Invoke` để đăng ký routes với Gin engine sử dụng FX handler.

```go
package mymodule

import (
	"wnapi/internal/pkg/logger"
	"wnapi/modules/mymodule/api"
	"github.com/gin-gonic/gin"
)

// RegisterMyRoutes đăng ký routes của module với Gin engine.
// Hàm này được gọi thông qua fx.Invoke, nhận Gin engine, handler và logger từ FX.
func RegisterMyRoutes(engine *gin.Engine, handler *api.Handler, log logger.Logger) {
	log.Info("Registering mymodule routes")

	// Sử dụng logic đăng ký routes của handler
	if err := handler.RegisterRoutes(engine); err != nil {
		log.Error("Failed to register mymodule routes", "error", err)
		return
	}

	log.Info("Mymodule routes registered successfully")
}
```

## Quy tắc quan trọng

### 1. FX Module Structure
-   **Bắt buộc**: Mỗi module phải có file `fx.go` với function `Module() fx.Option`
-   **Bắt buộc**: File `providers.go` chứa tất cả FX providers
-   **Bắt buộc**: File `routes.go` với FX-compatible route registration
-   **Tùy chọn**: Files `bootstrap.go` và `module.go` cho backward compatibility (không sử dụng trong FX hiện tại)

### 2. FX Providers Pattern
-   Tất cả constructors phải là FX providers
-   Sử dụng dependency injection thay vì manual initialization
-   Provider functions phải return `(interface, error)` hoặc `interface`
-   Sử dụng `fx.Provide()` để đăng ký providers

### 3. FX Route Registration
-   **Bắt buộc**: Sử dụng `fx.Invoke(RegisterXXXRoutes)` thay vì legacy route registration
-   Function signature: `func RegisterXXXRoutes(engine *gin.Engine, handler *api.Handler, log logger.Logger)`
-   Đảm bảo error handling và logging trong route registration

### 4. Cấu hình từ biến môi trường
-   Sử dụng `config.Config` interface từ FX container
-   Providers nhận `config.Config` parameter
-   Sử dụng `cfg.GetString()`, `cfg.GetDuration()`, etc.
-   Đặt giá trị mặc định qua `cfg.GetStringWithDefault()`

### 5. Tên Module
-   Sử dụng `snake_case` cho tên thư mục
-   Sử dụng `camelCase` cho tên package Go
-   Module name trong FX phải match với folder name

### 6. Database
-   Luôn sử dụng `INT UNSIGNED` cho ID
-   Luôn có `created_at` và `updated_at`
-   Sử dụng `utf8mb4` và `utf8mb4_unicode_ci`
-   Tên bảng nên có prefix là tên module (ví dụ: `auth_users`, `auth_sessions`)

## Best Practices

### 1. Naming Conventions

```go
// ✅ Đúng - Tên provider rõ ràng và nhất quán
func NewUserService() service.UserService { /* ... */ }
func NewUserRepository() repository.UserRepository { /* ... */ }
func NewUserHandler() *api.UserHandler { /* ... */ }

// ❌ Sai - Tên không rõ ràng hoặc không nhất quán
func NewService() interface{} { /* ... */ }
func NewRepo() interface{} { /* ... */ }
```

### 2. Interface Usage

```go
// ✅ Đúng - Sử dụng interface cho loose coupling và type safety
fx.Annotate(
    repository.NewUserRepository,
    fx.As(new(repository.UserRepositoryInterface)),
)

// ❌ Sai - Sử dụng concrete type trực tiếp khi có thể dùng interface
repository.NewUserRepository, // Nếu NewUserRepository trả về *repository.UserRepository
```

### 3. Error Handling trong Providers

```go
// ✅ Đúng - Provider có error handling rõ ràng
func NewMyService(repo internal.MyRepositoryInterface) (internal.MyServiceInterface, error) {
    if repo == nil {
        return nil, fmt.Errorf("MyRepositoryInterface is a required dependency")
    }
    return service.New(repo), nil
}

// ❌ Sai - Không có error handling, có thể dẫn đến panic nếu dependency là nil
func NewMyService(repo internal.MyRepositoryInterface) internal.MyServiceInterface {
    return service.New(repo) // Sẽ panic nếu repo là nil và service.New không kiểm tra
}
```

### 4. Context Propagation
-   Luôn truyền `context.Context` qua các lớp (handler -> service -> repository)
-   Sử dụng `context` để quản lý timeout, cancellation và truyền metadata (ví dụ: `tenant_id`, `user_id`)

## Troubleshooting

### 1. Circular Dependency Error

**Lỗi**: `cycle detected in dependency graph`

**Nguyên nhân**: FX phát hiện một vòng lặp trong biểu đồ phụ thuộc của các providers. Ví dụ: Provider A cần B, và Provider B cần A.

**Giải pháp**:

1.  **Xem xét lại kiến trúc**: Đánh giá lại mối quan hệ giữa các components. Có thể một dependency là không cần thiết hoặc có thể được giải quyết bằng cách khác.
2.  **Tách shared functionality**: Di chuyển các chức năng chung vào một module hoặc package riêng mà cả hai module đều có thể phụ thuộc vào (theo một hướng).
3.  **Sử dụng Event System**: Thay vì phụ thuộc trực tiếp, một module có thể phát hành một event và module kia lắng nghe event đó. Điều này phá vỡ sự phụ thuộc trực tiếp.
4.  **Sử dụng `fx.In` và `fx.Out`**: Trong một số trường hợp phức tạp, có thể cần cấu trúc lại providers bằng cách sử dụng các struct `fx.In` và `fx.Out` để làm rõ các dependencies và outputs.

### 2. Provider Not Found Error

**Lỗi**: `missing type: *service.MyService` hoặc `no provider found for ...`

**Nguyên nhân**: FX không thể tìm thấy một provider phù hợp cho một dependency mà một component khác yêu cầu.

**Giải pháp**:

1.  **Kiểm tra `fx.Provide`**: Đảm bảo constructor function cho dependency đó đã được đăng ký trong `fx.Provide()` của module tương ứng.
2.  **Kiểm tra Return Type**: Đảm bảo hàm provider trả về đúng kiểu dữ liệu (hoặc interface) mà consumer mong đợi.
3.  **Sử dụng `fx.As`**: Nếu bạn đang cung cấp một concrete type nhưng muốn nó được inject như một interface, hãy sử dụng `fx.Annotate` với `fx.As`.
4.  **Kiểm tra Import Statements**: Đảm bảo tất cả các package cần thiết đã được import đúng cách.

### 3. Route Not Registered (404 Errors)

**Lỗi**: Module load thành công nhưng các API endpoints trả về 404 Not Found.

**Nguyên nhân**: Hàm đăng ký routes (`RegisterMyRoutes` trong ví dụ) không được gọi hoặc không được gọi đúng cách bởi FX.

**Giải pháp**:

1.  **Kiểm tra `fx.Invoke`**: Đảm bảo hàm đăng ký routes đã được thêm vào `fx.Invoke()` trong `fx.go` của module.
2.  **Kiểm tra Signature**: Chữ ký của hàm đăng ký routes phải khớp với các dependencies mà FX có thể cung cấp (ví dụ: `*gin.Engine`, `*api.Handler`, `logger.Logger`).
3.  **Kiểm tra `handler.RegisterRoutes`**: Đảm bảo logic bên trong hàm đăng ký routes thực sự thêm các routes vào Gin engine được truyền vào.

### 4. Dependencies là `nil`

**Lỗi**: Một dependency được inject vào constructor nhưng lại có giá trị `nil` khi sử dụng.

**Nguyên nhân**: Thường xảy ra khi provider trả về `(nil, error)` và lỗi đó bị bỏ qua, hoặc khi một dependency không được đăng ký đúng cách nhưng FX không báo lỗi `no provider found` (ví dụ: do type assertion sai).

**Giải pháp**:

1.  **Kiểm tra Error Handling**: Đảm bảo tất cả các providers trả về `(Type, error)` và lỗi được xử lý đúng cách.
2.  **Kiểm tra `fx.Annotate`**: Nếu bạn đang sử dụng `fx.Annotate` với `fx.As`, hãy chắc chắn rằng interface bạn đang bind tới là chính xác và constructor thực sự trả về một implementation của interface đó.
3.  **Sử dụng `fx.In`**: Đối với các constructor có nhiều dependencies, việc sử dụng `fx.In` có thể giúp FX kiểm tra chặt chẽ hơn các dependencies đầu vào.

## Ví dụ

### Module với Database Repository

```go
// fx.go (trong fx.Provide)
fx.Annotate(
    mysql.NewMyRepository, // Constructor của cài đặt MySQL
    fx.As(new(internal.MyRepositoryInterface)), // Bind với interface
),
fx.Annotate(
    NewMyService, // Constructor của service
    fx.As(new(internal.MyServiceInterface)), // Bind với interface
),

// providers.go
func NewMyRepository(dbManager *database.Manager, log logger.Logger) (internal.MyRepositoryInterface, error) {
    return mysql.NewRepository(dbManager, log)
}

func NewMyService(
    repo internal.MyRepositoryInterface,
    config *internal.MyModuleConfig,
    log logger.Logger,
) internal.MyServiceInterface {
    return service.New(repo, *config, log)
}
```

### Module với External Service (Inter-module communication)

```go
// Giả sử module 'notification' cung cấp service.NotificationService
// fx.go (trong fx.Provide của module hiện tại)
fx.Annotate(
    NewMyService,
    fx.As(new(internal.MyServiceInterface)),
),

// providers.go
func NewMyService(
    repo internal.MyRepositoryInterface,
    notificationService notification.NotificationServiceInterface, // Nhận service từ module 'notification'
    log logger.Logger,
) internal.MyServiceInterface {
    return service.New(repo, notificationService, log)
}
```

## Checklist khi tạo module mới

-   [ ] Cấu trúc thư mục module tuân thủ chuẩn FX (`fx.go`, `providers.go`, `routes.go`, v.v.)
-   [ ] File `fx.go` định nghĩa `func Module() fx.Option` và sử dụng `fx.Provide`, `fx.Invoke` đúng cách.
-   [ ] Tất cả các constructor functions được đăng ký trong `providers.go` và được thêm vào `fx.Provide()`.
-   [ ] Sử dụng `fx.Annotate` với `fx.As` khi bind concrete types với interfaces.
-   [ ] Các hàm provider có xử lý lỗi và trả về `(Type, error)` khi cần thiết.
-   [ ] Hàm đăng ký routes được định nghĩa trong `routes.go` và được thêm vào `fx.Invoke()`.
-   [ ] Chữ ký của hàm đăng ký routes khớp với các dependencies mà FX cung cấp (ví dụ: `*gin.Engine`, `*api.Handler`, `logger.Logger`).
-   [ ] Cấu hình module được load thông qua `config.Config` từ FX container.
-   [ ] Module được thêm vào danh sách modules trong `cmd/fx-server/main.go`.
-   [ ] Đã kiểm tra không có circular dependencies giữa các modules.
-   [ ] Đã viết unit/integration tests cho module và các providers của nó.

## Tham khảo

-   Uber FX Documentation
-   Dependency Injection Patterns
-   Tài liệu chi tiết về Module System
