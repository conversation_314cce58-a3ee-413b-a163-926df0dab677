# <PERSON>ệ thống Queue - <PERSON><PERSON><PERSON> li<PERSON>u <PERSON>ỹ thuật

## Tổng quan Kiến trúc

<PERSON>ệ thống queue cung cấp một framework hoàn chỉnh để xử lý tác vụ bất đồng bộ với hỗ trợ multi-tenant, middleware, và quản lý lỗi. <PERSON>ệ thống được thiết kế theo pattern interface để hỗ trợ nhiều backend kh<PERSON><PERSON>hau (Asynq, Redis, v.v.).

### Các Interface Chính

```go
// Queue - Interface chính kết hợp tất cả chức năng
type Queue interface {
    QueueClient     // Enqueue, schedule, cancel tasks
    QueueInspector  // Monitor, manage queues
    GetWorkerServer() WorkerServer  // Task processing
    GetScheduler() Scheduler        // Cron scheduling
    Health(ctx context.Context) error
    Close() error
}

// QueueClient - Quản lý tác vụ
type QueueClient interface {
    EnqueueTask(ctx context.Context, taskType string, payload interface{}, opts *EnqueueOptions) (*TaskInfo, error)
    Enqueue(ctx context.Context, taskType string, payload interface{}, opts *EnqueueOptions) (*TaskInfo, error) // deprecated, use EnqueueTask
    ScheduleTask(ctx context.Context, taskType string, payload interface{}, processAt time.Time, opts *EnqueueOptions) (*TaskInfo, error)
    EnqueueBatch(ctx context.Context, tasks []BatchTask) ([]*TaskInfo, error)
    Cancel(ctx context.Context, taskID string) error
    Delete(ctx context.Context, taskID string) error
}

// WorkerServer - Xử lý tác vụ
type WorkerServer interface {
    RegisterProcessor(taskType string, processor TaskProcessor)
    RegisterMiddleware(middleware ...Middleware)
    Start(ctx context.Context) error
    Stop() error
}
```

## Khái niệm Cốt lõi

### TaskInfo - Thông tin Tác vụ

```go
type TaskInfo struct {
    ID            string                 `json:"id"`
    Type          string                 `json:"type"`
    Payload       map[string]interface{} `json:"payload"`
    Queue         string                 `json:"queue"`
    Tenant        string                 `json:"tenant,omitempty"`
    State         TaskState              `json:"state"`
    MaxRetry      int                    `json:"max_retry"`
    Retried       int                    `json:"retried"`
    LastFailure   string                 `json:"last_failure,omitempty"`
    NextProcessAt time.Time              `json:"next_process_at"`
    CreatedAt     time.Time              `json:"created_at"`
}
```

### TaskState - Trạng thái Tác vụ

```go
const (
    TaskStatePending   TaskState = "pending"    // Chờ xử lý
    TaskStateActive    TaskState = "active"     // Đang xử lý
    TaskStateScheduled TaskState = "scheduled"  // Đã lên lịch
    TaskStateRetry     TaskState = "retry"      // Chờ thử lại
    TaskStateArchived  TaskState = "archived"   // Lưu trữ (thất bại)
    TaskStateCompleted TaskState = "completed"  // Hoàn thành
    TaskStateFailed    TaskState = "failed"     // Thất bại
)
```

### TaskDefinition - Định nghĩa Tác vụ

```go
type TaskDefinition struct {
    Type             string                    // Loại tác vụ (unique)
    Description      string                    // Mô tả
    PayloadValidator func(interface{}) error   // Validation payload
    Options          *TaskOptions              // Cấu hình mặc định
    Handler          TaskHandler               // Xử lý tác vụ
}

type TaskOptions struct {
    Queue    string        // Tên queue
    Timeout  time.Duration // Timeout xử lý
    MaxRetry int          // Số lần thử lại tối đa
    Priority int          // Độ ưu tiên
}
```

## Hướng dẫn Tích hợp

### 1. Đăng ký Task Definitions

Mỗi module cần đăng ký các task definitions trong file `types/common.go`:

```go
// modules/notification/types/common.go
package types

import (
    "time"
    "wnapi/internal/pkg/queue"
)

const (
    TaskTypeSendEmail        = "notification.send_email"
    TaskTypeSendPushNotification = "notification.send_push"
)

// GetTaskDefinitions trả về danh sách task definitions
func GetTaskDefinitions() []*queue.TaskDefinition {
    return []*queue.TaskDefinition{
        {
            Type:        TaskTypeSendEmail,
            Description: "Gửi email đến người dùng",
            PayloadValidator: ValidateSendEmailPayload,
            Options: &queue.TaskOptions{
                Queue:    "emails",
                Timeout:  30 * time.Second,
                MaxRetry: 3,
                Priority: 1,
            },
            Handler: nil, // Sẽ được set trong service
        },
    }
}

// ValidateSendEmailPayload validates email payload
func ValidateSendEmailPayload(payload interface{}) error {
    data, ok := payload.(map[string]interface{})
    if !ok {
        return errors.New("invalid payload format")
    }

    if _, ok := data["to"]; !ok {
        return errors.New("missing 'to' field")
    }

    if _, ok := data["subject"]; !ok {
        return errors.New("missing 'subject' field")
    }

    return nil
}
```

### 2. Implement Task Handlers

```go
// modules/notification/services/email_service.go
package services

import (
    "context"
    "wnapi/internal/pkg/queue"
    "wnapi/modules/notification/types"
)

type EmailService struct {
    queueClient queue.QueueClient
    // ... other dependencies
}

// HandleSendEmail xử lý task gửi email
func (s *EmailService) HandleSendEmail(ctx context.Context, task *queue.TaskInfo) *queue.ProcessResult {
    // Extract tenant từ context hoặc task
    tenantID := task.Tenant
    if tenantID == "" {
        return &queue.ProcessResult{
            Success: false,
            Error:   errors.New("missing tenant ID"),
        }
    }

    // Parse payload
    payload := task.Payload
    to, _ := payload["to"].(string)
    subject, _ := payload["subject"].(string)
    body, _ := payload["body"].(string)

    // Validate payload
    if err := types.ValidateSendEmailPayload(payload); err != nil {
        return &queue.ProcessResult{
            Success:   false,
            Error:     err,
            Retryable: false, // Validation errors không retry
        }
    }

    // Gửi email
    err := s.sendEmail(ctx, tenantID, to, subject, body)
    if err != nil {
        return &queue.ProcessResult{
            Success:   false,
            Error:     err,
            Retryable: true, // Network errors có thể retry
        }
    }

    return &queue.ProcessResult{
        Success: true,
        Result:  map[string]interface{}{"sent_at": time.Now()},
    }
}

// EnqueueSendEmail enqueue task gửi email
func (s *EmailService) EnqueueSendEmail(ctx context.Context, tenantID, to, subject, body string) (*queue.TaskInfo, error) {
    payload := map[string]interface{}{
        "to":      to,
        "subject": subject,
        "body":    body,
    }

    opts := &queue.EnqueueOptions{
        Queue:    "emails",
        TenantID: tenantID,
        Timeout:  30 * time.Second,
        MaxRetry: 3,
    }

    return s.queueClient.EnqueueTask(ctx, types.TaskTypeSendEmail, payload, opts)
}
```

### 3. Đăng ký Handlers với Worker

```go
// modules/notification/module.go
package notification

import (
    "wnapi/internal/pkg/queue"
    "wnapi/modules/notification/services"
    "wnapi/modules/notification/types"
)

type Module struct {
    emailService *services.EmailService
    workerServer queue.WorkerServer
}

func (m *Module) RegisterTaskHandlers() {
    // Đăng ký middleware
    m.workerServer.RegisterMiddleware(
        middleware.NewTenantMiddleware(),
        middleware.NewLoggingMiddleware(),
        middleware.NewMetricsMiddleware(),
    )

    // Đăng ký task handlers
    m.workerServer.RegisterProcessor(
        types.TaskTypeSendEmail,
        queue.TaskProcessorFunc(m.emailService.HandleSendEmail),
    )

    m.workerServer.RegisterProcessor(
        types.TaskTypeSendPushNotification,
        queue.TaskProcessorFunc(m.emailService.HandlePushNotification),
    )
}
```

## Patterns Tích hợp

### 1. Service-to-Service Communication

```go
// modules/auth/services/user_service.go
func (s *UserService) CreateUser(ctx context.Context, req *CreateUserRequest) (*User, error) {
    // Tạo user
    user, err := s.repository.Create(ctx, req.TenantID, req)
    if err != nil {
        return nil, err
    }

    // Enqueue welcome email task
    _, err = s.notificationQueue.EnqueueSendEmail(ctx,
        req.TenantID,
        user.Email,
        "Chào mừng bạn đến với hệ thống",
        "Tài khoản của bạn đã được tạo thành công",
    )
    if err != nil {
        s.logger.Warn("Failed to enqueue welcome email", "error", err)
        // Không return error vì user đã được tạo thành công
    }

    return user, nil
}
```

### 2. Multi-tenant Task Processing

```go
// internal/pkg/queue/middleware/tenant.go
type TenantMiddleware struct {
    tenantService tenant.Service
}

func (m *TenantMiddleware) Process(ctx context.Context, task *queue.TaskInfo, next queue.TaskProcessor) *queue.ProcessResult {
    // Validate tenant
    if task.Tenant == "" {
        return &queue.ProcessResult{
            Success: false,
            Error:   errors.New("missing tenant ID"),
        }
    }

    // Verify tenant exists và active
    tenant, err := m.tenantService.GetByID(ctx, task.Tenant)
    if err != nil {
        return &queue.ProcessResult{
            Success: false,
            Error:   fmt.Errorf("invalid tenant: %w", err),
        }
    }

    if !tenant.IsActive {
        return &queue.ProcessResult{
            Success: false,
            Error:   errors.New("tenant is inactive"),
        }
    }

    // Set tenant context
    ctx = context.WithValue(ctx, "tenant_id", task.Tenant)
    ctx = context.WithValue(ctx, "tenant", tenant)

    return next.ProcessTask(ctx, task)
}
```

### 3. Batch Operations

```go
// Gửi email hàng loạt
func (s *EmailService) EnqueueBulkEmails(ctx context.Context, tenantID string, emails []EmailData) error {
    var tasks []queue.BatchTask

    for _, email := range emails {
        payload := map[string]interface{}{
            "to":      email.To,
            "subject": email.Subject,
            "body":    email.Body,
        }

        tasks = append(tasks, queue.BatchTask{
            Type:    types.TaskTypeSendEmail,
            Payload: payload,
            Options: &queue.EnqueueOptions{
                TenantID: tenantID,
                Queue:    "emails",
            },
        })
    }

    _, err := s.queueClient.EnqueueBatch(ctx, tasks)
    return err
}
```

### 4. Error Handling và Retry Strategies

```go
// Custom error handling middleware
type ErrorHandlingMiddleware struct {
    logger queue.Logger
}

func (m *ErrorHandlingMiddleware) Process(ctx context.Context, task *queue.TaskInfo, next queue.TaskProcessor) *queue.ProcessResult {
    result := next.ProcessTask(ctx, task)

    if !result.Success && result.Error != nil {
        // Log error với context
        m.logger.Error("Task failed",
            "task_id", task.ID,
            "task_type", task.Type,
            "tenant", task.Tenant,
            "error", result.Error,
            "retried", task.Retried,
            "max_retry", task.MaxRetry,
        )

        // Determine retry strategy
        switch {
        case isValidationError(result.Error):
            result.Retryable = false
        case isNetworkError(result.Error):
            result.Retryable = true
        case isRateLimitError(result.Error):
            result.Retryable = true
            // Exponential backoff
            delay := time.Duration(math.Pow(2, float64(task.Retried))) * time.Second
            result.RetryAfter = &delay
        default:
            result.Retryable = task.Retried < task.MaxRetry
        }
    }

    return result
}
```

### 5. Scheduled Tasks

```go
// Lên lịch task định kỳ
func (s *ReportService) ScheduleDailyReport(ctx context.Context, tenantID string) error {
    // Lên lịch chạy hàng ngày lúc 6:00 AM
    cronSpec := "0 6 * * *"

    payload := map[string]interface{}{
        "report_type": "daily",
        "tenant_id":   tenantID,
    }

    opts := &queue.ScheduleOptions{
        Queue:    "reports",
        TenantID: tenantID,
        Timezone: "Asia/Ho_Chi_Minh",
    }

    scheduleID, err := s.scheduler.Schedule(
        types.TaskTypeGenerateReport,
        cronSpec,
        payload,
        opts,
    )

    if err != nil {
        return fmt.Errorf("failed to schedule daily report: %w", err)
    }

    s.logger.Info("Daily report scheduled", "schedule_id", scheduleID, "tenant", tenantID)
    return nil
}
```

## Best Practices

### 1. Task Naming Conventions

```go
// Format: {module}.{action}.{resource}
const (
    TaskTypeSendEmail           = "notification.send.email"
    TaskTypeSendPushNotification = "notification.send.push_notification"
    TaskTypeProcessImage        = "file.process.image"
    TaskTypeGenerateThumbnail   = "file.generate.thumbnail"
    TaskTypeIndexDocument       = "search.index.document"
    TaskTypeCleanupSessions     = "auth.cleanup.sessions"
)
```

### 2. Payload Structure Design

```go
// Good: Structured payload với validation
type EmailPayload struct {
    To          string            `json:"to" validate:"required,email"`
    Subject     string            `json:"subject" validate:"required,max=255"`
    Body        string            `json:"body" validate:"required"`
    TemplateID  string            `json:"template_id,omitempty"`
    Variables   map[string]string `json:"variables,omitempty"`
    Attachments []Attachment      `json:"attachments,omitempty"`
}

// Bad: Unstructured payload
payload := map[string]interface{}{
    "data": "some random data",
    "stuff": 123,
}
```

### 3. Queue Configuration

```go
// config/queue.yaml
queue:
  redis:
    addr: "localhost:6379"
    password: ""
    db: 0
    pool_size: 10

  worker:
    concurrency: 10
    queues:
      emails:
        name: "emails"
        priority: 1
        enabled: true
      notifications:
        name: "notifications"
        priority: 2
        enabled: true
      reports:
        name: "reports"
        priority: 3
        enabled: true
      maintenance:
        name: "maintenance"
        priority: 5
        enabled: true

  scheduler:
    enabled: true
    timezone: "Asia/Ho_Chi_Minh"
```

### 4. Performance Considerations

```go
// Sử dụng batch operations cho volume lớn
func (s *Service) ProcessBulkData(ctx context.Context, data []DataItem) error {
    const batchSize = 100

    for i := 0; i < len(data); i += batchSize {
        end := i + batchSize
        if end > len(data) {
            end = len(data)
        }

        batch := data[i:end]
        if err := s.enqueueBatch(ctx, batch); err != nil {
            return err
        }
    }

    return nil
}

// Sử dụng connection pooling
func (s *Service) enqueueWithRetry(ctx context.Context, task Task) error {
    return retry.Do(func() error {
        return s.queueClient.EnqueueTask(ctx, task.Type, task.Payload, task.Options)
    }, retry.Attempts(3), retry.Delay(time.Second))
}
```

## API Reference

### QueueClient Methods

```go
// EnqueueTask - Thêm task vào queue (preferred method)
EnqueueTask(ctx context.Context, taskType string, payload interface{}, opts *EnqueueOptions) (*TaskInfo, error)

// Enqueue - Thêm task vào queue (deprecated, use EnqueueTask)
Enqueue(ctx context.Context, taskType string, payload interface{}, opts *EnqueueOptions) (*TaskInfo, error)

// ScheduleTask - Lên lịch task chạy tại thời điểm cụ thể
ScheduleTask(ctx context.Context, taskType string, payload interface{}, processAt time.Time, opts *EnqueueOptions) (*TaskInfo, error)

// EnqueueBatch - Thêm nhiều task cùng lúc
EnqueueBatch(ctx context.Context, tasks []BatchTask) ([]*TaskInfo, error)

// Cancel - Hủy task
Cancel(ctx context.Context, taskID string) error

// Delete - Xóa task
Delete(ctx context.Context, taskID string) error

// GetTask - Lấy thông tin task
GetTask(ctx context.Context, taskID string) (*TaskInfo, error)

// ListTasks - Liệt kê tasks với filter
ListTasks(ctx context.Context, filters *TaskFilters) ([]*TaskInfo, error)
```

### QueueInspector Methods

```go
// GetQueueInfo - Lấy thông tin queue
GetQueueInfo(ctx context.Context, queueName string) (*QueueInfo, error)

// ListQueues - Liệt kê tất cả queues
ListQueues(ctx context.Context) ([]*QueueInfo, error)

// PauseQueue - Tạm dừng queue
PauseQueue(ctx context.Context, queueName string) error

// UnpauseQueue - Tiếp tục queue
UnpauseQueue(ctx context.Context, queueName string) error

// DeleteQueue - Xóa queue và tất cả tasks
DeleteQueue(ctx context.Context, queueName string) error

// GetTasksByState - Lấy tasks theo trạng thái
GetTasksByState(ctx context.Context, queueName string, state TaskState, limit int) ([]*TaskInfo, error)
```

### Configuration Options

```go
type EnqueueOptions struct {
    Queue      string        `json:"queue,omitempty"`
    TenantID   string        `json:"tenant_id,omitempty"`
    Timeout    time.Duration `json:"timeout,omitempty"`
    MaxRetry   int          `json:"max_retry,omitempty"`
    Priority   int          `json:"priority,omitempty"`
    ProcessAt  time.Time    `json:"process_at,omitempty"`
    UniqueKey  string       `json:"unique_key,omitempty"`
    Group      string       `json:"group,omitempty"`
    Retention  time.Duration `json:"retention,omitempty"`
}

type TaskFilters struct {
    Queue  string      `json:"queue,omitempty"`
    Tenant string      `json:"tenant,omitempty"`
    Type   string      `json:"type,omitempty"`
    State  []TaskState `json:"state,omitempty"`
    Limit  int         `json:"limit,omitempty"`
    Offset int         `json:"offset,omitempty"`
    Since  *time.Time  `json:"since,omitempty"`
    Until  *time.Time  `json:"until,omitempty"`
}
```

## Monitoring và Debugging

### 1. Health Checks

```go
func (s *Service) HealthCheck(ctx context.Context) error {
    // Check queue connection
    if err := s.queue.Health(ctx); err != nil {
        return fmt.Errorf("queue health check failed: %w", err)
    }

    // Check worker status
    if !s.queue.GetWorkerServer().IsRunning() {
        return errors.New("worker server is not running")
    }

    return nil
}
```

### 2. Metrics Collection

```go
type MetricsMiddleware struct {
    metrics metrics.Collector
}

func (m *MetricsMiddleware) Process(ctx context.Context, task *queue.TaskInfo, next queue.TaskProcessor) *queue.ProcessResult {
    start := time.Now()

    result := next.ProcessTask(ctx, task)

    duration := time.Since(start)

    // Collect metrics
    m.metrics.Counter("queue.tasks.processed").
        WithTags("type", task.Type, "tenant", task.Tenant, "success", fmt.Sprintf("%t", result.Success)).
        Inc()

    m.metrics.Histogram("queue.tasks.duration").
        WithTags("type", task.Type).
        Observe(duration.Seconds())

    if !result.Success {
        m.metrics.Counter("queue.tasks.failed").
            WithTags("type", task.Type, "error", result.Error.Error()).
            Inc()
    }

    return result
}
```

### 3. Logging Best Practices

```go
func (s *Service) HandleTask(ctx context.Context, task *queue.TaskInfo) *queue.ProcessResult {
    logger := s.logger.With(
        "task_id", task.ID,
        "task_type", task.Type,
        "tenant", task.Tenant,
        "queue", task.Queue,
    )

    logger.Info("Processing task started")

    // Process task
    result := s.processTask(ctx, task)

    if result.Success {
        logger.Info("Task completed successfully", "duration", result.Duration)
    } else {
        logger.Error("Task failed", "error", result.Error, "retryable", result.Retryable)
    }

    return result
}
```

## Troubleshooting

### Common Issues

1. **Task không được xử lý**: Kiểm tra worker server có đang chạy và queue có được pause không
2. **Memory leak**: Đảm bảo close connections và cleanup resources
3. **High latency**: Tối ưu payload size và sử dụng batch operations
4. **Failed tasks**: Implement proper error handling và retry logic

### Debug Commands

```bash
# Kiểm tra queue status
curl -X GET /api/admin/queues

# Xem failed tasks
curl -X GET /api/admin/queues/emails/tasks?state=failed

# Retry failed tasks
curl -X POST /api/admin/queues/emails/retry-failed

# Pause/unpause queue
curl -X POST /api/admin/queues/emails/pause
curl -X POST /api/admin/queues/emails/unpause
```
