# Event System Best Practices

## Tổng quan

Hệ thống event của WNAPI được xây dựng dựa trên Watermill và Redis Streams, cung cấp một giải pháp event-driven architecture mạnh mẽ và có thể mở rộng.

## Kiến trúc

### Thành phần chính

1. **Publisher**: <PERSON><PERSON>t hành events
2. **Subscriber**: Lắng nghe và xử lý events
3. **Router**: Định tuyến events đến handlers
4. **Event Types**: Định nghĩa các loại events
5. **Handlers**: <PERSON><PERSON> lý logic nghiệp vụ cho events

### Luồng xử lý

```
[Module] -> [Publisher] -> [Redis Stream] -> [Subscriber] -> [Router] -> [<PERSON><PERSON>]
```

## Quy tắc đặt tên Events

### Format: `{domain}.{entity}.{action}`

- **domain**: auth, blog, product, order, system
- **entity**: user, post, comment, product, order
- **action**: created, updated, deleted, published, etc.

### Ví dụ:
- `auth.user.created`
- `blog.post.published`
- `product.inventory.changed`
- `order.payment.received`

## Cách sử dụng

### 1. Phát hành Events

```go
// Trong module Auth
func (s *Service) CreateUser(ctx context.Context, req CreateUserRequest) error {
    // Tạo user...
    
    // Phát hành event
    err := s.eventPublisher.PublishUserCreated(ctx, tenantID, websiteID, user)
    if err != nil {
        log.Printf("Failed to publish user created event: %v", err)
        // Không return error - event publishing không nên làm fail business logic
    }
    
    return nil
}
```

### 2. Xử lý Events

```go
// Handler cho welcome email
func SendWelcomeEmailHandler(ctx context.Context, event *events.BaseEvent, payload interface{}) error {
    userPayload := payload.(*types.UserCreatedPayload)
    
    // Gửi email chào mừng
    return emailService.SendWelcomeEmail(userPayload.Email, userPayload.FullName)
}
```

### 3. Đăng ký Handlers

```go
// Trong handlers/registration.go
func AuthHandlers() []HandlerRegistration {
    return []HandlerRegistration{
        {
            Name:      "send_welcome_email",
            EventType: types.AuthUserCreated,
            HandlerFunc: CreateHandler(
                types.AuthUserCreated,
                SendWelcomeEmailHandler,
                &types.UserCreatedPayload{},
            ),
        },
    }
}
```

## Best Practices

### 1. Event Design

- **Immutable**: Events không bao giờ thay đổi sau khi được phát hành
- **Self-contained**: Chứa đủ thông tin để xử lý
- **Versioned**: Có thể thêm version cho backward compatibility
- **Meaningful**: Tên event phải rõ ràng và có ý nghĩa

### 2. Error Handling

- **Graceful degradation**: Event publishing failure không nên làm fail business logic
- **Retry mechanism**: Sử dụng retry cho transient errors
- **Dead letter queue**: Xử lý events không thể process
- **Monitoring**: Log và monitor event failures

### 3. Performance

- **Async processing**: Events được xử lý bất đồng bộ
- **Batching**: Group multiple events khi có thể
- **Partitioning**: Sử dụng tenant_id để partition
- **Resource limits**: Giới hạn memory và CPU usage

### 4. Testing

- **Mock publisher**: Sử dụng MockPublisher cho unit tests
- **Integration tests**: Test end-to-end event flow
- **Event assertions**: Verify events được phát hành đúng
- **Handler testing**: Test handlers độc lập

### 5. Monitoring

- **Health checks**: Monitor Redis connection và event processing
- **Metrics**: Track event counts, processing time, errors
- **Alerting**: Alert khi có failures hoặc performance issues
- **Logging**: Log event processing với correlation IDs

## Cấu hình

### Environment Variables

```env
# Event System
EVENT_ENABLED=true
REDIS_HOST=localhost
REDIS_PORT=6379
EVENT_CONSUMER_GROUP=wnapi_consumers

# Performance
EVENT_PUBLISHER_MAX_LEN=10000
EVENT_RETRY_MAX_ATTEMPTS=3

# Monitoring
EVENT_LOGGING_ENABLED=true
EVENT_METRICS_ENABLED=true
```

### Docker Compose

```yaml
redis:
  image: redis:7.0-alpine
  ports:
    - "6379:6379"
  volumes:
    - redis_data:/data
```

## Troubleshooting

### Common Issues

1. **Redis connection errors**
   - Check Redis server status
   - Verify connection parameters
   - Check network connectivity

2. **Event processing delays**
   - Monitor consumer lag
   - Check handler performance
   - Scale consumers if needed

3. **Memory issues**
   - Monitor Redis memory usage
   - Adjust max stream length
   - Implement event cleanup

### Debugging

```bash
# Check Redis streams
redis-cli XINFO STREAM auth.user.created

# Check consumer groups
redis-cli XINFO GROUPS auth.user.created

# Monitor event processing
curl http://localhost:8080/health/events/details
```

## Migration Guide

### Adding New Events

1. Định nghĩa event type trong `types/constants.go`
2. Tạo payload struct trong `types/{domain}_events.go`
3. Thêm publisher method trong module
4. Tạo handlers nếu cần
5. Đăng ký handlers trong `registration.go`
6. Thêm tests

### Versioning Events

```go
// V1
type UserCreatedPayloadV1 struct {
    UserID uint   `json:"user_id"`
    Email  string `json:"email"`
}

// V2 - backward compatible
type UserCreatedPayloadV2 struct {
    UserID   uint   `json:"user_id"`
    Email    string `json:"email"`
    FullName string `json:"full_name,omitempty"` // New field
}
```

## Security

- **Tenant isolation**: Events chỉ được xử lý trong tenant context
- **Authentication**: Verify user permissions trong handlers
- **Data sanitization**: Sanitize sensitive data trong events
- **Audit logging**: Log security-relevant events

## Scalability

- **Horizontal scaling**: Thêm consumers để scale processing
- **Partitioning**: Sử dụng tenant-based partitioning
- **Load balancing**: Distribute events across consumers
- **Resource monitoring**: Monitor và adjust resources theo load
