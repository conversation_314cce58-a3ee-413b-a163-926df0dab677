# Tài Liệu Kiến Trúc WNAPI

## Tổng Quan Kiến Trúc

WNAPI được thiết kế theo kiến trúc mô-đun hóa cao với hỗ trợ multi-tenant, mỗi thành phần chức năng được tách thành các module riêng biệt, cho phép tùy chỉnh và mở rộng một cách linh hoạt.

## Các Thành Phần Chính

### Core Framework (internal/core)

Core Framework là trung tâm của ứng dụng, cung cấp các chức năng cơ bản và đã được chuyển đổi sang sử dụng `uber-go/fx` cho quản lý dependency injection và lifecycle:

- **fx.App**: Quản lý vòng đời ứng dụng, khởi tạo và hủy các module, thay thế cho `AppBootstrap` truyền thống.
- **fx.Provide**: <PERSON><PERSON> chế đăng ký các dependencies (services, repositories, configurations) vào DI container.
- **fx.Invoke**: <PERSON><PERSON> chế thực thi các hàm sau khi tất cả dependencies đã được resolved, thường dùng để đăng ký routes hoặc khởi tạo các background process.
- **fx.Lifecycle**: Quản lý các hook cho startup và shutdown của ứng dụng.
- **Config**: Đọc và quản lý cấu hình từ file YAML và biến môi trường, được cung cấp thông qua FX.
- **Server**: Quản lý HTTP server và định tuyến với Gin framework, được khởi tạo và quản lý bởi FX.

### Module System

Mỗi module trong hệ thống hiện được định nghĩa như một `fx.Module`, tuân thủ các nguyên tắc của `uber-go/fx` để quản lý dependencies và lifecycle.

```go
import "go.uber.org/fx"

// Module returns the FX module for a specific domain
func Module() fx.Option {
	return fx.Module("module_name",
		// fx.Provide: Đăng ký các constructor functions
		fx.Provide(
			// ... các providers của module
		),
		// fx.Invoke: Đăng ký các hàm sẽ được gọi khi ứng dụng khởi động
		fx.Invoke(
			// ... các invokes của module (ví dụ: đăng ký routes)
		),
	)
}
```

Các module được tạo ra và đăng ký với hệ thống vào thời điểm biên dịch thông qua cơ chế `init()` function và factory pattern:

```go
func init() {
    // Đăng ký module với global registry
    core.RegisterModuleFactory("auth", NewModule)
}
```

### Plugin System

Hệ thống plugin cho phép mở rộng chức năng mà không cần sửa đổi code core, tuân theo interface:

```go
type Plugin interface {
    // Name trả về tên của plugin
    Name() string

    // Init khởi tạo plugin
    Init(ctx context.Context) error

    // Shutdown dọn dẹp tài nguyên của plugin
    Shutdown(ctx context.Context) error
}
```

### Database Layer

Cung cấp truy cập đến database với:

- **Connection Management**: Quản lý pool connection
- **Migration System**: Hỗ trợ database migration
- **Repository Pattern**: Cung cấp interface truy cập dữ liệu

### Project System

Cho phép tạo nhiều dự án khác nhau từ cùng một codebase với:

- **Project-specific Config**: Cấu hình riêng cho từng dự án
- **Module Activation**: Kích hoạt/vô hiệu hóa module theo dự án
- **Environment Settings**: Cấu hình môi trường

## Luồng Dữ Liệu & Xử Lý Request

1. HTTP Request đến Server
2. Middleware xử lý (logging, authentication, etc.)
3. Router định tuyến request đến module thích hợp
4. Handler xử lý request:
   - Chuyển đổi dữ liệu (DTOs)
   - Gọi service layer
   - Service thực hiện business logic
   - Repository truy xuất/cập nhật dữ liệu
5. Response được trả về

## Các Pattern Sử Dụng

- **Dependency Injection**: Thông qua module factory và constructor
- **Repository Pattern**: Truy cập dữ liệu
- **Factory Pattern**: Tạo module và plugin
- **Strategy Pattern**: Xử lý business logic
- **Middleware Pattern**: Xử lý request/response

## Mở Rộng Hệ Thống

### Tạo Module Mới

1. Tạo thư mục module mới trong `modules/`
2. Implement Module interface
3. Đăng ký module với registry trong `init()`
4. Tạo các thành phần cần thiết (api, service, models, repository)

### Tạo Plugin Mới

1. Tạo thư mục plugin mới trong `plugins/`
2. Implement Plugin interface
3. Đăng ký plugin với registry trong `init()`

### Tạo Project Mới

1. Tạo thư mục project mới trong `projects/`
2. Tạo file config.yaml xác định module và plugin cần sử dụng
3. Cấu hình database và các thiết lập khác

## Hiệu Năng & Scale

- **Goroutines**: Xử lý bất đồng bộ
- **Connection Pooling**: Tối ưu kết nối database
- **Caching Mechanisms**: Giảm tải database
- **Horizontal Scaling**: Khả năng scale thông qua nhiều instance