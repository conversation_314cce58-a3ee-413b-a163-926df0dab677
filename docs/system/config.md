# WNAPI Module Configuration Guide

## 📋 Tổng quan

WNAPI sử dụng hệ thống cấu hình tập trung dựa trên `internal/pkg/config/viperconfig` với thứ tự ưu tiên:

1. **Environment Variables** (cao nhất)
2. **Config File** (trung bình)
3. **Centralized Defaults** (thấp nhất - được định nghĩa trong `setGlobalDefaultConfig()`)

## 🏗️ Kiến trúc Cấu hình

### Centralized Configuration System

- **Interface**: `internal/pkg/config/config.go` - Định nghĩa interface `Config`
- **Implementation**: `internal/pkg/config/viperconfig/viper.go` - Triển khai với Viper
- **Defaults**: Tất cả giá trị mặc định được định nghĩa trong `setGlobalDefaultConfig()`
- **Loading**: Ứng dụng load config một lần và chia sẻ cho tất cả modules

### Module Configuration Pattern

Mỗi module phải tuân theo pattern chuẩn:

```go
// 1. Định nghĩa struct config với field names = environment variable names
type ModuleConfig struct {
    MODULE_SETTING_NAME string `yaml:"module_setting_name" env:"MODULE_SETTING_NAME"`
    MODULE_ENABLED      bool   `yaml:"module_enabled" env:"MODULE_ENABLED"`
}

// 2. Tạo function load config từ app config
func NewModuleConfigFromAppConfig(appConfig config.Config) (*ModuleConfig, error) {
    cfg := &ModuleConfig{
        MODULE_SETTING_NAME: appConfig.GetString("MODULE_SETTING_NAME"),
        MODULE_ENABLED:      appConfig.GetBool("MODULE_ENABLED"),
    }
    return cfg, nil
}

// 3. Sử dụng trong module.go
func NewModule(app *core.AppBootstrap, config map[string]interface{}) (core.Module, error) {
    moduleConfig, err := NewModuleConfigFromAppConfig(app.GetConfig())
    if err != nil {
        return nil, err
    }
    // ...
}
```

## 🔧 Hướng dẫn Tạo Module Configuration

### Bước 1: Định nghĩa Struct Configuration

Tạo file `modules/yourmodule/internal/config.go`:

```go
package internal

import (
    "fmt"
    "time"
    "wnapi/internal/pkg/config"
)

// YourModuleConfig chứa cấu hình cho module
// Field names PHẢI match với environment variable names
type YourModuleConfig struct {
    // Basic settings
    YOURMODULE_ENABLED          bool          `yaml:"yourmodule_enabled" env:"YOURMODULE_ENABLED"`
    YOURMODULE_DEBUG            bool          `yaml:"yourmodule_debug" env:"YOURMODULE_DEBUG"`
    YOURMODULE_MESSAGE          string        `yaml:"yourmodule_message" env:"YOURMODULE_MESSAGE"`

    // Database settings (nếu module có DB riêng)
    YOURMODULE_DB_HOST          string        `yaml:"yourmodule_db_host" env:"YOURMODULE_DB_HOST"`
    YOURMODULE_DB_PORT          int           `yaml:"yourmodule_db_port" env:"YOURMODULE_DB_PORT"`

    // API settings
    YOURMODULE_API_TIMEOUT      time.Duration `yaml:"yourmodule_api_timeout" env:"YOURMODULE_API_TIMEOUT"`
    YOURMODULE_MAX_REQUESTS     int           `yaml:"yourmodule_max_requests" env:"YOURMODULE_MAX_REQUESTS"`

    // Shared settings (sử dụng config chung)
    JWT_ISSUER                  string        `yaml:"jwt_issuer" env:"JWT_ISSUER"`
    QUEUE_ENABLED               bool          `yaml:"queue_enabled" env:"QUEUE_ENABLED"`
    EVENT_REDIS_HOST            string        `yaml:"event_redis_host" env:"EVENT_REDIS_HOST"`
}

// NewYourModuleConfigFromAppConfig nạp cấu hình từ centralized config
func NewYourModuleConfigFromAppConfig(appConfig config.Config) (*YourModuleConfig, error) {
    cfg := &YourModuleConfig{
        // Module-specific settings
        YOURMODULE_ENABLED:          appConfig.GetBool("YOURMODULE_ENABLED"),
        YOURMODULE_DEBUG:            appConfig.GetBool("YOURMODULE_DEBUG"),
        YOURMODULE_MESSAGE:          appConfig.GetString("YOURMODULE_MESSAGE"),
        YOURMODULE_DB_HOST:          appConfig.GetString("YOURMODULE_DB_HOST"),
        YOURMODULE_DB_PORT:          appConfig.GetInt("YOURMODULE_DB_PORT"),
        YOURMODULE_API_TIMEOUT:      appConfig.GetDuration("YOURMODULE_API_TIMEOUT"),
        YOURMODULE_MAX_REQUESTS:     appConfig.GetInt("YOURMODULE_MAX_REQUESTS"),

        // Shared settings
        JWT_ISSUER:                  appConfig.GetString("JWT_ISSUER"),
        QUEUE_ENABLED:               appConfig.GetBool("QUEUE_ENABLED"),
        EVENT_REDIS_HOST:            appConfig.GetString("EVENT_REDIS_HOST"),
    }

    // Validation (nếu cần)
    if cfg.YOURMODULE_MESSAGE == "" {
        return nil, fmt.Errorf("cấu hình YourModule thiếu: YOURMODULE_MESSAGE")
    }

    return cfg, nil
}
```

### Bước 2: Thêm Defaults vào Centralized Config

Cập nhật `internal/pkg/config/viperconfig/viper.go` trong function `setGlobalDefaultConfig()`:

```go
func setGlobalDefaultConfig(v *viper.Viper) {
    // ... existing defaults ...

    // YourModule defaults
    v.SetDefault("YOURMODULE_ENABLED", true)
    v.SetDefault("YOURMODULE_DEBUG", false)
    v.SetDefault("YOURMODULE_MESSAGE", "Xin chào từ module YourModule!")
    v.SetDefault("YOURMODULE_DB_HOST", "localhost")
    v.SetDefault("YOURMODULE_DB_PORT", 3306)
    v.SetDefault("YOURMODULE_API_TIMEOUT", 30*time.Second)
    v.SetDefault("YOURMODULE_MAX_REQUESTS", 100)
}
```

### Bước 3: Sử dụng trong Module

Cập nhật `modules/yourmodule/module.go`:

```go
package yourmodule

import (
    "context"
    "wnapi/internal/core"
    "wnapi/modules/yourmodule/internal"
)

func NewModule(app *core.AppBootstrap, config map[string]interface{}) (core.Module, error) {
    logger := app.GetLogger()

    // Đọc cấu hình từ centralized config
    moduleConfig, err := internal.NewYourModuleConfigFromAppConfig(app.GetConfig())
    if err != nil {
        logger.Error("Lỗi đọc cấu hình YourModule", "error", err)
        return nil, err
    }

    // Log cấu hình
    logger.Info("YourModule Config: Enabled=%v, Debug=%v, Message=%s",
        moduleConfig.YOURMODULE_ENABLED, moduleConfig.YOURMODULE_DEBUG, moduleConfig.YOURMODULE_MESSAGE)

    // Backward compatibility (nếu cần)
    if enabled, ok := config["enabled"].(bool); ok {
        moduleConfig.YOURMODULE_ENABLED = enabled
    }

    // Khởi tạo module với config
    return &Module{
        config: moduleConfig,
        logger: logger,
        // ...
    }, nil
}
```

### Bước 4: Cập nhật Environment Files

Thêm vào `.env.example`:

```env
# =============================================================================
# YOURMODULE CONFIGURATION
# =============================================================================
YOURMODULE_ENABLED=true
YOURMODULE_DEBUG=false
YOURMODULE_MESSAGE=Xin chào từ module YourModule!
YOURMODULE_DB_HOST=localhost
YOURMODULE_DB_PORT=3306
YOURMODULE_API_TIMEOUT=30s
YOURMODULE_MAX_REQUESTS=100
```

## 📝 Naming Conventions

### Environment Variable Names

- **Format**: `MODULE_SETTING_NAME`
- **Module Prefix**: Tên module viết HOA + underscore
- **Examples**:
  - `NOTIFICATION_EMAIL_HOST`
  - `PRODUCT_MAX_TITLE_LENGTH`
  - `TENANT_DEFAULT_TIMEZONE`

### Struct Field Names

- **Rule**: Field name PHẢI giống với environment variable name
- **Examples**:
  ```go
  NOTIFICATION_EMAIL_HOST: appConfig.GetString("NOTIFICATION_EMAIL_HOST")
  PRODUCT_MAX_TITLE_LENGTH: appConfig.GetInt("PRODUCT_MAX_TITLE_LENGTH")
  ```

### Shared Configuration

Một số config được chia sẻ giữa các modules:

- `JWT_*` - JWT settings
- `QUEUE_*` - Queue system settings
- `EVENT_*` - Event system settings
- `DB_*` - Database settings (global)

## 🎯 Best Practices

### 1. Configuration Organization

```go
// ✅ GOOD: Nhóm theo chức năng
type ModuleConfig struct {
    // Core settings
    MODULE_ENABLED bool
    MODULE_DEBUG   bool

    // Database settings
    MODULE_DB_HOST string
    MODULE_DB_PORT int

    // API settings
    MODULE_API_TIMEOUT time.Duration
    MODULE_API_RETRIES int

    // Shared settings
    JWT_ISSUER     string
    QUEUE_ENABLED  bool
}

// ❌ BAD: Không nhóm, khó đọc
type ModuleConfig struct {
    MODULE_ENABLED     bool
    JWT_ISSUER         string
    MODULE_DB_HOST     string
    QUEUE_ENABLED      bool
    MODULE_API_TIMEOUT time.Duration
}
```

### 2. Validation

```go
// ✅ GOOD: Validate required fields
func NewModuleConfigFromAppConfig(appConfig config.Config) (*ModuleConfig, error) {
    cfg := &ModuleConfig{
        MODULE_API_KEY: appConfig.GetString("MODULE_API_KEY"),
    }

    if cfg.MODULE_API_KEY == "" {
        return nil, fmt.Errorf("MODULE_API_KEY is required")
    }

    return cfg, nil
}
```

### 3. Type Safety

```go
// ✅ GOOD: Sử dụng đúng type
MODULE_TIMEOUT:      appConfig.GetDuration("MODULE_TIMEOUT")
MODULE_MAX_SIZE:     appConfig.GetInt("MODULE_MAX_SIZE")
MODULE_ENABLED:      appConfig.GetBool("MODULE_ENABLED")

// ❌ BAD: Sử dụng sai type
MODULE_TIMEOUT:      time.Duration(appConfig.GetInt("MODULE_TIMEOUT")) * time.Second
```

### 4. Documentation

```go
// ✅ GOOD: Document các field
type ModuleConfig struct {
    // MODULE_ENABLED: Bật/tắt module
    MODULE_ENABLED bool `yaml:"module_enabled" env:"MODULE_ENABLED"`

    // MODULE_API_TIMEOUT: Timeout cho API calls (default: 30s)
    MODULE_API_TIMEOUT time.Duration `yaml:"module_api_timeout" env:"MODULE_API_TIMEOUT"`
}
```
