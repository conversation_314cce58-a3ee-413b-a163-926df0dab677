# Hướng Dẫn Sử Dụng Migration

## Giới Thiệu

Framework hỗ trợ migration database với cấu trúc module, phân tách các migration theo từng module. Hệ thống sử dụng thư viện `golang-migrate/migrate` để thực hiện các migration.

## Nguyên Tắc SOA và Migration Theo Module

### 1. **<PERSON>ân thủ nguyên tắc SOA**
- **Tính tự trị của service**: Mỗi module quản lý hoàn toàn database schema của riêng mình
- **Chủ quyền service**: Team phát triển module chịu trách nhiệm từ code đến database migration
- **Kết nối lỏng lẻo**: Module không phụ thuộc vào cấu trúc thư mục migration tập trung
- **Triển khai độc lập**: <PERSON><PERSON> thể deploy từng module mà không ảnh hưởng migration của module khác

### 2. **Lợ<PERSON> ích trong phát triển**
- **<PERSON><PERSON><PERSON> lập giữa các team**: Mỗi team có thể phát triển migration độc lập mà không xung đột
- **Đóng gói module**: Migration là phần của module, không tách rời
- **Quản lý phiên bản dễ dàng**: Migration version được quản lý theo từng module
- **Module tự chứa**: Module có thể được tái sử dụng ở các dự án khác với migration đi kèm

## Cấu Trúc Migration

```
migrations/                          # Chỉ chứa system migration
    ├── 000001_init_schema.up.sql
    └── 000001_init_schema.down.sql

modules/                             # Mỗi module có migration riêng
    ├── auth/                        # Module xác thực
    │   ├── module.go
    │   ├── api/
    │   ├── domain/
    │   ├── repository/
    │   ├── dto/
    │   └── migrations/              # Migration trong module
    │       ├── 000001_create_users_table.up.sql
    │       ├── 000001_create_users_table.down.sql
    │       ├── 000002_add_refresh_tokens.up.sql
    │       └── 000002_add_refresh_tokens.down.sql
    │
    └── hello/                       # Module hello
        ├── module.go
        └── migrations/              # Migration trong module
            ├── 000001_create_greetings_table.up.sql
            └── 000001_create_greetings_table.down.sql
```

Thứ tự chạy migration:
1. Các migration trong `/migrations/` (system) sẽ được chạy trước tiên
2. Các migration trong `/modules/<module_name>/migrations/` sẽ được chạy theo thứ tự của module, được xác định bởi phương thức `GetMigrationOrder()` trong module (số nhỏ hơn sẽ chạy trước)

## Lệnh Migration

### Chạy Migration

```bash
# Chạy migration cho project mặc định
make migrate

# Chạy migration cho project cụ thể
make migrate PROJECT=sample
```

### Rollback Migration

```bash
# Rollback migration cho project mặc định
make migrate-down

# Rollback migration cho project cụ thể
make migrate-down PROJECT=sample
```

### Kiểm Tra Version

```bash
# Kiểm tra version migration hiện tại
make migrate-version PROJECT=sample
```

### Tạo Migration Mới

```bash
# Tạo migration mới cho module auth
make create-migration MODULE=auth NAME=add_roles

# Hoặc sử dụng lệnh trực tiếp
go run cmd/migrate/main.go -action=create -module=auth -name=add_roles
```

## Triển Khai Module với Migration

Mỗi module cần triển khai hai phương thức để hỗ trợ migration:

```go
// GetMigrationPath trả về đường dẫn chứa migrations
func (m *Module) GetMigrationPath() string {
    return filepath.Join("modules", "my_module", "migrations")
}

// GetMigrationOrder trả về thứ tự ưu tiên khi chạy migration
func (m *Module) GetMigrationOrder() int {
    return 5 // Số thấp hơn sẽ chạy trước
}
```

## Xử Lý Lỗi Thường Gặp

### 1. Lỗi "module not found in registry"

Lỗi này xảy ra khi module chưa được đăng ký vào registry trước khi chạy migration. Có hai cách khắc phục:

#### Cách 1: Đảm bảo module đã được đăng ký
```go
// Trong file module.go của module
func init() {
    core.RegisterModuleFactory("my_module", NewModule)
}
```

#### Cách 2: Chạy migrate với project cụ thể
```bash
go run cmd/migrate/main.go -project=sample
```

### 2. Lỗi "Dirty database version"

Lỗi này xảy ra khi một migration trước đó bị lỗi và database bị đánh dấu là "dirty". Có 3 cách xử lý:

#### Cách 1: Sửa lỗi và force version
```bash
# Xem version hiện tại
go run cmd/migrate/main.go -project=sample -action=version

# Force version
go run cmd/migrate/main.go -project=sample -action=up -force -version=1
```

#### Cách 2: Sửa thủ công bảng migration
```sql
-- Đối với system migration
UPDATE system_schema_migrations SET dirty = false WHERE version = 1;

-- Đối với module migration
UPDATE module_auth_schema_migrations SET dirty = false WHERE version = 1;
```

#### Cách 3: Drop và tạo lại database (chỉ dùng trong môi trường development)
```sql
DROP DATABASE your_database;
CREATE DATABASE your_database;
```

### 3. Lỗi cú pháp SQL trong migration

Lỗi này thường xảy ra do cú pháp SQL không đúng. Một số lưu ý:

- Không đặt câu lệnh `USE database;` trong file migration
- Chỉ sử dụng một database dialect (MySQL, PostgreSQL) trong mỗi migration
- Kết thúc mỗi câu lệnh SQL bằng dấu chấm phẩy
- Tránh sử dụng các cú pháp đặc biệt của từng loại database

## Thực Hành Tốt

1. **Phân chia migration**:
   - Mỗi migration file chỉ nên thực hiện một thay đổi cụ thể
   - Đặt tên rõ ràng để hiểu mục đích của migration

2. **Bảo đảm idempotence**:
   - Sử dụng các điều kiện `IF EXISTS` hoặc `IF NOT EXISTS`
   - Kiểm tra trước khi thực hiện thay đổi

3. **Bảo đảm tính nhất quán**:
   - Cùng một loại thay đổi nên có cú pháp giống nhau
   - Sử dụng các quy ước đặt tên nhất quán

4. **Quản lý phụ thuộc**:
   - Nếu migration phụ thuộc vào module khác, đảm bảo thiết lập `GetMigrationOrder()` đúng

5. **Kiểm thử**:
   - Luôn kiểm thử migration trước khi commit code
   - Xác minh cả khi chạy up và down migration

6. **Rollback plan**:
   - Đảm bảo file down.sql thực hiện hoàn toàn ngược lại các thay đổi trong up.sql
   - Kiểm tra rollback hoạt động đúng

## Ví Dụ Migration File

### Tạo bảng mới (up.sql)
```sql
-- Migration: create_products
-- Module: catalog
-- Created: 2023-05-23 15:30:45

CREATE TABLE IF NOT EXISTS products (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    price DECIMAL(10, 2) NOT NULL DEFAULT 0.00,
    status ENUM('active', 'inactive', 'draft') DEFAULT 'draft',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_status (status),
    INDEX idx_name (name)
);
```

### Xóa bảng (down.sql)
```sql
-- Rollback: create_products
-- Module: catalog
-- Created: 2023-05-23 15:30:45

DROP TABLE IF EXISTS products;
``` 