# Command Development Guide

Hướng dẫn phát triển CL<PERSON> commands cho hệ thống WNAPI.

## C<PERSON>u trúc Command

### 1. <PERSON><PERSON><PERSON> nghĩa Command

Mỗi command phải tuân theo cấu trúc sau:

```go
type CommandName struct {
    serviceField ServiceInterface
}

func NewCommandName(service ServiceInterface) *cobra.Command {
    cmd := &CommandName{
        serviceField: service,
    }
    
    cobraCmd := &cobra.Command{
        Use:   "command-name",
        Short: "Mô tả ngắn gọn",
        Long:  `<PERSON><PERSON> tả chi tiết về command`,
        Example: `<PERSON><PERSON> dụ sử dụng command`,
        RunE: cmd.run,
    }
    
    // Thêm flags
    cobraCmd.Flags().String("flag-name", "default", "Mô tả flag")
    cobraCmd.MarkFlagRequired("flag-name")
    
    return cobraCmd
}
```

### 2. Triển khai Logic

```go
func (c *CommandName) run(cmd *cobra.Command, args []string) error {
    // L<PERSON>y flags
    flagValue, _ := cmd.Flags().GetString("flag-name")
    
    // Validate input
    if flagValue == "" {
        return fmt.Errorf("flag-name là bắt buộc")
    }
    
    // Gọi service
    result, err := c.serviceField.DoSomething(flagValue)
    if err != nil {
        console.Error(fmt.Sprintf("Lỗi: %v", err))
        return err
    }
    
    // Hiển thị kết quả
    console.Success("Thành công!")
    console.Info(fmt.Sprintf("Kết quả: %v", result))
    
    return nil
}
```

### 3. Đăng ký Command

Trong file `commands/commands.go` của module:

```go
func RegisterModuleCommands(registry *console.Registry, services...) error {
    // Tạo command
    cmd := NewCommandName(service)
    
    // Đăng ký
    registry.RegisterCommand(cmd)
    
    return nil
}
```

## Quy tắc Phát triển

### 1. Naming Convention
- Command name: `kebab-case` (vd: `create-user`, `list-posts`)
- Struct name: `PascalCase` (vd: `CreateUserCommand`)
- Function name: `camelCase` (vd: `NewCreateUserCommand`)

### 2. Flags
- Sử dụng `kebab-case` cho flag names
- Đánh dấu required flags bằng `MarkFlagRequired()`
- Cung cấp default values hợp lý
- Viết mô tả rõ ràng cho mỗi flag

### 3. Error Handling
- Validate tất cả input trước khi xử lý
- Trả về error messages có ý nghĩa
- Sử dụng `console.Error()` để hiển thị lỗi
- Log errors với context đầy đủ

### 4. Output
- Sử dụng `console.Success()` cho thông báo thành công
- Sử dụng `console.Info()` cho thông tin bổ sung
- Sử dụng `console.Warning()` cho cảnh báo
- Format output nhất quán

### 5. Documentation
- Viết `Short` description ngắn gọn
- Viết `Long` description chi tiết
- Cung cấp `Example` thực tế
- Document tất cả flags

## Ví dụ Hoàn chỉnh

```go
// create_post.go
package commands

import (
    "fmt"
    "github.com/spf13/cobra"
    "wnapi/internal/pkg/console"
    "wnapi/modules/blog/service"
)

type CreatePostCommand struct {
    postService service.PostService
}

func NewCreatePostCommand(postService service.PostService) *cobra.Command {
    cmd := &CreatePostCommand{
        postService: postService,
    }
    
    cobraCmd := &cobra.Command{
        Use:   "create-post",
        Short: "Tạo bài viết mới",
        Long: `Tạo bài viết mới với các thông tin:
- Title: Tiêu đề bài viết
- Content: Nội dung bài viết
- Category: Danh mục bài viết`,
        Example: `  # Tạo bài viết
  wnapi create-post --title="My Post" --content="Content here" --category="tech"`,
        RunE: cmd.run,
    }
    
    // Thêm flags
    cobraCmd.Flags().String("title", "", "Tiêu đề bài viết (bắt buộc)")
    cobraCmd.Flags().String("content", "", "Nội dung bài viết (bắt buộc)")
    cobraCmd.Flags().String("category", "general", "Danh mục (mặc định: general)")
    cobraCmd.Flags().Bool("published", false, "Xuất bản ngay (mặc định: false)")
    
    // Required flags
    cobraCmd.MarkFlagRequired("title")
    cobraCmd.MarkFlagRequired("content")
    
    return cobraCmd
}

func (c *CreatePostCommand) run(cmd *cobra.Command, args []string) error {
    // Lấy flags
    title, _ := cmd.Flags().GetString("title")
    content, _ := cmd.Flags().GetString("content")
    category, _ := cmd.Flags().GetString("category")
    published, _ := cmd.Flags().GetBool("published")
    
    // Validate
    if len(title) < 3 {
        return fmt.Errorf("tiêu đề phải có ít nhất 3 ký tự")
    }
    
    // Tạo bài viết
    post, err := c.postService.CreatePost(title, content, category, published)
    if err != nil {
        console.Error(fmt.Sprintf("Lỗi tạo bài viết: %v", err))
        return err
    }
    
    // Hiển thị kết quả
    console.Success("Bài viết đã được tạo thành công!")
    console.Info(fmt.Sprintf("ID: %d", post.ID))
    console.Info(fmt.Sprintf("Tiêu đề: %s", post.Title))
    console.Info(fmt.Sprintf("Trạng thái: %s", post.Status))
    
    return nil
}
```

## Best Practices

1. **Separation of Concerns**: Command chỉ xử lý CLI logic, business logic để trong service
2. **Consistent Interface**: Tất cả commands trong module nên có interface tương tự
3. **Error Messages**: Viết error messages bằng tiếng Việt, rõ ràng và hữu ích
4. **Testing**: Viết unit tests cho command logic
5. **Documentation**: Document command trong module README

## Module Integration

Đảm bảo module được import trong `cmd/cli/main.go`:

```go
import (
    _ "wnapi/modules/your_module" // Import để đăng ký commands
)
```