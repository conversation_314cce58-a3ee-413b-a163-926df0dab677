├── cmd/
│   └── server/
│       └── main.go                         # Entry point chính
│
├── config/
│   ├── app.yaml                            # Cấu hình chung ứng dụng
│   ├── db.yaml                             # Cấu hình database
│   └── modules.yaml                        # Cấu hình kích hoạt module
│
├── modules/                                # Modules chức năng được nhóm theo domain
│   │
│   ├── core/                               # Nhóm modules cốt lõi hệ thống
│   │   ├── auth/                           # Module xác thực và phân quyền
│   │   │   ├── module.go
│   │   │   ├── api/
│   │   │   │   ├── handler.go
│   │   │   │   ├── middleware.go
│   │   │   │   └── router.go
│   │   │   ├── domain/
│   │   │   │   ├── model.go
│   │   │   │   └── service.go
│   │   │   ├── repository/
│   │   │   │   ├── repository.go
│   │   │   │   └── mysql_repository.go
│   │   │   ├── dto/
│   │   │   │   └── auth_dto.go
│   │   │   └── migrations/
│   │   │       └── 001_create_auth_tables.sql
│   │   │
│   │   ├── user/                           # Module quản lý người dùng
│   │   │   ├── module.go
│   │   │   ├── api/
│   │   │   ├── domain/
│   │   │   ├── repository/
│   │   │   ├── dto/
│   │   │   └── migrations/
│   │   │
│   │   ├── tenant/                         # Module quản lý tenant
│   │   │   ├── module.go
│   │   │   ├── api/
│   │   │   │   ├── handler.go
│   │   │   │   ├── middleware.go
│   │   │   │   └── router.go
│   │   │   ├── domain/
│   │   │   │   ├── model.go              # Tenant, TenantSettings, TenantPlan
│   │   │   │   └── service.go
│   │   │   ├── repository/
│   │   │   ├── dto/
│   │   │   └── migrations/
│   │   │
│   │   └── website/                        # Module quản lý nhiều website
│   │       ├── module.go
│   │       ├── api/
│   │       │   ├── handler.go
│   │       │   ├── middleware.go          # Website identification middleware
│   │       │   └── router.go
│   │       ├── domain/
│   │       │   ├── model.go              # Website, WebsiteModule, WebsiteMenu
│   │       │   └── service.go
│   │       ├── repository/
│   │       ├── dto/
│   │       └── migrations/
│   │
│   ├── content/                            # Nhóm modules quản lý nội dung
│   │   ├── blog/                           # Module blog
│   │   │   ├── module.go
│   │   │   ├── api/
│   │   │   │   ├── handler.go
│   │   │   │   ├── post_handler.go
│   │   │   │   ├── category_handler.go
│   │   │   │   ├── comment_handler.go
│   │   │   │   ├── tag_handler.go
│   │   │   │   ├── middleware.go
│   │   │   │   └── router.go
│   │   │   ├── domain/
│   │   │   │   ├── model.go
│   │   │   │   ├── post.go               # Post model
│   │   │   │   ├── category.go           # Category model
│   │   │   │   ├── comment.go            # Comment model
│   │   │   │   ├── tag.go                # Tag model
│   │   │   │   ├── service.go
│   │   │   │   ├── post_service.go
│   │   │   │   ├── category_service.go
│   │   │   │   ├── comment_service.go
│   │   │   │   └── tag_service.go
│   │   │   ├── repository/
│   │   │   │   ├── repository.go
│   │   │   │   ├── post_repository.go
│   │   │   │   ├── category_repository.go
│   │   │   │   ├── comment_repository.go
│   │   │   │   ├── tag_repository.go
│   │   │   │   ├── mysql/
│   │   │   │   │   ├── post_repository.go
│   │   │   │   │   ├── category_repository.go
│   │   │   │   │   ├── comment_repository.go
│   │   │   │   │   └── tag_repository.go
│   │   │   │   └── cache/
│   │   │   │       ├── post_cache.go
│   │   │   │       └── category_cache.go
│   │   │   ├── dto/
│   │   │   │   ├── post_dto.go
│   │   │   │   ├── category_dto.go
│   │   │   │   ├── comment_dto.go
│   │   │   │   ├── tag_dto.go
│   │   │   │   └── filter_dto.go
│   │   │   └── migrations/
│   │   │       ├── 001_create_post_tables.sql
│   │   │       ├── 002_create_category_tables.sql
│   │   │       ├── 003_create_tag_tables.sql
│   │   │       ├── 004_create_comment_tables.sql
│   │   │       └── 005_create_post_tag_tables.sql
│   │   │
│   │   ├── cms/                            # Module quản lý nội dung tổng quát
│   │   │   ├── module.go
│   │   │   ├── api/
│   │   │   ├── domain/
│   │   │   ├── repository/
│   │   │   ├── dto/
│   │   │   └── migrations/
│   │   │
│   │   ├── media/                          # Module quản lý file media
│   │   │   ├── module.go
│   │   │   ├── api/
│   │   │   ├── domain/
│   │   │   ├── repository/
│   │   │   ├── dto/
│   │   │   └── migrations/
│   │   │
│   │   └── theme/                          # Module quản lý theme/giao diện
│   │       ├── module.go
│   │       ├── api/
│   │       ├── domain/
│   │       │   ├── model.go              # Theme, ThemeTemplate, ThemeAsset
│   │       │   └── service.go
│   │       ├── repository/
│   │       ├── dto/
│   │       └── migrations/
│   │
│   ├── ecommerce/                          # Nhóm modules thương mại điện tử
│   │   ├── product/                        # Module quản lý sản phẩm
│   │   │   ├── module.go
│   │   │   ├── api/
│   │   │   │   ├── handler.go
│   │   │   │   ├── middleware.go
│   │   │   │   └── router.go
│   │   │   ├── domain/
│   │   │   │   ├── model.go              # Product, Category, Attribute
│   │   │   │   └── service.go
│   │   │   ├── repository/
│   │   │   │   ├── repository.go
│   │   │   │   └── mysql_repository.go
│   │   │   ├── dto/
│   │   │   │   ├── product_dto.go
│   │   │   │   └── filter_dto.go
│   │   │   └── migrations/
│   │   │       ├── 001_create_products.sql
│   │   │       └── 002_create_categories.sql
│   │   │
│   │   ├── cart/                           # Module giỏ hàng
│   │   │   ├── module.go
│   │   │   ├── api/
│   │   │   ├── domain/
│   │   │   │   ├── model.go              # Cart, CartItem
│   │   │   │   └── service.go
│   │   │   ├── repository/
│   │   │   ├── dto/
│   │   │   └── migrations/
│   │   │
│   │   ├── order/                          # Module đơn hàng
│   │   │   ├── module.go
│   │   │   ├── api/
│   │   │   ├── domain/
│   │   │   │   ├── model.go              # Order, OrderItem, OrderStatus
│   │   │   │   └── service.go
│   │   │   ├── repository/
│   │   │   ├── dto/
│   │   │   │   ├── order_dto.go
│   │   │   │   └── order_filter_dto.go
│   │   │   └── migrations/
│   │   │       ├── 001_create_orders.sql
│   │   │       └── 002_create_order_items.sql
│   │   │
│   │   ├── shipping/                       # Module vận chuyển
│   │   │   ├── module.go
│   │   │   ├── api/
│   │   │   ├── domain/
│   │   │   │   ├── model.go              # ShippingMethod, ShippingZone
│   │   │   │   └── service.go
│   │   │   ├── repository/
│   │   │   ├── dto/
│   │   │   └── migrations/
│   │   │
│   │   ├── discount/                       # Module khuyến mãi
│   │   │   ├── module.go
│   │   │   ├── api/
│   │   │   ├── domain/
│   │   │   │   ├── model.go              # Coupon, Discount
│   │   │   │   └── service.go
│   │   │   ├── repository/
│   │   │   ├── dto/
│   │   │   └── migrations/
│   │   │
│   │   └── inventory/                      # Module quản lý kho
│   │       ├── module.go
│   │       ├── api/
│   │       ├── domain/
│   │       ├── repository/
│   │       ├── dto/
│   │       └── migrations/
│   │
│   ├── marketing/                          # Nhóm modules marketing
│   │   ├── email/                          # Module email marketing
│   │   │   ├── module.go
│   │   │   ├── api/
│   │   │   ├── domain/
│   │   │   ├── repository/
│   │   │   ├── dto/
│   │   │   └── migrations/
│   │   │
│   │   ├── sms/                            # Module SMS marketing
│   │   │   ├── module.go
│   │   │   ├── api/
│   │   │   ├── domain/
│   │   │   ├── repository/
│   │   │   ├── dto/
│   │   │   └── migrations/
│   │   │
│   │   ├── campaign/                       # Module quản lý chiến dịch
│   │   │   ├── module.go
│   │   │   ├── api/
│   │   │   ├── domain/
│   │   │   ├── repository/
│   │   │   ├── dto/
│   │   │   └── migrations/
│   │   │
│   │   └── newsletter/                     # Module newsletter
│   │       ├── module.go
│   │       ├── api/
│   │       ├── domain/
│   │       ├── repository/
│   │       ├── dto/
│   │       └── migrations/
│   │
│   ├── analytics/                          # Nhóm modules phân tích
│   │   ├── tracking/                       # Module tracking hành vi
│   │   │   ├── module.go
│   │   │   ├── api/
│   │   │   ├── domain/
│   │   │   ├── repository/
│   │   │   ├── dto/
│   │   │   └── migrations/
│   │   │
│   │   ├── report/                         # Module báo cáo
│   │   │   ├── module.go
│   │   │   ├── api/
│   │   │   ├── domain/
│   │   │   ├── repository/
│   │   │   ├── dto/
│   │   │   └── migrations/
│   │   │
│   │   └── dashboard/                      # Module dashboard
│   │       ├── module.go
│   │       ├── api/
│   │       ├── domain/
│   │       ├── repository/
│   │       ├── dto/
│   │       └── migrations/
│   │
│   └── integration/                        # Nhóm modules tích hợp bên thứ ba
│       ├── notification/                   # Module thông báo tổng hợp
│       │   ├── module.go
│       │   ├── api/
│       │   ├── domain/
│       │   ├── repository/
│       │   ├── dto/
│       │   └── migrations/
│       │
│       ├── sync/                           # Module đồng bộ dữ liệu
│       │   ├── module.go
│       │   ├── api/
│       │   ├── domain/
│       │   ├── repository/
│       │   ├── dto/
│       │   └── migrations/
│       │
│       └── webhook/                        # Module webhook
│           ├── module.go
│           ├── api/
│           ├── domain/
│           ├── repository/
│           ├── dto/
│           └── migrations/
│
├── plugins/                                # Hệ thống plugin mở rộng
│   ├── registry.go                         # Registry quản lý plugins
│   ├── interface.go                        # Interface định nghĩa plugin
│   │
│   ├── payment/                            # Nhóm plugin thanh toán
│   │   ├── stripe/                         # Plugin Stripe
│   │   │   ├── plugin.go
│   │   │   ├── handler.go
│   │   │   └── service.go
│   │   │
│   │   ├── paypal/                         # Plugin PayPal
│   │   │   ├── plugin.go
│   │   │   ├── handler.go
│   │   │   └── service.go
│   │   │
│   │   ├── momo/                           # Plugin MoMo (VN)
│   │   │   ├── plugin.go
│   │   │   ├── handler.go
│   │   │   └── service.go
│   │   │
│   │   └── vnpay/                          # Plugin VNPay (VN)
│   │       ├── plugin.go
│   │       ├── handler.go
│   │       └── service.go
│   │
│   ├── social/                             # Nhóm plugin social login
│   │   ├── google/                         # Plugin Google OAuth
│   │   │   ├── plugin.go
│   │   │   ├── handler.go
│   │   │   └── service.go
│   │   │
│   │   ├── facebook/                       # Plugin Facebook OAuth
│   │   │   ├── plugin.go
│   │   │   ├── handler.go
│   │   │   └── service.go
│   │   │
│   │   └── zalo/                           # Plugin Zalo OAuth (VN)
│   │       ├── plugin.go
│   │       ├── handler.go
│   │       └── service.go
│   │
│   ├── storage/                            # Nhóm plugin lưu trữ
│   │   ├── aws_s3/                         # Plugin AWS S3
│   │   │   ├── plugin.go
│   │   │   ├── handler.go
│   │   │   └── service.go
│   │   │
│   │   ├── cloudinary/                     # Plugin Cloudinary
│   │   │   ├── plugin.go
│   │   │   ├── handler.go
│   │   │   └── service.go
│   │   │
│   │   └── local/                          # Plugin lưu trữ local
│   │       ├── plugin.go
│   │       ├── handler.go
│   │       └── service.go
│   │
│   └── notification/                       # Nhóm plugin thông báo
│       ├── firebase/                       # Plugin Firebase Push
│       │   ├── plugin.go
│       │   ├── handler.go
│       │   └── service.go
│       │
│       ├── onesignal/                      # Plugin OneSignal
│       │   ├── plugin.go
│       │   ├── handler.go
│       │   └── service.go
│       │
│       └── telegram/                       # Plugin Telegram Bot
│           ├── plugin.go
│           ├── handler.go
│           └── service.go
│
├── internal/                               # Package dùng chung nội bộ
│   ├── core/                               # Core framework
│   │   ├── app.go                          # App initialization
│   │   ├── module.go                       # Module interface & registry
│   │   ├── plugin.go                       # Plugin loader
│   │   ├── config.go                       # Config loader
│   │   └── server.go                       # HTTP server
│   │
│   ├── database/                           # Database utilities
│   │   ├── connection.go                   # DB connection manager
│   │   ├── migration.go                    # Migration utilities
│   │   ├── tenant_context.go               # Tenant context modifier
│   │   └── website_context.go              # Website context modifier
│   │
│   ├── middleware/                         # Shared middleware
│   │   ├── auth.go                         # Authentication middleware
│   │   ├── logger.go                       # Logging middleware
│   │   ├── cors.go                         # CORS middleware
│   │   └── rate_limit.go                   # Rate limiting middleware
│   │
│   └── pkg/                                # Shared utilities
│       ├── logger/                         # Logging utilities
│       ├── errors/                         # Error handling
│       ├── validator/                      # Validation utilities
│       ├── context/                        # Context utilities
│       ├── response/                       # Response formatting
│       ├── cache/                          # Cache utilities
│       ├── queue/                          # Queue utilities
│       └── utils/                          # Misc utilities
│
├── projects/                               # Định nghĩa dự án cụ thể
│   ├── blog_site/                          # Dự án blog
│   │   ├── config.yaml                     # Cấu hình ứng dụng
│   │   └── modules.yaml                    # Kích hoạt: core, content/blog
│   │
│   ├── ecommerce/                          # Dự án thương mại điện tử
│   │   ├── config.yaml
│   │   └── modules.yaml                    # Kích hoạt: core, ecommerce/*
│   │
│   ├── multi_tenant/                       # Dự án multi-tenant
│   │   ├── config.yaml
│   │   └── modules.yaml                    # Kích hoạt: core/*, content/*, ecommerce/*
│   │
│   ├── multi_website/                      # Dự án multi-website
│   │   ├── config.yaml
│   │   └── modules.yaml                    # Kích hoạt tất cả modules
│   │
│   └── saas_platform/                      # Dự án SaaS platform đầy đủ
│       ├── config.yaml
│       └── modules.yaml                    # Kích hoạt tất cả modules và plugins
│
├── migrations/                             # Migration chung toàn hệ thống
│   └── init_schema.sql                     # Initial schema setup
│
├── scripts/                                # Utility scripts
│   ├── start.sh                            # Script start ứng dụng
│   ├── build.sh                            # Script build
│   ├── test.sh                             # Script test
│   ├── migrate.sh                          # Script migration
│   ├── create-module.sh                    # Script tạo module mới
│   └── deploy.sh                           # Script deployment
│
├── tests/                                  # Tests
│   ├── unit/                               # Unit tests
│   │   ├── core/
│   │   ├── content/
│   │   ├── ecommerce/
│   │   └── marketing/
│   ├── integration/                        # Integration tests
│   │   ├── api/
│   │   └── database/
│   └── e2e/                                # End-to-end tests
│       ├── blog/
│       └── ecommerce/
│
├── docs/                                   # Tài liệu
│   ├── architecture.md                     # Tài liệu kiến trúc
│   ├── modules/                            # Tài liệu từng nhóm module
│   │   ├── core.md
│   │   ├── content.md
│   │   ├── ecommerce.md
│   │   ├── marketing.md
│   │   └── analytics.md
│   ├── api/                                # API documentation
│   │   ├── swagger.yaml                    # Swagger API specs
│   │   └── postman/                        # Postman collections
│   └── guides/                             # Developer guides
│       ├── getting-started.md
│       ├── module-development.md
│       ├── plugin-development.md
│       └── deployment.md
│
├── go.mod                                  # Go modules
├── go.sum                                  # Go modules checksums
├── Makefile                                # Build automation
├── Dockerfile                              # Docker configuration
├── docker-compose.yml                      # Docker Compose dev
├── docker-compose.prod.yml                 # Docker Compose prod
└── README.md                               # Tài liệu dự án chính