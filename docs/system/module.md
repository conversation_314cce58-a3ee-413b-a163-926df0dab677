# Hướng dẫn Tạo Module Mới với FX System

## Tổng quan

Hệ thống module hiện tại sử dụng **FX (Uber's dependency injection framework)** để quản lý lifecycle và dependencies. Mỗi module được thiết kế theo pattern modular, có thể load/unload độc lập và tự quản lý dependencies.

## Cấu trúc Module Chuẩn

```
modules/auth/
├── fx.go                        # FX module definition và registration
├── providers.go                 # FX providers (constructors)
├── routes.go                    # FX-compatible route registration
├── bootstrap.go                 # Legacy bootstrap (backward compatibility)
├── module.go                    # Legacy module definition (backward compatibility)
├── internal/                    # Định nghĩa cấu hình và các kiểu dữ liệu nội bộ
│   ├── config.go                # Cấu hình module
│   └── types.go                 # Định nghĩa types, interfaces và xử lý lỗi
├── dto/                         # Data Transfer Objects
│   ├── auth.go                  # DTO cơ bản
│   ├── login.go                 # DTO đăng nhập
│   └── register.go              # DTO đăng ký
├── repository/                  # Repository implementations
│   ├── interfaces.go            # Repository interfaces
│   └── mysql/                   # MySQL implementations
│       └── auth_repository.go   # MySQL repository
├── service/                     # Service implementations
│   ├── interfaces.go            # Service interfaces
│   └── auth_service.go          # Service implementation
├── api/                         # API handlers
│   ├── handler.go               # Main API handler
│   └── handlers/                # Specific handlers
│       └── admin_auth_handler.go # Admin auth handler
├── migrations/                  # SQL migrations
│   ├── 001_create_users.up.sql           # Migration tạo bảng
│   └── 001_create_users.down.sql         # Migration rollback
├── queue/                       # Queue handlers (optional)
│   └── handlers.go              # Queue task handlers
└── events/                      # Event handlers (optional)
    └── handlers.go              # Event handlers
```

## Giải thích Chi Tiết Từng File

### 1. File `fx.go` - FX Module Definition

**Nhiệm vụ**: Định nghĩa FX module và đăng ký với hệ thống FX.

**Ví dụ**:
```go
package auth

import (
	"go.uber.org/fx"
	"wnapi/internal/config"
	"wnapi/internal/pkg/logger"
	"wnapi/modules/auth/api"
	"wnapi/modules/auth/internal"
	"wnapi/modules/auth/service"
)

// Module returns the FX module for auth
func Module() fx.Option {
	return fx.Module("auth",
		// Providers - tạo dependencies
		fx.Provide(
			NewAuthConfig,
			NewAuthRepository,
			NewAuthService,
			NewAuthHandler,
		),

		// Route registration using FX handler
		fx.Invoke(RegisterAuthRoutes),
	)
}
```

### 2. File `providers.go` - FX Providers

**Nhiệm vụ**: Chứa các constructor functions (providers) cho FX dependency injection.

**Ví dụ**:
```go
package auth

import (
	"wnapi/internal/config"
	"wnapi/internal/database"
	"wnapi/internal/pkg/logger"
	"wnapi/modules/auth/api"
	"wnapi/modules/auth/internal"
	"wnapi/modules/auth/repository/mysql"
	"wnapi/modules/auth/service"
)

// NewAuthConfig creates auth configuration
func NewAuthConfig(cfg config.Config) *internal.AuthConfig {
	accessExpiry := cfg.GetDuration("JWT_ACCESS_TOKEN_EXPIRATION")
	refreshExpiry := cfg.GetDuration("JWT_REFRESH_TOKEN_EXPIRATION")

	return &internal.AuthConfig{
		JWTSecret:          cfg.GetString("JWT_ACCESS_SIGNING_KEY"),
		AccessTokenExpiry:  accessExpiry,
		RefreshTokenExpiry: refreshExpiry,
		Issuer:             cfg.GetString("JWT_ISSUER"),
		Message:            cfg.GetStringWithDefault("AUTH_MESSAGE", "Hello from Auth module!"),
		MaxSessionsPerUser: cfg.GetIntWithDefault("AUTH_MAX_SESSIONS_PER_USER", 10),
	}
}

// NewAuthRepository creates auth repository
func NewAuthRepository(dbManager *database.Manager, log logger.Logger) (internal.Repository, error) {
	return mysql.NewRepository(dbManager, log)
}

// NewAuthService creates auth service with dependencies
func NewAuthService(
	repo internal.Repository,
	config *internal.AuthConfig,
	log logger.Logger,
) service.AuthService {
	return service.New(repo, *config, log)
}

// NewAuthHandler creates auth API handler
func NewAuthHandler(authService service.AuthService) *api.Handler {
	return api.NewHandler(authService)
}
```

### 3. File `routes.go` - FX-Compatible Route Registration

**Nhiệm vụ**: Đăng ký routes với Gin engine sử dụng FX handler.

**Ví dụ**:
```go
package auth

import (
	"wnapi/internal/pkg/logger"
	"wnapi/modules/auth/api"
	"github.com/gin-gonic/gin"
)

// RegisterAuthRoutes registers auth routes with Gin engine
func RegisterAuthRoutes(engine *gin.Engine, handler *api.Handler, log logger.Logger) {
	log.Info("Registering auth routes")

	// Use existing route registration logic
	if err := handler.RegisterRoutes(engine); err != nil {
		log.Error("Failed to register auth routes", "error", err)
		return
	}

	log.Info("Auth routes registered successfully")
}
```

### 4. Files `bootstrap.go` và `module.go` - Legacy Support

**Nhiệm vụ**: Hỗ trợ backward compatibility với hệ thống cũ (không sử dụng trong FX system).

**Lưu ý**: Các file này được giữ lại để tương thích ngược nhưng không được sử dụng trong hệ thống FX hiện tại.

### 3. Thư mục `internal/`
**Nhiệm vụ**: Chứa cấu hình và các kiểu dữ liệu nội bộ của module.

#### File `config.go`
**Ví dụ**:
```go
package internal

import (
	"fmt"
	"log"
	"os"
	"time"

	"github.com/spf13/viper"
)

// LoadAuthConfig đọc cấu hình auth từ biến môi trường
func LoadAuthConfig() (*AuthConfig, error) {
	// Khởi tạo Viper
	v := viper.New()
	v.AutomaticEnv()
	v.SetEnvPrefix("AUTH")

	// Tải file .env nếu có
	if _, err := os.Stat(".env"); err == nil {
		v.SetConfigFile(".env")
		if err := v.ReadInConfig(); err != nil {
			log.Printf("Cảnh báo: không thể tải file .env: %v", err)
		}
	}

	// Thiết lập giá trị mặc định
	v.SetDefault("JWT_SECRET", "default_jwt_secret_change_me_in_production")
	v.SetDefault("ACCESS_TOKEN_EXPIRY", "15m")
	v.SetDefault("REFRESH_TOKEN_EXPIRY", "168h")
	v.SetDefault("MESSAGE", "Xin chào từ module Auth!")

	// Khởi tạo config từ Viper
	cfg := &AuthConfig{
		JWTSecret:          v.GetString("JWT_SECRET"),
		AccessTokenExpiry:  v.GetDuration("ACCESS_TOKEN_EXPIRY"),
		RefreshTokenExpiry: v.GetDuration("REFRESH_TOKEN_EXPIRY"),
		Message:            v.GetString("MESSAGE"),
	}

	return cfg, nil
}
```

#### File `types.go`
**Ví dụ**:
```go
package internal

import (
	"context"
	"net/http"
	"time"
	"wnapi/modules/auth/dto"
)

// AuthConfig chứa cấu hình auth service
type AuthConfig struct {
	JWTSecret          string        `yaml:"jwt_secret" env:"JWT_SECRET"`
	AccessTokenExpiry  time.Duration `yaml:"access_token_expiry" env:"ACCESS_TOKEN_EXPIRY" envDefault:"15m"`
	RefreshTokenExpiry time.Duration `yaml:"refresh_token_expiry" env:"REFRESH_TOKEN_EXPIRY" envDefault:"168h"`
	Message            string        `env:"MESSAGE" envDefault:"Xin chào từ module Auth!"`
}

// ServiceError định nghĩa các lỗi service
type ServiceError string

const (
	// ErrInvalidCredentials là lỗi khi thông tin đăng nhập không hợp lệ
	ErrInvalidCredentials ServiceError = "invalid_credentials"
	// ErrUserAlreadyExists là lỗi khi user đã tồn tại
	ErrUserAlreadyExists ServiceError = "user_already_exists"
	// ErrInvalidToken là lỗi khi token không hợp lệ
	ErrInvalidToken ServiceError = "invalid_token"
	// ErrExpiredToken là lỗi khi token đã hết hạn
	ErrExpiredToken ServiceError = "expired_token"
	// ErrUserNotFound là lỗi khi không tìm thấy user
	ErrUserNotFound ServiceError = "user_not_found"
	// ErrTokenNotFound là lỗi khi không tìm thấy token
	ErrTokenNotFound ServiceError = "token_not_found"
	// ErrInvalidPassword là lỗi khi mật khẩu không hợp lệ
	ErrInvalidPassword ServiceError = "invalid_password"
)

// ErrorResponse định nghĩa cấu trúc phản hồi lỗi
type ErrorResponse struct {
	StatusCode int    // HTTP status code
	Message    string // Thông báo lỗi hiển thị cho người dùng
	ErrorCode  string // Mã lỗi nội bộ
}

// ErrorMap ánh xạ ServiceError với thông tin phản hồi lỗi tương ứng
var ErrorMap = map[ServiceError]ErrorResponse{
	ErrInvalidCredentials: {
		StatusCode: http.StatusUnauthorized,
		Message:    "Thông tin đăng nhập không hợp lệ",
		ErrorCode:  "INVALID_CREDENTIALS",
	},
	ErrUserAlreadyExists: {
		StatusCode: http.StatusConflict,
		Message:    "Người dùng đã tồn tại",
		ErrorCode:  "USER_ALREADY_EXISTS",
	},
	// Các lỗi khác...
}

func (e ServiceError) Error() string {
	return string(e)
}

// GetErrorResponse trả về thông tin phản hồi lỗi dựa trên ServiceError
func GetErrorResponse(err error) ErrorResponse {
	if serviceErr, ok := err.(ServiceError); ok {
		if resp, exists := ErrorMap[serviceErr]; exists {
			return resp
		}
	}

	// Mặc định trả về lỗi hệ thống nếu không tìm thấy lỗi trong map
	return ErrorResponse{
		StatusCode: http.StatusInternalServerError,
		Message:    "Lỗi hệ thống",
		ErrorCode:  "INTERNAL_ERROR",
	}
}

// AuthService định nghĩa interface cho auth service
type AuthService interface {
	Register(ctx context.Context, req dto.RegisterRequest) (*UserInfo, error)
	Login(ctx context.Context, req dto.LoginRequest) (*dto.LoginResponse, error)
	RefreshToken(ctx context.Context, refreshToken string) (*dto.LoginResponse, error)
	ValidateToken(tokenString string) (map[string]interface{}, error)
}

// Các định nghĩa khác...
```

### 4. Thư mục `dto/`
**Nhiệm vụ**: Định nghĩa các Data Transfer Objects (DTOs) để giao tiếp API.

**Ví dụ** (`login.go`):
```go
package dto

// LoginRequest chứa thông tin đăng nhập
type LoginRequest struct {
	Email      string `json:"email" binding:"required,email"`
	Password   string `json:"password" binding:"required"`
	AdminLogin bool   `json:"-"` // Field nội bộ, không xuất hiện trong JSON
}

// LoginResponse chứa thông tin trả về sau khi đăng nhập
type LoginResponse struct {
	AccessToken           string `json:"access_token"`
	AccessTokenExpiresIn  int    `json:"access_token_expires_in"`
	RefreshToken          string `json:"refresh_token"`
	RefreshTokenExpiresIn int    `json:"refresh_token_expires_in"`
	TokenType             string `json:"token_type"`
	UserID                int64  `json:"user_id"`
	Email                 string `json:"email"`
	TenantID              int    `json:"tenant_id"`
}
```

### 5. Thư mục `repository/`
**Nhiệm vụ**: Triển khai các repository interfaces để tương tác với cơ sở dữ liệu.

**Ví dụ** (`repository.go`):
```go
package repository

import (
	"context"
	"database/sql"
	"errors"
	"time"

	"wnapi/internal/database"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/tracing"
	"wnapi/modules/auth/internal"

	"github.com/google/uuid"
	"github.com/jmoiron/sqlx"
	"go.opentelemetry.io/otel/attribute"
	"golang.org/x/crypto/bcrypt"
)

// mysqlRepository triển khai Repository interface sử dụng sqlx
type mysqlRepository struct {
	db     *sqlx.DB
	logger logger.Logger
}

// NewMySQLRepository tạo một repository mới
func NewMySQLRepository(dbManager *database.Manager, logger logger.Logger) (internal.Repository, error) {
	if dbManager == nil {
		return nil, errors.New("database manager không được để trống")
	}

	db := dbManager.GetDB()
	if db == nil {
		return nil, errors.New("không thể kết nối tới cơ sở dữ liệu")
	}

	return &mysqlRepository{
		db:     db,
		logger: logger,
	}, nil
}

// GetUserByEmail lấy thông tin người dùng theo email
func (r *mysqlRepository) GetUserByEmail(ctx context.Context, email string) (*internal.User, error) {
	// Tạo span cho truy vấn database
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "auth_users")
	defer span.End()

	// Thêm thông tin về operation
	tracing.AddSpanAttributes(ctx,
		attribute.String("db.table", "auth_users"),
		attribute.String("db.operation", "get_user_by_email"),
		attribute.String("auth.email", email),
	)

	var user internal.User
	query := `SELECT id, username, email, password_hash, full_name, is_active, created_at, updated_at
              FROM auth_users WHERE email = ?`

	tracing.AddSpanAttributes(ctx, tracing.DBStatement(query))

	err := r.db.GetContext(ctx, &user, query, email)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			tracing.AddSpanAttributes(ctx, attribute.Bool("db.record_found", false))
			return nil, internal.ErrUserNotFound
		}
		r.logger.Error("Không thể lấy thông tin người dùng", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return nil, err
	}

	tracing.AddSpanAttributes(ctx,
		attribute.Bool("db.record_found", true),
		attribute.Int("auth.user_id", user.ID),
		attribute.String("auth.username", user.Username),
	)

	return &user, nil
}

// Các phương thức khác...
```

### 6. Thư mục `service/`
**Nhiệm vụ**: Triển khai các service interfaces, chứa business logic.

**Ví dụ** (`auth_service.go`):
```go
package service

import (
	"context"
	"errors"
	"fmt"
	"time"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/tracing"
	"wnapi/modules/auth/dto"
	"wnapi/modules/auth/internal"

	"github.com/golang-jwt/jwt/v4"
	"github.com/google/uuid"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"golang.org/x/crypto/bcrypt"
)

// Service triển khai AuthService interface
type Service struct {
	repo   internal.Repository
	config internal.AuthConfig
	logger logger.Logger
}

// NewService tạo một auth service mới
func NewService(repo internal.Repository, config internal.AuthConfig, log logger.Logger) internal.AuthService {
	// Đặt giá trị mặc định
	if config.AccessTokenExpiry == 0 {
		config.AccessTokenExpiry = 15 * time.Minute
	}
	if config.RefreshTokenExpiry == 0 {
		config.RefreshTokenExpiry = 7 * 24 * time.Hour // 7 ngày
	}

	return &Service{
		repo:   repo,
		config: config,
		logger: log,
	}
}

// Login xác thực người dùng và trả về token
func (s *Service) Login(ctx context.Context, req dto.LoginRequest) (*dto.LoginResponse, error) {
	// Tạo span cho toàn bộ quá trình đăng nhập
	ctx, span := tracing.StartSpan(ctx, "auth-service", "login")
	defer span.End()

	// Thêm thuộc tính vào span (che dấu thông tin nhạy cảm)
	tracing.AddSpanAttributes(ctx,
		attribute.String("auth.email", req.Email),
		attribute.Bool("auth.admin_login", req.AdminLogin),
	)

	// Lấy user từ database bằng email
	var user *internal.User
	err := tracing.WithSpan(ctx, "auth-service", "get_user_by_email", func(ctx context.Context) error {
		var err error
		user, err = s.repo.GetUserByEmail(ctx, req.Email)
		if err != nil {
			if errors.Is(err, internal.ErrUserNotFound) {
				tracing.AddSpanAttributes(ctx, attribute.String("auth.error", "user_not_found"))
				return internal.ErrInvalidCredentials
			}
			s.logger.Error("Failed to get user", logger.String("error", err.Error()))
			return err
		}
		return nil
	})

	if err != nil {
		tracing.RecordError(ctx, err)
		return nil, err
	}

	// Logic xử lý khác...

	return &dto.LoginResponse{
		// Dữ liệu trả về...
	}, nil
}

// Các phương thức khác...
```

### 7. Thư mục `api/`
**Nhiệm vụ**: Xử lý HTTP requests, định nghĩa API endpoints.

#### File `routes.go`
**Ví dụ**:
```go
package api

import (
	"fmt"
	"wnapi/internal/core"
	"wnapi/internal/pkg/tracing"
	"wnapi/modules/auth/api/handlers"
	"wnapi/modules/auth/internal"

	"github.com/gin-gonic/gin"
)

// Handler là đối tượng chính xử lý API cho module Auth
type Handler struct {
	adminAuthHandler *handlers.AdminAuthHandler
	routes           []string
}

// NewHandler tạo một handler mới
func NewHandler(authService internal.AuthService) *Handler {
	return &Handler{
		adminAuthHandler: handlers.NewAdminAuthHandler(authService),
		routes:           make([]string, 0),
	}
}

// RegisterRoutes đăng ký tất cả routes cho module Auth
func (h *Handler) RegisterRoutes(server *core.Server) error {
	// API Group
	apiGroup := server.Group("/api/v1/auth")

	// Thêm middleware tracing cho tất cả các route auth
	apiGroup.Use(tracing.GinMiddleware("auth"))

	// Lưu lại danh sách các route để hiển thị
	basePath := "/api/v1/auth"

	// Health check endpoint
	apiGroup.GET("/healthy", h.healthCheck)
	h.routes = append(h.routes, fmt.Sprintf("GET %s/healthy", basePath))

	// Không yêu cầu xác thực
	apiGroup.POST("/login", h.adminAuthHandler.Login)
	h.routes = append(h.routes, fmt.Sprintf("POST %s/login", basePath))

	// Các routes khác...

	return nil
}

// healthCheck là endpoint kiểm tra trạng thái hoạt động của module
func (h *Handler) healthCheck(c *gin.Context) {
	c.JSON(200, gin.H{
		"status":  "ok",
		"module":  "auth",
		"message": "Auth module is running",
	})
}
```

#### File `handlers/admin_auth_handler.go`
**Ví dụ**:
```go
package handlers

import (
	"net/http"

	"wnapi/internal/pkg/response"
	"wnapi/internal/pkg/tracing"
	"wnapi/modules/auth/dto"
	"wnapi/modules/auth/internal"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
)

// AdminAuthHandler xử lý HTTP requests cho admin auth
type AdminAuthHandler struct {
	authService internal.AuthService
}

// NewAdminAuthHandler tạo handler mới
func NewAdminAuthHandler(authService internal.AuthService) *AdminAuthHandler {
	return &AdminAuthHandler{authService: authService}
}

// Login xử lý request đăng nhập
func (h *AdminAuthHandler) Login(c *gin.Context) {
	// Bắt đầu span cho operation login
	ctx, span := tracing.StartGinSpan(c, "auth", "login")
	defer span.End()

	var req dto.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		// Ghi lại lỗi vào span
		tracing.RecordGinError(c, err)
		tracing.AddGinSpanAttributes(c, attribute.String("auth.error_type", "invalid_request"))

		details := []interface{}{map[string]string{"message": err.Error()}}
		response.BadRequest(c, "Định dạng yêu cầu không hợp lệ", "INVALID_REQUEST", details)
		return
	}

	// Thêm tham số để chỉ định rằng đây là đăng nhập admin
	req.AdminLogin = true

	resp, err := h.authService.Login(ctx, req)
	if err != nil {
		// Ghi lại lỗi và phân loại lỗi trong span
		tracing.RecordGinError(c, err)

		// Lấy thông tin phản hồi lỗi từ định nghĩa trong internal
		errResp := internal.GetErrorResponse(err)
		tracing.AddGinSpanAttributes(c, attribute.String("auth.error_type", errResp.ErrorCode))

		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, errResp.StatusCode, errResp.Message, errResp.ErrorCode, details)
		return
	}

	// Đánh dấu span là thành công
	tracing.SetSpanStatus(ctx, codes.Ok, "Login successful")
	tracing.AddGinSpanAttributes(c,
		attribute.Int64("auth.user_id", resp.UserID),
		attribute.Bool("auth.success", true),
	)
	tracing.AddGinSpanEvent(c, "login_success")

	response.Success(c, resp, nil)
}

// Các phương thức khác...
```

### 8. Thư mục `migrations/`
**Nhiệm vụ**: Chứa các script SQL migration để quản lý schema cơ sở dữ liệu.

**Ví dụ** (`001_create_users.up.sql`):
```sql
CREATE TABLE auth_users (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(100) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(255),
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

**Ví dụ** (`001_create_users.down.sql`):
```sql
DROP TABLE IF EXISTS auth_users;
```

## FX System - Dependency Injection

### Tổng quan FX System

Hệ thống hiện tại sử dụng **Uber FX** để quản lý dependency injection và module lifecycle. FX cung cấp:

- **Dependency Injection**: Tự động resolve dependencies
- **Lifecycle Management**: Quản lý startup/shutdown
- **Module System**: Tổ chức code theo modules
- **Type Safety**: Compile-time dependency checking

### Cách FX hoạt động

1. **Providers**: Functions tạo dependencies
2. **Consumers**: Functions sử dụng dependencies (via fx.Invoke)
3. **Modules**: Nhóm providers và consumers
4. **App**: Container chứa tất cả modules

### Module Loading trong FX

Modules được load trong `cmd/fx-server/main.go`:

```go
fxApp := fxapp.NewAppWithModules([]string{"tenant", "auth", "notification", "media"},
    fx.Provide(
        // Global providers
        config.New,
        database.NewManager,
        logger.New,
    ),
)
```

### FX-Compatible Route Registration Pattern

**Tất cả modules phải tuân theo pattern này:**

```go
// RegisterXXXRoutes registers XXX routes with Gin engine
func RegisterXXXRoutes(engine *gin.Engine, handler *api.Handler, log logger.Logger) {
    log.Info("Registering XXX routes")

    // Use existing route registration logic
    if err := handler.RegisterRoutes(engine); err != nil {
        log.Error("Failed to register XXX routes", "error", err)
        return
    }

    log.Info("XXX routes registered successfully")
}
```

**Lưu ý quan trọng**: Function này phải được gọi trong `fx.Invoke()` của module để đảm bảo routes được đăng ký với FX handler thay vì legacy handler.

## Quy tắc quan trọng

### 1. FX Module Structure
- **Bắt buộc**: Mỗi module phải có file `fx.go` với function `Module() fx.Option`
- **Bắt buộc**: File `providers.go` chứa tất cả FX providers
- **Bắt buộc**: File `routes.go` với FX-compatible route registration
- **Tùy chọn**: Files `bootstrap.go` và `module.go` cho backward compatibility

### 2. FX Providers Pattern
- Tất cả constructors phải là FX providers
- Sử dụng dependency injection thay vì manual initialization
- Provider functions phải return (interface, error) hoặc interface
- Sử dụng `fx.Provide()` để đăng ký providers

### 3. FX Route Registration
- **Bắt buộc**: Sử dụng `fx.Invoke(RegisterXXXRoutes)` thay vì legacy route registration
- Function signature: `func RegisterXXXRoutes(engine *gin.Engine, handler *api.Handler, log logger.Logger)`
- Đảm bảo error handling và logging trong route registration

### 4. Cấu hình từ biến môi trường
- Sử dụng `config.Config` interface từ FX container
- Providers nhận `config.Config` parameter
- Sử dụng `cfg.GetString()`, `cfg.GetDuration()`, etc.
- Đặt giá trị mặc định qua `cfg.GetStringWithDefault()`

### 5. Tên Module
- Sử dụng snake_case cho tên thư mục
- Sử dụng camelCase cho tên package Go
- Module name trong FX phải match với folder name

### 6. Database
- Luôn sử dụng `INT UNSIGNED` cho ID
- Luôn có `created_at` và `updated_at`
- Sử dụng `utf8mb4` và `utf8mb4_unicode_ci`
- Tên bảng nên có prefix là tên module (ví dụ: `auth_users`, `auth_sessions`)

### 4. Xử lý lỗi và Response Format
#### 4.1 Định nghĩa lỗi
- Định nghĩa lỗi trong package internal sử dụng `ServiceError`
- Định nghĩa bảng ánh xạ `ErrorMap` để kết nối `ServiceError` với HTTP status code và mã lỗi
- Sử dụng `GetErrorResponse()` để chuyển đổi lỗi thành cấu trúc phản hồi chuẩn

```go
// ServiceError định nghĩa các lỗi service
type ServiceError string

const (
	ErrInvalidCredentials ServiceError = "invalid_credentials"
	ErrUserAlreadyExists ServiceError = "user_already_exists"
	// Các lỗi khác...
)

// ErrorResponse định nghĩa cấu trúc phản hồi lỗi
type ErrorResponse struct {
	StatusCode int    // HTTP status code
	Message    string // Thông báo lỗi hiển thị cho người dùng
	ErrorCode  string // Mã lỗi nội bộ
}

// ErrorMap ánh xạ ServiceError với thông tin phản hồi lỗi tương ứng
var ErrorMap = map[ServiceError]ErrorResponse{
	ErrInvalidCredentials: {
		StatusCode: http.StatusUnauthorized,
		Message:    "Thông tin đăng nhập không hợp lệ",
		ErrorCode:  "INVALID_CREDENTIALS",
	},
	// Các lỗi khác...
}

// GetErrorResponse trả về thông tin phản hồi lỗi dựa trên ServiceError
func GetErrorResponse(err error) ErrorResponse {
	if serviceErr, ok := err.(ServiceError); ok {
		if resp, exists := ErrorMap[serviceErr]; exists {
			return resp
		}
	}

	// Mặc định trả về lỗi hệ thống nếu không tìm thấy lỗi trong map
	return ErrorResponse{
		StatusCode: http.StatusInternalServerError,
		Message:    "Lỗi hệ thống",
		ErrorCode:  "INTERNAL_ERROR",
	}
}
```

#### 4.2 Sử dụng trong Handler

```go
func (h *AdminAuthHandler) Login(c *gin.Context) {
	// Xử lý logic...

	resp, err := h.authService.Login(ctx, req)
	if err != nil {
		// Lấy thông tin phản hồi lỗi từ định nghĩa trong internal
		errResp := internal.GetErrorResponse(err)

		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, errResp.StatusCode, errResp.Message, errResp.ErrorCode, details)
		return
	}

	// Trả về thành công
	response.Success(c, resp, nil)
}
```

#### 4.3 Cấu trúc phản hồi
```json
// Success
{
    "status": {
        "code": 200,
        "message": "Operation completed successfully",
        "success": true,
        "error_code": null,
        "path": "/api/auth/login",
        "timestamp": "2024-03-15T14:35:22Z"
    },
    "data": {} // hoặc []
}

// Error
{
    "status": {
        "code": 400,
        "message": "Error message",
        "success": false,
        "error_code": "ERROR_CODE",
        "path": "/api/auth/login",
        "timestamp": "2024-03-15T14:35:22Z",
        "details": [
            {
                "field": "email",
                "message": "Email không hợp lệ"
            }
        ]
    }
}
```

### 5. Logging và Tracing
- Sử dụng logger từ app
- Sử dụng OpenTelemetry để tracing
- Thêm middleware tracing cho các routes
- Thêm span cho các operations quan trọng

```go
// Tạo span cho truy vấn database
ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "auth_users")
defer span.End()

// Thêm thông tin về operation
tracing.AddSpanAttributes(ctx,
	attribute.String("db.table", "auth_users"),
	attribute.String("db.operation", "get_user_by_email"),
	attribute.String("auth.email", email),
)

// Ghi lại lỗi trong span
tracing.RecordError(ctx, err)
```

### 6. Context
- Luôn truyền context trong các method
- Sử dụng context để cancel operations
- Truyền context giữa các layers (handler → service → repository)

### 7. Migration
- Đặt tên file theo định dạng: `[số thứ tự]_[mô tả].[up/down].sql`
- Số thứ tự bắt đầu từ 001 và tăng dần
- Luôn có cả file up và down
- Đảm bảo tên bảng có prefix là tên module

### 8. Bảo mật
- Không hard-code secret keys, sử dụng biến môi trường
- Sử dụng bcrypt để hash mật khẩu
- Validate input từ user
- Che giấu thông tin nhạy cảm trong logs và tracing (passwords, tokens)

### 9. Validation
- Sử dụng tags trong struct để validate dữ liệu đầu vào
- Xử lý lỗi validation và trả về thông báo phù hợp
- Sử dụng i18n cho thông báo lỗi

### 10. File .env và .env.example
- Tạo file `.env.example` trong thư mục dự án để làm mẫu
- Không commit file `.env` chứa thông tin nhạy cảm
- Sử dụng định dạng sau:

```
# Auth Module Configuration
JWT_ACCESS_SIGNING_KEY=super_secret_access_key_change_me_in_production
JWT_REFRESH_SIGNING_KEY=super_secret_refresh_key_change_me_in_production
JWT_ACCESS_TOKEN_EXPIRATION=168h
JWT_REFRESH_TOKEN_EXPIRATION=168h
JWT_ISSUER=wnapi
AUTH_MESSAGE=Xin chào từ module Auth!
```

## Tóm tắt: Tạo Module Mới với FX System

### Checklist tạo module mới

**1. Tạo cấu trúc thư mục:**
```
modules/your_module/
├── fx.go                    # FX module definition
├── providers.go             # FX providers
├── routes.go                # FX-compatible route registration
├── internal/
│   ├── config.go           # Module configuration
│   └── types.go            # Types, interfaces, errors
├── dto/                    # Data Transfer Objects
├── repository/             # Repository implementations
├── service/                # Service implementations
├── api/                    # API handlers
└── migrations/             # SQL migrations
```

**2. Implement FX files:**
- `fx.go`: Module definition với `Module() fx.Option`
- `providers.go`: Tất cả constructors là FX providers
- `routes.go`: Function `RegisterYourModuleRoutes(engine, handler, log)`

**3. Đăng ký module:**
- Thêm module name vào `cmd/fx-server/main.go`
- Đảm bảo module được load trong FX app

**4. Test:**
- Kiểm tra module load thành công
- Test API endpoints
- Verify dependency injection hoạt động

### Lưu ý quan trọng

- **Bắt buộc**: Sử dụng FX-compatible route registration
- **Bắt buộc**: Tất cả dependencies phải qua FX providers
- **Khuyến nghị**: Tuân theo naming conventions và patterns hiện tại
- **Khuyến nghị**: Implement proper error handling và logging

Với cấu trúc và quy tắc này, bạn có thể dễ dàng tạo một module mới tuân thủ các tiêu chuẩn của hệ thống FX.

## Troubleshooting: Route Không Nhận (404 Errors)

### Vấn đề thường gặp

Khi tạo module mới hoặc thêm routes mới, có thể gặp lỗi **404 Not Found** mặc dù routes đã được định nghĩa. Đây là những nguyên nhân và cách khắc phục phổ biến:

### 1. Import Cycle Dependencies

**Triệu chứng:**
- Module không load được
- Lỗi "import cycle not allowed"
- Routes không được đăng ký

**Nguyên nhân:**
- Circular imports giữa các packages
- Interface definitions ở sai vị trí

**Giải pháp:**
```go
// ❌ SAI: Định nghĩa interface trong internal/types.go và import dto
package internal

import "wnapi/modules/blog/dto" // Tạo circular dependency

type PostService interface {
    CreatePost(req dto.CreatePostRequest) error
}

// ✅ ĐÚNG: Di chuyển interface vào service package
package service

import "wnapi/modules/blog/dto"

type PostService interface {
    CreatePost(req dto.CreatePostRequest) error
}
```

**Cách kiểm tra:**
```bash
go build ./modules/your_module/...
# Nếu có import cycle, lệnh này sẽ báo lỗi
```

### 2. FX Module Registration Issues

**Triệu chứng:**
- Module không xuất hiện trong FX logs
- Routes không được đăng ký
- Dependencies không được inject

**Nguyên nhân:**
- Module chưa được thêm vào module list
- FX module definition sai cú pháp
- Provider functions không đúng signature

**Giải pháp:**

**a. Kiểm tra module registration:**
```go
// cmd/fx-server/main.go
fxApp := fxapp.NewAppWithModules([]string{
    "tenant",
    "auth",
    "notification",
    "media",
    "blog", // ✅ Đảm bảo module được thêm vào đây
})
```

**b. Kiểm tra FX module definition:**
```go
// modules/blog/fx.go
func (m *BlogModule) Module() fx.Option {
    return fx.Module("blog", // ✅ Tên module phải match với folder name
        fx.Provide(
            // ✅ Tất cả providers phải có đúng signature
            NewBlogConfig,
            NewPostService,
            NewBlogHandler,
        ),
        fx.Invoke(RegisterBlogRoutes), // ✅ Route registration phải được invoke
    )
}
```

**c. Kiểm tra provider signatures:**
```go
// ❌ SAI: Provider không return interface
func NewPostService() *postService {
    return &postService{}
}

// ✅ ĐÚNG: Provider return interface
func NewPostService() service.PostService {
    return &postService{}
}
```

### 3. Route Registration Problems

**Triệu chứng:**
- Module load thành công nhưng routes không hoạt động
- Gin debug logs không hiển thị routes
- 404 errors cho tất cả endpoints

**Nguyên nhân:**
- Route registration function không được gọi
- Handler method signature sai
- Gin engine không được truyền đúng

**Giải pháp:**

**a. Kiểm tra route registration function:**
```go
// ❌ SAI: Function không được invoke trong FX
func (m *BlogModule) Module() fx.Option {
    return fx.Module("blog",
        fx.Provide(NewBlogHandler),
        // Missing fx.Invoke(RegisterBlogRoutes)
    )
}

// ✅ ĐÚNG: Function được invoke
func (m *BlogModule) Module() fx.Option {
    return fx.Module("blog",
        fx.Provide(NewBlogHandler),
        fx.Invoke(RegisterBlogRoutes), // ✅ Route registration được invoke
    )
}
```

**b. Kiểm tra handler method signature:**
```go
// ❌ SAI: Method signature không đúng
func RegisterBlogRoutes(handler *api.Handler) {
    // Missing engine parameter
}

// ✅ ĐÚNG: Đúng signature cho FX
func RegisterBlogRoutes(engine *gin.Engine, handler *api.Handler, log logger.Logger) {
    log.Info("Registering blog routes")

    if err := handler.RegisterRoutesWithEngine(engine); err != nil {
        log.Error("Failed to register blog routes", "error", err)
        return
    }

    log.Info("Blog routes registered successfully")
}
```

**c. Kiểm tra handler implementation:**
```go
// api/handler.go
func (h *Handler) RegisterRoutesWithEngine(engine *gin.Engine) error {
    // ✅ Sử dụng engine parameter thay vì internal router
    adminGroup := engine.Group("/api/admin/v1/blog")

    adminGroup.GET("/posts", h.postHandler.List)
    adminGroup.POST("/posts", h.postHandler.Create)

    return nil
}
```

### 4. Dependency Injection Issues

**Triệu chứng:**
- Panic khi khởi động
- "no provider found" errors
- Dependencies là nil

**Nguyên nhân:**
- Provider dependencies không được khai báo
- Circular dependencies trong providers
- Interface/implementation mismatch

**Giải pháp:**

**a. Kiểm tra provider dependencies:**
```go
// ❌ SAI: Provider thiếu dependencies
func NewPostService() service.PostService {
    return &postService{} // Missing repository dependency
}

// ✅ ĐÚNG: Provider có đầy đủ dependencies
func NewPostService(
    postRepo *mysql.PostRepository,
    categoryRepo *mysql.CategoryRepository,
    tagRepo repository.TagRepository,
    scheduleService service.ScheduleService, // ✅ Tất cả dependencies được khai báo
) service.PostService {
    tagRepoImpl := tagRepo.(*mysql.TagRepository)
    return &postService{
        postRepo:        postRepo,
        categoryRepo:    categoryRepo,
        tagRepo:         *tagRepoImpl,
        scheduleService: scheduleService,
    }
}
```

**b. Kiểm tra provider order:**
```go
// fx.go - Providers phải được sắp xếp theo thứ tự dependency
fx.Provide(
    // Repositories first (no dependencies)
    mysql.NewPostRepository,
    mysql.NewCategoryRepository,
    mysql.NewScheduleRepository,

    // Services next (depend on repositories)
    NewScheduleService,
    NewPostService, // ✅ PostService phải sau ScheduleService

    // Handlers last (depend on services)
    NewBlogHandler,
),
```

### 5. Debugging Steps

**Bước 1: Kiểm tra module loading**
```bash
# Chạy server và kiểm tra logs
go run cmd/fx-server/main.go

# Tìm logs như:
# [INFO] Loading module name=blog
# [Fx] PROVIDE service.PostService <= wnapi/modules/blog.NewPostService()
# [Fx] INVOKE wnapi/modules/blog.RegisterBlogRoutes()
```

**Bước 2: Kiểm tra route registration**
```bash
# Tìm Gin debug logs:
# [GIN-debug] POST /api/admin/v1/blog/posts --> handler.Create
# [GIN-debug] GET  /api/admin/v1/blog/posts --> handler.List
```

**Bước 3: Test compilation**
```bash
# Kiểm tra import cycles
go build ./modules/blog/...

# Kiểm tra toàn bộ project
go build ./...
```

**Bước 4: Test API endpoints**
```bash
# Test health check trước
curl http://localhost:9033/api/admin/v1/blog/healthy

# Sau đó test main endpoints
curl -X POST http://localhost:9033/api/admin/v1/blog/posts \
  -H "Content-Type: application/json" \
  -d '{"title": "Test Post"}'
```

### 6. Common Patterns That Work

**Pattern 1: Clean FX Module Structure**
```go
// fx.go
func (m *BlogModule) Module() fx.Option {
    return fx.Module("blog",
        fx.Provide(
            NewBlogConfig,
            mysql.NewPostRepository,
            NewPostService,
            NewBlogHandler,
        ),
        fx.Invoke(RegisterBlogRoutes),
    )
}
```

**Pattern 2: Proper Route Registration**
```go
// providers.go
func RegisterBlogRoutes(engine *gin.Engine, handler *api.Handler, log logger.Logger) error {
    log.Info("Registering blog routes")

    if err := handler.RegisterRoutesWithEngine(engine); err != nil {
        log.Error("Failed to register blog routes", "error", err)
        return err
    }

    log.Info("Blog routes registered successfully")
    return nil
}
```

**Pattern 3: Interface Placement**
```go
// service/interfaces.go - Đặt interfaces trong service package
type PostService interface {
    CreatePost(ctx context.Context, tenantID uint, req request.CreatePostRequest) (*response.PostResponse, error)
}

// service/post_service.go - Implementation trong cùng package
type postService struct {
    // fields
}

func NewPostService(...) PostService {
    return &postService{...}
}
```

### 7. Checklist Debug Route Issues

- [ ] Module name trong `cmd/fx-server/main.go`
- [ ] FX module definition syntax đúng
- [ ] Provider functions có đúng signature
- [ ] Route registration function được invoke
- [ ] Handler method signature đúng với FX requirements
- [ ] Dependencies được khai báo đầy đủ
- [ ] Không có import cycles
- [ ] Gin debug logs hiển thị routes
- [ ] Health check endpoint hoạt động
- [ ] Main endpoints trả về đúng response

Với checklist này, bạn có thể nhanh chóng xác định và khắc phục các vấn đề về route registration trong hệ thống FX.

### 8. Case Study: Blog Module Route Issues

**Vấn đề gặp phải:**
Blog module được tạo với đầy đủ cấu trúc nhưng API endpoints trả về 404 Not Found.

**Root Cause Analysis:**
1. **Import Cycle**: Circular dependency giữa `internal/types.go` và `dto` packages
2. **Missing Dependencies**: PostService thiếu ScheduleService dependency
3. **FX Registration**: Route registration function không được invoke đúng cách

**Giải pháp thực hiện:**

**Bước 1: Khắc phục Import Cycle**
```go
// ❌ Trước: internal/types.go
package internal
import "wnapi/modules/blog/dto" // Tạo circular dependency

// ✅ Sau: service/interfaces.go
package service
import "wnapi/modules/blog/dto" // Không có circular dependency
```

**Bước 2: Cập nhật FX Dependencies**
```go
// providers.go
func NewPostService(
    postRepo *mysql.PostRepository,
    categoryRepo *mysql.CategoryRepository,
    tagRepo repository.TagRepository,
    scheduleService service.ScheduleService, // ✅ Thêm dependency
) service.PostService {
    return &postService{
        postRepo:        postRepo,
        categoryRepo:    categoryRepo,
        tagRepo:         tagRepo,
        scheduleService: scheduleService, // ✅ Inject dependency
    }
}
```

**Bước 3: Đảm bảo FX Module Registration**
```go
// fx.go
func (m *BlogModule) Module() fx.Option {
    return fx.Module("blog",
        fx.Provide(
            NewBlogConfig,
            mysql.NewPostRepository,
            mysql.NewScheduleRepository,    // ✅ Repository dependencies
            NewScheduleService,             // ✅ Service dependencies
            NewPostService,                 // ✅ Đúng thứ tự
            NewBlogHandler,
        ),
        fx.Invoke(RegisterBlogRoutes),      // ✅ Route registration
    )
}
```

**Kết quả:**
- ✅ Module load thành công trong FX logs
- ✅ Routes được đăng ký: `POST /api/admin/v1/blog/posts`
- ✅ API endpoints hoạt động bình thường
- ✅ Blog schedule flow tích hợp thành công

**Bài học:**
1. **Luôn kiểm tra import cycles** trước khi implement logic
2. **Sắp xếp providers theo dependency order** trong FX module
3. **Test từng bước**: Module loading → Route registration → API functionality
4. **Sử dụng FX logs** để debug dependency injection issues

Đây là ví dụ điển hình về cách troubleshoot và khắc phục route issues trong hệ thống FX.

## Chuyển đổi Repository từ sqlx sang GORM

### Tổng quan
GORM là một ORM (Object Relational Mapping) phổ biến cho Go, giúp đơn giản hóa các thao tác với cơ sở dữ liệu. Việc chuyển đổi từ sqlx sang GORM giúp code dễ đọc hơn, giảm thiểu lỗi và tận dụng các tính năng mạnh mẽ của GORM.

### Các bước chuyển đổi

#### 1. Cập nhật Model
- Thay đổi tag `db` thành tag `gorm`
- Thêm các tùy chọn như `column`, `primaryKey`, `autoIncrement`, `index`, `default`, v.v.

**Ví dụ**:
```go
// Trước
type User struct {
    UserID          uint       `db:"user_id" json:"user_id"`
    Email           string     `db:"email" json:"email"`
    PasswordHash    string     `db:"password_hash" json:"-"`
    CreatedAt       time.Time  `db:"created_at" json:"created_at"`
}

// Sau
type User struct {
    UserID          uint       `gorm:"column:user_id;primaryKey;autoIncrement" json:"user_id"`
    Email           string     `gorm:"column:email;uniqueIndex" json:"email"`
    PasswordHash    string     `gorm:"column:password_hash" json:"-"`
    CreatedAt       time.Time  `gorm:"column:created_at;autoCreateTime" json:"created_at"`
}
```

#### 2. Cập nhật Repository struct
- Đổi tên struct để phản ánh công nghệ mới (tùy chọn)
- Thay `*sqlx.DB` hoặc `*sql.DB` bằng `*gorm.DB`
- Thêm `logger` để ghi log lỗi

**Ví dụ**:
```go
// Trước
type mysqlRepository struct {
    db     *sqlx.DB
}

// Sau
type gormRepository struct {
    db     *gorm.DB
    logger logger.Logger
}
```

#### 3. Cập nhật constructor
- Chuyển đổi từ sqlx.DB sang gorm.DB
- Thêm tham số logger

**Ví dụ**:
```go
// Trước
func NewEmailVerificationRepository(db *sqlx.DB) repository.EmailVerificationRepository {
    return &SQLxEmailVerificationRepository{db: db}
}

// Sau
func NewEmailVerificationRepository(db *gorm.DB, logger logger.Logger) repository.EmailVerificationRepository {
    return &GormEmailVerificationRepository{
        db:     db,
        logger: logger,
    }
}
```

#### 4. Tạo kết nối GORM từ sqlx.DB
Nếu hệ thống vẫn đang sử dụng sqlx.DB ở cấp cao hơn, bạn cần tạo kết nối GORM từ sqlx:

```go
func NewMySQLRepository(dbManager *database.Manager, logger logger.Logger) (internal.Repository, error) {
    if dbManager == nil {
        return nil, errors.New("database manager không được để trống")
    }

    sqlxDB := dbManager.GetDB()
    if sqlxDB == nil {
        return nil, errors.New("không thể kết nối tới cơ sở dữ liệu")
    }

    // Create GORM DB from sqlx DB
    gormDB, err := gorm.Open(mysql.New(mysql.Config{
        Conn: sqlxDB.DB,
    }), &gorm.Config{})
    if err != nil {
        return nil, fmt.Errorf("failed to initialize GORM: %w", err)
    }

    return &mysqlRepository{
        db:     gormDB,
        logger: logger,
    }, nil
}
```

#### 5. Chuyển đổi các phương thức CRUD

**a. Create**

```go
// Trước (sqlx)
query := `INSERT INTO users (email, password_hash) VALUES (:email, :password_hash)`
_, err := r.db.NamedExecContext(ctx, query, user)

// Sau (GORM)
result := r.db.WithContext(ctx).Create(user)
if result.Error != nil {
    r.logger.Error("Không thể tạo user", logger.String("error", result.Error.Error()))
    return result.Error
}
```

**b. Read**

```go
// Trước (sqlx)
query := `SELECT * FROM users WHERE id = ?`
var user User
err := r.db.GetContext(ctx, &user, query, id)
if err == sql.ErrNoRows {
    return nil, ErrNotFound
}

// Sau (GORM)
var user User
result := r.db.WithContext(ctx).Where("id = ?", id).First(&user)
if result.Error != nil {
    if errors.Is(result.Error, gorm.ErrRecordNotFound) {
        return nil, ErrNotFound
    }
    r.logger.Error("Không thể lấy user", logger.String("error", result.Error.Error()))
    return nil, result.Error
}
```

**c. Update**

```go
// Trước (sqlx)
query := `UPDATE users SET email = :email, updated_at = :updated_at WHERE id = :id`
_, err := r.db.NamedExecContext(ctx, query, user)

// Sau (GORM)
// Phương pháp 1: Cập nhật toàn bộ bản ghi
result := r.db.WithContext(ctx).Save(user)

// Phương pháp 2: Cập nhật các trường cụ thể
result := r.db.WithContext(ctx).Model(&User{}).
    Where("id = ?", user.ID).
    Updates(map[string]interface{}{
        "email":      user.Email,
        "updated_at": time.Now(),
    })
```

**d. Delete**

```go
// Trước (sqlx)
query := `DELETE FROM users WHERE id = ?`
_, err := r.db.ExecContext(ctx, query, id)

// Sau (GORM)
result := r.db.WithContext(ctx).Delete(&User{}, id)
```

#### 6. Xử lý lỗi với GORM

```go
if result.Error != nil {
    if errors.Is(result.Error, gorm.ErrRecordNotFound) {
        // Xử lý khi không tìm thấy bản ghi
        return nil, ErrNotFound
    }
    // Log lỗi
    r.logger.Error("Lỗi truy vấn cơ sở dữ liệu", logger.String("error", result.Error.Error()))
    // Trả về lỗi có context
    return nil, fmt.Errorf("failed to get user: %w", result.Error)
}
```

### Ví dụ cụ thể: Chuyển đổi Email Verification Repository

#### Trước (SQLx)

```go
// SQLxEmailVerificationRepository là cài đặt MySQL cho EmailVerificationRepository
type SQLxEmailVerificationRepository struct {
    db *sqlx.DB
}

// Create lưu token xác thực email vào database
func (r *SQLxEmailVerificationRepository) Create(ctx context.Context, verification *models.EmailVerification) error {
    now := time.Now()
    verification.CreatedAt = now
    verification.UpdatedAt = now

    query := `
        INSERT INTO auth_email_verifications (
            user_id, email, token, verified, expires_at, created_at, updated_at
        ) VALUES (
            :user_id, :email, :token, :verified, :expires_at, :created_at, :updated_at
        )
    `

    _, err := r.db.NamedExecContext(ctx, query, verification)
    if err != nil {
        return fmt.Errorf("failed to create email verification: %w", err)
    }

    return nil
}
```

#### Sau (GORM)

```go
// GormEmailVerificationRepository là cài đặt MySQL cho EmailVerificationRepository sử dụng GORM
type GormEmailVerificationRepository struct {
    db     *gorm.DB
    logger logger.Logger
}

// Create lưu token xác thực email vào database
func (r *GormEmailVerificationRepository) Create(ctx context.Context, verification *models.EmailVerification) error {
    now := time.Now()
    verification.CreatedAt = now
    verification.UpdatedAt = now

    result := r.db.WithContext(ctx).Create(verification)
    if result.Error != nil {
        r.logger.Error("Không thể tạo xác thực email", logger.String("error", result.Error.Error()))
        return fmt.Errorf("failed to create email verification: %w", result.Error)
    }

    return nil
}
```

### Lưu ý quan trọng
1. Luôn sử dụng `WithContext(ctx)` để truyền context qua các lớp
2. Sử dụng `errors.Is` để so sánh lỗi
3. Ghi log lỗi chi tiết
4. Bổ sung các tag gorm phù hợp trong model
5. Khi cần truy vấn phức tạp, có thể vẫn sử dụng raw SQL với GORM:
   ```go
   r.db.WithContext(ctx).Raw("SELECT * FROM users WHERE age > ?", 18).Scan(&users)
   ```

### Ưu điểm khi chuyển sang GORM
1. Code ngắn gọn, dễ đọc hơn
2. Giảm thiểu lỗi liên quan đến SQL
3. Tự động xử lý map giữa struct và bảng dữ liệu
4. Các tính năng mạnh mẽ như hooks, preload, relationship
5. Dễ dàng mở rộng và bảo trì

## CLI Mode - Tắt Services Không Cần Thiết

### Tổng quan
CLI_MODE là một biến môi trường được sử dụng để phân biệt khi ứng dụng đang chạy ở chế độ CLI (Command Line Interface) và tự động tắt các services không cần thiết như event system, queue system, background workers, v.v.

### Khi nào sử dụng CLI_MODE
- **Migration tools**: Khi chạy database migrations
- **CLI commands**: Khi chạy các lệnh CLI
- **Scripts**: Khi chạy các scripts maintenance
- **Testing**: Khi chạy unit tests hoặc integration tests

### Cách hoạt động
Khi `CLI_MODE=true`, hệ thống sẽ tự động:
- Tắt event system (EVENT_ENABLED=false)
- Tắt queue system (QUEUE_ENABLED=false)
- Tắt notification workers
- Tắt background services
- Tắt các external API calls không cần thiết

### Cấu hình
Thêm vào file `.env.example`:
```bash
# =============================================================================
# CLI MODE CONFIGURATION
# =============================================================================
# Khi CLI_MODE=true, tự động tắt event system, queue system và các service không cần thiết
# Được sử dụng bởi migration tool và CLI commands
CLI_MODE=false
```

### Implement CLI_MODE trong Module

#### 1. Trong NewModule function
```go
func NewModule(app *core.AppBootstrap, config map[string]interface{}) (core.Module, error) {
    logger := app.GetLogger()

    // Kiểm tra CLI mode
    cliMode := app.GetConfig().GetBoolWithDefault("CLI_MODE", false)

    // Khởi tạo queue client (tự động tắt nếu CLI mode)
    var queueClient queue.QueueClient
    queueEnabled := app.GetConfig().GetBoolWithDefault("QUEUE_ENABLED", true) && !cliMode

    if !queueEnabled {
        if cliMode {
            logger.Info("Queue client bị tắt do CLI mode")
        } else {
            logger.Info("Queue client không được kích hoạt")
        }
        queueClient = nil
    } else {
        // Khởi tạo queue client bình thường
        queueClient = initQueueClient(app.GetConfig())
    }

    // Khởi tạo event publisher (tự động tắt nếu CLI mode)
    var eventPublisher *events.Publisher
    eventEnabled := app.GetConfig().GetBoolWithDefault("EVENT_ENABLED", true) && !cliMode

    if eventEnabled {
        eventPublisher = initEventPublisher(app.GetConfig())
    } else {
        if cliMode {
            logger.Info("Event publisher bị tắt do CLI mode")
        } else {
            logger.Info("Event publisher không được kích hoạt")
        }
    }

    return &Module{
        // ... other fields
        queueClient:    queueClient,
        eventPublisher: eventPublisher,
    }, nil
}
```

#### 2. Trong Init method
```go
func (m *Module) Init(ctx context.Context) error {
    m.logger.Info("Initializing module")

    // Kiểm tra CLI mode
    appConfig := m.app.GetConfig()
    cliMode := appConfig.GetBoolWithDefault("CLI_MODE", false)

    // Khởi tạo background workers (tự động tắt nếu CLI mode)
    workerEnabled := appConfig.GetBoolWithDefault("WORKER_ENABLED", true) && !cliMode
    if workerEnabled {
        m.logger.Info("Khởi tạo background workers")
        // Khởi tạo workers
    } else {
        if cliMode {
            m.logger.Info("Background workers bị tắt do CLI mode")
        } else {
            m.logger.Info("Background workers không được kích hoạt")
        }
    }

    // Đăng ký event handlers (tự động tắt nếu CLI mode)
    eventEnabled := appConfig.GetBoolWithDefault("EVENT_ENABLED", true) && !cliMode
    if m.eventPublisher != nil && eventEnabled {
        if err := m.registerEventHandlers(ctx); err != nil {
            return fmt.Errorf("failed to register event handlers: %w", err)
        }
    } else if cliMode {
        m.logger.Info("Event handlers bị tắt do CLI mode")
    }

    // Đăng ký queue processors (tự động tắt nếu CLI mode)
    queueEnabled := appConfig.GetBoolWithDefault("QUEUE_ENABLED", true) && !cliMode
    if m.queueClient != nil && queueEnabled {
        if err := m.registerQueueProcessors(ctx); err != nil {
            return fmt.Errorf("failed to register queue processors: %w", err)
        }
    } else if cliMode {
        m.logger.Info("Queue processors bị tắt do CLI mode")
    }

    return nil
}
```

#### 3. Trong CLI applications
```go
func main() {
    // Thiết lập CLI mode để tự động tắt các service không cần thiết
    os.Setenv("CLI_MODE", "true")

    // Initialize application
    app, err := core.NewApp()
    if err != nil {
        logger.NewConsoleLogger("cli", logger.LevelError).
            Fatal("Failed to initialize application", "error", err)
    }
    defer app.Cleanup(context.Background())

    // CLI logic...
}
```

#### 4. Trong migration tools
```go
func main() {
    // Thiết lập CLI mode để tự động tắt các service không cần thiết
    os.Setenv("CLI_MODE", "true")

    log := logger.NewConsoleLogger("migrate", logger.LevelDebug)
    log.Info("Migration optimization: Enabled CLI mode to disable unnecessary services",
        "cli_mode", true)

    // Migration logic...
}
```

### Pattern cho các loại services

#### Event Publishers/Subscribers
```go
// Trong module initialization
eventEnabled := app.GetConfig().GetBoolWithDefault("EVENT_ENABLED", true) && !cliMode
if eventEnabled {
    // Khởi tạo event publisher/subscriber
} else {
    if cliMode {
        logger.Info("Event system bị tắt do CLI mode")
    }
}
```

#### Queue Clients/Workers
```go
// Trong module initialization
queueEnabled := app.GetConfig().GetBoolWithDefault("QUEUE_ENABLED", true) && !cliMode
if queueEnabled {
    // Khởi tạo queue client/worker
} else {
    if cliMode {
        logger.Info("Queue system bị tắt do CLI mode")
    }
}
```

#### Background Services
```go
// Trong Init method
workerEnabled := appConfig.GetBoolWithDefault("WORKER_ENABLED", true) && !cliMode
if workerEnabled {
    // Khởi tạo background workers
} else {
    if cliMode {
        logger.Info("Background workers bị tắt do CLI mode")
    }
}
```

#### External API Calls
```go
// Trong service methods
if !m.cliMode {
    // Gọi external APIs
    err := m.callExternalAPI(ctx, data)
    if err != nil {
        return err
    }
} else {
    m.logger.Info("External API call skipped due to CLI mode")
}
```

### Lợi ích của CLI_MODE
1. **Tăng tốc độ**: CLI tools chạy nhanh hơn khi không cần khởi tạo các services không cần thiết
2. **Giảm dependencies**: Không cần Redis, external APIs khi chạy migrations
3. **Tập trung hóa**: Chỉ cần thiết lập 1 biến thay vì nhiều biến riêng lẻ
4. **Nhất quán**: Tất cả modules đều tuân theo cùng pattern
5. **Dễ debug**: Logs rõ ràng về việc services nào bị tắt và tại sao

### Best Practices
1. **Luôn kiểm tra CLI_MODE** trước khi khởi tạo services không cần thiết
2. **Log rõ ràng** khi services bị tắt do CLI mode
3. **Graceful degradation**: Module vẫn hoạt động được khi thiếu một số services
4. **Test cả hai modes**: Đảm bảo module hoạt động đúng ở cả CLI mode và server mode
5. **Document dependencies**: Ghi rõ services nào cần thiết cho từng chức năng
