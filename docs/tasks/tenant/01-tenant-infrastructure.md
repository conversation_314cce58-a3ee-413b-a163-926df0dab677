# Task 01: Tenant Module Infrastructure Setup

## Objective
Thiết lập cấu trúc module cơ bản cho tenant module theo đúng patterns từ modules/auth, bao gồm directory structure, module registration, và bootstrap configuration.

## Input
- Existing modules/auth structure as reference
- Existing modules/tenant/migrations/ directory

## Output
- Complete module directory structure
- Module registration file
- Bootstrap configuration
- Internal configuration setup

## Requirements
1. <PERSON>ân thủ hoàn toàn directory structure của modules/auth
2. Sử dụng dependency injection patterns
3. Multi-tenant architecture compliance
4. Proper module registration với main application

## Implementation Steps

### Step 1: Tạo Directory Structure
Tạo các thư mục theo pattern modules/auth:

```bash
mkdir -p modules/tenant/api/handlers
mkdir -p modules/tenant/service
mkdir -p modules/tenant/repository/mysql
mkdir -p modules/tenant/models
mkdir -p modules/tenant/dto
mkdir -p modules/tenant/internal
```

### Step 2: Tạo Module Registration (modules/tenant/module.go)
```go
package tenant

import (
	"wnapi/internal/pkg/logger"
	"wnapi/internal/database"
	
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// Module định nghĩa tenant module
type Module struct {
	name    string
	logger  logger.Logger
	handler *api.Handler
}

// NewModule tạo instance mới của tenant module
func NewModule(logger logger.Logger) *Module {
	return &Module{
		name:   "tenant",
		logger: logger,
	}
}

// Name trả về tên module
func (m *Module) Name() string {
	return m.name
}

// Initialize khởi tạo module với dependencies
func (m *Module) Initialize(dbManager *database.Manager, gormDB *gorm.DB) error {
	// Bootstrap sẽ được implement trong task tiếp theo
	handler, err := Bootstrap(dbManager, gormDB, m.logger)
	if err != nil {
		return err
	}
	
	m.handler = handler
	return nil
}

// RegisterRoutes đăng ký routes cho module
func (m *Module) RegisterRoutes(router *gin.Engine) error {
	if m.handler == nil {
		return fmt.Errorf("module chưa được initialize")
	}
	
	return m.handler.RegisterRoutes(router)
}

// Health check cho module
func (m *Module) Health() map[string]interface{} {
	return map[string]interface{}{
		"module": m.name,
		"status": "healthy",
	}
}
```

### Step 3: Tạo Bootstrap Configuration (modules/tenant/bootstrap.go)
```go
package tenant

import (
	"wnapi/internal/database"
	"wnapi/internal/pkg/logger"
	"wnapi/modules/tenant/api"
	"wnapi/modules/tenant/internal"
	"wnapi/modules/tenant/repository"
	"wnapi/modules/tenant/repository/mysql"
	"wnapi/modules/tenant/service"
	
	"gorm.io/gorm"
)

// Bootstrap khởi tạo tất cả dependencies cho tenant module
func Bootstrap(
	dbManager *database.Manager,
	gormDB *gorm.DB,
	logger logger.Logger,
) (*api.Handler, error) {
	// Load configuration
	config, err := internal.LoadConfig()
	if err != nil {
		return nil, fmt.Errorf("failed to load tenant config: %w", err)
	}

	// Initialize repository
	tenantRepo, err := mysql.NewTenantRepository(gormDB, logger)
	if err != nil {
		return nil, fmt.Errorf("failed to create tenant repository: %w", err)
	}

	// Initialize service
	tenantService := service.NewTenantService(tenantRepo, config, logger)

	// Initialize API handler
	handler := api.NewHandler(tenantService, logger)

	logger.Info("Tenant module bootstrapped successfully")
	return handler, nil
}
```

### Step 4: Tạo Internal Configuration (modules/tenant/internal/config.go)
```go
package internal

import (
	"os"
	"strconv"
)

// TenantConfig chứa cấu hình cho tenant module
type TenantConfig struct {
	MaxTenantsPerUser int    `env:"TENANT_MAX_PER_USER" envDefault:"5"`
	DefaultPlan       string `env:"TENANT_DEFAULT_PLAN" envDefault:"standard"`
	Message           string `env:"MESSAGE" envDefault:"Xin chào từ module Tenant!"`
}

// LoadConfig tải cấu hình từ environment variables
func LoadConfig() (*TenantConfig, error) {
	config := &TenantConfig{}
	
	// Load max tenants per user
	if val := os.Getenv("TENANT_MAX_PER_USER"); val != "" {
		if maxTenants, err := strconv.Atoi(val); err == nil {
			config.MaxTenantsPerUser = maxTenants
		}
	} else {
		config.MaxTenantsPerUser = 5
	}
	
	// Load default plan
	if val := os.Getenv("TENANT_DEFAULT_PLAN"); val != "" {
		config.DefaultPlan = val
	} else {
		config.DefaultPlan = "standard"
	}
	
	// Load message
	if val := os.Getenv("MESSAGE"); val != "" {
		config.Message = val
	} else {
		config.Message = "Xin chào từ module Tenant!"
	}
	
	return config, nil
}
```

## File Paths
- `modules/tenant/module.go`
- `modules/tenant/bootstrap.go`
- `modules/tenant/internal/config.go`
- Directory structure: `modules/tenant/api/handlers/`, `modules/tenant/service/`, etc.

## Acceptance Criteria
1. ✅ Directory structure matches modules/auth exactly
2. ✅ Module registration follows established patterns
3. ✅ Bootstrap configuration properly initializes dependencies
4. ✅ Internal configuration loads from environment variables
5. ✅ Code builds successfully with `make build`
6. ✅ No import cycles or dependency issues
7. ✅ Proper error handling and logging

## Dependencies
- None (foundation task)

## Estimated Time
1-2 hours

## Notes
- Tạo foundation cho tất cả tasks tiếp theo
- Đảm bảo tuân thủ patterns từ modules/auth
- Chuẩn bị cho dependency injection trong các tasks sau
