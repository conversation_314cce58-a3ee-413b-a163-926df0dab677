# Task 04: Tenant Repository Implementation

## Objective
Triển khai repository layer cho tenant module theo patterns từ modules/auth/repository, sử dụng GORM cho CRUD operations và raw SQL cho list operations với cursor-based pagination.

## Input
- Repository patterns từ modules/auth/repository/
- Interface definitions từ task 02
- <PERSON>rror handling từ task 03

## Output
- Repository interface definitions
- MySQL repository implementation
- Cursor-based pagination cho list operations
- GORM integration cho CRUD operations

## Requirements
1. GORM cho CRUD operations (Create, Read, Update, Delete)
2. Raw SQL cho list operations với cursor-based pagination
3. Multi-tenant data isolation
4. Proper error handling và logging
5. Transaction support
6. Repository interface pattern

## Implementation Steps

### Step 1: Tạo Repository Interface (modules/tenant/repository/repository.go)
```go
package repository

import (
	"context"
	"wnapi/modules/tenant/models"
)

// TenantRepository định nghĩa interface cho tenant repository
type TenantRepository interface {
	// CRUD operations với GORM
	Create(ctx context.Context, tenant *models.Tenant) (*models.Tenant, error)
	GetByID(ctx context.Context, tenantID uint) (*models.Tenant, error)
	GetByCode(ctx context.Context, tenantCode string) (*models.Tenant, error)
	Update(ctx context.Context, tenant *models.Tenant) (*models.Tenant, error)
	Delete(ctx context.Context, tenantID uint) error
	
	// List operations với raw SQL và cursor-based pagination
	List(ctx context.Context, limit int, cursor string) ([]*models.Tenant, string, bool, error)
	ListByStatus(ctx context.Context, status models.TenantStatus, limit int, cursor string) ([]*models.Tenant, string, bool, error)
	
	// Business operations
	ExistsByCode(ctx context.Context, tenantCode string) (bool, error)
	CountByStatus(ctx context.Context, status models.TenantStatus) (int64, error)
	GetExpiredTenants(ctx context.Context) ([]*models.Tenant, error)
	
	// Batch operations
	UpdateStatus(ctx context.Context, tenantIDs []uint, status models.TenantStatus) error
	BulkDelete(ctx context.Context, tenantIDs []uint) error
}

// NewTenantRepository tạo repository implementation
func NewTenantRepository(db interface{}, logger interface{}) (TenantRepository, error) {
	// Implementation sẽ được thực hiện trong mysql package
	return nil, nil
}
```

### Step 2: Tạo MySQL Implementation (modules/tenant/repository/mysql/tenant_repository.go)
```go
package mysql

import (
	"context"
	"database/sql"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"strings"
	"time"
	
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/tracing"
	"wnapi/modules/tenant/internal"
	"wnapi/modules/tenant/models"
	
	"gorm.io/gorm"
)

// tenantRepository triển khai TenantRepository interface
type tenantRepository struct {
	db     *gorm.DB
	logger logger.Logger
}

// NewTenantRepository tạo instance mới của tenant repository
func NewTenantRepository(db *gorm.DB, logger logger.Logger) (*tenantRepository, error) {
	if db == nil {
		return nil, fmt.Errorf("database connection is required")
	}
	
	if logger == nil {
		return nil, fmt.Errorf("logger is required")
	}
	
	return &tenantRepository{
		db:     db,
		logger: logger,
	}, nil
}

// Create tạo tenant mới sử dụng GORM
func (r *tenantRepository) Create(ctx context.Context, tenant *models.Tenant) (*models.Tenant, error) {
	return tracing.WithSpanReturn(ctx, "tenant-repository", "create", func(ctx context.Context) (*models.Tenant, error) {
		if tenant == nil {
			return nil, internal.ErrBadRequest
		}
		
		// Set default values
		if tenant.Status == "" {
			tenant.Status = models.TenantStatusActive
		}
		if tenant.PlanType == "" {
			tenant.PlanType = internal.DefaultPlanType
		}
		
		// Create tenant
		if err := r.db.WithContext(ctx).Create(tenant).Error; err != nil {
			r.logger.Error("Failed to create tenant", 
				logger.String("error", err.Error()),
				logger.String("tenant_code", tenant.TenantCode))
			
			if strings.Contains(err.Error(), "Duplicate entry") && strings.Contains(err.Error(), "tenant_code") {
				return nil, internal.NewTenantCodeExistsError(tenant.TenantCode)
			}
			
			return nil, internal.Wrap(internal.ErrCodeInternalServer, "Failed to create tenant", err)
		}
		
		r.logger.Info("Tenant created successfully",
			logger.Uint("tenant_id", tenant.TenantID),
			logger.String("tenant_code", tenant.TenantCode))
		
		return tenant, nil
	})
}

// GetByID lấy tenant theo ID sử dụng GORM
func (r *tenantRepository) GetByID(ctx context.Context, tenantID uint) (*models.Tenant, error) {
	return tracing.WithSpanReturn(ctx, "tenant-repository", "get_by_id", func(ctx context.Context) (*models.Tenant, error) {
		var tenant models.Tenant
		
		err := r.db.WithContext(ctx).Where("tenant_id = ?", tenantID).First(&tenant).Error
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return nil, internal.NewTenantNotFoundError(tenantID)
			}
			
			r.logger.Error("Failed to get tenant by ID",
				logger.String("error", err.Error()),
				logger.Uint("tenant_id", tenantID))
			
			return nil, internal.Wrap(internal.ErrCodeInternalServer, "Failed to get tenant", err)
		}
		
		return &tenant, nil
	})
}

// GetByCode lấy tenant theo code sử dụng GORM
func (r *tenantRepository) GetByCode(ctx context.Context, tenantCode string) (*models.Tenant, error) {
	return tracing.WithSpanReturn(ctx, "tenant-repository", "get_by_code", func(ctx context.Context) (*models.Tenant, error) {
		var tenant models.Tenant
		
		err := r.db.WithContext(ctx).Where("tenant_code = ?", tenantCode).First(&tenant).Error
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return nil, internal.New(internal.ErrCodeTenantNotFound, "vi")
			}
			
			r.logger.Error("Failed to get tenant by code",
				logger.String("error", err.Error()),
				logger.String("tenant_code", tenantCode))
			
			return nil, internal.Wrap(internal.ErrCodeInternalServer, "Failed to get tenant", err)
		}
		
		return &tenant, nil
	})
}

// Update cập nhật tenant sử dụng GORM
func (r *tenantRepository) Update(ctx context.Context, tenant *models.Tenant) (*models.Tenant, error) {
	return tracing.WithSpanReturn(ctx, "tenant-repository", "update", func(ctx context.Context) (*models.Tenant, error) {
		if tenant == nil || tenant.TenantID == 0 {
			return nil, internal.ErrBadRequest
		}
		
		// Update tenant
		result := r.db.WithContext(ctx).Save(tenant)
		if result.Error != nil {
			r.logger.Error("Failed to update tenant",
				logger.String("error", result.Error.Error()),
				logger.Uint("tenant_id", tenant.TenantID))
			
			if strings.Contains(result.Error.Error(), "Duplicate entry") && strings.Contains(result.Error.Error(), "tenant_code") {
				return nil, internal.NewTenantCodeExistsError(tenant.TenantCode)
			}
			
			return nil, internal.Wrap(internal.ErrCodeInternalServer, "Failed to update tenant", result.Error)
		}
		
		if result.RowsAffected == 0 {
			return nil, internal.NewTenantNotFoundError(tenant.TenantID)
		}
		
		r.logger.Info("Tenant updated successfully",
			logger.Uint("tenant_id", tenant.TenantID),
			logger.String("tenant_code", tenant.TenantCode))
		
		return tenant, nil
	})
}

// Delete xóa tenant sử dụng GORM
func (r *tenantRepository) Delete(ctx context.Context, tenantID uint) error {
	return tracing.WithSpan(ctx, "tenant-repository", "delete", func(ctx context.Context) error {
		result := r.db.WithContext(ctx).Delete(&models.Tenant{}, tenantID)
		if result.Error != nil {
			r.logger.Error("Failed to delete tenant",
				logger.String("error", result.Error.Error()),
				logger.Uint("tenant_id", tenantID))
			
			return internal.Wrap(internal.ErrCodeInternalServer, "Failed to delete tenant", result.Error)
		}
		
		if result.RowsAffected == 0 {
			return internal.NewTenantNotFoundError(tenantID)
		}
		
		r.logger.Info("Tenant deleted successfully",
			logger.Uint("tenant_id", tenantID))
		
		return nil
	})
}

// Cursor structure cho pagination
type Cursor struct {
	TenantID  uint      `json:"tenant_id"`
	CreatedAt time.Time `json:"created_at"`
}

// encodeCursor mã hóa cursor thành string
func (r *tenantRepository) encodeCursor(cursor Cursor) string {
	data, _ := json.Marshal(cursor)
	return base64.URLEncoding.EncodeToString(data)
}

// decodeCursor giải mã cursor từ string
func (r *tenantRepository) decodeCursor(cursorStr string) (*Cursor, error) {
	if cursorStr == "" {
		return nil, nil
	}
	
	data, err := base64.URLEncoding.DecodeString(cursorStr)
	if err != nil {
		return nil, err
	}
	
	var cursor Cursor
	if err := json.Unmarshal(data, &cursor); err != nil {
		return nil, err
	}
	
	return &cursor, nil
}

// List lấy danh sách tenants với cursor-based pagination sử dụng raw SQL
func (r *tenantRepository) List(ctx context.Context, limit int, cursor string) ([]*models.Tenant, string, bool, error) {
	return tracing.WithSpanReturn(ctx, "tenant-repository", "list", func(ctx context.Context) ([]*models.Tenant, string, bool, error) {
		// Validate limit
		if limit <= 0 || limit > internal.MaxListLimit {
			limit = internal.DefaultListLimit
		}
		
		// Decode cursor
		cursorData, err := r.decodeCursor(cursor)
		if err != nil {
			r.logger.Warn("Invalid cursor", logger.String("cursor", cursor), logger.String("error", err.Error()))
			cursorData = nil
		}
		
		// Build query
		query := `
			SELECT tenant_id, tenant_name, tenant_code, status, plan_type, 
				   subscription_expires_at, created_at, updated_at
			FROM tenants
		`
		args := []interface{}{}
		
		if cursorData != nil {
			query += ` WHERE (created_at, tenant_id) < (?, ?)`
			args = append(args, cursorData.CreatedAt, cursorData.TenantID)
		}
		
		query += ` ORDER BY created_at DESC, tenant_id DESC LIMIT ?`
		args = append(args, limit+1) // +1 để check hasMore
		
		// Execute query
		rows, err := r.db.WithContext(ctx).Raw(query, args...).Rows()
		if err != nil {
			r.logger.Error("Failed to list tenants",
				logger.String("error", err.Error()))
			return nil, "", false, internal.Wrap(internal.ErrCodeInternalServer, "Failed to list tenants", err)
		}
		defer rows.Close()
		
		// Scan results
		var tenants []*models.Tenant
		for rows.Next() {
			var tenant models.Tenant
			if err := r.db.ScanRows(rows, &tenant); err != nil {
				r.logger.Error("Failed to scan tenant row",
					logger.String("error", err.Error()))
				continue
			}
			tenants = append(tenants, &tenant)
		}
		
		// Check for more results
		hasMore := len(tenants) > limit
		if hasMore {
			tenants = tenants[:limit] // Remove extra item
		}
		
		// Generate next cursor
		var nextCursor string
		if hasMore && len(tenants) > 0 {
			lastTenant := tenants[len(tenants)-1]
			nextCursor = r.encodeCursor(Cursor{
				TenantID:  lastTenant.TenantID,
				CreatedAt: lastTenant.CreatedAt,
			})
		}
		
		return tenants, nextCursor, hasMore, nil
	})
}
```

## File Paths
- `modules/tenant/repository/repository.go`
- `modules/tenant/repository/mysql/tenant_repository.go`

## Acceptance Criteria
1. ✅ Repository interface tuân thủ patterns từ modules/auth
2. ✅ GORM cho CRUD operations
3. ✅ Raw SQL cho list operations với cursor-based pagination
4. ✅ Proper error handling và logging
5. ✅ Multi-tenant data isolation ready
6. ✅ Code builds successfully với `make build`
7. ✅ Transaction support và performance optimization

## Dependencies
- Task 01: Tenant Infrastructure Setup
- Task 02: Tenant Models and Internal Types
- Task 03: Tenant Error Handling Setup

## Estimated Time
2-3 hours

## Notes
- Foundation cho service layer
- Cursor-based pagination cho scalability
- Error handling integration với business logic
