# Task 06: Tenant Service Implementation

## Objective
Triển khai business logic layer cho tenant module theo patterns từ modules/auth/service, với comprehensive business rules, validation, và error handling.

## Input
- Service patterns từ modules/auth/service/
- Repository interface từ task 04
- D<PERSON>s từ task 05
- <PERSON>rror handling từ task 03

## Output
- Tenant service implementation
- Business logic validation
- Transaction management
- Event publishing integration
- Comprehensive error handling

## Requirements
1. Business logic validation và rules
2. Repository pattern integration
3. Transaction management
4. Proper error handling và logging
5. Event publishing cho business events
6. Multi-tenant compliance
7. Performance optimization

## Implementation Steps

### Step 1: Tạo Service Interface (modules/tenant/service/service.go)
```go
package service

import (
	"context"
	"time"
	"wnapi/modules/tenant/dto"
	"wnapi/modules/tenant/internal"
)

// TenantService định nghĩa interface cho tenant service
type TenantService interface {
	// CRUD operations
	Create(ctx context.Context, req dto.CreateTenantRequest) (*dto.CreateTenantResponse, error)
	GetByID(ctx context.Context, tenantID uint) (*dto.GetTenantResponse, error)
	GetByCode(ctx context.Context, tenantCode string) (*dto.GetTenantResponse, error)
	Update(ctx context.Context, tenantID uint, req dto.UpdateTenantRequest) (*dto.UpdateTenantResponse, error)
	Delete(ctx context.Context, tenantID uint) error

	// List operations
	List(ctx context.Context, req dto.ListTenantsRequest) (*dto.ListTenantsResponse, *internal.PaginationMeta, error)

	// Business operations
	ActivateTenant(ctx context.Context, tenantID uint) (*dto.ActivateTenantResponse, error)
	SuspendTenant(ctx context.Context, tenantID uint, req dto.SuspendTenantRequest) (*dto.SuspendTenantResponse, error)
	ExtendSubscription(ctx context.Context, tenantID uint, req dto.ExtendSubscriptionRequest) (*dto.ExtendSubscriptionResponse, error)
	ValidateTenantAccess(ctx context.Context, tenantCode string) (*dto.ValidateTenantAccessResponse, error)

	// Utility operations
	CheckTenantCodeAvailability(ctx context.Context, tenantCode string) (bool, error)
	GetExpiredTenants(ctx context.Context) ([]*dto.TenantSummary, error)
}

// PaginationMeta cho cursor-based pagination
type PaginationMeta struct {
	NextCursor string `json:"next_cursor,omitempty"`
	HasMore    bool   `json:"has_more,omitempty"`
}
```

### Step 2: Tạo Service Implementation (modules/tenant/service/tenant_service.go)
```go
package service

import (
	"context"
	"fmt"
	"time"

	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/tracing"
	"wnapi/modules/tenant/dto"
	"wnapi/modules/tenant/internal"
	"wnapi/modules/tenant/models"
	"wnapi/modules/tenant/repository"
)

// tenantService triển khai TenantService interface
type tenantService struct {
	repo   repository.TenantRepository
	config *internal.TenantConfig
	logger logger.Logger
	// publisher *events.Publisher // Sẽ được thêm khi có event system
}

// NewTenantService tạo instance mới của tenant service
func NewTenantService(
	repo repository.TenantRepository,
	config *internal.TenantConfig,
	logger logger.Logger,
) TenantService {
	return &tenantService{
		repo:   repo,
		config: config,
		logger: logger,
	}
}

// Create tạo tenant mới với business validation
func (s *tenantService) Create(ctx context.Context, req dto.CreateTenantRequest) (*dto.CreateTenantResponse, error) {
	return tracing.WithSpanReturn(ctx, "tenant-service", "create", func(ctx context.Context) (*dto.CreateTenantResponse, error) {
		// Validate business rules
		if err := s.validateCreateRequest(ctx, req); err != nil {
			return nil, err
		}

		// Check tenant code availability
		exists, err := s.repo.ExistsByCode(ctx, req.TenantCode)
		if err != nil {
			s.logger.Error("Failed to check tenant code existence",
				logger.String("error", err.Error()),
				logger.String("tenant_code", req.TenantCode))
			return nil, internal.Wrap(internal.ErrCodeInternalServer, "Failed to validate tenant code", err)
		}

		if exists {
			return nil, internal.NewTenantCodeExistsError(req.TenantCode)
		}

		// Convert DTO to model
		tenant := req.ToModel()

		// Create tenant
		createdTenant, err := s.repo.Create(ctx, tenant)
		if err != nil {
			s.logger.Error("Failed to create tenant",
				logger.String("error", err.Error()),
				logger.String("tenant_code", req.TenantCode))
			return nil, err
		}

		// Convert to response
		var response dto.CreateTenantResponse
		response.FromModel(createdTenant)

		s.logger.Info("Tenant created successfully",
			logger.Uint("tenant_id", createdTenant.TenantID),
			logger.String("tenant_code", createdTenant.TenantCode))

		// TODO: Publish tenant created event
		// s.publishTenantCreatedEvent(ctx, createdTenant)

		return &response, nil
	})
}

// GetByID lấy tenant theo ID
func (s *tenantService) GetByID(ctx context.Context, tenantID uint) (*dto.GetTenantResponse, error) {
	return tracing.WithSpanReturn(ctx, "tenant-service", "get_by_id", func(ctx context.Context) (*dto.GetTenantResponse, error) {
		if tenantID == 0 {
			return nil, internal.New(internal.ErrCodeBadRequest, "vi")
		}

		tenant, err := s.repo.GetByID(ctx, tenantID)
		if err != nil {
			return nil, err
		}

		var response dto.GetTenantResponse
		response.FromModel(tenant)

		return &response, nil
	})
}

// GetByCode lấy tenant theo code
func (s *tenantService) GetByCode(ctx context.Context, tenantCode string) (*dto.GetTenantResponse, error) {
	return tracing.WithSpanReturn(ctx, "tenant-service", "get_by_code", func(ctx context.Context) (*dto.GetTenantResponse, error) {
		if tenantCode == "" {
			return nil, internal.New(internal.ErrCodeBadRequest, "vi")
		}

		tenant, err := s.repo.GetByCode(ctx, tenantCode)
		if err != nil {
			return nil, err
		}

		var response dto.GetTenantResponse
		response.FromModel(tenant)

		return &response, nil
	})
}

// Update cập nhật tenant với business validation
func (s *tenantService) Update(ctx context.Context, tenantID uint, req dto.UpdateTenantRequest) (*dto.UpdateTenantResponse, error) {
	return tracing.WithSpanReturn(ctx, "tenant-service", "update", func(ctx context.Context) (*dto.UpdateTenantResponse, error) {
		if tenantID == 0 {
			return nil, internal.New(internal.ErrCodeBadRequest, "vi")
		}

		if !req.HasChanges() {
			return nil, internal.New(internal.ErrCodeBadRequest, "vi")
		}

		// Get existing tenant
		tenant, err := s.repo.GetByID(ctx, tenantID)
		if err != nil {
			return nil, err
		}

		// Validate business rules
		if err := s.validateUpdateRequest(ctx, req, tenant); err != nil {
			return nil, err
		}

		// Apply changes
		req.ApplyToModel(tenant)

		// Update tenant
		updatedTenant, err := s.repo.Update(ctx, tenant)
		if err != nil {
			s.logger.Error("Failed to update tenant",
				logger.String("error", err.Error()),
				logger.Uint("tenant_id", tenantID))
			return nil, err
		}

		// Convert to response
		var response dto.UpdateTenantResponse
		response.FromModel(updatedTenant)

		s.logger.Info("Tenant updated successfully",
			logger.Uint("tenant_id", tenantID))

		// TODO: Publish tenant updated event
		// s.publishTenantUpdatedEvent(ctx, updatedTenant)

		return &response, nil
	})
}

// Delete xóa tenant với business validation
func (s *tenantService) Delete(ctx context.Context, tenantID uint) error {
	return tracing.WithSpan(ctx, "tenant-service", "delete", func(ctx context.Context) error {
		if tenantID == 0 {
			return internal.New(internal.ErrCodeBadRequest, "vi")
		}

		// Check if tenant exists
		tenant, err := s.repo.GetByID(ctx, tenantID)
		if err != nil {
			return err
		}

		// Business validation - có thể thêm rules như không xóa tenant đang active
		if err := s.validateDeleteRequest(ctx, tenant); err != nil {
			return err
		}

		// Delete tenant
		if err := s.repo.Delete(ctx, tenantID); err != nil {
			s.logger.Error("Failed to delete tenant",
				logger.String("error", err.Error()),
				logger.Uint("tenant_id", tenantID))
			return err
		}

		s.logger.Info("Tenant deleted successfully",
			logger.Uint("tenant_id", tenantID))

		// TODO: Publish tenant deleted event
		// s.publishTenantDeletedEvent(ctx, tenant)

		return nil
	})
}

// List lấy danh sách tenants với pagination
func (s *tenantService) List(ctx context.Context, req dto.ListTenantsRequest) (*dto.ListTenantsResponse, *PaginationMeta, error) {
	return tracing.WithSpanReturn(ctx, "tenant-service", "list", func(ctx context.Context) (*dto.ListTenantsResponse, *PaginationMeta, error) {
		limit := req.GetLimit()

		var tenants []*models.Tenant
		var nextCursor string
		var hasMore bool
		var err error

		// List by status or all
		if status := req.GetStatus(); status != "" {
			tenants, nextCursor, hasMore, err = s.repo.ListByStatus(ctx, status, limit, req.Cursor)
		} else {
			tenants, nextCursor, hasMore, err = s.repo.List(ctx, limit, req.Cursor)
		}

		if err != nil {
			s.logger.Error("Failed to list tenants",
				logger.String("error", err.Error()))
			return nil, nil, err
		}

		// Convert to response
		var response dto.ListTenantsResponse
		response.FromModels(tenants)

		// Create pagination meta
		meta := &PaginationMeta{
			NextCursor: nextCursor,
			HasMore:    hasMore,
		}

		return &response, meta, nil
	})
}

// ActivateTenant kích hoạt tenant
func (s *tenantService) ActivateTenant(ctx context.Context, tenantID uint) (*dto.ActivateTenantResponse, error) {
	return tracing.WithSpanReturn(ctx, "tenant-service", "activate", func(ctx context.Context) (*dto.ActivateTenantResponse, error) {
		if tenantID == 0 {
			return nil, internal.New(internal.ErrCodeBadRequest, "vi")
		}

		// Get tenant
		tenant, err := s.repo.GetByID(ctx, tenantID)
		if err != nil {
			return nil, err
		}

		// Check if already active
		if tenant.Status == models.TenantStatusActive {
			var response dto.ActivateTenantResponse
			response.FromModel(tenant)
			return &response, nil
		}

		// Update status
		tenant.Status = models.TenantStatusActive

		updatedTenant, err := s.repo.Update(ctx, tenant)
		if err != nil {
			s.logger.Error("Failed to activate tenant",
				logger.String("error", err.Error()),
				logger.Uint("tenant_id", tenantID))
			return nil, err
		}

		var response dto.ActivateTenantResponse
		response.FromModel(updatedTenant)

		s.logger.Info("Tenant activated successfully",
			logger.Uint("tenant_id", tenantID))

		// TODO: Publish tenant activated event
		// s.publishTenantActivatedEvent(ctx, updatedTenant)

		return &response, nil
	})
}

// SuspendTenant tạm ngưng tenant
func (s *tenantService) SuspendTenant(ctx context.Context, tenantID uint, req dto.SuspendTenantRequest) (*dto.SuspendTenantResponse, error) {
	return tracing.WithSpanReturn(ctx, "tenant-service", "suspend", func(ctx context.Context) (*dto.SuspendTenantResponse, error) {
		if tenantID == 0 {
			return nil, internal.New(internal.ErrCodeBadRequest, "vi")
		}

		// Get tenant
		tenant, err := s.repo.GetByID(ctx, tenantID)
		if err != nil {
			return nil, err
		}

		// Update status
		tenant.Status = models.TenantStatusSuspended

		updatedTenant, err := s.repo.Update(ctx, tenant)
		if err != nil {
			s.logger.Error("Failed to suspend tenant",
				logger.String("error", err.Error()),
				logger.Uint("tenant_id", tenantID))
			return nil, err
		}

		var response dto.SuspendTenantResponse
		response.FromModel(updatedTenant, req.Reason)

		s.logger.Info("Tenant suspended successfully",
			logger.Uint("tenant_id", tenantID),
			logger.String("reason", req.Reason))

		// TODO: Publish tenant suspended event
		// s.publishTenantSuspendedEvent(ctx, updatedTenant, req.Reason)

		return &response, nil
	})
}

// ExtendSubscription gia hạn subscription
func (s *tenantService) ExtendSubscription(ctx context.Context, tenantID uint, req dto.ExtendSubscriptionRequest) (*dto.ExtendSubscriptionResponse, error) {
	return tracing.WithSpanReturn(ctx, "tenant-service", "extend_subscription", func(ctx context.Context) (*dto.ExtendSubscriptionResponse, error) {
		if tenantID == 0 {
			return nil, internal.New(internal.ErrCodeBadRequest, "vi")
		}

		// Validate expiration date
		if req.ExpiresAt.Before(time.Now()) {
			return nil, internal.NewValidation(internal.ErrCodeValidationFailed, "vi", map[string]string{
				"expires_at": "Ngày hết hạn phải sau thời điểm hiện tại",
			})
		}

		// Get tenant
		tenant, err := s.repo.GetByID(ctx, tenantID)
		if err != nil {
			return nil, err
		}

		// Update subscription
		tenant.SubscriptionExpiresAt = &req.ExpiresAt

		updatedTenant, err := s.repo.Update(ctx, tenant)
		if err != nil {
			s.logger.Error("Failed to extend subscription",
				logger.String("error", err.Error()),
				logger.Uint("tenant_id", tenantID))
			return nil, err
		}

		var response dto.ExtendSubscriptionResponse
		response.FromModel(updatedTenant)

		s.logger.Info("Subscription extended successfully",
			logger.Uint("tenant_id", tenantID),
			logger.Time("expires_at", req.ExpiresAt))

		// TODO: Publish subscription extended event
		// s.publishSubscriptionExtendedEvent(ctx, updatedTenant)

		return &response, nil
	})
}

// ValidateTenantAccess kiểm tra quyền truy cập tenant
func (s *tenantService) ValidateTenantAccess(ctx context.Context, tenantCode string) (*dto.ValidateTenantAccessResponse, error) {
	return tracing.WithSpanReturn(ctx, "tenant-service", "validate_access", func(ctx context.Context) (*dto.ValidateTenantAccessResponse, error) {
		if tenantCode == "" {
			return nil, internal.New(internal.ErrCodeBadRequest, "vi")
		}

		tenant, err := s.repo.GetByCode(ctx, tenantCode)
		if err != nil {
			return nil, err
		}

		var response dto.ValidateTenantAccessResponse
		response.FromModel(tenant)

		return &response, nil
	})
}

// CheckTenantCodeAvailability kiểm tra tính khả dụng của tenant code
func (s *tenantService) CheckTenantCodeAvailability(ctx context.Context, tenantCode string) (bool, error) {
	return tracing.WithSpanReturn(ctx, "tenant-service", "check_code_availability", func(ctx context.Context) (bool, error) {
		if tenantCode == "" {
			return false, internal.New(internal.ErrCodeBadRequest, "vi")
		}

		exists, err := s.repo.ExistsByCode(ctx, tenantCode)
		if err != nil {
			return false, err
		}

		return !exists, nil // Available if not exists
	})
}

// GetExpiredTenants lấy danh sách tenants đã hết hạn
func (s *tenantService) GetExpiredTenants(ctx context.Context) ([]*dto.TenantSummary, error) {
	return tracing.WithSpanReturn(ctx, "tenant-service", "get_expired", func(ctx context.Context) ([]*dto.TenantSummary, error) {
		tenants, err := s.repo.GetExpiredTenants(ctx)
		if err != nil {
			return nil, err
		}

		summaries := make([]*dto.TenantSummary, len(tenants))
		for i, tenant := range tenants {
			summaries[i] = &dto.TenantSummary{}
			// Convert model to summary (similar to FromModel method)
			summaries[i].TenantID = tenant.TenantID
			summaries[i].TenantName = tenant.TenantName
			summaries[i].TenantCode = tenant.TenantCode
			summaries[i].Status = tenant.Status
			summaries[i].PlanType = tenant.PlanType
			summaries[i].SubscriptionExpiresAt = tenant.SubscriptionExpiresAt
			summaries[i].IsActive = tenant.IsActive()
			summaries[i].IsExpired = tenant.IsExpired()
			summaries[i].CanAccess = tenant.CanAccess()
			summaries[i].CreatedAt = tenant.CreatedAt
			summaries[i].UpdatedAt = tenant.UpdatedAt
		}

		return summaries, nil
	})
}

// Validation helper methods
func (s *tenantService) validateCreateRequest(ctx context.Context, req dto.CreateTenantRequest) error {
	// Validate tenant name
	if err := internal.ValidateTenantName(req.TenantName); err != nil {
		return err
	}

	// Validate tenant code
	if err := internal.ValidateTenantCode(req.TenantCode); err != nil {
		return err
	}

	// Validate plan type
	if req.PlanType != "" {
		if err := internal.ValidatePlanType(req.PlanType); err != nil {
			return err
		}
	}

	// Validate subscription expiration
	if req.SubscriptionExpiresAt != nil && req.SubscriptionExpiresAt.Before(time.Now()) {
		return internal.NewValidation(internal.ErrCodeValidationFailed, "vi", map[string]string{
			"subscription_expires_at": "Ngày hết hạn phải sau thời điểm hiện tại",
		})
	}

	return nil
}

func (s *tenantService) validateUpdateRequest(ctx context.Context, req dto.UpdateTenantRequest, tenant *models.Tenant) error {
	// Validate tenant name if provided
	if req.TenantName != nil {
		if err := internal.ValidateTenantName(*req.TenantName); err != nil {
			return err
		}
	}

	// Validate tenant code if provided
	if req.TenantCode != nil {
		if err := internal.ValidateTenantCode(*req.TenantCode); err != nil {
			return err
		}

		// Check if new code is different and available
		if *req.TenantCode != tenant.TenantCode {
			exists, err := s.repo.ExistsByCode(ctx, *req.TenantCode)
			if err != nil {
				return internal.Wrap(internal.ErrCodeInternalServer, "Failed to validate tenant code", err)
			}
			if exists {
				return internal.NewTenantCodeExistsError(*req.TenantCode)
			}
		}
	}

	// Validate plan type if provided
	if req.PlanType != nil {
		if err := internal.ValidatePlanType(*req.PlanType); err != nil {
			return err
		}
	}

	// Validate subscription expiration if provided
	if req.SubscriptionExpiresAt != nil && req.SubscriptionExpiresAt.Before(time.Now()) {
		return internal.NewValidation(internal.ErrCodeValidationFailed, "vi", map[string]string{
			"subscription_expires_at": "Ngày hết hạn phải sau thời điểm hiện tại",
		})
	}

	return nil
}

func (s *tenantService) validateDeleteRequest(ctx context.Context, tenant *models.Tenant) error {
	// Business rule: Có thể thêm validation như không xóa tenant đang có users active
	// Hiện tại cho phép xóa tất cả tenants

	// Example validation (có thể uncomment khi cần):
	// if tenant.Status == models.TenantStatusActive {
	//     return internal.NewValidation(internal.ErrCodeValidationFailed, "vi", map[string]string{
	//         "status": "Không thể xóa tenant đang hoạt động",
	//     })
	// }

	return nil
}
```

## File Paths
- `modules/tenant/service/service.go`
- `modules/tenant/service/tenant_service.go`

## Acceptance Criteria
1. ✅ Service interface tuân thủ patterns từ modules/auth
2. ✅ Comprehensive business logic validation
3. ✅ Repository pattern integration
4. ✅ Proper error handling và logging
5. ✅ Transaction management ready
6. ✅ Event publishing integration points
7. ✅ Code builds successfully với `make build`

## Dependencies
- Task 01: Tenant Infrastructure Setup
- Task 02: Tenant Models and Internal Types
- Task 03: Tenant Error Handling Setup
- Task 04: Tenant Repository Implementation
- Task 05: Tenant DTOs

## Estimated Time
2-3 hours

## Notes
- Foundation cho API layer
- Business logic validation comprehensive
- Event publishing integration points prepared
- Chuẩn bị cho handler implementation
