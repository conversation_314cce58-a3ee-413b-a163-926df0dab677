# Task 02: Tenant Data Models and Internal Types

## Objective
Tạo data models và internal types cho tenant module dựa trên existing database schema và patterns từ modules/auth.

## Input
- Database schema từ modules/tenant/migrations/001_create_tenants.up.sql
- Patterns từ modules/auth/models/ và modules/auth/internal/types.go

## Output
- Tenant GORM model
- Internal types và interfaces
- Status enums và constants

## Requirements
1. Sử dụng INT UNSIGNED cho tất cả ID fields
2. GORM model mapping với database schema
3. Proper validation tags
4. Status enums cho tenant status
5. Interface definitions cho service layer

## Implementation Steps

### Step 1: Tạo Tenant Model (modules/tenant/models/tenant.go)
```go
package models

import (
	"time"
)

// TenantStatus định nghĩa các trạng thái của tenant
type TenantStatus string

const (
	TenantStatusActive    TenantStatus = "active"
	TenantStatusInactive  TenantStatus = "inactive"
	TenantStatusSuspended TenantStatus = "suspended"
	TenantStatusTrial     TenantStatus = "trial"
)

// IsValid kiểm tra tính hợp lệ của tenant status
func (s TenantStatus) IsValid() bool {
	switch s {
	case TenantStatusActive, TenantStatusInactive, TenantStatusSuspended, TenantStatusTrial:
		return true
	default:
		return false
	}
}

// String trả về string representation của status
func (s TenantStatus) String() string {
	return string(s)
}

// Tenant model mapping với database table
type Tenant struct {
	TenantID                uint          `gorm:"column:tenant_id;primaryKey;autoIncrement" json:"tenant_id"`
	TenantName              string        `gorm:"column:tenant_name;not null" json:"tenant_name"`
	TenantCode              string        `gorm:"column:tenant_code;uniqueIndex;not null" json:"tenant_code"`
	Status                  TenantStatus  `gorm:"column:status;default:active" json:"status"`
	PlanType                string        `gorm:"column:plan_type;default:standard" json:"plan_type"`
	SubscriptionExpiresAt   *time.Time    `gorm:"column:subscription_expires_at" json:"subscription_expires_at"`
	CreatedAt               time.Time     `gorm:"column:created_at" json:"created_at"`
	UpdatedAt               time.Time     `gorm:"column:updated_at" json:"updated_at"`
}

// TableName trả về tên table trong database
func (Tenant) TableName() string {
	return "tenants"
}

// IsActive kiểm tra tenant có đang active không
func (t *Tenant) IsActive() bool {
	return t.Status == TenantStatusActive
}

// IsExpired kiểm tra subscription có hết hạn không
func (t *Tenant) IsExpired() bool {
	if t.SubscriptionExpiresAt == nil {
		return false
	}
	return t.SubscriptionExpiresAt.Before(time.Now())
}

// CanAccess kiểm tra tenant có thể truy cập hệ thống không
func (t *Tenant) CanAccess() bool {
	return t.IsActive() && !t.IsExpired()
}
```

### Step 2: Tạo Internal Types (modules/tenant/internal/types.go)
```go
package internal

import (
	"context"
	"wnapi/modules/tenant/models"
)

// TenantInfo chứa thông tin tenant đã được xử lý
type TenantInfo struct {
	TenantID                uint                  `json:"tenant_id"`
	TenantName              string                `json:"tenant_name"`
	TenantCode              string                `json:"tenant_code"`
	Status                  models.TenantStatus   `json:"status"`
	PlanType                string                `json:"plan_type"`
	SubscriptionExpiresAt   *time.Time            `json:"subscription_expires_at"`
	IsActive                bool                  `json:"is_active"`
	IsExpired               bool                  `json:"is_expired"`
	CanAccess               bool                  `json:"can_access"`
	CreatedAt               time.Time             `json:"created_at"`
	UpdatedAt               time.Time             `json:"updated_at"`
}

// Repository định nghĩa interface cho tenant repository
type Repository interface {
	// CRUD operations
	Create(ctx context.Context, tenant *models.Tenant) (*models.Tenant, error)
	GetByID(ctx context.Context, tenantID uint) (*models.Tenant, error)
	GetByCode(ctx context.Context, tenantCode string) (*models.Tenant, error)
	Update(ctx context.Context, tenant *models.Tenant) (*models.Tenant, error)
	Delete(ctx context.Context, tenantID uint) error
	
	// List operations với cursor-based pagination
	List(ctx context.Context, limit int, cursor string) ([]*models.Tenant, string, bool, error)
	ListByStatus(ctx context.Context, status models.TenantStatus, limit int, cursor string) ([]*models.Tenant, string, bool, error)
	
	// Business operations
	ExistsByCode(ctx context.Context, tenantCode string) (bool, error)
	CountByStatus(ctx context.Context, status models.TenantStatus) (int64, error)
	GetExpiredTenants(ctx context.Context) ([]*models.Tenant, error)
}

// Service định nghĩa interface cho tenant service
type Service interface {
	// CRUD operations
	Create(ctx context.Context, req CreateTenantRequest) (*TenantInfo, error)
	GetByID(ctx context.Context, tenantID uint) (*TenantInfo, error)
	GetByCode(ctx context.Context, tenantCode string) (*TenantInfo, error)
	Update(ctx context.Context, tenantID uint, req UpdateTenantRequest) (*TenantInfo, error)
	Delete(ctx context.Context, tenantID uint) error
	
	// List operations
	List(ctx context.Context, req ListTenantsRequest) (*ListTenantsResponse, error)
	
	// Business operations
	ActivateTenant(ctx context.Context, tenantID uint) (*TenantInfo, error)
	SuspendTenant(ctx context.Context, tenantID uint, reason string) (*TenantInfo, error)
	ExtendSubscription(ctx context.Context, tenantID uint, expiresAt time.Time) (*TenantInfo, error)
	ValidateTenantAccess(ctx context.Context, tenantCode string) (*TenantInfo, error)
}

// Request/Response types sẽ được định nghĩa trong dto package
type CreateTenantRequest interface{}
type UpdateTenantRequest interface{}
type ListTenantsRequest interface{}
type ListTenantsResponse interface{}
```

### Step 3: Tạo Constants (modules/tenant/internal/constants.go)
```go
package internal

// Plan types
const (
	PlanTypeStandard   = "standard"
	PlanTypePremium    = "premium"
	PlanTypeEnterprise = "enterprise"
	PlanTypeTrial      = "trial"
)

// Validation constants
const (
	MinTenantNameLength = 3
	MaxTenantNameLength = 255
	MinTenantCodeLength = 3
	MaxTenantCodeLength = 50
	
	// Tenant code pattern (alphanumeric + hyphens)
	TenantCodePattern = `^[a-zA-Z0-9-]+$`
)

// Default values
const (
	DefaultPlanType        = PlanTypeStandard
	DefaultListLimit       = 20
	MaxListLimit          = 100
	DefaultTrialDuration  = 30 // days
)

// Error messages
const (
	ErrMsgTenantNotFound     = "Tenant không tồn tại"
	ErrMsgTenantCodeExists   = "Mã tenant đã tồn tại"
	ErrMsgTenantInactive     = "Tenant không hoạt động"
	ErrMsgTenantExpired      = "Tenant đã hết hạn"
	ErrMsgInvalidTenantCode  = "Mã tenant không hợp lệ"
	ErrMsgInvalidPlanType    = "Loại gói không hợp lệ"
)

// ValidPlanTypes danh sách các plan types hợp lệ
var ValidPlanTypes = []string{
	PlanTypeStandard,
	PlanTypePremium,
	PlanTypeEnterprise,
	PlanTypeTrial,
}

// IsValidPlanType kiểm tra plan type có hợp lệ không
func IsValidPlanType(planType string) bool {
	for _, valid := range ValidPlanTypes {
		if planType == valid {
			return true
		}
	}
	return false
}
```

## File Paths
- `modules/tenant/models/tenant.go`
- `modules/tenant/internal/types.go`
- `modules/tenant/internal/constants.go`

## Acceptance Criteria
1. ✅ Tenant model maps correctly với database schema
2. ✅ Sử dụng INT UNSIGNED cho tenant_id
3. ✅ Status enums và validation methods
4. ✅ Repository và Service interfaces định nghĩa đầy đủ
5. ✅ Constants và validation helpers
6. ✅ Code builds successfully với `make build`
7. ✅ Proper GORM tags và JSON serialization

## Dependencies
- Task 01: Tenant Infrastructure Setup

## Estimated Time
1-2 hours

## Notes
- Foundation cho repository và service implementations
- Tuân thủ patterns từ modules/auth/models và modules/auth/internal
- Chuẩn bị cho business logic implementation
