# Tenant Module Implementation Plan

## Tổng Quan

Triển khai module tenant theo đúng kiến trúc multi-tenant đã được thiết lập trong module auth. Module này sẽ quản lý thông tin tenant và cung cấp các API CRUD cho việc quản lý tenant trong hệ thống.

## Kiến Trúc Tham Chiếu

Module tenant sẽ tuân thủ hoàn toàn kiến trúc của `modules/auth`:
- Directory structure: api/, repository/, service/, dto/, models/, migrations/
- Multi-tenant architecture với tenantID parameters
- Middleware ordering: tenant -> auth -> rbac
- Repository/service interface patterns
- GORM cho CRUD operations, raw SQL cho list operations với cursor-based pagination
- Combined request/response DTOs trong single files

## Cấu Trúc Thư Mục Mục Tiêu

```
modules/tenant/
├── api/                    # API layer
│   ├── handlers/          # HTTP request handlers
│   └── routes.go          # Route definitions
├── service/               # Business logic layer
│   └── tenant_service.go  # Core tenant service
├── repository/            # Data access layer
│   ├── repository.go      # Repository interfaces
│   └── mysql/            # MySQL implementations
├── models/               # Data models
├── dto/                  # Request/Response DTOs
├── migrations/           # Database migrations (đã có)
├── internal/            # Internal configurations
├── bootstrap.go         # Dependencies initialization
└── module.go           # Module registration
```

## Danh Sách Tasks

### Giai Đoạn 1: Module Infrastructure (Tasks 01-03)
- **01-tenant-infrastructure.md**: Thiết lập cấu trúc module cơ bản
- **02-tenant-models.md**: Tạo data models và internal types
- **03-tenant-errors.md**: Thiết lập error handling patterns

### Giai Đoạn 2: Data Layer (Tasks 04-06)
- **04-tenant-repository.md**: Triển khai repository interfaces và MySQL implementation
- **05-tenant-dto.md**: Tạo Data Transfer Objects
- **06-tenant-service.md**: Triển khai business logic layer

### Giai Đoạn 3: API Layer (Tasks 07-09)
- **07-tenant-handlers.md**: Triển khai HTTP handlers
- **08-tenant-routes.md**: Thiết lập routing và middleware
- **09-tenant-api-integration.md**: Tích hợp API với existing systems

### Giai Đoạn 4: Multi-Tenant Integration (Tasks 10-12)
- **10-tenant-multi-tenant.md**: Tích hợp multi-tenant architecture
- **11-tenant-auth-integration.md**: Tích hợp với auth và RBAC systems
- **12-tenant-permissions.md**: Thiết lập permission system

### Giai Đoạn 5: Testing & Documentation (Tasks 13-15)
- **13-tenant-unit-tests.md**: Viết unit tests cho tất cả components
- **14-tenant-api-collection.md**: Tạo Bruno API collection
- **15-tenant-documentation.md**: Viết documentation hoàn chỉnh

## Thứ Tự Thực Hiện

Tasks phải được thực hiện theo thứ tự từ 01 đến 15 do dependencies:
- Tasks 01-03: Có thể thực hiện song song
- Tasks 04-06: Phụ thuộc vào 01-03
- Tasks 07-09: Phụ thuộc vào 04-06
- Tasks 10-12: Phụ thuộc vào 07-09
- Tasks 13-15: Phụ thuộc vào tất cả tasks trước

## Acceptance Criteria Chung

Mỗi task phải đảm bảo:
1. ✅ Code build thành công với `make build`
2. ✅ Tuân thủ patterns từ modules/auth
3. ✅ Multi-tenant architecture compliance
4. ✅ Error handling sử dụng internal/pkg/errors và internal/pkg/response
5. ✅ Proper dependency injection
6. ✅ Unit tests coverage >= 80%
7. ✅ Documentation đầy đủ bằng tiếng Việt

## Estimated Timeline

- **Total**: 15 tasks × 1-2 hours = 15-30 hours
- **Giai đoạn 1**: 3-6 hours
- **Giai đoạn 2**: 4-8 hours  
- **Giai đoạn 3**: 4-8 hours
- **Giai đoạn 4**: 2-4 hours
- **Giai đoạn 5**: 2-4 hours

## Dependencies

- ✅ modules/auth (reference implementation)
- ✅ internal/pkg/errors (error handling)
- ✅ internal/pkg/response (API responses)
- ✅ internal/pkg/permission (permission system)
- ✅ Database migrations (đã có sẵn)

## Notes

- Tất cả ID fields sử dụng INT UNSIGNED theo codebase convention
- API responses tuân thủ format chuẩn với data/meta structure
- Cursor-based pagination cho list operations
- JWT authentication middleware integration
- Vietnamese documentation và comments
