# Task 05: Tenant Data Transfer Objects (DTOs)

## Objective
Tạo Data Transfer Objects cho tenant module theo patterns từ modules/auth/dto, với combined request/response types trong single files và proper validation tags.

## Input
- DTO patterns từ modules/auth/dto/
- API response format từ docs/api-response-format.md
- Business requirements từ tasks trước

## Output
- Request/Response DTOs cho tất cả operations
- Validation tags và rules
- Cursor-based pagination DTOs
- Combined request/response trong single files

## Requirements
1. Combined request/response DTOs trong single files
2. Proper validation tags (binding, json)
3. Cursor-based pagination support
4. API response format compliance
5. Multi-language validation messages
6. Business logic validation

## Implementation Steps

### Step 1: Tạo Create Tenant DTO (modules/tenant/dto/create_tenant.go)
```go
package dto

import (
	"time"
	"wnapi/modules/tenant/models"
)

// CreateTenantRequest là request body cho API tạo tenant
type CreateTenantRequest struct {
	TenantName              string    `json:"tenant_name" binding:"required,min=3,max=255"`
	TenantCode              string    `json:"tenant_code" binding:"required,min=3,max=50,alphanum"`
	PlanType                string    `json:"plan_type" binding:"omitempty,oneof=standard premium enterprise trial"`
	SubscriptionExpiresAt   *time.Time `json:"subscription_expires_at" binding:"omitempty"`
}

// CreateTenantResponse là response cho API tạo tenant
type CreateTenantResponse struct {
	TenantID                uint                  `json:"tenant_id"`
	TenantName              string                `json:"tenant_name"`
	TenantCode              string                `json:"tenant_code"`
	Status                  models.TenantStatus   `json:"status"`
	PlanType                string                `json:"plan_type"`
	SubscriptionExpiresAt   *time.Time            `json:"subscription_expires_at"`
	IsActive                bool                  `json:"is_active"`
	IsExpired               bool                  `json:"is_expired"`
	CanAccess               bool                  `json:"can_access"`
	CreatedAt               time.Time             `json:"created_at"`
	UpdatedAt               time.Time             `json:"updated_at"`
}

// ToModel chuyển đổi request thành model
func (req *CreateTenantRequest) ToModel() *models.Tenant {
	tenant := &models.Tenant{
		TenantName:            req.TenantName,
		TenantCode:            req.TenantCode,
		Status:                models.TenantStatusActive, // Default status
		PlanType:              req.PlanType,
		SubscriptionExpiresAt: req.SubscriptionExpiresAt,
	}
	
	// Set default plan type if not provided
	if tenant.PlanType == "" {
		tenant.PlanType = "standard"
	}
	
	return tenant
}

// FromModel chuyển đổi model thành response
func (resp *CreateTenantResponse) FromModel(tenant *models.Tenant) {
	resp.TenantID = tenant.TenantID
	resp.TenantName = tenant.TenantName
	resp.TenantCode = tenant.TenantCode
	resp.Status = tenant.Status
	resp.PlanType = tenant.PlanType
	resp.SubscriptionExpiresAt = tenant.SubscriptionExpiresAt
	resp.IsActive = tenant.IsActive()
	resp.IsExpired = tenant.IsExpired()
	resp.CanAccess = tenant.CanAccess()
	resp.CreatedAt = tenant.CreatedAt
	resp.UpdatedAt = tenant.UpdatedAt
}
```

### Step 2: Tạo Update Tenant DTO (modules/tenant/dto/update_tenant.go)
```go
package dto

import (
	"time"
	"wnapi/modules/tenant/models"
)

// UpdateTenantRequest là request body cho API cập nhật tenant
type UpdateTenantRequest struct {
	TenantName              *string    `json:"tenant_name" binding:"omitempty,min=3,max=255"`
	TenantCode              *string    `json:"tenant_code" binding:"omitempty,min=3,max=50,alphanum"`
	Status                  *string    `json:"status" binding:"omitempty,oneof=active inactive suspended trial"`
	PlanType                *string    `json:"plan_type" binding:"omitempty,oneof=standard premium enterprise trial"`
	SubscriptionExpiresAt   *time.Time `json:"subscription_expires_at" binding:"omitempty"`
}

// UpdateTenantResponse là response cho API cập nhật tenant
type UpdateTenantResponse struct {
	TenantID                uint                  `json:"tenant_id"`
	TenantName              string                `json:"tenant_name"`
	TenantCode              string                `json:"tenant_code"`
	Status                  models.TenantStatus   `json:"status"`
	PlanType                string                `json:"plan_type"`
	SubscriptionExpiresAt   *time.Time            `json:"subscription_expires_at"`
	IsActive                bool                  `json:"is_active"`
	IsExpired               bool                  `json:"is_expired"`
	CanAccess               bool                  `json:"can_access"`
	CreatedAt               time.Time             `json:"created_at"`
	UpdatedAt               time.Time             `json:"updated_at"`
}

// ApplyToModel áp dụng changes từ request vào model
func (req *UpdateTenantRequest) ApplyToModel(tenant *models.Tenant) {
	if req.TenantName != nil {
		tenant.TenantName = *req.TenantName
	}
	
	if req.TenantCode != nil {
		tenant.TenantCode = *req.TenantCode
	}
	
	if req.Status != nil {
		tenant.Status = models.TenantStatus(*req.Status)
	}
	
	if req.PlanType != nil {
		tenant.PlanType = *req.PlanType
	}
	
	if req.SubscriptionExpiresAt != nil {
		tenant.SubscriptionExpiresAt = req.SubscriptionExpiresAt
	}
}

// HasChanges kiểm tra request có thay đổi nào không
func (req *UpdateTenantRequest) HasChanges() bool {
	return req.TenantName != nil ||
		   req.TenantCode != nil ||
		   req.Status != nil ||
		   req.PlanType != nil ||
		   req.SubscriptionExpiresAt != nil
}

// FromModel chuyển đổi model thành response
func (resp *UpdateTenantResponse) FromModel(tenant *models.Tenant) {
	resp.TenantID = tenant.TenantID
	resp.TenantName = tenant.TenantName
	resp.TenantCode = tenant.TenantCode
	resp.Status = tenant.Status
	resp.PlanType = tenant.PlanType
	resp.SubscriptionExpiresAt = tenant.SubscriptionExpiresAt
	resp.IsActive = tenant.IsActive()
	resp.IsExpired = tenant.IsExpired()
	resp.CanAccess = tenant.CanAccess()
	resp.CreatedAt = tenant.CreatedAt
	resp.UpdatedAt = tenant.UpdatedAt
}
```

### Step 3: Tạo Get Tenant DTO (modules/tenant/dto/get_tenant.go)
```go
package dto

import (
	"time"
	"wnapi/modules/tenant/models"
)

// GetTenantResponse là response cho API lấy thông tin tenant
type GetTenantResponse struct {
	TenantID                uint                  `json:"tenant_id"`
	TenantName              string                `json:"tenant_name"`
	TenantCode              string                `json:"tenant_code"`
	Status                  models.TenantStatus   `json:"status"`
	PlanType                string                `json:"plan_type"`
	SubscriptionExpiresAt   *time.Time            `json:"subscription_expires_at"`
	IsActive                bool                  `json:"is_active"`
	IsExpired               bool                  `json:"is_expired"`
	CanAccess               bool                  `json:"can_access"`
	CreatedAt               time.Time             `json:"created_at"`
	UpdatedAt               time.Time             `json:"updated_at"`
}

// FromModel chuyển đổi model thành response
func (resp *GetTenantResponse) FromModel(tenant *models.Tenant) {
	resp.TenantID = tenant.TenantID
	resp.TenantName = tenant.TenantName
	resp.TenantCode = tenant.TenantCode
	resp.Status = tenant.Status
	resp.PlanType = tenant.PlanType
	resp.SubscriptionExpiresAt = tenant.SubscriptionExpiresAt
	resp.IsActive = tenant.IsActive()
	resp.IsExpired = tenant.IsExpired()
	resp.CanAccess = tenant.CanAccess()
	resp.CreatedAt = tenant.CreatedAt
	resp.UpdatedAt = tenant.UpdatedAt
}
```

### Step 4: Tạo List Tenants DTO (modules/tenant/dto/list_tenants.go)
```go
package dto

import (
	"time"
	"wnapi/modules/tenant/models"
)

// ListTenantsRequest là request cho API lấy danh sách tenants
type ListTenantsRequest struct {
	Limit  int    `form:"limit" binding:"omitempty,min=1,max=100"`
	Cursor string `form:"cursor" binding:"omitempty"`
	Status string `form:"status" binding:"omitempty,oneof=active inactive suspended trial"`
}

// ListTenantsResponse là response cho API lấy danh sách tenants
type ListTenantsResponse struct {
	Tenants []TenantSummary `json:"tenants"`
}

// TenantSummary là thông tin tóm tắt của tenant trong list
type TenantSummary struct {
	TenantID                uint                  `json:"tenant_id"`
	TenantName              string                `json:"tenant_name"`
	TenantCode              string                `json:"tenant_code"`
	Status                  models.TenantStatus   `json:"status"`
	PlanType                string                `json:"plan_type"`
	SubscriptionExpiresAt   *time.Time            `json:"subscription_expires_at"`
	IsActive                bool                  `json:"is_active"`
	IsExpired               bool                  `json:"is_expired"`
	CanAccess               bool                  `json:"can_access"`
	CreatedAt               time.Time             `json:"created_at"`
	UpdatedAt               time.Time             `json:"updated_at"`
}

// GetLimit trả về limit với default value
func (req *ListTenantsRequest) GetLimit() int {
	if req.Limit <= 0 {
		return 20 // Default limit
	}
	if req.Limit > 100 {
		return 100 // Max limit
	}
	return req.Limit
}

// GetStatus trả về status filter
func (req *ListTenantsRequest) GetStatus() models.TenantStatus {
	if req.Status == "" {
		return ""
	}
	return models.TenantStatus(req.Status)
}

// FromModels chuyển đổi slice models thành response
func (resp *ListTenantsResponse) FromModels(tenants []*models.Tenant) {
	resp.Tenants = make([]TenantSummary, len(tenants))
	
	for i, tenant := range tenants {
		resp.Tenants[i] = TenantSummary{
			TenantID:              tenant.TenantID,
			TenantName:            tenant.TenantName,
			TenantCode:            tenant.TenantCode,
			Status:                tenant.Status,
			PlanType:              tenant.PlanType,
			SubscriptionExpiresAt: tenant.SubscriptionExpiresAt,
			IsActive:              tenant.IsActive(),
			IsExpired:             tenant.IsExpired(),
			CanAccess:             tenant.CanAccess(),
			CreatedAt:             tenant.CreatedAt,
			UpdatedAt:             tenant.UpdatedAt,
		}
	}
}
```

### Step 5: Tạo Business Operation DTOs (modules/tenant/dto/tenant_operations.go)
```go
package dto

import (
	"time"
	"wnapi/modules/tenant/models"
)

// ActivateTenantResponse là response cho API kích hoạt tenant
type ActivateTenantResponse struct {
	TenantID                uint                  `json:"tenant_id"`
	TenantName              string                `json:"tenant_name"`
	TenantCode              string                `json:"tenant_code"`
	Status                  models.TenantStatus   `json:"status"`
	PlanType                string                `json:"plan_type"`
	SubscriptionExpiresAt   *time.Time            `json:"subscription_expires_at"`
	IsActive                bool                  `json:"is_active"`
	IsExpired               bool                  `json:"is_expired"`
	CanAccess               bool                  `json:"can_access"`
	CreatedAt               time.Time             `json:"created_at"`
	UpdatedAt               time.Time             `json:"updated_at"`
}

// SuspendTenantRequest là request cho API tạm ngưng tenant
type SuspendTenantRequest struct {
	Reason string `json:"reason" binding:"required,min=10,max=500"`
}

// SuspendTenantResponse là response cho API tạm ngưng tenant
type SuspendTenantResponse struct {
	TenantID                uint                  `json:"tenant_id"`
	TenantName              string                `json:"tenant_name"`
	TenantCode              string                `json:"tenant_code"`
	Status                  models.TenantStatus   `json:"status"`
	PlanType                string                `json:"plan_type"`
	SubscriptionExpiresAt   *time.Time            `json:"subscription_expires_at"`
	IsActive                bool                  `json:"is_active"`
	IsExpired               bool                  `json:"is_expired"`
	CanAccess               bool                  `json:"can_access"`
	SuspendReason           string                `json:"suspend_reason"`
	CreatedAt               time.Time             `json:"created_at"`
	UpdatedAt               time.Time             `json:"updated_at"`
}

// ExtendSubscriptionRequest là request cho API gia hạn subscription
type ExtendSubscriptionRequest struct {
	ExpiresAt time.Time `json:"expires_at" binding:"required"`
}

// ExtendSubscriptionResponse là response cho API gia hạn subscription
type ExtendSubscriptionResponse struct {
	TenantID                uint                  `json:"tenant_id"`
	TenantName              string                `json:"tenant_name"`
	TenantCode              string                `json:"tenant_code"`
	Status                  models.TenantStatus   `json:"status"`
	PlanType                string                `json:"plan_type"`
	SubscriptionExpiresAt   *time.Time            `json:"subscription_expires_at"`
	IsActive                bool                  `json:"is_active"`
	IsExpired               bool                  `json:"is_expired"`
	CanAccess               bool                  `json:"can_access"`
	CreatedAt               time.Time             `json:"created_at"`
	UpdatedAt               time.Time             `json:"updated_at"`
}

// ValidateTenantAccessResponse là response cho API validate tenant access
type ValidateTenantAccessResponse struct {
	TenantID                uint                  `json:"tenant_id"`
	TenantName              string                `json:"tenant_name"`
	TenantCode              string                `json:"tenant_code"`
	Status                  models.TenantStatus   `json:"status"`
	PlanType                string                `json:"plan_type"`
	SubscriptionExpiresAt   *time.Time            `json:"subscription_expires_at"`
	IsActive                bool                  `json:"is_active"`
	IsExpired               bool                  `json:"is_expired"`
	CanAccess               bool                  `json:"can_access"`
	AccessGranted           bool                  `json:"access_granted"`
	AccessMessage           string                `json:"access_message"`
	CreatedAt               time.Time             `json:"created_at"`
	UpdatedAt               time.Time             `json:"updated_at"`
}

// Helper methods để convert từ model
func (resp *ActivateTenantResponse) FromModel(tenant *models.Tenant) {
	resp.TenantID = tenant.TenantID
	resp.TenantName = tenant.TenantName
	resp.TenantCode = tenant.TenantCode
	resp.Status = tenant.Status
	resp.PlanType = tenant.PlanType
	resp.SubscriptionExpiresAt = tenant.SubscriptionExpiresAt
	resp.IsActive = tenant.IsActive()
	resp.IsExpired = tenant.IsExpired()
	resp.CanAccess = tenant.CanAccess()
	resp.CreatedAt = tenant.CreatedAt
	resp.UpdatedAt = tenant.UpdatedAt
}

func (resp *SuspendTenantResponse) FromModel(tenant *models.Tenant, reason string) {
	resp.TenantID = tenant.TenantID
	resp.TenantName = tenant.TenantName
	resp.TenantCode = tenant.TenantCode
	resp.Status = tenant.Status
	resp.PlanType = tenant.PlanType
	resp.SubscriptionExpiresAt = tenant.SubscriptionExpiresAt
	resp.IsActive = tenant.IsActive()
	resp.IsExpired = tenant.IsExpired()
	resp.CanAccess = tenant.CanAccess()
	resp.SuspendReason = reason
	resp.CreatedAt = tenant.CreatedAt
	resp.UpdatedAt = tenant.UpdatedAt
}

func (resp *ExtendSubscriptionResponse) FromModel(tenant *models.Tenant) {
	resp.TenantID = tenant.TenantID
	resp.TenantName = tenant.TenantName
	resp.TenantCode = tenant.TenantCode
	resp.Status = tenant.Status
	resp.PlanType = tenant.PlanType
	resp.SubscriptionExpiresAt = tenant.SubscriptionExpiresAt
	resp.IsActive = tenant.IsActive()
	resp.IsExpired = tenant.IsExpired()
	resp.CanAccess = tenant.CanAccess()
	resp.CreatedAt = tenant.CreatedAt
	resp.UpdatedAt = tenant.UpdatedAt
}

func (resp *ValidateTenantAccessResponse) FromModel(tenant *models.Tenant) {
	resp.TenantID = tenant.TenantID
	resp.TenantName = tenant.TenantName
	resp.TenantCode = tenant.TenantCode
	resp.Status = tenant.Status
	resp.PlanType = tenant.PlanType
	resp.SubscriptionExpiresAt = tenant.SubscriptionExpiresAt
	resp.IsActive = tenant.IsActive()
	resp.IsExpired = tenant.IsExpired()
	resp.CanAccess = tenant.CanAccess()
	resp.AccessGranted = tenant.CanAccess()
	resp.CreatedAt = tenant.CreatedAt
	resp.UpdatedAt = tenant.UpdatedAt
	
	// Set access message
	if resp.AccessGranted {
		resp.AccessMessage = "Tenant có thể truy cập hệ thống"
	} else if !resp.IsActive {
		resp.AccessMessage = "Tenant không hoạt động"
	} else if resp.IsExpired {
		resp.AccessMessage = "Gói dịch vụ đã hết hạn"
	} else {
		resp.AccessMessage = "Không có quyền truy cập"
	}
}
```

## File Paths
- `modules/tenant/dto/create_tenant.go`
- `modules/tenant/dto/update_tenant.go`
- `modules/tenant/dto/get_tenant.go`
- `modules/tenant/dto/list_tenants.go`
- `modules/tenant/dto/tenant_operations.go`

## Acceptance Criteria
1. ✅ Combined request/response DTOs trong single files
2. ✅ Proper validation tags và business rules
3. ✅ Cursor-based pagination support
4. ✅ Model conversion methods
5. ✅ API response format compliance
6. ✅ Code builds successfully với `make build`
7. ✅ Comprehensive coverage cho tất cả operations

## Dependencies
- Task 01: Tenant Infrastructure Setup
- Task 02: Tenant Models and Internal Types
- Task 03: Tenant Error Handling Setup

## Estimated Time
1-2 hours

## Notes
- Foundation cho API layer
- Validation rules cho business logic
- Chuẩn bị cho service layer implementation
