# Task 03: Tenant Error Handling Setup

## Objective
Thiết lập error handling patterns cho tenant module theo đúng chuẩn từ modules/auth/internal/errors.go và internal/pkg/errors.

## Input
- Error patterns từ modules/auth/internal/errors.go
- Common error handling từ internal/pkg/errors
- Error constants từ task 02

## Output
- Tenant-specific error codes và messages
- Error handling functions
- Multi-language error messages

## Requirements
1. Kế thừa error patterns từ internal/pkg/errors
2. Định nghĩa tenant-specific error codes
3. Multi-language support (Vietnamese/English)
4. Proper HTTP status code mapping
5. Error helper functions

## Implementation Steps

### Step 1: Tạo Error Definitions (modules/tenant/internal/errors.go)
```go
package internal

import (
	pkgErrors "wnapi/internal/pkg/errors"
)

// ErrorCode mở rộng từ mã lỗi chung
type ErrorCode = pkgErrors.ErrorCode

// Tenant error codes - <PERSON><PERSON> thừa lỗi chung
const (
	// Lỗi chung - sử dụng từ package errors
	ErrCodeUnknown          = pkgErrors.ErrCodeUnknown
	ErrCodeValidationFailed = pkgErrors.ErrCodeValidationFailed
	ErrCodeBadRequest       = pkgErrors.ErrCodeBadRequest
	ErrCodeInternalServer   = pkgErrors.ErrCodeInternalServer
	ErrCodeNotFound         = pkgErrors.ErrCodeNotFound
	ErrCodeForbidden        = pkgErrors.ErrCodeForbidden
	ErrCodeConflict         = pkgErrors.ErrCodeConflict
	ErrCodeUnauthorized     = pkgErrors.ErrCodeUnauthorized
	ErrCodeTimeout          = pkgErrors.ErrCodeTimeout

	// Tenant-specific error codes
	ErrCodeTenantNotFound        ErrorCode = "TENANT_NOT_FOUND"
	ErrCodeTenantCodeExists      ErrorCode = "TENANT_CODE_EXISTS"
	ErrCodeTenantInactive        ErrorCode = "TENANT_INACTIVE"
	ErrCodeTenantExpired         ErrorCode = "TENANT_EXPIRED"
	ErrCodeTenantSuspended       ErrorCode = "TENANT_SUSPENDED"
	ErrCodeInvalidTenantCode     ErrorCode = "INVALID_TENANT_CODE"
	ErrCodeInvalidPlanType       ErrorCode = "INVALID_PLAN_TYPE"
	ErrCodeTenantLimitExceeded   ErrorCode = "TENANT_LIMIT_EXCEEDED"
	ErrCodeSubscriptionRequired  ErrorCode = "SUBSCRIPTION_REQUIRED"
	ErrCodeTenantAccessDenied    ErrorCode = "TENANT_ACCESS_DENIED"
)

// ErrorMessages định nghĩa thông báo lỗi đa ngôn ngữ
var ErrorMessages = map[ErrorCode]map[string]string{
	ErrCodeTenantNotFound: {
		"en": "Tenant not found",
		"vi": "Không tìm thấy tenant",
	},
	ErrCodeTenantCodeExists: {
		"en": "Tenant code already exists",
		"vi": "Mã tenant đã tồn tại",
	},
	ErrCodeTenantInactive: {
		"en": "Tenant is inactive",
		"vi": "Tenant không hoạt động",
	},
	ErrCodeTenantExpired: {
		"en": "Tenant subscription has expired",
		"vi": "Gói dịch vụ tenant đã hết hạn",
	},
	ErrCodeTenantSuspended: {
		"en": "Tenant has been suspended",
		"vi": "Tenant đã bị tạm ngưng",
	},
	ErrCodeInvalidTenantCode: {
		"en": "Invalid tenant code format",
		"vi": "Định dạng mã tenant không hợp lệ",
	},
	ErrCodeInvalidPlanType: {
		"en": "Invalid plan type",
		"vi": "Loại gói dịch vụ không hợp lệ",
	},
	ErrCodeTenantLimitExceeded: {
		"en": "Tenant limit exceeded",
		"vi": "Đã vượt quá giới hạn số lượng tenant",
	},
	ErrCodeSubscriptionRequired: {
		"en": "Valid subscription required",
		"vi": "Yêu cầu gói dịch vụ hợp lệ",
	},
	ErrCodeTenantAccessDenied: {
		"en": "Access to tenant denied",
		"vi": "Không có quyền truy cập tenant",
	},
}

// GetMessage trả về thông báo lỗi theo ngôn ngữ
func GetMessage(code ErrorCode, lang string) string {
	// Kiểm tra trong bản đồ lỗi riêng của tenant
	if messages, exists := ErrorMessages[code]; exists {
		if message, exists := messages[lang]; exists {
			return message
		}
		// Fallback to English trong ErrorMessages
		if message, exists := messages["en"]; exists {
			return message
		}
	}

	// Kiểm tra trong bản đồ lỗi chung
	return pkgErrors.GetCommonMessage(code, lang)
}

// AppError kế thừa từ pkgErrors
type AppError = pkgErrors.AppError

// Error helper functions
func Wrap(code ErrorCode, message string, err error) *AppError {
	return &AppError{
		Code:       code,
		Message:    message,
		HTTPStatus: getHTTPStatus(code),
		Internal:   err,
	}
}

func New(code ErrorCode, lang string) *AppError {
	return &AppError{
		Code:       code,
		Message:    GetMessage(code, lang),
		HTTPStatus: getHTTPStatus(code),
	}
}

func NewWithDetails(code ErrorCode, lang string, details string) *AppError {
	return &AppError{
		Code:       code,
		Message:    GetMessage(code, lang),
		Details:    details,
		HTTPStatus: getHTTPStatus(code),
	}
}

func NewValidation(code ErrorCode, lang string, fields map[string]string) *AppError {
	return &AppError{
		Code:       code,
		Message:    GetMessage(code, lang),
		Fields:     fields,
		HTTPStatus: 400,
	}
}

// getHTTPStatus trả về HTTP status code tương ứng với error code
func getHTTPStatus(code ErrorCode) int {
	switch code {
	case ErrCodeTenantNotFound:
		return 404
	case ErrCodeTenantCodeExists:
		return 409
	case ErrCodeTenantInactive, ErrCodeTenantExpired, ErrCodeTenantSuspended, ErrCodeTenantAccessDenied:
		return 403
	case ErrCodeInvalidTenantCode, ErrCodeInvalidPlanType, ErrCodeValidationFailed, ErrCodeBadRequest:
		return 400
	case ErrCodeTenantLimitExceeded:
		return 429
	case ErrCodeSubscriptionRequired:
		return 402 // Payment Required
	case ErrCodeUnauthorized:
		return 401
	default:
		return pkgErrors.GetDefaultHTTPStatus(code)
	}
}
```

### Step 2: Tạo Business Logic Errors (modules/tenant/internal/business_errors.go)
```go
package internal

import (
	"fmt"
	"time"
)

// Business logic error variables
var (
	ErrTenantNotFound        = New(ErrCodeTenantNotFound, "vi")
	ErrTenantCodeExists      = New(ErrCodeTenantCodeExists, "vi")
	ErrTenantInactive        = New(ErrCodeTenantInactive, "vi")
	ErrTenantExpired         = New(ErrCodeTenantExpired, "vi")
	ErrTenantSuspended       = New(ErrCodeTenantSuspended, "vi")
	ErrInvalidTenantCode     = New(ErrCodeInvalidTenantCode, "vi")
	ErrInvalidPlanType       = New(ErrCodeInvalidPlanType, "vi")
	ErrTenantLimitExceeded   = New(ErrCodeTenantLimitExceeded, "vi")
	ErrSubscriptionRequired  = New(ErrCodeSubscriptionRequired, "vi")
	ErrTenantAccessDenied    = New(ErrCodeTenantAccessDenied, "vi")
)

// Business logic helper functions
func NewTenantNotFoundError(tenantID uint) *AppError {
	return NewWithDetails(
		ErrCodeTenantNotFound,
		"vi",
		fmt.Sprintf("Tenant ID: %d", tenantID),
	)
}

func NewTenantCodeExistsError(tenantCode string) *AppError {
	return NewWithDetails(
		ErrCodeTenantCodeExists,
		"vi",
		fmt.Sprintf("Tenant code: %s", tenantCode),
	)
}

func NewTenantExpiredError(tenantID uint, expiresAt time.Time) *AppError {
	return NewWithDetails(
		ErrCodeTenantExpired,
		"vi",
		fmt.Sprintf("Tenant ID: %d, Expired at: %s", tenantID, expiresAt.Format("2006-01-02 15:04:05")),
	)
}

func NewInvalidTenantCodeError(tenantCode string, reason string) *AppError {
	return NewWithDetails(
		ErrCodeInvalidTenantCode,
		"vi",
		fmt.Sprintf("Tenant code: %s, Reason: %s", tenantCode, reason),
	)
}

func NewTenantLimitExceededError(currentCount, maxCount int) *AppError {
	return NewWithDetails(
		ErrCodeTenantLimitExceeded,
		"vi",
		fmt.Sprintf("Current: %d, Max allowed: %d", currentCount, maxCount),
	)
}

// Validation error helpers
func NewTenantValidationError(fields map[string]string) *AppError {
	return NewValidation(ErrCodeValidationFailed, "vi", fields)
}

func ValidateTenantCode(tenantCode string) *AppError {
	if len(tenantCode) < MinTenantCodeLength {
		return NewTenantValidationError(map[string]string{
			"tenant_code": fmt.Sprintf("Mã tenant phải có ít nhất %d ký tự", MinTenantCodeLength),
		})
	}
	
	if len(tenantCode) > MaxTenantCodeLength {
		return NewTenantValidationError(map[string]string{
			"tenant_code": fmt.Sprintf("Mã tenant không được vượt quá %d ký tự", MaxTenantCodeLength),
		})
	}
	
	// Validate pattern (alphanumeric + hyphens)
	matched, _ := regexp.MatchString(TenantCodePattern, tenantCode)
	if !matched {
		return NewTenantValidationError(map[string]string{
			"tenant_code": "Mã tenant chỉ được chứa chữ cái, số và dấu gạch ngang",
		})
	}
	
	return nil
}

func ValidateTenantName(tenantName string) *AppError {
	if len(tenantName) < MinTenantNameLength {
		return NewTenantValidationError(map[string]string{
			"tenant_name": fmt.Sprintf("Tên tenant phải có ít nhất %d ký tự", MinTenantNameLength),
		})
	}
	
	if len(tenantName) > MaxTenantNameLength {
		return NewTenantValidationError(map[string]string{
			"tenant_name": fmt.Sprintf("Tên tenant không được vượt quá %d ký tự", MaxTenantNameLength),
		})
	}
	
	return nil
}

func ValidatePlanType(planType string) *AppError {
	if !IsValidPlanType(planType) {
		return NewTenantValidationError(map[string]string{
			"plan_type": fmt.Sprintf("Loại gói không hợp lệ. Các giá trị hợp lệ: %v", ValidPlanTypes),
		})
	}
	
	return nil
}
```

## File Paths
- `modules/tenant/internal/errors.go`
- `modules/tenant/internal/business_errors.go`

## Acceptance Criteria
1. ✅ Error codes tuân thủ patterns từ modules/auth
2. ✅ Multi-language error messages (Vietnamese/English)
3. ✅ Proper HTTP status code mapping
4. ✅ Business logic error helpers
5. ✅ Validation error functions
6. ✅ Code builds successfully với `make build`
7. ✅ Error handling consistency với internal/pkg/errors

## Dependencies
- Task 01: Tenant Infrastructure Setup
- Task 02: Tenant Models and Internal Types

## Estimated Time
1-2 hours

## Notes
- Foundation cho error handling trong tất cả layers
- Tuân thủ error patterns từ modules/auth
- Chuẩn bị cho repository và service error handling
