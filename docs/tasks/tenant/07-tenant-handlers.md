# Task 07: Tenant HTTP Handlers Implementation

## Objective
<PERSON><PERSON><PERSON> khai HTTP handlers cho tenant module theo patterns từ modules/auth/api/handlers, với comprehensive request handling, validation, và response formatting.

## Input
- <PERSON><PERSON> patterns từ modules/auth/api/handlers/
- Service interface từ task 06
- DTOs từ task 05
- Response formatting từ internal/pkg/response

## Output
- HTTP handlers cho tất cả tenant operations
- Request validation và binding
- Response formatting theo chuẩn API
- Error handling integration
- Logging và tracing

## Requirements
1. Tuân thủ handler patterns từ modules/auth
2. Request validation và binding
3. Service layer integration
4. Response formatting với internal/pkg/response
5. Proper error handling và logging
6. Tracing integration
7. Multi-tenant context handling

## Implementation Steps

### Step 1: Tạo Tenant Handler (modules/tenant/api/handlers/tenant_handler.go)
```go
package handlers

import (
	"net/http"
	"strconv"

	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/response"
	"wnapi/internal/pkg/tracing"
	"wnapi/modules/tenant/dto"
	"wnapi/modules/tenant/internal"
	"wnapi/modules/tenant/service"

	"github.com/gin-gonic/gin"
)

// TenantHandler xử lý các HTTP requests cho tenant operations
type TenantHandler struct {
	service service.TenantService
	logger  logger.Logger
}

// NewTenantHandler tạo instance mới của TenantHandler
func NewTenantHandler(service service.TenantService, logger logger.Logger) *TenantHandler {
	return &TenantHandler{
		service: service,
		logger:  logger,
	}
}

// Create xử lý request tạo tenant mới
// @Summary Tạo tenant mới
// @Description Tạo tenant mới với thông tin được cung cấp
// @Tags tenants
// @Accept json
// @Produce json
// @Param request body dto.CreateTenantRequest true "Thông tin tenant"
// @Success 201 {object} response.Response{data=dto.CreateTenantResponse}
// @Failure 400 {object} response.Response
// @Failure 409 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/admin/v1/tenants [post]
func (h *TenantHandler) Create(c *gin.Context) {
	ctx := c.Request.Context()

	tracing.WithSpan(ctx, "tenant-handler", "create", func(ctx context.Context) {
		var req dto.CreateTenantRequest

		// Bind và validate request
		if err := c.ShouldBindJSON(&req); err != nil {
			h.logger.Warn("Invalid create tenant request",
				logger.String("error", err.Error()),
				logger.String("path", c.Request.URL.Path))

			response.BadRequest(c, "Dữ liệu không hợp lệ", string(internal.ErrCodeValidationFailed), err.Error())
			return
		}

		// Call service
		result, err := h.service.Create(ctx, req)
		if err != nil {
			h.handleServiceError(c, err, "create tenant")
			return
		}

		h.logger.Info("Tenant created successfully",
			logger.Uint("tenant_id", result.TenantID),
			logger.String("tenant_code", result.TenantCode))

		response.Created(c, result, nil)
	})
}

// GetByID xử lý request lấy tenant theo ID
// @Summary Lấy thông tin tenant theo ID
// @Description Lấy thông tin chi tiết của tenant theo ID
// @Tags tenants
// @Produce json
// @Param id path uint true "Tenant ID"
// @Success 200 {object} response.Response{data=dto.GetTenantResponse}
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/admin/v1/tenants/{id} [get]
func (h *TenantHandler) GetByID(c *gin.Context) {
	ctx := c.Request.Context()

	tracing.WithSpan(ctx, "tenant-handler", "get_by_id", func(ctx context.Context) {
		// Parse tenant ID
		tenantID, err := h.parseTenantID(c)
		if err != nil {
			return // Error already handled
		}

		// Call service
		result, err := h.service.GetByID(ctx, tenantID)
		if err != nil {
			h.handleServiceError(c, err, "get tenant by ID")
			return
		}

		response.Success(c, result, nil)
	})
}

// GetByCode xử lý request lấy tenant theo code
// @Summary Lấy thông tin tenant theo code
// @Description Lấy thông tin chi tiết của tenant theo tenant code
// @Tags tenants
// @Produce json
// @Param code path string true "Tenant Code"
// @Success 200 {object} response.Response{data=dto.GetTenantResponse}
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/admin/v1/tenants/code/{code} [get]
func (h *TenantHandler) GetByCode(c *gin.Context) {
	ctx := c.Request.Context()

	tracing.WithSpan(ctx, "tenant-handler", "get_by_code", func(ctx context.Context) {
		tenantCode := c.Param("code")
		if tenantCode == "" {
			response.BadRequest(c, "Tenant code is required", string(internal.ErrCodeBadRequest), nil)
			return
		}

		// Call service
		result, err := h.service.GetByCode(ctx, tenantCode)
		if err != nil {
			h.handleServiceError(c, err, "get tenant by code")
			return
		}

		response.Success(c, result, nil)
	})
}

// Update xử lý request cập nhật tenant
// @Summary Cập nhật thông tin tenant
// @Description Cập nhật thông tin tenant theo ID
// @Tags tenants
// @Accept json
// @Produce json
// @Param id path uint true "Tenant ID"
// @Param request body dto.UpdateTenantRequest true "Thông tin cập nhật"
// @Success 200 {object} response.Response{data=dto.UpdateTenantResponse}
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 409 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/admin/v1/tenants/{id} [put]
func (h *TenantHandler) Update(c *gin.Context) {
	ctx := c.Request.Context()

	tracing.WithSpan(ctx, "tenant-handler", "update", func(ctx context.Context) {
		// Parse tenant ID
		tenantID, err := h.parseTenantID(c)
		if err != nil {
			return // Error already handled
		}

		var req dto.UpdateTenantRequest

		// Bind và validate request
		if err := c.ShouldBindJSON(&req); err != nil {
			h.logger.Warn("Invalid update tenant request",
				logger.String("error", err.Error()),
				logger.Uint("tenant_id", tenantID))

			response.BadRequest(c, "Dữ liệu không hợp lệ", string(internal.ErrCodeValidationFailed), err.Error())
			return
		}

		// Call service
		result, err := h.service.Update(ctx, tenantID, req)
		if err != nil {
			h.handleServiceError(c, err, "update tenant")
			return
		}

		h.logger.Info("Tenant updated successfully",
			logger.Uint("tenant_id", tenantID))

		response.Success(c, result, nil)
	})
}

// Delete xử lý request xóa tenant
// @Summary Xóa tenant
// @Description Xóa tenant theo ID
// @Tags tenants
// @Param id path uint true "Tenant ID"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/admin/v1/tenants/{id} [delete]
func (h *TenantHandler) Delete(c *gin.Context) {
	ctx := c.Request.Context()

	tracing.WithSpan(ctx, "tenant-handler", "delete", func(ctx context.Context) {
		// Parse tenant ID
		tenantID, err := h.parseTenantID(c)
		if err != nil {
			return // Error already handled
		}

		// Call service
		if err := h.service.Delete(ctx, tenantID); err != nil {
			h.handleServiceError(c, err, "delete tenant")
			return
		}

		h.logger.Info("Tenant deleted successfully",
			logger.Uint("tenant_id", tenantID))

		response.Success(c, map[string]interface{}{
			"message":   "Tenant đã được xóa thành công",
			"tenant_id": tenantID,
		}, nil)
	})
}

// List xử lý request lấy danh sách tenants
// @Summary Lấy danh sách tenants
// @Description Lấy danh sách tenants với pagination
// @Tags tenants
// @Produce json
// @Param limit query int false "Số lượng items per page" default(20)
// @Param cursor query string false "Cursor cho pagination"
// @Param status query string false "Filter theo status" Enums(active, inactive, suspended, trial)
// @Success 200 {object} response.Response{data=dto.ListTenantsResponse,meta=response.Meta}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/admin/v1/tenants [get]
func (h *TenantHandler) List(c *gin.Context) {
	ctx := c.Request.Context()

	tracing.WithSpan(ctx, "tenant-handler", "list", func(ctx context.Context) {
		var req dto.ListTenantsRequest

		// Bind query parameters
		if err := c.ShouldBindQuery(&req); err != nil {
			h.logger.Warn("Invalid list tenants request",
				logger.String("error", err.Error()))

			response.BadRequest(c, "Tham số không hợp lệ", string(internal.ErrCodeValidationFailed), err.Error())
			return
		}

		// Call service
		result, meta, err := h.service.List(ctx, req)
		if err != nil {
			h.handleServiceError(c, err, "list tenants")
			return
		}

		// Convert service meta to response meta
		responseMeta := &response.Meta{
			NextCursor: meta.NextCursor,
			HasMore:    meta.HasMore,
		}

		response.Success(c, result.Tenants, responseMeta)
	})
}

// ActivateTenant xử lý request kích hoạt tenant
// @Summary Kích hoạt tenant
// @Description Kích hoạt tenant theo ID
// @Tags tenants
// @Param id path uint true "Tenant ID"
// @Success 200 {object} response.Response{data=dto.ActivateTenantResponse}
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/admin/v1/tenants/{id}/activate [post]
func (h *TenantHandler) ActivateTenant(c *gin.Context) {
	ctx := c.Request.Context()

	tracing.WithSpan(ctx, "tenant-handler", "activate", func(ctx context.Context) {
		// Parse tenant ID
		tenantID, err := h.parseTenantID(c)
		if err != nil {
			return // Error already handled
		}

		// Call service
		result, err := h.service.ActivateTenant(ctx, tenantID)
		if err != nil {
			h.handleServiceError(c, err, "activate tenant")
			return
		}

		h.logger.Info("Tenant activated successfully",
			logger.Uint("tenant_id", tenantID))

		response.Success(c, result, nil)
	})
}

// SuspendTenant xử lý request tạm ngưng tenant
// @Summary Tạm ngưng tenant
// @Description Tạm ngưng tenant theo ID với lý do
// @Tags tenants
// @Accept json
// @Produce json
// @Param id path uint true "Tenant ID"
// @Param request body dto.SuspendTenantRequest true "Lý do tạm ngưng"
// @Success 200 {object} response.Response{data=dto.SuspendTenantResponse}
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/admin/v1/tenants/{id}/suspend [post]
func (h *TenantHandler) SuspendTenant(c *gin.Context) {
	ctx := c.Request.Context()

	tracing.WithSpan(ctx, "tenant-handler", "suspend", func(ctx context.Context) {
		// Parse tenant ID
		tenantID, err := h.parseTenantID(c)
		if err != nil {
			return // Error already handled
		}

		var req dto.SuspendTenantRequest

		// Bind và validate request
		if err := c.ShouldBindJSON(&req); err != nil {
			h.logger.Warn("Invalid suspend tenant request",
				logger.String("error", err.Error()),
				logger.Uint("tenant_id", tenantID))

			response.BadRequest(c, "Dữ liệu không hợp lệ", string(internal.ErrCodeValidationFailed), err.Error())
			return
		}

		// Call service
		result, err := h.service.SuspendTenant(ctx, tenantID, req)
		if err != nil {
			h.handleServiceError(c, err, "suspend tenant")
			return
		}

		h.logger.Info("Tenant suspended successfully",
			logger.Uint("tenant_id", tenantID),
			logger.String("reason", req.Reason))

		response.Success(c, result, nil)
	})
}

// ExtendSubscription xử lý request gia hạn subscription
// @Summary Gia hạn subscription
// @Description Gia hạn subscription của tenant
// @Tags tenants
// @Accept json
// @Produce json
// @Param id path uint true "Tenant ID"
// @Param request body dto.ExtendSubscriptionRequest true "Thông tin gia hạn"
// @Success 200 {object} response.Response{data=dto.ExtendSubscriptionResponse}
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/admin/v1/tenants/{id}/extend-subscription [post]
func (h *TenantHandler) ExtendSubscription(c *gin.Context) {
	ctx := c.Request.Context()

	tracing.WithSpan(ctx, "tenant-handler", "extend_subscription", func(ctx context.Context) {
		// Parse tenant ID
		tenantID, err := h.parseTenantID(c)
		if err != nil {
			return // Error already handled
		}

		var req dto.ExtendSubscriptionRequest

		// Bind và validate request
		if err := c.ShouldBindJSON(&req); err != nil {
			h.logger.Warn("Invalid extend subscription request",
				logger.String("error", err.Error()),
				logger.Uint("tenant_id", tenantID))

			response.BadRequest(c, "Dữ liệu không hợp lệ", string(internal.ErrCodeValidationFailed), err.Error())
			return
		}

		// Call service
		result, err := h.service.ExtendSubscription(ctx, tenantID, req)
		if err != nil {
			h.handleServiceError(c, err, "extend subscription")
			return
		}

		h.logger.Info("Subscription extended successfully",
			logger.Uint("tenant_id", tenantID),
			logger.Time("expires_at", req.ExpiresAt))

		response.Success(c, result, nil)
	})
}

// ValidateAccess xử lý request validate tenant access
// @Summary Validate tenant access
// @Description Kiểm tra quyền truy cập của tenant
// @Tags tenants
// @Produce json
// @Param code path string true "Tenant Code"
// @Success 200 {object} response.Response{data=dto.ValidateTenantAccessResponse}
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/admin/v1/tenants/validate/{code} [get]
func (h *TenantHandler) ValidateAccess(c *gin.Context) {
	ctx := c.Request.Context()

	tracing.WithSpan(ctx, "tenant-handler", "validate_access", func(ctx context.Context) {
		tenantCode := c.Param("code")
		if tenantCode == "" {
			response.BadRequest(c, "Tenant code is required", string(internal.ErrCodeBadRequest), nil)
			return
		}

		// Call service
		result, err := h.service.ValidateTenantAccess(ctx, tenantCode)
		if err != nil {
			h.handleServiceError(c, err, "validate tenant access")
			return
		}

		response.Success(c, result, nil)
	})
}

// CheckCodeAvailability xử lý request kiểm tra tính khả dụng của tenant code
// @Summary Kiểm tra tính khả dụng của tenant code
// @Description Kiểm tra xem tenant code có thể sử dụng được không
// @Tags tenants
// @Produce json
// @Param code path string true "Tenant Code"
// @Success 200 {object} response.Response{data=map[string]bool}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/admin/v1/tenants/check-code/{code} [get]
func (h *TenantHandler) CheckCodeAvailability(c *gin.Context) {
	ctx := c.Request.Context()

	tracing.WithSpan(ctx, "tenant-handler", "check_code_availability", func(ctx context.Context) {
		tenantCode := c.Param("code")
		if tenantCode == "" {
			response.BadRequest(c, "Tenant code is required", string(internal.ErrCodeBadRequest), nil)
			return
		}

		// Call service
		available, err := h.service.CheckTenantCodeAvailability(ctx, tenantCode)
		if err != nil {
			h.handleServiceError(c, err, "check tenant code availability")
			return
		}

		response.Success(c, map[string]interface{}{
			"tenant_code": tenantCode,
			"available":   available,
			"message":     func() string {
				if available {
					return "Tenant code có thể sử dụng"
				}
				return "Tenant code đã được sử dụng"
			}(),
		}, nil)
	})
}

// Helper methods

// parseTenantID parses tenant ID từ URL parameter
func (h *TenantHandler) parseTenantID(c *gin.Context) (uint, error) {
	idStr := c.Param("id")
	if idStr == "" {
		response.BadRequest(c, "Tenant ID is required", string(internal.ErrCodeBadRequest), nil)
		return 0, fmt.Errorf("tenant ID is required")
	}

	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		h.logger.Warn("Invalid tenant ID format",
			logger.String("id", idStr),
			logger.String("error", err.Error()))

		response.BadRequest(c, "Tenant ID không hợp lệ", string(internal.ErrCodeBadRequest), nil)
		return 0, fmt.Errorf("invalid tenant ID format")
	}

	return uint(id), nil
}

// handleServiceError xử lý lỗi từ service layer
func (h *TenantHandler) handleServiceError(c *gin.Context, err error, operation string) {
	h.logger.Error("Service error",
		logger.String("operation", operation),
		logger.String("error", err.Error()),
		logger.String("path", c.Request.URL.Path))

	// Check if it's an AppError
	if appErr, ok := err.(*internal.AppError); ok {
		switch appErr.Code {
		case internal.ErrCodeTenantNotFound:
			response.NotFound(c, appErr.Message)
		case internal.ErrCodeTenantCodeExists:
			response.ErrorWithDetails(c, http.StatusConflict, appErr.Message, string(appErr.Code), appErr.Details)
		case internal.ErrCodeValidationFailed, internal.ErrCodeBadRequest:
			if appErr.Fields != nil {
				// Validation error with field details
				details := make([]response.Detail, 0, len(appErr.Fields))
				for field, message := range appErr.Fields {
					details = append(details, response.Detail{
						Field:   field,
						Message: message,
					})
				}
				response.ValidationError(c, details)
			} else {
				response.BadRequest(c, appErr.Message, string(appErr.Code), appErr.Details)
			}
		case internal.ErrCodeTenantInactive, internal.ErrCodeTenantExpired, internal.ErrCodeTenantSuspended, internal.ErrCodeTenantAccessDenied:
			response.Forbidden(c, appErr.Message)
		case internal.ErrCodeUnauthorized:
			response.Unauthorized(c, appErr.Message)
		case internal.ErrCodeTenantLimitExceeded:
			response.ErrorWithDetails(c, http.StatusTooManyRequests, appErr.Message, string(appErr.Code), appErr.Details)
		case internal.ErrCodeSubscriptionRequired:
			response.ErrorWithDetails(c, http.StatusPaymentRequired, appErr.Message, string(appErr.Code), appErr.Details)
		default:
			response.InternalServerError(c, "Đã xảy ra lỗi hệ thống")
		}
	} else {
		// Generic error
		response.InternalServerError(c, "Đã xảy ra lỗi hệ thống")
	}
}
```

## File Paths
- `modules/tenant/api/handlers/tenant_handler.go`

## Acceptance Criteria
1. ✅ Handler patterns tuân thủ modules/auth
2. ✅ Request validation và binding
3. ✅ Service layer integration
4. ✅ Response formatting với internal/pkg/response
5. ✅ Proper error handling và logging
6. ✅ Tracing integration
7. ✅ Code builds successfully với `make build`

## Dependencies
- Task 01: Tenant Infrastructure Setup
- Task 02: Tenant Models and Internal Types
- Task 03: Tenant Error Handling Setup
- Task 05: Tenant DTOs
- Task 06: Tenant Service Implementation

## Estimated Time
2-3 hours

## Notes
- Foundation cho routing setup
- Comprehensive API coverage
- Error handling integration
- Chuẩn bị cho routes registration
