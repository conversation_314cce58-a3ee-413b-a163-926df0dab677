# Task 09: Notification Module Migration

## Objective
Migrate notification module từ AppBootstrap-based system sang fx.Module architecture. Notification module cung cấp email services cho auth và các modules khác.

## Input
- Core module migrations từ Tasks 04-08
- Existing notification module structure
- Current email service implementations
- Queue system integration

## Output
- Notification module fx.Module implementation
- Email service providers
- Queue handler registration
- Template management integration

## Requirements

### 1. Technical Requirements
- Independent module (no dependencies)
- Provide email services to other modules
- Queue-based email processing
- Template management system

### 2. Architecture Requirements
- Multi-tenant email isolation
- Repository patterns với tenant filtering
- Queue handlers for async processing
- Template rendering system

## Implementation Steps

### Step 1: Create Notification FX Module

**File: `modules/notification/fx.go`**
```go
package notification

import (
	"go.uber.org/fx"
	
	"wnapi/internal/fx/modules"
	"wnapi/modules/notification/api"
	"wnapi/modules/notification/internal"
	"wnapi/modules/notification/queue"
	"wnapi/modules/notification/repository/mysql"
	"wnapi/modules/notification/service"
)

// NotificationModule implements FXModule interface
type NotificationModule struct{}

// Name returns module name
func (m *NotificationModule) Name() string {
	return "notification"
}

// Dependencies returns module dependencies
func (m *NotificationModule) Dependencies() []string {
	return []string{} // Independent module
}

// Priority returns loading priority
func (m *NotificationModule) Priority() int {
	return 5 // Load early to provide services to other modules
}

// Enabled checks if module should be loaded
func (m *NotificationModule) Enabled(config map[string]interface{}) bool {
	if enabled, ok := config["enabled"].(bool); ok {
		return enabled
	}
	return true // Default enabled
}

// Module returns fx.Module for notification
func (m *NotificationModule) Module() fx.Option {
	return fx.Module("notification",
		// Providers
		fx.Provide(
			// Configuration
			NewNotificationConfig,
			
			// Repositories
			mysql.NewNotificationRepository,
			mysql.NewEmailTemplateRepository,
			
			// Services
			fx.Annotate(
				NewEmailService,
				fx.As(new(service.EmailService)),
			),
			fx.Annotate(
				NewNotificationService,
				fx.As(new(service.NotificationService)),
			),
			fx.Annotate(
				NewTemplateService,
				fx.As(new(service.TemplateService)),
			),
			
			// Queue handlers
			NewSendEmailHandler,
			NewSendWelcomeEmailHandler,
			NewSendVerificationEmailHandler,
			NewSendPasswordResetEmailHandler,
			
			// Handlers
			NewNotificationHandler,
		),
		
		// Invocations
		fx.Invoke(
			RegisterNotificationRoutes,
			RegisterQueueHandlers,
		),
	)
}

// Register notification module with global registry
func init() {
	modules.RegisterModule(&NotificationModule{})
}
```

### Step 2: Create Notification Providers

**File: `modules/notification/providers.go`**
```go
package notification

import (
	"github.com/jmoiron/sqlx"
	"gorm.io/gorm"
	
	"wnapi/internal/pkg/auth"
	"wnapi/internal/pkg/config"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/permission"
	"wnapi/internal/pkg/queue"
	"wnapi/modules/notification/api"
	"wnapi/modules/notification/internal"
	notificationQueue "wnapi/modules/notification/queue"
	"wnapi/modules/notification/repository"
	"wnapi/modules/notification/service"
)

// NewNotificationConfig creates notification configuration
func NewNotificationConfig(cfg config.Config) *internal.NotificationConfig {
	return &internal.NotificationConfig{
		SMTPHost:         cfg.GetString("SMTP_HOST"),
		SMTPPort:         cfg.GetIntWithDefault("SMTP_PORT", 587),
		SMTPUsername:     cfg.GetString("SMTP_USERNAME"),
		SMTPPassword:     cfg.GetString("SMTP_PASSWORD"),
		SMTPEncryption:   cfg.GetStringWithDefault("SMTP_ENCRYPTION", "tls"),
		FromEmail:        cfg.GetString("MAIL_FROM_EMAIL"),
		FromName:         cfg.GetStringWithDefault("MAIL_FROM_NAME", "WNAPI"),
		ReplyToEmail:     cfg.GetString("MAIL_REPLY_TO_EMAIL"),
		MaxRetries:       cfg.GetIntWithDefault("NOTIFICATION_MAX_RETRIES", 3),
		RetryDelay:       cfg.GetDurationWithDefault("NOTIFICATION_RETRY_DELAY", 60), // 1 minute
		QueueEnabled:     cfg.GetBoolWithDefault("QUEUE_ENABLED", true),
		TemplateCache:    cfg.GetBoolWithDefault("NOTIFICATION_TEMPLATE_CACHE", true),
	}
}

// NewEmailService creates email service with dependencies
func NewEmailService(
	config *internal.NotificationConfig,
	log logger.Logger,
) *service.EmailService {
	return service.NewEmailService(config, log)
}

// NewNotificationService creates notification service with dependencies
func NewNotificationService(
	notificationRepo repository.NotificationRepository,
	emailService *service.EmailService,
	templateService *service.TemplateService,
	queueManager queue.Manager,
	config *internal.NotificationConfig,
	log logger.Logger,
) *service.NotificationService {
	return service.NewNotificationService(
		notificationRepo,
		emailService,
		templateService,
		queueManager,
		config,
		log,
	)
}

// NewTemplateService creates template service with dependencies
func NewTemplateService(
	templateRepo repository.EmailTemplateRepository,
	config *internal.NotificationConfig,
	log logger.Logger,
) *service.TemplateService {
	return service.NewTemplateService(templateRepo, config, log)
}

// NewSendEmailHandler creates send email queue handler
func NewSendEmailHandler(
	emailService *service.EmailService,
	log logger.Logger,
) *notificationQueue.SendEmailHandler {
	return notificationQueue.NewSendEmailHandler(emailService, log)
}

// NewSendWelcomeEmailHandler creates welcome email queue handler
func NewSendWelcomeEmailHandler(
	notificationService *service.NotificationService,
	cfg config.Config,
	log logger.Logger,
) *notificationQueue.SendWelcomeEmailHandler {
	webURL := cfg.GetString("WEB_URL")
	return notificationQueue.NewSendWelcomeEmailHandler(notificationService, webURL, log)
}

// NewSendVerificationEmailHandler creates verification email queue handler
func NewSendVerificationEmailHandler(
	notificationService *service.NotificationService,
	cfg config.Config,
	log logger.Logger,
) *notificationQueue.SendVerificationEmailHandler {
	webURL := cfg.GetString("WEB_URL")
	return notificationQueue.NewSendVerificationEmailHandler(notificationService, webURL, log)
}

// NewSendPasswordResetEmailHandler creates password reset email queue handler
func NewSendPasswordResetEmailHandler(
	notificationService *service.NotificationService,
	cfg config.Config,
	log logger.Logger,
) *notificationQueue.SendPasswordResetEmailHandler {
	webURL := cfg.GetString("WEB_URL")
	return notificationQueue.NewSendPasswordResetEmailHandler(notificationService, webURL, log)
}

// NewNotificationHandler creates notification API handler with dependencies
func NewNotificationHandler(
	notificationService *service.NotificationService,
	emailService *service.EmailService,
	templateService *service.TemplateService,
	mwFactory *permission.MiddlewareFactory,
	jwtService *auth.JWTService,
	log logger.Logger,
	db *sqlx.DB,
	gormDB *gorm.DB,
) *api.Handler {
	return api.NewHandlerWithDependencies(
		notificationService,
		emailService,
		templateService,
		mwFactory,
		jwtService,
		log,
		db,
		gormDB,
	)
}
```

### Step 3: Create Queue Handler Registration

**File: `modules/notification/queue_registration.go`**
```go
package notification

import (
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/queue"
	notificationQueue "wnapi/modules/notification/queue"
)

// RegisterQueueHandlers registers notification queue handlers
func RegisterQueueHandlers(
	queueManager queue.Manager,
	sendEmailHandler *notificationQueue.SendEmailHandler,
	sendWelcomeEmailHandler *notificationQueue.SendWelcomeEmailHandler,
	sendVerificationEmailHandler *notificationQueue.SendVerificationEmailHandler,
	sendPasswordResetEmailHandler *notificationQueue.SendPasswordResetEmailHandler,
	log logger.Logger,
) error {
	log.Info("Registering notification queue handlers")
	
	// Register send email handler
	if err := queueManager.RegisterHandler("send_email", sendEmailHandler.Handle); err != nil {
		log.Error("Failed to register send_email handler", "error", err)
		return err
	}
	
	// Register welcome email handler
	if err := queueManager.RegisterHandler("send_welcome_email", sendWelcomeEmailHandler.Handle); err != nil {
		log.Error("Failed to register send_welcome_email handler", "error", err)
		return err
	}
	
	// Register verification email handler
	if err := queueManager.RegisterHandler("send_verification_email", sendVerificationEmailHandler.Handle); err != nil {
		log.Error("Failed to register send_verification_email handler", "error", err)
		return err
	}
	
	// Register password reset email handler
	if err := queueManager.RegisterHandler("send_password_reset_email", sendPasswordResetEmailHandler.Handle); err != nil {
		log.Error("Failed to register send_password_reset_email handler", "error", err)
		return err
	}
	
	log.Info("Notification queue handlers registered successfully")
	return nil
}
```

### Step 4: Create Route Registration

**File: `modules/notification/routes.go`**
```go
package notification

import (
	"github.com/gin-gonic/gin"
	"wnapi/modules/notification/api"
	"wnapi/internal/pkg/logger"
)

// RegisterNotificationRoutes registers notification routes with Gin engine
func RegisterNotificationRoutes(engine *gin.Engine, handler *api.Handler, log logger.Logger) {
	log.Info("Registering notification routes")
	
	// Use existing route registration logic
	if err := handler.RegisterRoutes(engine); err != nil {
		log.Error("Failed to register notification routes", "error", err)
		return
	}
	
	log.Info("Notification routes registered successfully")
}

// printRoutes logs all registered routes for debugging
func printRoutes(engine *gin.Engine, log logger.Logger) {
	routes := engine.Routes()
	log.Info("Notification module routes registered", "count", len(routes))
	
	for _, route := range routes {
		log.Debug("Route registered", 
			"method", route.Method,
			"path", route.Path,
			"handler", route.Handler,
		)
	}
}
```

### Step 5: Update Notification API Handler

**File: `modules/notification/api/routes.go` (update)**
```go
// Update NewHandlerWithDependencies to work with fx
func NewHandlerWithDependencies(
	notificationService *service.NotificationService,
	emailService *service.EmailService,
	templateService *service.TemplateService,
	mwFactory *permission.MiddlewareFactory,
	jwtService *auth.JWTService,
	log logger.Logger,
	db *sqlx.DB,
	gormDB *gorm.DB,
) *Handler {
	// Create sub-handlers
	adminNotificationHandler := handlers.NewAdminNotificationHandler(notificationService)
	adminEmailTemplateHandler := handlers.NewAdminEmailTemplateHandler(templateService)

	return &Handler{
		adminNotificationHandler:  adminNotificationHandler,
		adminEmailTemplateHandler: adminEmailTemplateHandler,
		middlewareFactory:         mwFactory,
		jwtService:               jwtService,
		logger:                   log,
		db:                       db,
		gormDB:                   gormDB,
	}
}

// RegisterRoutes registers notification routes with Gin engine
func (h *Handler) RegisterRoutes(engine *gin.Engine) error {
	h.logger.Info("Setting up notification routes")

	// Create API group
	apiGroup := engine.Group("/api/v1/notifications")

	// Protected routes (authentication required)
	h.setupProtectedRoutes(apiGroup)

	// Admin routes (authentication + admin permissions required)
	h.setupAdminRoutes(apiGroup)

	h.logger.Info("Notification routes setup completed")
	return nil
}

// Add printRoutes function for consistency
func (h *Handler) printRoutes() {
	h.logger.Info("Notification module routes registered")
	// Implementation can be added if needed for debugging
}
```

### Step 6: Update Queue Handlers

**File: `modules/notification/queue/send_welcome_email.go` (update)**
```go
// Update to work with fx dependency injection
func NewSendWelcomeEmailHandler(
	notificationService *service.NotificationService,
	webURL string,
	log logger.Logger,
) *SendWelcomeEmailHandler {
	return &SendWelcomeEmailHandler{
		notificationService: notificationService,
		webURL:             webURL,
		logger:             log,
	}
}

// Handle processes welcome email queue tasks
func (h *SendWelcomeEmailHandler) Handle(ctx context.Context, task *queue.TaskInfo) error {
	h.logger.Info("Processing welcome email task", "task_id", task.ID)
	
	// Parse payload
	payload, err := h.parsePayload(task.Payload)
	if err != nil {
		return fmt.Errorf("failed to parse payload: %w", err)
	}
	
	// Create notification from template
	notification, err := h.notificationService.CreateNotificationFromTemplate(
		ctx,
		"welcome_email",
		payload.Email,
		payload.TemplateData,
		payload.TenantID,
	)
	if err != nil {
		return fmt.Errorf("failed to create notification: %w", err)
	}
	
	// Send notification
	return h.notificationService.SendNotification(ctx, notification.ID)
}
```

### Step 7: Create Notification Module Tests

**File: `modules/notification/fx_test.go`**
```go
package notification

import (
	"testing"
	
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"
	"wnapi/internal/fx/providers"
)

func TestNotificationModule(t *testing.T) {
	notificationModule := &NotificationModule{}
	
	// Test module interface
	if notificationModule.Name() != "notification" {
		t.Errorf("Expected module name 'notification', got '%s'", notificationModule.Name())
	}
	
	if notificationModule.Priority() != 5 {
		t.Errorf("Expected priority 5, got %d", notificationModule.Priority())
	}
	
	deps := notificationModule.Dependencies()
	if len(deps) != 0 {
		t.Errorf("Expected 0 dependencies, got %d", len(deps))
	}
}

func TestNotificationModuleIntegration(t *testing.T) {
	app := fxtest.New(t,
		// Core providers
		fx.Provide(
			providers.NewConfig,
			providers.NewLogger,
			providers.NewDBManager,
			providers.NewGormDB,
			providers.NewMockQueueManager,
		),
		
		// Notification module
		notificationModule.Module(),
		
		// Test that notification services are available
		fx.Invoke(func(
			emailService *service.EmailService,
			notificationService *service.NotificationService,
			templateService *service.TemplateService,
		) {
			if emailService == nil {
				t.Error("Email service not provided")
			}
			if notificationService == nil {
				t.Error("Notification service not provided")
			}
			if templateService == nil {
				t.Error("Template service not provided")
			}
		}),
	)
	
	app.RequireStart()
	app.RequireStop()
}
```

### Step 8: Update Module Registration

**File: `modules/notification/module.go` (comment out old registration)**
```go
// Comment out old module registration
// func init() {
// 	core.RegisterModuleFactory("notification", NewModule)
// }

// Add note about fx migration
// NOTE: This module has been migrated to fx.Module
// See fx.go for the new implementation
```

## Acceptance Criteria

### Functional Requirements
- [ ] Notification module implements FXModule interface
- [ ] All notification services available through fx DI
- [ ] Email services working correctly
- [ ] Queue handlers registered và functional
- [ ] Template system integrated

### Technical Requirements
- [ ] `make build` passes without errors
- [ ] Module loads independently
- [ ] Queue integration working
- [ ] No breaking changes to notification API
- [ ] Email delivery functional

### Code Quality
- [ ] All providers documented
- [ ] Error handling preserved
- [ ] Test coverage maintained
- [ ] Logging integration working
- [ ] Performance not degraded

## File Paths

### New Files
- `modules/notification/fx.go`
- `modules/notification/providers.go`
- `modules/notification/queue_registration.go`
- `modules/notification/routes.go`
- `modules/notification/fx_test.go`

### Modified Files
- `modules/notification/api/routes.go` (update handler creation)
- `modules/notification/queue/send_welcome_email.go` (update for fx)
- `modules/notification/module.go` (comment out old registration)

## Dependencies
- Core providers (database, config, logger, queue)
- Independent module (no module dependencies)

## Estimated Time
2 hours

## Notes
- Independent module providing services to others
- Critical for auth module email functionality
- Queue integration essential
- Template system must work correctly
