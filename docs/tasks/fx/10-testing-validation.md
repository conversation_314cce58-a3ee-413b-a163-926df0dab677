# Task 10: Testing and Validation

## Objective
Tạo comprehensive testing suite để validate fx implementation. Ensure tất cả functionality working correctly, performance không degraded, và system stability maintained.

## Input
- Completed fx implementation từ Tasks 01-09
- All modules migrated to fx.Module
- Working fx-based application
- Legacy system removed

## Output
- Comprehensive test suite
- Performance benchmarks
- Integration tests
- Load testing results
- Validation reports

## Requirements

### 1. Technical Requirements
- 100% functionality preservation
- Performance benchmarks
- Memory usage validation
- Startup time measurement
- Error handling verification

### 2. Quality Requirements
- Integration test coverage
- Unit test coverage > 90%
- Load testing scenarios
- Stress testing validation
- Regression testing

## Implementation Steps

### Step 1: Create FX Integration Tests

**File: `tests/fx/integration_test.go`**
```go
package fx_test

import (
	"context"
	"testing"
	"time"
	
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"
	"wnapi/internal/fx"
	"wnapi/internal/fx/providers"
	
	// Import all modules
	_ "wnapi/modules/auth"
	_ "wnapi/modules/tenant"
	_ "wnapi/modules/rbac"
	_ "wnapi/modules/notification"
)

// TestFullApplicationStartup tests complete application startup
func TestFullApplicationStartup(t *testing.T) {
	app := fx.NewApp(
		// Core providers
		fx.Provide(
			providers.NewConfig,
			providers.NewLogger,
			providers.NewDBManager,
			providers.NewGormDB,
			providers.NewSqlxDB,
			providers.NewAppCache,
			providers.NewJWTConfig,
			providers.NewJWTService,
			providers.NewPermissionFactory,
			providers.NewGinEngine,
			providers.NewMockQueueManager,
			providers.NewMockEventPublisher,
		),
		
		// Test all services are available
		fx.Invoke(func(
			cfg config.Config,
			log logger.Logger,
			dbManager *database.Manager,
			gormDB *gorm.DB,
			sqlxDB *sqlx.DB,
			cache cache.Cache,
			jwtService *auth.JWTService,
			mwFactory *permission.MiddlewareFactory,
			engine *gin.Engine,
		) {
			// Validate all core services
			if cfg == nil {
				t.Error("Config not available")
			}
			if log == nil {
				t.Error("Logger not available")
			}
			if dbManager == nil {
				t.Error("Database manager not available")
			}
			if gormDB == nil {
				t.Error("GORM DB not available")
			}
			if sqlxDB == nil {
				t.Error("Sqlx DB not available")
			}
			if cache == nil {
				t.Error("Cache not available")
			}
			if jwtService == nil {
				t.Error("JWT service not available")
			}
			if mwFactory == nil {
				t.Error("Middleware factory not available")
			}
			if engine == nil {
				t.Error("Gin engine not available")
			}
		}),
	)
	
	// Test startup and shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	
	if err := app.Start(ctx); err != nil {
		t.Fatalf("Failed to start application: %v", err)
	}
	
	if err := app.Stop(ctx); err != nil {
		t.Fatalf("Failed to stop application: %v", err)
	}
}

// TestModuleLoading tests module loading order and dependencies
func TestModuleLoading(t *testing.T) {
	// Test module loading with specific order
	moduleOrder := []string{"tenant", "auth", "rbac", "notification"}
	
	for _, moduleName := range moduleOrder {
		t.Run("Module_"+moduleName, func(t *testing.T) {
			// Test individual module loading
			testModuleLoading(t, moduleName)
		})
	}
}

func testModuleLoading(t *testing.T, moduleName string) {
	// Implementation to test specific module loading
	t.Logf("Testing module: %s", moduleName)
	// Add specific module tests here
}
```

### Step 2: Create Performance Benchmarks

**File: `tests/fx/benchmark_test.go`**
```go
package fx_test

import (
	"context"
	"testing"
	"time"
	
	"go.uber.org/fx"
	"wnapi/internal/fx"
)

// BenchmarkApplicationStartup benchmarks application startup time
func BenchmarkApplicationStartup(b *testing.B) {
	for i := 0; i < b.N; i++ {
		app := fx.NewApp()
		
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		
		start := time.Now()
		if err := app.Start(ctx); err != nil {
			b.Fatalf("Failed to start app: %v", err)
		}
		startupTime := time.Since(start)
		
		if err := app.Stop(ctx); err != nil {
			b.Fatalf("Failed to stop app: %v", err)
		}
		cancel()
		
		b.ReportMetric(float64(startupTime.Milliseconds()), "startup_ms")
	}
}

// BenchmarkMemoryUsage benchmarks memory usage
func BenchmarkMemoryUsage(b *testing.B) {
	var m1, m2 runtime.MemStats
	
	runtime.GC()
	runtime.ReadMemStats(&m1)
	
	for i := 0; i < b.N; i++ {
		app := fx.NewApp()
		
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		app.Start(ctx)
		app.Stop(ctx)
		cancel()
	}
	
	runtime.GC()
	runtime.ReadMemStats(&m2)
	
	memUsed := m2.TotalAlloc - m1.TotalAlloc
	b.ReportMetric(float64(memUsed)/float64(b.N), "bytes_per_op")
}

// BenchmarkDependencyInjection benchmarks DI performance
func BenchmarkDependencyInjection(b *testing.B) {
	app := fx.NewApp(
		fx.Provide(
			providers.NewConfig,
			providers.NewLogger,
			providers.NewAppCache,
		),
		fx.Invoke(func(
			cfg config.Config,
			log logger.Logger,
			cache cache.Cache,
		) {
			// Measure DI resolution time
		}),
	)
	
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	
	b.ResetTimer()
	
	for i := 0; i < b.N; i++ {
		start := time.Now()
		app.Start(ctx)
		injectionTime := time.Since(start)
		app.Stop(ctx)
		
		b.ReportMetric(float64(injectionTime.Nanoseconds()), "injection_ns")
	}
}
```

### Step 3: Create Load Testing

**File: `tests/fx/load_test.go`**
```go
package fx_test

import (
	"context"
	"net/http"
	"sync"
	"testing"
	"time"
	
	"go.uber.org/fx"
	"wnapi/internal/fx"
)

// TestConcurrentRequests tests application under concurrent load
func TestConcurrentRequests(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping load test in short mode")
	}
	
	// Start application
	app := fx.NewApp()
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	
	if err := app.Start(ctx); err != nil {
		t.Fatalf("Failed to start app: %v", err)
	}
	defer app.Stop(ctx)
	
	// Wait for server to be ready
	time.Sleep(2 * time.Second)
	
	// Test concurrent requests
	concurrency := 100
	requestsPerWorker := 10
	
	var wg sync.WaitGroup
	errors := make(chan error, concurrency*requestsPerWorker)
	
	for i := 0; i < concurrency; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			
			for j := 0; j < requestsPerWorker; j++ {
				resp, err := http.Get("http://localhost:8080/health")
				if err != nil {
					errors <- err
					continue
				}
				resp.Body.Close()
				
				if resp.StatusCode != http.StatusOK {
					errors <- fmt.Errorf("unexpected status: %d", resp.StatusCode)
				}
			}
		}()
	}
	
	wg.Wait()
	close(errors)
	
	// Check for errors
	errorCount := 0
	for err := range errors {
		t.Logf("Request error: %v", err)
		errorCount++
	}
	
	totalRequests := concurrency * requestsPerWorker
	successRate := float64(totalRequests-errorCount) / float64(totalRequests) * 100
	
	t.Logf("Success rate: %.2f%% (%d/%d)", successRate, totalRequests-errorCount, totalRequests)
	
	if successRate < 95.0 {
		t.Errorf("Success rate too low: %.2f%%", successRate)
	}
}

// TestMemoryLeaks tests for memory leaks during operation
func TestMemoryLeaks(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping memory leak test in short mode")
	}
	
	var m1, m2 runtime.MemStats
	
	// Baseline measurement
	runtime.GC()
	runtime.ReadMemStats(&m1)
	
	// Run multiple cycles
	for i := 0; i < 10; i++ {
		app := fx.NewApp()
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		
		app.Start(ctx)
		time.Sleep(100 * time.Millisecond)
		app.Stop(ctx)
		cancel()
	}
	
	// Final measurement
	runtime.GC()
	runtime.ReadMemStats(&m2)
	
	memGrowth := m2.HeapAlloc - m1.HeapAlloc
	t.Logf("Memory growth: %d bytes", memGrowth)
	
	// Allow some growth but not excessive
	maxAllowedGrowth := uint64(10 * 1024 * 1024) // 10MB
	if memGrowth > maxAllowedGrowth {
		t.Errorf("Excessive memory growth: %d bytes", memGrowth)
	}
}
```

### Step 4: Create Regression Tests

**File: `tests/fx/regression_test.go`**
```go
package fx_test

import (
	"context"
	"testing"
	"time"
	
	"go.uber.org/fx"
	"wnapi/internal/fx"
)

// TestAPICompatibility tests that all APIs still work
func TestAPICompatibility(t *testing.T) {
	testCases := []struct {
		name     string
		endpoint string
		method   string
		expected int
	}{
		{"Health Check", "/health", "GET", 200},
		{"Auth Login", "/api/v1/auth/login", "POST", 400}, // Bad request without body
		{"Tenant List", "/api/v1/tenants", "GET", 401},   // Unauthorized without auth
		{"RBAC Roles", "/api/v1/rbac/roles", "GET", 401}, // Unauthorized without auth
	}
	
	// Start application
	app := fx.NewApp()
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	
	if err := app.Start(ctx); err != nil {
		t.Fatalf("Failed to start app: %v", err)
	}
	defer app.Stop(ctx)
	
	// Wait for server to be ready
	time.Sleep(2 * time.Second)
	
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			req, err := http.NewRequest(tc.method, "http://localhost:8080"+tc.endpoint, nil)
			if err != nil {
				t.Fatalf("Failed to create request: %v", err)
			}
			
			client := &http.Client{Timeout: 5 * time.Second}
			resp, err := client.Do(req)
			if err != nil {
				t.Fatalf("Request failed: %v", err)
			}
			defer resp.Body.Close()
			
			if resp.StatusCode != tc.expected {
				t.Errorf("Expected status %d, got %d", tc.expected, resp.StatusCode)
			}
		})
	}
}

// TestDatabaseConnections tests database connectivity
func TestDatabaseConnections(t *testing.T) {
	app := fxtest.New(t,
		fx.Provide(
			providers.NewConfig,
			providers.NewLogger,
			providers.NewDBManager,
			providers.NewGormDB,
			providers.NewSqlxDB,
		),
		fx.Invoke(func(
			dbManager *database.Manager,
			gormDB *gorm.DB,
			sqlxDB *sqlx.DB,
		) {
			// Test GORM connection
			var count int64
			if err := gormDB.Raw("SELECT 1").Count(&count).Error; err != nil {
				t.Errorf("GORM connection failed: %v", err)
			}
			
			// Test sqlx connection
			if err := sqlxDB.Ping(); err != nil {
				t.Errorf("Sqlx connection failed: %v", err)
			}
		}),
	)
	
	app.RequireStart()
	app.RequireStop()
}

// TestServiceInteractions tests service-to-service communication
func TestServiceInteractions(t *testing.T) {
	app := fxtest.New(t,
		// Include all modules
		fx.Provide(
			providers.NewConfig,
			providers.NewLogger,
			providers.NewDBManager,
			providers.NewGormDB,
			providers.NewMockQueueManager,
			providers.NewMockEventPublisher,
		),
		
		// Test service interactions
		fx.Invoke(func(
			// Add service parameters as they become available
		) {
			// Test inter-service communication
			t.Log("Testing service interactions")
		}),
	)
	
	app.RequireStart()
	app.RequireStop()
}
```

### Step 5: Create Validation Report Generator

**File: `scripts/generate-validation-report.sh`**
```bash
#!/bin/bash

# Generate comprehensive validation report

echo "=== FX Implementation Validation Report ==="
echo "Generated: $(date)"
echo ""

# Run tests and capture results
echo "1. Running Integration Tests..."
go test ./tests/fx/integration_test.go -v > integration_results.txt 2>&1
if [ $? -eq 0 ]; then
    echo "✅ Integration tests PASSED"
else
    echo "❌ Integration tests FAILED"
fi

echo ""
echo "2. Running Performance Benchmarks..."
go test ./tests/fx/benchmark_test.go -bench=. -benchmem > benchmark_results.txt 2>&1
echo "✅ Benchmarks completed"

echo ""
echo "3. Running Load Tests..."
go test ./tests/fx/load_test.go -v > load_test_results.txt 2>&1
if [ $? -eq 0 ]; then
    echo "✅ Load tests PASSED"
else
    echo "❌ Load tests FAILED"
fi

echo ""
echo "4. Running Regression Tests..."
go test ./tests/fx/regression_test.go -v > regression_results.txt 2>&1
if [ $? -eq 0 ]; then
    echo "✅ Regression tests PASSED"
else
    echo "❌ Regression tests FAILED"
fi

echo ""
echo "5. Checking Build Status..."
make build > build_results.txt 2>&1
if [ $? -eq 0 ]; then
    echo "✅ Build PASSED"
else
    echo "❌ Build FAILED"
fi

echo ""
echo "6. Generating Coverage Report..."
go test ./... -coverprofile=coverage.out > coverage_results.txt 2>&1
go tool cover -html=coverage.out -o coverage.html
COVERAGE=$(go tool cover -func=coverage.out | grep total | awk '{print $3}')
echo "📊 Test Coverage: $COVERAGE"

echo ""
echo "=== Summary ==="
echo "All validation tests completed."
echo "Check individual result files for details:"
echo "- integration_results.txt"
echo "- benchmark_results.txt"
echo "- load_test_results.txt"
echo "- regression_results.txt"
echo "- build_results.txt"
echo "- coverage_results.txt"
echo "- coverage.html"
```

### Step 6: Create Test Configuration

**File: `.env.test`**
```env
# Test environment configuration
APP_ENV=test
LOG_LEVEL=debug

# Database (use test database)
DB_TYPE=mysql
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=test_user
DB_PASSWORD=test_password
DB_DATABASE=wnapi_test

# Disable external services for testing
QUEUE_ENABLED=false
EVENT_ENABLED=false
SMTP_HOST=localhost
SMTP_PORT=1025

# JWT settings
JWT_ACCESS_SIGNING_KEY=test_access_key
JWT_REFRESH_SIGNING_KEY=test_refresh_key
JWT_ACCESS_TOKEN_EXPIRATION=1h
JWT_REFRESH_TOKEN_EXPIRATION=24h
JWT_ISSUER=wnapi-test

# Server settings
SERVER_ADDR=:8080
WEB_URL=http://localhost:3000

# Module settings
MODULES_ENABLED=tenant,auth,rbac,notification
```

### Step 7: Update Makefile

**File: `Makefile` (add test targets)**
```makefile
.PHONY: test-fx
test-fx: ## Run FX-specific tests
	@echo "Running FX tests..."
	@go test ./tests/fx/... -v
	@echo "FX tests completed"

.PHONY: benchmark-fx
benchmark-fx: ## Run FX benchmarks
	@echo "Running FX benchmarks..."
	@go test ./tests/fx/benchmark_test.go -bench=. -benchmem
	@echo "FX benchmarks completed"

.PHONY: load-test-fx
load-test-fx: ## Run FX load tests
	@echo "Running FX load tests..."
	@go test ./tests/fx/load_test.go -v -timeout=5m
	@echo "FX load tests completed"

.PHONY: validate-fx
validate-fx: ## Run complete FX validation
	@echo "Running complete FX validation..."
	@./scripts/generate-validation-report.sh
	@echo "FX validation completed"

.PHONY: test-all-fx
test-all-fx: test-fx benchmark-fx load-test-fx validate-fx ## Run all FX tests
```

## Acceptance Criteria

### Functional Requirements
- [ ] All integration tests passing
- [ ] Performance benchmarks within acceptable ranges
- [ ] Load tests showing system stability
- [ ] Regression tests confirming no functionality loss
- [ ] API compatibility maintained

### Technical Requirements
- [ ] Test coverage > 90%
- [ ] Memory usage not increased significantly
- [ ] Startup time not degraded
- [ ] No memory leaks detected
- [ ] Concurrent request handling stable

### Quality Requirements
- [ ] Comprehensive test suite created
- [ ] Validation report generated
- [ ] Performance baselines established
- [ ] Documentation updated
- [ ] CI/CD integration ready

## File Paths

### New Files
- `tests/fx/integration_test.go`
- `tests/fx/benchmark_test.go`
- `tests/fx/load_test.go`
- `tests/fx/regression_test.go`
- `scripts/generate-validation-report.sh`
- `.env.test`

### Modified Files
- `Makefile` (add test targets)

## Dependencies
- All previous fx migration tasks (01-09)
- Working fx-based application
- Test database setup

## Estimated Time
2 hours

## Notes
- Critical validation step before production
- Establish performance baselines
- Ensure no regressions introduced
- Document all test results thoroughly
