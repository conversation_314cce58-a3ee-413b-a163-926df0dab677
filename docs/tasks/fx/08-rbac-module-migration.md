# Task 08: RBAC Module Migration

## Objective
Migrate RBAC module từ AppBootstrap-based system sang fx.Module architecture. RBAC module phải load sau auth và tenant modules để sử dụng user và tenant services.

## Input
- Tenant và Auth module migrations từ Tasks 04-05
- Existing RBAC module structure
- Current RBAC service implementations
- Permission system integration

## Output
- RBAC module fx.Module implementation
- RBAC service providers
- Permission middleware integration
- Event handler registration

## Requirements

### 1. Technical Requirements
- Load after auth và tenant modules
- Integrate với permission middleware factory
- Support event-driven role assignment
- Maintain RBAC service interfaces

### 2. Architecture Requirements
- Multi-tenant RBAC isolation
- Repository patterns với tenant filtering
- Event handlers for user.created/updated
- Permission checking middleware

## Implementation Steps

### Step 1: Create RBAC FX Module

**File: `modules/rbac/fx.go`**
```go
package rbac

import (
	"go.uber.org/fx"
	
	"wnapi/internal/fx/modules"
	"wnapi/modules/rbac/api"
	"wnapi/modules/rbac/events/handlers"
	"wnapi/modules/rbac/internal"
	"wnapi/modules/rbac/repository/mysql"
	"wnapi/modules/rbac/service"
)

// RBACModule implements FXModule interface
type RBACModule struct{}

// Name returns module name
func (m *RBACModule) Name() string {
	return "rbac"
}

// Dependencies returns module dependencies
func (m *RBACModule) Dependencies() []string {
	return []string{"tenant", "auth"}
}

// Priority returns loading priority
func (m *RBACModule) Priority() int {
	return 20 // Load after auth (10) and tenant (1)
}

// Enabled checks if module should be loaded
func (m *RBACModule) Enabled(config map[string]interface{}) bool {
	if enabled, ok := config["enabled"].(bool); ok {
		return enabled
	}
	return true // Default enabled
}

// Module returns fx.Module for RBAC
func (m *RBACModule) Module() fx.Option {
	return fx.Module("rbac",
		// Providers
		fx.Provide(
			// Configuration
			NewRBACConfig,
			
			// Repositories
			mysql.NewRoleRepository,
			mysql.NewPermissionRepository,
			mysql.NewUserRoleRepository,
			mysql.NewRolePermissionRepository,
			mysql.NewPermissionGroupRepository,
			
			// Services
			fx.Annotate(
				NewRoleService,
				fx.As(new(service.RoleService)),
			),
			fx.Annotate(
				NewPermissionService,
				fx.As(new(service.PermissionService)),
			),
			fx.Annotate(
				NewUserRoleService,
				fx.As(new(service.UserRoleService)),
			),
			fx.Annotate(
				NewPermissionGroupService,
				fx.As(new(service.PermissionGroupService)),
			),
			
			// Event handlers
			NewUserCreatedHandler,
			NewUserUpdatedHandler,
			
			// Handlers
			NewRBACHandler,
		),
		
		// Invocations
		fx.Invoke(
			RegisterRBACRoutes,
			RegisterEventHandlers,
		),
	)
}

// Register RBAC module with global registry
func init() {
	modules.RegisterModule(&RBACModule{})
}
```

### Step 2: Create RBAC Providers

**File: `modules/rbac/providers.go`**
```go
package rbac

import (
	"github.com/jmoiron/sqlx"
	"gorm.io/gorm"
	
	"wnapi/internal/pkg/auth"
	"wnapi/internal/pkg/config"
	"wnapi/internal/pkg/events"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/permission"
	"wnapi/modules/rbac/api"
	"wnapi/modules/rbac/events/handlers"
	"wnapi/modules/rbac/internal"
	"wnapi/modules/rbac/repository"
	"wnapi/modules/rbac/service"
	authService "wnapi/modules/auth/service"
	tenantService "wnapi/modules/tenant/service"
)

// NewRBACConfig creates RBAC configuration
func NewRBACConfig(cfg config.Config) *internal.RBACConfig {
	return &internal.RBACConfig{
		DefaultRole:           cfg.GetStringWithDefault("RBAC_DEFAULT_ROLE", "user"),
		AutoAssignRole:        cfg.GetBoolWithDefault("RBAC_AUTO_ASSIGN_ROLE", true),
		CacheExpiration:       cfg.GetDurationWithDefault("RBAC_CACHE_EXPIRATION", 300), // 5 minutes
		MaxRolesPerUser:       cfg.GetIntWithDefault("RBAC_MAX_ROLES_PER_USER", 10),
		PermissionCacheExpiry: cfg.GetDurationWithDefault("RBAC_PERMISSION_CACHE_EXPIRY", 600), // 10 minutes
	}
}

// NewRoleService creates role service with dependencies
func NewRoleService(
	roleRepo repository.RoleRepository,
	config *internal.RBACConfig,
	log logger.Logger,
) service.RoleService {
	return service.NewRoleService(roleRepo, config, log)
}

// NewPermissionService creates permission service with dependencies
func NewPermissionService(
	permissionRepo repository.PermissionRepository,
	config *internal.RBACConfig,
	log logger.Logger,
) service.PermissionService {
	return service.NewPermissionService(permissionRepo, config, log)
}

// NewUserRoleService creates user role service with dependencies
func NewUserRoleService(
	userRoleRepo repository.UserRoleRepository,
	roleRepo repository.RoleRepository,
	config *internal.RBACConfig,
	log logger.Logger,
) service.UserRoleService {
	return service.NewUserRoleService(userRoleRepo, roleRepo, config, log)
}

// NewPermissionGroupService creates permission group service
func NewPermissionGroupService(
	permissionGroupRepo repository.PermissionGroupRepository,
	permissionRepo repository.PermissionRepository,
	config *internal.RBACConfig,
	log logger.Logger,
) service.PermissionGroupService {
	return service.NewPermissionGroupService(permissionGroupRepo, permissionRepo, config, log)
}

// NewUserCreatedHandler creates user created event handler
func NewUserCreatedHandler(
	userRoleService service.UserRoleService,
	roleService service.RoleService,
	tenantSvc tenantService.TenantService,
	log logger.Logger,
) *handlers.UserCreatedHandler {
	return handlers.NewUserCreatedHandler(userRoleService, roleService, tenantSvc, log)
}

// NewUserUpdatedHandler creates user updated event handler
func NewUserUpdatedHandler(
	userRoleService service.UserRoleService,
	log logger.Logger,
) *handlers.UserUpdatedHandler {
	return handlers.NewUserUpdatedHandler(userRoleService, log)
}

// NewRBACHandler creates RBAC API handler with dependencies
func NewRBACHandler(
	roleService service.RoleService,
	permissionService service.PermissionService,
	userRoleService service.UserRoleService,
	permissionGroupService service.PermissionGroupService,
	mwFactory *permission.MiddlewareFactory,
	jwtService *auth.JWTService,
	log logger.Logger,
	db *sqlx.DB,
	gormDB *gorm.DB,
) *api.Handler {
	return api.NewHandlerWithDependencies(
		roleService,
		permissionService,
		userRoleService,
		permissionGroupService,
		mwFactory,
		jwtService,
		log,
		db,
		gormDB,
	)
}
```

### Step 3: Create Route Registration

**File: `modules/rbac/routes.go`**
```go
package rbac

import (
	"github.com/gin-gonic/gin"
	"wnapi/modules/rbac/api"
	"wnapi/internal/pkg/logger"
)

// RegisterRBACRoutes registers RBAC routes with Gin engine
func RegisterRBACRoutes(engine *gin.Engine, handler *api.Handler, log logger.Logger) {
	log.Info("Registering RBAC routes")
	
	// Use existing route registration logic
	if err := handler.RegisterRoutes(engine); err != nil {
		log.Error("Failed to register RBAC routes", "error", err)
		return
	}
	
	log.Info("RBAC routes registered successfully")
}

// printRoutes logs all registered routes for debugging
func printRoutes(engine *gin.Engine, log logger.Logger) {
	routes := engine.Routes()
	log.Info("RBAC module routes registered", "count", len(routes))
	
	for _, route := range routes {
		log.Debug("Route registered", 
			"method", route.Method,
			"path", route.Path,
			"handler", route.Handler,
		)
	}
}
```

### Step 4: Create Event Handler Registration

**File: `modules/rbac/events.go`**
```go
package rbac

import (
	"wnapi/internal/pkg/events"
	"wnapi/internal/pkg/logger"
	"wnapi/modules/rbac/events/handlers"
)

// RegisterEventHandlers registers RBAC event handlers
func RegisterEventHandlers(
	publisher *events.Publisher,
	userCreatedHandler *handlers.UserCreatedHandler,
	userUpdatedHandler *handlers.UserUpdatedHandler,
	log logger.Logger,
) error {
	log.Info("Registering RBAC event handlers")
	
	// Register user.created handler
	if err := publisher.Subscribe("user.created", userCreatedHandler.Handle); err != nil {
		log.Error("Failed to register user.created handler", "error", err)
		return err
	}
	
	// Register user.updated handler
	if err := publisher.Subscribe("user.updated", userUpdatedHandler.Handle); err != nil {
		log.Error("Failed to register user.updated handler", "error", err)
		return err
	}
	
	log.Info("RBAC event handlers registered successfully")
	return nil
}
```

### Step 5: Update RBAC API Handler

**File: `modules/rbac/api/routes.go` (update)**
```go
// Update NewHandlerWithDependencies to work with fx
func NewHandlerWithDependencies(
	roleService service.RoleService,
	permissionService service.PermissionService,
	userRoleService service.UserRoleService,
	permissionGroupService service.PermissionGroupService,
	mwFactory *permission.MiddlewareFactory,
	jwtService *auth.JWTService,
	log logger.Logger,
	db *sqlx.DB,
	gormDB *gorm.DB,
) *Handler {
	// Create sub-handlers
	adminRoleHandler := handlers.NewAdminRoleHandler(roleService)
	adminPermissionHandler := handlers.NewAdminPermissionHandler(permissionService)
	adminUserRoleHandler := handlers.NewAdminUserRoleHandler(userRoleService)
	adminPermissionGroupHandler := handlers.NewAdminPermissionGroupHandler(permissionGroupService)

	return &Handler{
		adminRoleHandler:            adminRoleHandler,
		adminPermissionHandler:      adminPermissionHandler,
		adminUserRoleHandler:        adminUserRoleHandler,
		adminPermissionGroupHandler: adminPermissionGroupHandler,
		middlewareFactory:           mwFactory,
		jwtService:                  jwtService,
		logger:                      log,
		db:                          db,
		gormDB:                      gormDB,
	}
}

// RegisterRoutes registers RBAC routes with Gin engine
func (h *Handler) RegisterRoutes(engine *gin.Engine) error {
	h.logger.Info("Setting up RBAC routes")

	// Create API group
	apiGroup := engine.Group("/api/v1/rbac")

	// Protected routes (authentication required)
	h.setupProtectedRoutes(apiGroup)

	// Admin routes (authentication + admin permissions required)
	h.setupAdminRoutes(apiGroup)

	h.logger.Info("RBAC routes setup completed")
	return nil
}

// Add printRoutes function for consistency
func (h *Handler) printRoutes() {
	h.logger.Info("RBAC module routes registered")
	// Implementation can be added if needed for debugging
}
```

### Step 6: Update Event Handlers

**File: `modules/rbac/events/handlers/user_created.go` (update)**
```go
// Update to work with fx dependency injection
func NewUserCreatedHandler(
	userRoleService service.UserRoleService,
	roleService service.RoleService,
	tenantService tenantService.TenantService,
	log logger.Logger,
) *UserCreatedHandler {
	return &UserCreatedHandler{
		userRoleService: userRoleService,
		roleService:     roleService,
		tenantService:   tenantService,
		logger:          log,
	}
}

// Handle processes user.created events
func (h *UserCreatedHandler) Handle(event events.Event) error {
	h.logger.Info("Processing user.created event", "event_id", event.ID)
	
	// Extract user data from event
	userData, ok := event.Data.(map[string]interface{})
	if !ok {
		return fmt.Errorf("invalid event data format")
	}
	
	userID, ok := userData["user_id"].(uint)
	if !ok {
		return fmt.Errorf("user_id not found in event data")
	}
	
	tenantID, ok := userData["tenant_id"].(uint)
	if !ok {
		return fmt.Errorf("tenant_id not found in event data")
	}
	
	// Auto-assign default role
	return h.assignDefaultRole(event.Context, userID, tenantID)
}
```

### Step 7: Create RBAC Module Tests

**File: `modules/rbac/fx_test.go`**
```go
package rbac

import (
	"testing"
	
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"
	"wnapi/internal/fx/providers"
)

func TestRBACModule(t *testing.T) {
	rbacModule := &RBACModule{}
	
	// Test module interface
	if rbacModule.Name() != "rbac" {
		t.Errorf("Expected module name 'rbac', got '%s'", rbacModule.Name())
	}
	
	if rbacModule.Priority() != 20 {
		t.Errorf("Expected priority 20, got %d", rbacModule.Priority())
	}
	
	deps := rbacModule.Dependencies()
	expectedDeps := []string{"tenant", "auth"}
	if len(deps) != len(expectedDeps) {
		t.Errorf("Expected %d dependencies, got %d", len(expectedDeps), len(deps))
	}
}

func TestRBACModuleIntegration(t *testing.T) {
	app := fxtest.New(t,
		// Core providers
		fx.Provide(
			providers.NewConfig,
			providers.NewLogger,
			providers.NewDBManager,
			providers.NewGormDB,
			providers.NewMockEventPublisher,
		),
		
		// RBAC module
		rbacModule.Module(),
		
		// Test that RBAC services are available
		fx.Invoke(func(
			roleService service.RoleService,
			permissionService service.PermissionService,
			userRoleService service.UserRoleService,
		) {
			if roleService == nil {
				t.Error("Role service not provided")
			}
			if permissionService == nil {
				t.Error("Permission service not provided")
			}
			if userRoleService == nil {
				t.Error("User role service not provided")
			}
		}),
	)
	
	app.RequireStart()
	app.RequireStop()
}
```

### Step 8: Update Module Registration

**File: `modules/rbac/module.go` (comment out old registration)**
```go
// Comment out old module registration
// func init() {
// 	core.RegisterModuleFactory("rbac", NewModule)
// }

// Add note about fx migration
// NOTE: This module has been migrated to fx.Module
// See fx.go for the new implementation
```

## Acceptance Criteria

### Functional Requirements
- [ ] RBAC module implements FXModule interface
- [ ] All RBAC services available through fx DI
- [ ] RBAC routes registered correctly
- [ ] Event handlers working
- [ ] Permission middleware integrated

### Technical Requirements
- [ ] `make build` passes without errors
- [ ] RBAC module loads after auth/tenant
- [ ] Dependency injection working
- [ ] No breaking changes to RBAC API
- [ ] Event-driven role assignment working

### Code Quality
- [ ] All providers documented
- [ ] Error handling preserved
- [ ] Test coverage maintained
- [ ] Logging integration working
- [ ] Performance not degraded

## File Paths

### New Files
- `modules/rbac/fx.go`
- `modules/rbac/providers.go`
- `modules/rbac/routes.go`
- `modules/rbac/events.go`
- `modules/rbac/fx_test.go`

### Modified Files
- `modules/rbac/api/routes.go` (update handler creation)
- `modules/rbac/events/handlers/user_created.go` (update for fx)
- `modules/rbac/module.go` (comment out old registration)

## Dependencies
- Task 05: Tenant Module Migration
- Task 04: Auth Module Migration
- Event system integration

## Estimated Time
2 hours

## Notes
- Load after auth và tenant modules
- Integrate với event system
- Maintain permission middleware functionality
- Test event-driven role assignment thoroughly
