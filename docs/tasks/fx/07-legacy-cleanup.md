# Task 07: Legacy System Cleanup

## Objective
Loại bỏ các thành phần legacy sau khi fx implementation hoàn tất. Cleanup AppBootstrap, GlobalModuleRegistry, ServiceRegistry và các manual DI patterns.

## Input
- Completed fx implementation từ Tasks 01-06
- Working fx-based application
- Legacy code components to remove

## Output
- Removed AppBootstrap system
- Cleaned up GlobalModuleRegistry
- Updated ServiceRegistry usage
- Removed manual DI patterns

## Requirements

### 1. Technical Requirements
- Ensure fx implementation working 100%
- No breaking changes to public APIs
- Maintain backward compatibility where needed
- Comprehensive testing before removal

### 2. Safety Requirements
- Backup legacy code before removal
- Gradual removal approach
- Rollback plan available
- Monitor for regressions

## Implementation Steps

### Step 1: Backup Legacy Code

**File: `docs/legacy-backup/README.md`**
```markdown
# Legacy Code Backup

This directory contains backup of legacy code removed during fx migration.

## Backed Up Components

### AppBootstrap System
- `internal/core/app_bootstrap.go` - Main bootstrap logic
- `internal/core/module.go` - Module interface and registry
- `internal/core/service_registry.go` - Service registry implementation

### Module Implementations
- `modules/auth/module.go` - Auth module implementation
- `modules/tenant/module.go` - Tenant module implementation
- `modules/rbac/module.go` - RBAC module implementation

### Bootstrap Logic
- `internal/bootstrap/` - Bootstrap utilities
- `cmd/server/main.go.backup` - Original main.go

## Restoration Instructions

If rollback is needed:
1. Copy files from backup to original locations
2. Update imports and dependencies
3. Revert main.go changes
4. Remove fx-related code

## Removal Date
[Current Date]

## Migration Completion
- [x] FX implementation complete
- [x] All tests passing
- [x] Performance validated
- [x] Production deployment successful
```

### Step 2: Remove AppBootstrap

**File: `internal/core/app_bootstrap.go` (remove)**
```bash
# Move to backup first
mkdir -p docs/legacy-backup/internal/core
cp internal/core/app_bootstrap.go docs/legacy-backup/internal/core/
rm internal/core/app_bootstrap.go
```

**File: `internal/core/module.go` (remove)**
```bash
# Move to backup first
cp internal/core/module.go docs/legacy-backup/internal/core/
rm internal/core/module.go
```

### Step 3: Update Service Registry

**File: `internal/core/service_registry.go` (update for fx compatibility)**
```go
// internal/core/service_registry.go
package core

import (
	"fmt"
	"sync"
)

// ServiceRegistry provides backward compatibility for existing code
// that still uses service registry pattern
// 
// DEPRECATED: Use fx dependency injection instead
type ServiceRegistry struct {
	mu       sync.RWMutex
	services map[string]interface{}
}

// NewServiceRegistry creates a new ServiceRegistry
// 
// DEPRECATED: Use fx.Provide instead
func NewServiceRegistry() *ServiceRegistry {
	return &ServiceRegistry{
		services: make(map[string]interface{}),
	}
}

// Register registers a service
// 
// DEPRECATED: Use fx.Provide instead
func (r *ServiceRegistry) Register(name string, service interface{}) error {
	if name == "" {
		return fmt.Errorf("service name cannot be empty")
	}
	if service == nil {
		return fmt.Errorf("service cannot be nil")
	}

	r.mu.Lock()
	defer r.mu.Unlock()

	if _, exists := r.services[name]; exists {
		return fmt.Errorf("service with name '%s' already registered", name)
	}

	r.services[name] = service
	return nil
}

// Get retrieves a service
// 
// DEPRECATED: Use fx dependency injection instead
func (r *ServiceRegistry) Get(name string) (interface{}, bool) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	service, ok := r.services[name]
	return service, ok
}

// GetServiceTyped provides type-safe service retrieval
// 
// DEPRECATED: Use fx dependency injection instead
func GetServiceTyped[T any](r *ServiceRegistry, name string) (T, error) {
	var zero T

	service, ok := r.Get(name)
	if !ok {
		return zero, fmt.Errorf("service '%s' not found", name)
	}

	typedService, ok := service.(T)
	if !ok {
		return zero, fmt.Errorf("service '%s' has unexpected type", name)
	}

	return typedService, nil
}

// Service name constants for backward compatibility
const (
	TenantServiceName      = "tenant_service"
	AuthServiceName        = "auth_service"
	NotificationServiceName = "notification_service"
	RBACServiceName        = "rbac_service"
)
```

### Step 4: Clean Up Module Files

**File: `modules/auth/module.go` (update)**
```go
package auth

// DEPRECATED: This file contains legacy module implementation
// The module has been migrated to fx.Module in fx.go
// 
// This file is kept for backward compatibility and will be removed
// in a future version.

import (
	"fmt"
)

// LegacyModule provides backward compatibility
type LegacyModule struct {
	name string
}

// NewLegacyModule creates a legacy module instance
// 
// DEPRECATED: Use fx.Module instead
func NewLegacyModule() *LegacyModule {
	return &LegacyModule{name: "auth"}
}

// Name returns module name
func (m *LegacyModule) Name() string {
	return m.name
}

// Migration notice
func init() {
	fmt.Println("WARNING: auth module legacy code detected. Please migrate to fx.Module")
}
```

### Step 5: Update Import Statements

**File: `scripts/cleanup-imports.sh`**
```bash
#!/bin/bash

# Script to clean up imports after legacy removal

echo "Cleaning up legacy imports..."

# Find and update import statements
find . -name "*.go" -type f -exec grep -l "wnapi/internal/core" {} \; | while read file; do
    echo "Checking $file for legacy imports..."
    
    # Remove AppBootstrap imports
    sed -i '' '/wnapi\/internal\/core.*AppBootstrap/d' "$file"
    
    # Remove GlobalModuleRegistry imports
    sed -i '' '/wnapi\/internal\/core.*GlobalModuleRegistry/d' "$file"
    
    # Update remaining core imports to be more specific
    sed -i '' 's/wnapi\/internal\/core/wnapi\/internal\/fx\/modules/g' "$file"
done

echo "Import cleanup completed"

# Run go mod tidy to clean up dependencies
go mod tidy

echo "Dependencies cleaned up"
```

### Step 6: Remove Bootstrap Utilities

**File: `internal/bootstrap/` (evaluate and clean)**
```bash
# Review bootstrap utilities
ls -la internal/bootstrap/

# Keep only fx-compatible utilities
# Remove AppBootstrap-specific code
# Update remaining utilities to work with fx
```

### Step 7: Update Documentation

**File: `docs/migration/fx-migration-complete.md`**
```markdown
# FX Migration Completion

## Overview
The migration from AppBootstrap to uber-go/fx has been completed successfully.

## What Was Removed

### Core Components
- `internal/core/app_bootstrap.go` - Manual DI container
- `internal/core/module.go` - Legacy module interface
- Legacy module implementations in each module

### Bootstrap System
- Manual service initialization
- Global module registry
- Manual lifecycle management

## What Was Replaced

### Dependency Injection
- AppBootstrap → fx.App
- Manual service creation → fx.Provide
- Service registry → fx dependency injection

### Module System
- Module interface → FXModule interface
- GlobalModuleRegistry → fx module discovery
- Manual module loading → fx.Module

### Lifecycle Management
- Manual startup/shutdown → fx.Lifecycle
- Signal handling → fx lifecycle hooks

## Benefits Achieved

### Code Quality
- Explicit dependencies
- Type-safe injection
- Reduced global state
- Better testability

### Performance
- Faster startup time
- Lower memory usage
- Better resource management

### Maintainability
- Clearer architecture
- Easier debugging
- Better error messages

## Migration Checklist

- [x] Core providers implemented
- [x] Module system migrated
- [x] Auth module migrated
- [x] Tenant module migrated
- [x] Main.go refactored
- [x] Legacy code removed
- [x] Tests updated
- [x] Documentation updated

## Rollback Procedure

If rollback is needed:
1. Restore files from `docs/legacy-backup/`
2. Revert main.go changes
3. Update module registrations
4. Run tests to verify functionality

## Next Steps

1. Monitor application performance
2. Update remaining modules to fx
3. Remove deprecated warnings
4. Complete documentation update
```

### Step 8: Create Validation Tests

**File: `tests/fx-migration-validation_test.go`**
```go
package tests

import (
	"context"
	"testing"
	"time"
	
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"
	"wnapi/internal/fx"
)

// TestFXMigrationComplete validates that fx migration is complete
func TestFXMigrationComplete(t *testing.T) {
	// Test that application can start with fx
	app := fx.NewApp()
	if app == nil {
		t.Fatal("Failed to create fx app")
	}
	
	// Test that no legacy imports remain
	testNoLegacyImports(t)
	
	// Test that all modules are fx-compatible
	testModuleFXCompatibility(t)
	
	// Test application lifecycle
	testApplicationLifecycle(t)
}

func testNoLegacyImports(t *testing.T) {
	// This would be implemented to scan for legacy imports
	// For now, just a placeholder
	t.Log("Checking for legacy imports...")
}

func testModuleFXCompatibility(t *testing.T) {
	// Test that all modules implement FXModule interface
	t.Log("Checking module fx compatibility...")
}

func testApplicationLifecycle(t *testing.T) {
	app := fxtest.New(t,
		fx.Invoke(func() {
			// Test basic functionality
		}),
	)
	
	app.RequireStart()
	app.RequireStop()
}

// TestPerformanceRegression ensures no performance degradation
func TestPerformanceRegression(t *testing.T) {
	start := time.Now()
	
	app := fx.NewApp()
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	
	if err := app.Start(ctx); err != nil {
		t.Fatalf("Failed to start app: %v", err)
	}
	
	startupTime := time.Since(start)
	
	if err := app.Stop(ctx); err != nil {
		t.Fatalf("Failed to stop app: %v", err)
	}
	
	// Ensure startup time is reasonable (adjust threshold as needed)
	if startupTime > 5*time.Second {
		t.Errorf("Startup time too slow: %v", startupTime)
	}
	
	t.Logf("Startup time: %v", startupTime)
}
```

### Step 9: Update Makefile

**File: `Makefile` (update)**
```makefile
# Remove legacy-related targets
# Add fx-specific targets

.PHONY: fx-validate
fx-validate: ## Validate fx migration
	@echo "Validating fx migration..."
	@go test ./tests/fx-migration-validation_test.go -v
	@echo "FX migration validation completed"

.PHONY: cleanup-legacy
cleanup-legacy: ## Clean up legacy code
	@echo "Cleaning up legacy imports..."
	@./scripts/cleanup-imports.sh
	@echo "Legacy cleanup completed"

.PHONY: fx-test
fx-test: ## Run fx-specific tests
	@echo "Running fx tests..."
	@go test ./internal/fx/... -v
	@go test ./modules/*/fx_test.go -v
	@echo "FX tests completed"
```

## Acceptance Criteria

### Functional Requirements
- [ ] All legacy code removed or deprecated
- [ ] Application works without AppBootstrap
- [ ] No references to GlobalModuleRegistry
- [ ] ServiceRegistry usage minimized
- [ ] All tests passing

### Technical Requirements
- [ ] `make build` passes without errors
- [ ] No performance regression
- [ ] Memory usage not increased
- [ ] Startup time not degraded
- [ ] All functionality preserved

### Code Quality
- [ ] Clean codebase without legacy artifacts
- [ ] Updated documentation
- [ ] Proper deprecation warnings
- [ ] Backup of removed code
- [ ] Migration validation tests

## File Paths

### Removed Files
- `internal/core/app_bootstrap.go`
- `internal/core/module.go` (legacy parts)
- Legacy module implementations

### New Files
- `docs/legacy-backup/README.md`
- `docs/migration/fx-migration-complete.md`
- `tests/fx-migration-validation_test.go`
- `scripts/cleanup-imports.sh`

### Modified Files
- `internal/core/service_registry.go` (deprecation warnings)
- `modules/*/module.go` (deprecation notices)
- `Makefile` (updated targets)

## Dependencies
- Task 06: Main.go Refactoring
- All previous fx migration tasks
- Comprehensive testing

## Estimated Time
2 hours

## Notes
- Ensure complete fx implementation before cleanup
- Keep backup of all removed code
- Monitor for any regressions
- Update documentation thoroughly
