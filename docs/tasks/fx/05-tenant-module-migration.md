# Task 05: Tenant Module Migration

## Objective
Migrate tenant module từ AppBootstrap-based system sang fx.Module architecture. Tenant module phải load trước auth module để cung cấp multi-tenant support.

## Input
- Auth module migration từ Task 04
- Existing tenant module structure
- Current tenant service implementations
- Multi-tenant architecture requirements

## Output
- Tenant module fx.Module implementation
- Tenant service providers
- Tenant middleware integration
- Updated tenant module structure

## Requirements

### 1. Technical Requirements
- Load before auth module (higher priority)
- Provide tenant context middleware
- Support domain-based tenant resolution
- Maintain tenant service interfaces

### 2. Architecture Requirements
- Multi-tenant data isolation
- Tenant ID parameter in all operations
- Repository patterns với tenant filtering
- GORM for CRUD, raw SQL for lists

## Implementation Steps

### Step 1: Create Tenant FX Module

**File: `modules/tenant/fx.go`**
```go
package tenant

import (
	"go.uber.org/fx"
	
	"wnapi/internal/fx/modules"
	"wnapi/modules/tenant/api"
	"wnapi/modules/tenant/internal"
	"wnapi/modules/tenant/repository/mysql"
	"wnapi/modules/tenant/service"
)

// TenantModule implements FXModule interface
type TenantModule struct{}

// Name returns module name
func (m *TenantModule) Name() string {
	return "tenant"
}

// Dependencies returns module dependencies
func (m *TenantModule) Dependencies() []string {
	return []string{} // No dependencies - loads first
}

// Priority returns loading priority
func (m *TenantModule) Priority() int {
	return 1 // Highest priority - load first
}

// Enabled checks if module should be loaded
func (m *TenantModule) Enabled(config map[string]interface{}) bool {
	if enabled, ok := config["enabled"].(bool); ok {
		return enabled
	}
	return true // Default enabled
}

// Module returns fx.Module for tenant
func (m *TenantModule) Module() fx.Option {
	return fx.Module("tenant",
		// Providers
		fx.Provide(
			// Configuration
			NewTenantConfig,
			
			// Repositories
			mysql.NewTenantRepository,
			
			// Services
			fx.Annotate(
				NewTenantService,
				fx.As(new(service.TenantService)),
			),
			
			// Middleware
			NewTenantMiddleware,
			
			// Handlers
			NewTenantHandler,
		),
		
		// Route registration
		fx.Invoke(RegisterTenantRoutes),
	)
}

// Register tenant module with global registry
func init() {
	modules.RegisterModule(&TenantModule{})
}
```

### Step 2: Create Tenant Providers

**File: `modules/tenant/providers.go`**
```go
package tenant

import (
	"github.com/gin-gonic/gin"
	"github.com/jmoiron/sqlx"
	"gorm.io/gorm"
	
	"wnapi/internal/pkg/config"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/permission"
	"wnapi/modules/tenant/api"
	"wnapi/modules/tenant/internal"
	"wnapi/modules/tenant/middleware"
	"wnapi/modules/tenant/repository"
	"wnapi/modules/tenant/service"
)

// NewTenantConfig creates tenant configuration
func NewTenantConfig(cfg config.Config) *internal.TenantConfig {
	return &internal.TenantConfig{
		DefaultTenant:     cfg.GetStringWithDefault("TENANT_DEFAULT", "default"),
		DomainResolution:  cfg.GetBoolWithDefault("TENANT_DOMAIN_RESOLUTION", true),
		SubdomainMode:     cfg.GetBoolWithDefault("TENANT_SUBDOMAIN_MODE", false),
		RequireTenant:     cfg.GetBoolWithDefault("TENANT_REQUIRE", true),
		CacheExpiration:   cfg.GetDurationWithDefault("TENANT_CACHE_EXPIRATION", 300), // 5 minutes
	}
}

// NewTenantService creates tenant service with dependencies
func NewTenantService(
	repo repository.TenantRepository,
	config *internal.TenantConfig,
	log logger.Logger,
) service.TenantService {
	return service.NewTenantService(repo, config, log)
}

// NewTenantMiddleware creates tenant middleware
func NewTenantMiddleware(
	tenantService service.TenantService,
	config *internal.TenantConfig,
	log logger.Logger,
) gin.HandlerFunc {
	return middleware.NewTenantMiddleware(tenantService, config, log)
}

// NewTenantHandler creates tenant API handler with dependencies
func NewTenantHandler(
	tenantService service.TenantService,
	mwFactory *permission.MiddlewareFactory,
	log logger.Logger,
	db *sqlx.DB,
	gormDB *gorm.DB,
) *api.Handler {
	return api.NewHandlerWithDependencies(
		tenantService,
		mwFactory,
		log,
		db,
		gormDB,
	)
}

// TenantServiceParams defines parameters for tenant service creation
type TenantServiceParams struct {
	Repository repository.TenantRepository
	Config     *internal.TenantConfig
	Logger     logger.Logger
}

// NewTenantServiceWithParams creates tenant service with structured params
func NewTenantServiceWithParams(params TenantServiceParams) service.TenantService {
	return service.NewTenantService(params.Repository, params.Config, params.Logger)
}
```

### Step 3: Create Tenant Middleware Provider

**File: `modules/tenant/middleware/provider.go`**
```go
package middleware

import (
	"github.com/gin-gonic/gin"
	"wnapi/internal/pkg/logger"
	"wnapi/modules/tenant/internal"
	"wnapi/modules/tenant/service"
)

// NewTenantMiddleware creates tenant resolution middleware
func NewTenantMiddleware(
	tenantService service.TenantService,
	config *internal.TenantConfig,
	log logger.Logger,
) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Extract tenant from request
		tenantCode := extractTenantFromRequest(c, config)
		
		if tenantCode == "" {
			if config.RequireTenant {
				log.Warn("Tenant required but not found in request")
				c.JSON(400, gin.H{"error": "Tenant not specified"})
				c.Abort()
				return
			}
			tenantCode = config.DefaultTenant
		}
		
		// Validate tenant
		tenant, err := tenantService.GetByCode(c.Request.Context(), tenantCode)
		if err != nil {
			log.Error("Failed to resolve tenant", "tenant", tenantCode, "error", err)
			c.JSON(404, gin.H{"error": "Tenant not found"})
			c.Abort()
			return
		}
		
		// Set tenant context
		c.Set("tenant_id", tenant.ID)
		c.Set("tenant_code", tenant.Code)
		c.Set("tenant", tenant)
		
		log.Debug("Tenant resolved", "tenant_id", tenant.ID, "tenant_code", tenant.Code)
		c.Next()
	}
}

// extractTenantFromRequest extracts tenant identifier from request
func extractTenantFromRequest(c *gin.Context, config *internal.TenantConfig) string {
	// Try header first
	if tenantCode := c.GetHeader("X-Tenant-Code"); tenantCode != "" {
		return tenantCode
	}
	
	// Try subdomain if enabled
	if config.SubdomainMode {
		if tenantCode := extractFromSubdomain(c.Request.Host); tenantCode != "" {
			return tenantCode
		}
	}
	
	// Try domain resolution if enabled
	if config.DomainResolution {
		if tenantCode := extractFromDomain(c.Request.Host); tenantCode != "" {
			return tenantCode
		}
	}
	
	// Try query parameter
	if tenantCode := c.Query("tenant"); tenantCode != "" {
		return tenantCode
	}
	
	return ""
}

// extractFromSubdomain extracts tenant from subdomain
func extractFromSubdomain(host string) string {
	// Implementation for subdomain extraction
	// e.g., tenant1.example.com -> tenant1
	return ""
}

// extractFromDomain extracts tenant from domain mapping
func extractFromDomain(host string) string {
	// Implementation for domain-based tenant resolution
	// e.g., tenant1.com -> tenant1
	return ""
}
```

### Step 4: Create Route Registration

**File: `modules/tenant/routes.go`**
```go
package tenant

import (
	"github.com/gin-gonic/gin"
	"wnapi/modules/tenant/api"
	"wnapi/internal/pkg/logger"
)

// RegisterTenantRoutes registers tenant routes with Gin engine
func RegisterTenantRoutes(
	engine *gin.Engine, 
	handler *api.Handler, 
	tenantMiddleware gin.HandlerFunc,
	log logger.Logger,
) {
	log.Info("Registering tenant routes")
	
	// Apply tenant middleware globally
	engine.Use(tenantMiddleware)
	
	// Register tenant management routes
	if err := handler.RegisterRoutes(engine); err != nil {
		log.Error("Failed to register tenant routes", "error", err)
		return
	}
	
	log.Info("Tenant routes registered successfully")
}

// printRoutes logs all registered routes for debugging
func printRoutes(engine *gin.Engine, log logger.Logger) {
	routes := engine.Routes()
	log.Info("Tenant module routes registered", "count", len(routes))
	
	for _, route := range routes {
		log.Debug("Route registered", 
			"method", route.Method,
			"path", route.Path,
			"handler", route.Handler,
		)
	}
}
```

### Step 5: Update Tenant API Handler

**File: `modules/tenant/api/routes.go` (update)**
```go
// Update NewHandlerWithDependencies to work with fx
func NewHandlerWithDependencies(
	tenantService service.TenantService,
	mwFactory *permission.MiddlewareFactory,
	log logger.Logger,
	db *sqlx.DB,
	gormDB *gorm.DB,
) *Handler {
	// Create sub-handlers
	adminTenantHandler := handlers.NewAdminTenantHandler(tenantService)

	return &Handler{
		adminTenantHandler: adminTenantHandler,
		middlewareFactory:  mwFactory,
		logger:            log,
		db:                db,
		gormDB:            gormDB,
	}
}

// RegisterRoutes registers tenant routes with Gin engine
func (h *Handler) RegisterRoutes(engine *gin.Engine) error {
	h.logger.Info("Setting up tenant routes")

	// Create API group
	apiGroup := engine.Group("/api/v1/tenants")

	// Public routes (tenant resolution already applied)
	h.setupPublicRoutes(apiGroup)

	// Protected routes (authentication required)
	h.setupProtectedRoutes(apiGroup)

	// Admin routes (authentication + admin permissions required)
	h.setupAdminRoutes(apiGroup)

	h.logger.Info("Tenant routes setup completed")
	return nil
}

// Add printRoutes function for consistency
func (h *Handler) printRoutes() {
	h.logger.Info("Tenant module routes registered")
	// Implementation can be added if needed for debugging
}
```

### Step 6: Create Tenant Module Tests

**File: `modules/tenant/fx_test.go`**
```go
package tenant

import (
	"testing"
	
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"
	"wnapi/internal/fx/providers"
)

func TestTenantModule(t *testing.T) {
	tenantModule := &TenantModule{}
	
	// Test module interface
	if tenantModule.Name() != "tenant" {
		t.Errorf("Expected module name 'tenant', got '%s'", tenantModule.Name())
	}
	
	if tenantModule.Priority() != 1 {
		t.Errorf("Expected priority 1, got %d", tenantModule.Priority())
	}
	
	deps := tenantModule.Dependencies()
	if len(deps) != 0 {
		t.Errorf("Expected 0 dependencies, got %d", len(deps))
	}
}

func TestTenantModuleIntegration(t *testing.T) {
	app := fxtest.New(t,
		// Core providers
		fx.Provide(
			providers.NewConfig,
			providers.NewLogger,
			providers.NewDBManager,
			providers.NewGormDB,
		),
		
		// Tenant module
		tenantModule.Module(),
		
		// Test that tenant services are available
		fx.Invoke(func(
			tenantService service.TenantService,
			tenantMiddleware gin.HandlerFunc,
		) {
			if tenantService == nil {
				t.Error("Tenant service not provided")
			}
			if tenantMiddleware == nil {
				t.Error("Tenant middleware not provided")
			}
		}),
	)
	
	app.RequireStart()
	app.RequireStop()
}
```

### Step 7: Update Module Registration

**File: `modules/tenant/module.go` (comment out old registration)**
```go
// Comment out old module registration
// func init() {
// 	core.RegisterModuleFactory("tenant", NewModule)
// }

// Add note about fx migration
// NOTE: This module has been migrated to fx.Module
// See fx.go for the new implementation
```

## Acceptance Criteria

### Functional Requirements
- [ ] Tenant module implements FXModule interface
- [ ] Tenant service available through fx DI
- [ ] Tenant middleware applied globally
- [ ] Multi-tenant data isolation working
- [ ] Domain-based tenant resolution functional

### Technical Requirements
- [ ] `make build` passes without errors
- [ ] Tenant module loads before auth module
- [ ] Dependency injection working
- [ ] No breaking changes to tenant API
- [ ] Memory efficient implementation

### Code Quality
- [ ] All providers documented
- [ ] Error handling preserved
- [ ] Test coverage maintained
- [ ] Logging integration working
- [ ] Performance not degraded

## File Paths

### New Files
- `modules/tenant/fx.go`
- `modules/tenant/providers.go`
- `modules/tenant/middleware/provider.go`
- `modules/tenant/routes.go`
- `modules/tenant/fx_test.go`

### Modified Files
- `modules/tenant/api/routes.go` (update handler creation)
- `modules/tenant/module.go` (comment out old registration)

## Dependencies
- Task 04: Auth Module Migration (tenant loads before auth)
- Core providers (database, config, logger)

## Estimated Time
2 hours

## Notes
- Tenant module must load first (priority 1)
- Provides foundation for multi-tenant architecture
- Middleware applied globally to all routes
- Critical for auth module dependency
