# Task 06: Main.go Refactoring

## Objective
Refactor cmd/server/main.go để sử dụng fx.App thay vì AppBootstrap. Tạo clean, minimal main function với fx dependency injection và lifecycle management.

## Input
- Module migrations từ Tasks 04-05
- Current main.go với AppBootstrap
- Existing server startup logic
- Event và queue system integration

## Output
- Simplified main.go với fx.App
- Proper lifecycle management
- Event và queue system integration
- CLI mode support

## Requirements

### 1. Technical Requirements
- Maintain all existing functionality
- Support CLI mode (skip queue/events)
- Proper graceful shutdown
- Error handling và logging

### 2. Architecture Requirements
- Clean separation of concerns
- Configuration-driven startup
- Module loading order respected
- Service registry compatibility

## Implementation Steps

### Step 1: Create New Main Function

**File: `cmd/server/main.go` (replace)**
```go
package main

import (
	"context"
	"flag"
	"log"
	"os"
	"strings"
	
	"go.uber.org/fx"
	"wnapi/internal/fx"
	"wnapi/internal/fx/modules"
	"wnapi/internal/pkg/config/viperconfig"
	
	// Import modules để register với fx
	_ "wnapi/modules/auth"
	_ "wnapi/modules/tenant"
	_ "wnapi/modules/rbac"
	_ "wnapi/modules/notification"
	_ "wnapi/modules/hello"
	_ "wnapi/modules/media"
	_ "wnapi/modules/blog"
	_ "wnapi/modules/product"
)

func main() {
	// Parse command line flags
	var (
		configFile = flag.String("config", ".env", "Configuration file path")
		cliMode    = flag.Bool("cli", false, "Run in CLI mode (skip queue/events)")
		showHelp   = flag.Bool("help", false, "Show help message")
	)
	flag.Parse()
	
	if *showHelp {
		flag.Usage()
		os.Exit(0)
	}
	
	// Create fx app with configuration
	app := createApp(*configFile, *cliMode)
	
	// Run the application
	app.Run()
	
	// Check for errors
	if app.Err() != nil {
		log.Fatalf("Application failed: %v", app.Err())
	}
}

// createApp creates fx.App with proper configuration
func createApp(configFile string, cliMode bool) *fx.App {
	return fx.New(
		// Core providers
		fx.Provide(
			func() string { return configFile },
			createConfigProvider,
			fx.providers.NewLogger,
			fx.providers.NewDBManager,
			fx.providers.NewGormDB,
			fx.providers.NewSqlxDB,
			fx.providers.NewAppCache,
			fx.providers.NewJWTConfig,
			fx.providers.NewJWTService,
			fx.providers.NewPermissionFactory,
			fx.providers.NewGinEngine,
			fx.providers.NewTracingProvider,
		),
		
		// Conditional providers based on mode
		fx.Options(conditionalProviders(cliMode)...),
		
		// Module loading
		fx.Invoke(loadModules),
		
		// Lifecycle management
		fx.Invoke(registerLifecycleHooks),
		
		// Application startup
		fx.Invoke(startApplication),
	)
}

// createConfigProvider creates config with custom file path
func createConfigProvider(configFile string) (config.Config, error) {
	return viperconfig.NewConfigLoader().Load("@" + configFile)
}

// conditionalProviders returns providers based on application mode
func conditionalProviders(cliMode bool) []fx.Option {
	var opts []fx.Option
	
	if !cliMode {
		// Add queue and event providers for server mode
		opts = append(opts,
			fx.Provide(
				createQueueManager,
				createEventPublisher,
			),
		)
	} else {
		// Provide mock implementations for CLI mode
		opts = append(opts,
			fx.Provide(
				createMockQueue,
				createMockEventPublisher,
			),
		)
	}
	
	return opts
}
```

### Step 2: Create Queue và Event Providers

**File: `internal/fx/providers/queue.go`**
```go
package providers

import (
	"wnapi/internal/pkg/config"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/queue"
	"wnapi/internal/pkg/queue/asynq"
)

// NewQueueManager creates queue manager for server mode
func NewQueueManager(cfg config.Config, log logger.Logger) (queue.Manager, error) {
	if !cfg.GetBoolWithDefault("QUEUE_ENABLED", true) {
		log.Info("Queue system disabled")
		return &MockQueueManager{}, nil
	}
	
	queueConfig := queue.Config{
		RedisAddr:     cfg.GetStringWithDefault("REDIS_ADDR", "localhost:6379"),
		RedisPassword: cfg.GetString("REDIS_PASSWORD"),
		RedisDB:       cfg.GetIntWithDefault("REDIS_DB", 0),
		Concurrency:   cfg.GetIntWithDefault("QUEUE_CONCURRENCY", 10),
	}
	
	return asynq.NewManager(queueConfig, log)
}

// MockQueueManager provides mock implementation for CLI mode
type MockQueueManager struct{}

func (m *MockQueueManager) Start() error                    { return nil }
func (m *MockQueueManager) Stop() error                     { return nil }
func (m *MockQueueManager) Enqueue(task queue.Task) error   { return nil }
func (m *MockQueueManager) RegisterHandler(string, queue.Handler) error { return nil }

// NewMockQueueManager creates mock queue manager
func NewMockQueueManager() queue.Manager {
	return &MockQueueManager{}
}
```

**File: `internal/fx/providers/events.go`**
```go
package providers

import (
	"wnapi/internal/pkg/config"
	"wnapi/internal/pkg/events"
	"wnapi/internal/pkg/logger"
)

// NewEventPublisher creates event publisher for server mode
func NewEventPublisher(cfg config.Config, log logger.Logger) (*events.Publisher, error) {
	if !cfg.GetBoolWithDefault("EVENT_ENABLED", true) {
		log.Info("Event system disabled")
		return &MockEventPublisher{}, nil
	}
	
	return events.NewPublisher(log), nil
}

// MockEventPublisher provides mock implementation for CLI mode
type MockEventPublisher struct{}

func (m *MockEventPublisher) Publish(event events.Event) error { return nil }
func (m *MockEventPublisher) Subscribe(eventType string, handler events.Handler) error { return nil }

// NewMockEventPublisher creates mock event publisher
func NewMockEventPublisher() *events.Publisher {
	// Return actual publisher but with disabled functionality
	return &MockEventPublisher{}
}
```

### Step 3: Create Module Loading Logic

**File: `cmd/server/modules.go`**
```go
package main

import (
	"go.uber.org/fx"
	"wnapi/internal/fx/modules"
	"wnapi/internal/pkg/config"
	"wnapi/internal/pkg/logger"
)

// loadModules loads enabled modules based on configuration
func loadModules(cfg config.Config, log logger.Logger) error {
	log.Info("Loading modules")
	
	// Discover and load modules
	moduleOptions, err := modules.DiscoverModules(cfg, log)
	if err != nil {
		return fmt.Errorf("failed to discover modules: %w", err)
	}
	
	log.Info("Modules discovered", "count", len(moduleOptions))
	
	// Module loading is handled by fx automatically
	// This function just logs the process
	return nil
}

// getEnabledModules returns list of enabled modules from config
func getEnabledModules(cfg config.Config) []string {
	enabledStr := cfg.GetStringWithDefault("MODULES_ENABLED", "tenant,auth,rbac,notification")
	if enabledStr == "" {
		return []string{}
	}
	
	modules := strings.Split(enabledStr, ",")
	var cleaned []string
	for _, module := range modules {
		if trimmed := strings.TrimSpace(module); trimmed != "" {
			cleaned = append(cleaned, trimmed)
		}
	}
	
	return cleaned
}
```

### Step 4: Create Lifecycle Management

**File: `cmd/server/lifecycle.go`**
```go
package main

import (
	"context"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"
	
	"github.com/gin-gonic/gin"
	"go.uber.org/fx"
	"wnapi/internal/pkg/config"
	"wnapi/internal/pkg/events"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/queue"
)

// registerLifecycleHooks registers application lifecycle hooks
func registerLifecycleHooks(
	lc fx.Lifecycle,
	engine *gin.Engine,
	cfg config.Config,
	log logger.Logger,
	queueManager queue.Manager,
	eventPublisher *events.Publisher,
) {
	serverAddr := cfg.GetStringWithDefault("SERVER_ADDR", ":8080")
	srv := &http.Server{
		Addr:    serverAddr,
		Handler: engine,
	}
	
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			log.Info("Starting application services")
			
			// Start queue manager
			if err := queueManager.Start(); err != nil {
				return fmt.Errorf("failed to start queue manager: %w", err)
			}
			
			// Start HTTP server
			log.Info("Starting HTTP server", "addr", serverAddr)
			go func() {
				if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
					log.Fatal("Failed to run server", "error", err)
				}
			}()
			
			log.Info("Application started successfully")
			return nil
		},
		OnStop: func(ctx context.Context) error {
			log.Info("Stopping application services")
			
			// Stop HTTP server
			shutdownTimeout := cfg.GetDurationWithDefault("SERVER_SHUTDOWN_TIMEOUT", 10*time.Second)
			shutdownCtx, cancel := context.WithTimeout(context.Background(), shutdownTimeout)
			defer cancel()
			
			if err := srv.Shutdown(shutdownCtx); err != nil {
				log.Error("Failed to shutdown server gracefully", "error", err)
			}
			
			// Stop queue manager
			if err := queueManager.Stop(); err != nil {
				log.Error("Failed to stop queue manager", "error", err)
			}
			
			log.Info("Application stopped")
			return nil
		},
	})
}

// startApplication performs final application startup tasks
func startApplication(cfg config.Config, log logger.Logger) {
	log.Info("Application initialization completed")
	
	// Setup signal handling for graceful shutdown
	setupSignalHandling(log)
	
	// Log application info
	logApplicationInfo(cfg, log)
}

// setupSignalHandling sets up graceful shutdown on signals
func setupSignalHandling(log logger.Logger) {
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)
	
	go func() {
		sig := <-c
		log.Info("Received shutdown signal", "signal", sig)
		// fx will handle the actual shutdown through lifecycle hooks
	}()
}

// logApplicationInfo logs application startup information
func logApplicationInfo(cfg config.Config, log logger.Logger) {
	log.Info("Application Information",
		"env", cfg.GetString("APP_ENV"),
		"version", cfg.GetStringWithDefault("APP_VERSION", "unknown"),
		"server_addr", cfg.GetStringWithDefault("SERVER_ADDR", ":8080"),
	)
}
```

### Step 5: Update CLI Integration

**File: `cmd/cli/main.go` (update)**
```go
// Update CLI to use fx for consistency
func main() {
	// Set CLI mode flag
	os.Setenv("CLI_MODE", "true")
	os.Setenv("QUEUE_ENABLED", "false")
	os.Setenv("EVENT_ENABLED", "false")
	
	// Create fx app for CLI
	app := fx.New(
		// Core providers only
		fx.Provide(
			fx.providers.NewConfig,
			fx.providers.NewLogger,
			fx.providers.NewDBManager,
			fx.providers.NewGormDB,
			fx.providers.NewSqlxDB,
			fx.providers.NewAppCache,
			fx.providers.NewMockQueueManager,
			fx.providers.NewMockEventPublisher,
		),
		
		// CLI-specific initialization
		fx.Invoke(initializeCLI),
	)
	
	// Start app context for CLI operations
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	
	if err := app.Start(ctx); err != nil {
		log.Fatalf("Failed to start CLI app: %v", err)
	}
	defer app.Stop(ctx)
	
	// Run CLI commands
	runCLICommands()
}
```

### Step 6: Create Integration Tests

**File: `cmd/server/main_test.go`**
```go
package main

import (
	"context"
	"testing"
	"time"
	
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"
)

func TestCreateApp(t *testing.T) {
	// Test server mode
	app := createApp(".env.test", false)
	if app == nil {
		t.Fatal("Expected app to be created")
	}
	
	// Test CLI mode
	cliApp := createApp(".env.test", true)
	if cliApp == nil {
		t.Fatal("Expected CLI app to be created")
	}
}

func TestAppLifecycle(t *testing.T) {
	app := fxtest.New(t,
		// Minimal providers for testing
		fx.Provide(
			func() string { return ".env.test" },
			createConfigProvider,
			fx.providers.NewLogger,
			fx.providers.NewMockQueueManager,
			fx.providers.NewMockEventPublisher,
		),
		
		fx.Invoke(func(
			cfg config.Config,
			log logger.Logger,
		) {
			// Test that basic services are available
			if cfg == nil {
				t.Error("Config not available")
			}
			if log == nil {
				t.Error("Logger not available")
			}
		}),
	)
	
	app.RequireStart()
	app.RequireStop()
}
```

## Acceptance Criteria

### Functional Requirements
- [ ] Main.go uses fx.App instead of AppBootstrap
- [ ] All existing functionality preserved
- [ ] CLI mode working correctly
- [ ] Graceful shutdown implemented
- [ ] Module loading working

### Technical Requirements
- [ ] `make build` passes without errors
- [ ] Server starts và stops cleanly
- [ ] Memory usage optimized
- [ ] Error handling comprehensive
- [ ] Logging integration working

### Code Quality
- [ ] Clean, readable main function
- [ ] Proper separation of concerns
- [ ] Test coverage adequate
- [ ] Documentation complete
- [ ] Performance maintained

## File Paths

### New Files
- `cmd/server/modules.go`
- `cmd/server/lifecycle.go`
- `cmd/server/main_test.go`
- `internal/fx/providers/queue.go`
- `internal/fx/providers/events.go`

### Modified Files
- `cmd/server/main.go` (complete rewrite)
- `cmd/cli/main.go` (update to use fx)

## Dependencies
- Task 05: Tenant Module Migration
- Task 04: Auth Module Migration
- Core providers implementation

## Estimated Time
2 hours

## Notes
- Complete replacement of AppBootstrap
- Maintain backward compatibility
- Focus on clean, minimal main function
- Ensure all existing features work
