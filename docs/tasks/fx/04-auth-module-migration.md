# Task 04: Auth Module Migration

## Objective
Migrate auth module từ AppBootstrap-based system sang fx.Module architecture. Tạo fx.Module cho auth với proper dependency injection, service providers, và route registration.

## Input
- Module system từ Task 03
- Existing auth module structure
- Current auth service implementations
- Auth module dependencies

## Output
- Auth module fx.Module implementation
- Auth service providers
- Auth route registration với fx
- Updated auth module structure

## Requirements

### 1. Technical Requirements
- Maintain all existing auth functionality
- Support multi-tenant architecture
- Proper dependency injection
- Service interface compatibility

### 2. Architecture Requirements
- Follow established auth patterns
- Middleware ordering: tenant -> auth -> rbac
- Repository/service interface patterns
- GORM for CRUD, raw SQL for lists

## Implementation Steps

### Step 1: Create Auth FX Module

**File: `modules/auth/fx.go`**
```go
package auth

import (
	"go.uber.org/fx"
	
	"wnapi/internal/fx/modules"
	"wnapi/modules/auth/api"
	"wnapi/modules/auth/internal"
	"wnapi/modules/auth/repository/mysql"
	"wnapi/modules/auth/service"
	notificationService "wnapi/modules/notification/service"
)

// AuthModule implements FXModule interface
type AuthModule struct{}

// Name returns module name
func (m *AuthModule) Name() string {
	return "auth"
}

// Dependencies returns module dependencies
func (m *AuthModule) Dependencies() []string {
	return []string{"tenant", "notification"}
}

// Priority returns loading priority
func (m *AuthModule) Priority() int {
	return 10 // Load after core modules
}

// Enabled checks if module should be loaded
func (m *AuthModule) Enabled(config map[string]interface{}) bool {
	if enabled, ok := config["enabled"].(bool); ok {
		return enabled
	}
	return true // Default enabled
}

// Module returns fx.Module for auth
func (m *AuthModule) Module() fx.Option {
	return fx.Module("auth",
		// Providers
		fx.Provide(
			// Configuration
			NewAuthConfig,
			
			// Repositories
			mysql.NewMySQLRepositoryWithDB,
			mysql.NewEmailVerificationRepository,
			mysql.NewPasswordResetRepository,
			
			// Services
			fx.Annotate(
				NewAuthService,
				fx.As(new(internal.AuthService)),
			),
			fx.Annotate(
				NewUserService,
				fx.As(new(service.UserService)),
			),
			NewPasswordResetService,
			
			// Handlers
			NewAuthHandler,
		),
		
		// Route registration
		fx.Invoke(RegisterAuthRoutes),
	)
}

// Register auth module with global registry
func init() {
	modules.RegisterModule(&AuthModule{})
}
```

### Step 2: Create Auth Providers

**File: `modules/auth/providers.go`**
```go
package auth

import (
	"github.com/jmoiron/sqlx"
	"gorm.io/gorm"
	
	"wnapi/internal/pkg/auth"
	"wnapi/internal/pkg/config"
	"wnapi/internal/pkg/events"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/permission"
	"wnapi/modules/auth/api"
	"wnapi/modules/auth/internal"
	"wnapi/modules/auth/repository"
	"wnapi/modules/auth/service"
	notificationService "wnapi/modules/notification/service"
)

// NewAuthConfig creates auth configuration
func NewAuthConfig(cfg config.Config) *internal.AuthConfig {
	return &internal.AuthConfig{
		JWTSecret:                cfg.GetString("JWT_ACCESS_SIGNING_KEY"),
		JWTExpirationHours:       cfg.GetIntWithDefault("JWT_ACCESS_TOKEN_EXPIRATION_HOURS", 24),
		RefreshTokenExpirationDays: cfg.GetIntWithDefault("JWT_REFRESH_TOKEN_EXPIRATION_DAYS", 30),
		MaxConcurrentSessions:    cfg.GetIntWithDefault("AUTH_MAX_CONCURRENT_SESSIONS", 10),
		PasswordResetTokenExpiry: cfg.GetIntWithDefault("AUTH_PASSWORD_RESET_TOKEN_EXPIRY_HOURS", 1),
		EmailVerificationExpiry:  cfg.GetIntWithDefault("AUTH_EMAIL_VERIFICATION_EXPIRY_HOURS", 24),
	}
}

// NewAuthService creates auth service with dependencies
func NewAuthService(
	repo internal.Repository,
	config *internal.AuthConfig,
	log logger.Logger,
	emailService *notificationService.EmailService,
	emailVerificationRepo repository.EmailVerificationRepository,
	cfg config.Config,
	publisher *events.Publisher,
) internal.AuthService {
	webURL := cfg.GetString("WEB_URL")
	
	authService := service.NewServiceWithNotification(
		repo,
		*config,
		log,
		emailService,
		emailVerificationRepo,
		webURL,
	)
	
	// Set publisher if available
	if concreteService, ok := authService.(*service.Service); ok {
		concreteService.SetPublisher(publisher)
	}
	
	return authService
}

// NewUserService creates user service with dependencies
func NewUserService(
	repo internal.Repository,
	config *internal.AuthConfig,
	log logger.Logger,
	publisher *events.Publisher,
) service.UserService {
	return service.NewUserServiceWithPublisher(repo, *config, log, publisher)
}

// NewPasswordResetService creates password reset service
func NewPasswordResetService(
	repo repository.PasswordResetRepository,
	emailService *notificationService.EmailService,
	log logger.Logger,
	cfg config.Config,
) *service.PasswordResetService {
	webURL := cfg.GetString("WEB_URL")
	return service.NewPasswordResetService(repo, emailService, log, webURL)
}

// NewAuthHandler creates auth API handler with dependencies
func NewAuthHandler(
	authService internal.AuthService,
	userService service.UserService,
	passwordResetService *service.PasswordResetService,
	mwFactory *permission.MiddlewareFactory,
	jwtService *auth.JWTService,
	log logger.Logger,
	db *sqlx.DB,
	gormDB *gorm.DB,
) *api.Handler {
	return api.NewHandlerWithDependencies(
		authService,
		userService,
		passwordResetService,
		mwFactory,
		jwtService,
		log,
		db,
		gormDB,
	)
}
```

### Step 3: Create Route Registration

**File: `modules/auth/routes.go`**
```go
package auth

import (
	"github.com/gin-gonic/gin"
	"wnapi/modules/auth/api"
	"wnapi/internal/pkg/logger"
)

// RegisterAuthRoutes registers auth routes with Gin engine
func RegisterAuthRoutes(engine *gin.Engine, handler *api.Handler, log logger.Logger) {
	log.Info("Registering auth routes")
	
	// Use existing route registration logic
	if err := handler.RegisterRoutes(engine); err != nil {
		log.Error("Failed to register auth routes", "error", err)
		return
	}
	
	log.Info("Auth routes registered successfully")
}

// printRoutes logs all registered routes for debugging
func printRoutes(engine *gin.Engine, log logger.Logger) {
	routes := engine.Routes()
	log.Info("Auth module routes registered", "count", len(routes))
	
	for _, route := range routes {
		log.Debug("Route registered", 
			"method", route.Method,
			"path", route.Path,
			"handler", route.Handler,
		)
	}
}
```

### Step 4: Update Auth API Handler

**File: `modules/auth/api/routes.go` (update)**
```go
// Update NewHandlerWithDependencies to work with fx
func NewHandlerWithDependencies(
	authService internal.AuthService,
	userService service.UserService,
	passwordResetService *service.PasswordResetService,
	mwFactory *permission.MiddlewareFactory,
	jwtService *auth.JWTService,
	log logger.Logger,
	db *sqlx.DB,
	gormDB *gorm.DB,
) *Handler {
	// Create sub-handlers
	adminAuthHandler := handlers.NewAdminAuthHandler(authService, userService)
	adminPasswordResetHandler := handlers.NewAdminPasswordResetHandler(passwordResetService)
	adminEmailVerificationHandler := handlers.NewAdminEmailVerificationHandler(authService)
	adminUserHandler := handlers.NewAdminUserHandler(userService)

	return &Handler{
		adminAuthHandler:              adminAuthHandler,
		adminPasswordResetHandler:     adminPasswordResetHandler,
		adminEmailVerificationHandler: adminEmailVerificationHandler,
		adminUserHandler:              adminUserHandler,
		middlewareFactory:             mwFactory,
		jwtService:                    jwtService,
		logger:                        log,
		db:                            db,
		gormDB:                        gormDB,
	}
}

// RegisterRoutes registers auth routes with Gin engine
func (h *Handler) RegisterRoutes(engine *gin.Engine) error {
	h.logger.Info("Setting up auth routes")

	// Create API group
	apiGroup := engine.Group("/api/v1/auth")

	// Public routes (no authentication required)
	h.setupPublicRoutes(apiGroup)

	// Protected routes (authentication required)
	h.setupProtectedRoutes(apiGroup)

	// Admin routes (authentication + admin permissions required)
	h.setupAdminRoutes(apiGroup)

	h.logger.Info("Auth routes setup completed")
	return nil
}

// Add printRoutes function for consistency
func (h *Handler) printRoutes() {
	h.logger.Info("Auth module routes registered")
	// Implementation can be added if needed for debugging
}
```

### Step 5: Update Auth Service

**File: `modules/auth/service/auth_service.go` (update)**
```go
// Update NewServiceWithNotification to work with fx
func NewServiceWithNotification(
	repo internal.Repository,
	config internal.AuthConfig,
	log logger.Logger,
	emailService *notificationService.EmailService,
	emailVerificationRepo EmailVerificationRepository,
	webURL string,
) internal.AuthService {
	s := &Service{
		repo:                  repo,
		config:                config,
		logger:                log,
		emailService:          emailService,
		emailVerificationRepo: emailVerificationRepo,
		webURL:                webURL,
	}
	
	return s
}

// SetPublisher sets event publisher for the service
func (s *Service) SetPublisher(publisher *events.Publisher) {
	s.publisher = publisher
}

// SetMultiTenantServices sets tenant and RBAC services
func (s *Service) SetMultiTenantServices(tenantService, roleService, userRoleService interface{}) {
	s.tenantService = tenantService
	s.roleService = roleService
	s.userRoleService = userRoleService
}
```

### Step 6: Create Auth Module Tests

**File: `modules/auth/fx_test.go`**
```go
package auth

import (
	"testing"
	
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"
	"wnapi/internal/fx/providers"
)

func TestAuthModule(t *testing.T) {
	authModule := &AuthModule{}
	
	// Test module interface
	if authModule.Name() != "auth" {
		t.Errorf("Expected module name 'auth', got '%s'", authModule.Name())
	}
	
	if authModule.Priority() != 10 {
		t.Errorf("Expected priority 10, got %d", authModule.Priority())
	}
	
	deps := authModule.Dependencies()
	expectedDeps := []string{"tenant", "notification"}
	if len(deps) != len(expectedDeps) {
		t.Errorf("Expected %d dependencies, got %d", len(expectedDeps), len(deps))
	}
}

func TestAuthModuleIntegration(t *testing.T) {
	app := fxtest.New(t,
		// Core providers
		fx.Provide(
			providers.NewConfig,
			providers.NewLogger,
			providers.NewAppCache,
		),
		
		// Auth module
		authModule.Module(),
		
		// Test that auth services are available
		fx.Invoke(func(
			authService internal.AuthService,
			userService service.UserService,
		) {
			if authService == nil {
				t.Error("Auth service not provided")
			}
			if userService == nil {
				t.Error("User service not provided")
			}
		}),
	)
	
	app.RequireStart()
	app.RequireStop()
}
```

### Step 7: Update Module Registration

**File: `modules/auth/module.go` (comment out old registration)**
```go
// Comment out old module registration
// func init() {
// 	core.RegisterModuleFactory("auth", NewModule)
// }

// Add note about fx migration
// NOTE: This module has been migrated to fx.Module
// See fx.go for the new implementation
```

## Acceptance Criteria

### Functional Requirements
- [ ] Auth module implements FXModule interface
- [ ] All auth services available through fx DI
- [ ] Auth routes registered correctly
- [ ] Multi-tenant functionality preserved
- [ ] Service interfaces maintained

### Technical Requirements
- [ ] `make build` passes without errors
- [ ] Auth module loads in correct order
- [ ] Dependency injection working
- [ ] No breaking changes to auth API
- [ ] Memory efficient implementation

### Code Quality
- [ ] All providers documented
- [ ] Error handling preserved
- [ ] Test coverage maintained
- [ ] Logging integration working
- [ ] Performance not degraded

## File Paths

### New Files
- `modules/auth/fx.go`
- `modules/auth/providers.go`
- `modules/auth/routes.go`
- `modules/auth/fx_test.go`

### Modified Files
- `modules/auth/api/routes.go` (update handler creation)
- `modules/auth/service/auth_service.go` (update service creation)
- `modules/auth/module.go` (comment out old registration)

## Dependencies
- Task 03: Module System Migration
- Notification module (for email service)
- Tenant module (for multi-tenant support)

## Estimated Time
2 hours

## Notes
- Maintain backward compatibility
- Don't remove old module.go yet
- Focus on fx integration
- Test thoroughly with existing auth functionality
