# Task 03: Auth Data Models and JSON Files

## Objective
Tạo JSON data files cho auth module với support cho multiple environments, đảm bảo multi-tenant isolation và data consistency.

## Input
- Auth seed structure từ Task 02
- Auth models từ modules/auth/models/
- RBAC models từ modules/rbac/models/
- Environment specifications từ docs/seed.md

## Output
- JSON data files cho tất cả environments
- Environment-specific data variations
- Metadata và dependency specifications
- Data validation schemas

## Requirements
- Support dev, staging, prod environments
- Multi-tenant data isolation
- INT UNSIGNED cho tất cả ID fields
- Realistic development data
- Minimal production data
- Proper dependency ordering
- Password hashing cho user data

## Implementation Steps

### 1. Create Tenant Data Files (20 phút)
```
modules/auth/seeds/data/
├── tenants.json              # Default tenant data
├── tenants.dev.json          # Development-specific tenants
├── tenants.staging.json      # Staging-specific tenants
└── tenants.prod.json         # Production-specific tenants
```

### 2. Create User Data Files (30 phút)
```
modules/auth/seeds/data/
├── users.json                # Default user data
├── users.dev.json            # Development users (admin, test users)
├── users.staging.json        # Staging users (limited set)
└── users.prod.json           # Production users (system accounts only)
```

### 3. Create Role Data Files (20 phút)
```
modules/auth/seeds/data/
├── roles.json                # Default roles
├── roles.dev.json            # Development roles
├── roles.staging.json        # Staging roles
└── roles.prod.json           # Production roles
```

### 4. Create Permission Data Files (30 phút)
```
modules/auth/seeds/data/
├── permissions.json          # Default permissions
├── permissions.dev.json      # Development permissions
├── permissions.staging.json  # Staging permissions
└── permissions.prod.json     # Production permissions
```

### 5. Create User-Role Mapping Files (15 phút)
```
modules/auth/seeds/data/
├── user_roles.json           # Default user-role mappings
├── user_roles.dev.json       # Development mappings
├── user_roles.staging.json   # Staging mappings
└── user_roles.prod.json      # Production mappings
```

### 6. Create Role-Permission Mapping Files (15 phút)
```
modules/auth/seeds/data/
├── role_permissions.json     # Default role-permission mappings
├── role_permissions.dev.json # Development mappings
├── role_permissions.staging.json # Staging mappings
└── role_permissions.prod.json # Production mappings
```

## Data Specifications

### Tenant Data Structure
```json
{
  "environment": "dev",
  "metadata": {
    "version": "1.0.0",
    "description": "Default tenant data for development",
    "dependencies": [],
    "tags": ["tenants", "auth", "dev"]
  },
  "data": [
    {
      "id": 1,
      "name": "Default Tenant",
      "slug": "default",
      "description": "Default tenant for development",
      "active": true,
      "contact_email": "<EMAIL>",
      "contact_phone": "+1234567890"
    }
  ]
}
```

### User Data Structure
```json
{
  "environment": "dev",
  "metadata": {
    "version": "1.0.0",
    "description": "Development users with various roles",
    "dependencies": ["tenants", "roles"],
    "tags": ["users", "auth", "dev"]
  },
  "data": [
    {
      "user_id": 1,
      "tenant_id": 1,
      "username": "admin",
      "email": "<EMAIL>",
      "password": "admin123",
      "full_name": "System Administrator",
      "status": "active",
      "is_email_verified": true,
      "user_type": "admin"
    }
  ]
}
```

## Acceptance Criteria
- [ ] Tất cả JSON files được tạo cho 3 environments
- [ ] Data structure tuân thủ existing models
- [ ] INT UNSIGNED được sử dụng cho tất cả ID fields
- [ ] Multi-tenant isolation được đảm bảo
- [ ] Dependencies được specify đúng trong metadata
- [ ] Password được hash cho user data
- [ ] Development data realistic và đa dạng
- [ ] Production data minimal và secure
- [ ] JSON syntax valid và well-formatted

## File Paths
- `modules/auth/seeds/data/tenants*.json`
- `modules/auth/seeds/data/users*.json`
- `modules/auth/seeds/data/roles*.json`
- `modules/auth/seeds/data/permissions*.json`
- `modules/auth/seeds/data/user_roles*.json`
- `modules/auth/seeds/data/role_permissions*.json`

## Dependencies
- Task 02: Auth Module Seed Structure

## Estimated Time
2 hours

## Notes
- Focus on data quality và consistency
- Ensure proper tenant isolation
- Consider realistic scenarios cho development
- Security considerations cho production data
