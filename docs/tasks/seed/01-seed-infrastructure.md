# Task 01: Seed System Infrastructure

## Objective
Tạo hạ tầng cốt lõi cho hệ thống seed database, bao gồm interfaces, configuration, utilities và registry pattern theo thiết kế trong docs/seed.md.

## Input
- Thiết kế từ docs/seed.md
- Patterns từ modules/auth và modules/rbac
- Error handling patterns từ internal/pkg/errors
- Multi-tenant architecture patterns

## Output
- Core seed interfaces và contracts
- Configuration management system
- Data loading utilities
- Database batch operation utilities
- Module seed registry system

## Requirements
- Tuân thủ multi-tenant architecture với tenant ID parameters
- Sử dụng GORM cho database operations
- Sử dụng internal/pkg/errors cho error handling
- Support environment-aware configuration (dev, staging, prod)
- Batch processing cho performance
- Dependency management giữa các seeders

## Implementation Steps

### 1. Tạo Core Interfaces (30 phút)
```
internal/pkg/seed/
├── interface.go          # Seeder và ModuleSeed interfaces
├── config.go            # Configuration structures
├── loader.go            # Data loading utilities
├── database.go          # Database batch operations
└── registry.go          # Module seed registry
```

### 2. Implement Seeder Interface (15 phút)
- <PERSON><PERSON><PERSON> nghĩa Seeder interface với methods: Name(), Dependencies(), Run(), Rollback(), Description()
- Định nghĩa ModuleSeed interface với methods: ModuleName(), GetSeeders(), SeedAll(), SeedSpecific()

### 3. Implement Configuration System (20 phút)
- Config struct với environment, data_path, batch_size, skip_exists, modules
- SeedData struct với environment, data, metadata
- Metadata struct với version, created_at, description, dependencies, tags
- Environment variable support

### 4. Implement Data Loading Utilities (20 phút)
- LoadSeedData function cho loading JSON files theo environment
- LoadDataWithFallback function với fallback mechanism
- ValidateSeedData function cho validation
- ParseTemplateData function cho template support

### 5. Implement Database Utilities (20 phút)
- BatchInsert function với GORM batch processing
- UpsertData function cho insert/update operations
- CheckDataExists function cho existence checking
- Transaction support cho data consistency

### 6. Implement Registry System (15 phút)
- Global registry cho module seeds
- RegisterModuleSeed function
- GetModuleSeed và GetAllModuleSeeds functions
- ListAvailableSeeds function

## Acceptance Criteria
- [ ] All interfaces được định nghĩa đúng theo docs/seed.md
- [ ] Configuration system support environment variables
- [ ] Data loading utilities handle JSON files với metadata
- [ ] Database utilities support GORM batch operations
- [ ] Registry system cho phép đăng ký và discovery module seeds
- [ ] Error handling sử dụng internal/pkg/errors patterns
- [ ] Code tuân thủ existing codebase patterns
- [ ] Build thành công với `make build`

## File Paths
- `internal/pkg/seed/interface.go`
- `internal/pkg/seed/config.go`
- `internal/pkg/seed/loader.go`
- `internal/pkg/seed/database.go`
- `internal/pkg/seed/registry.go`

## Dependencies
- None (base infrastructure task)

## Estimated Time
2 hours

## Notes
- Đây là foundation task cho toàn bộ seed system
- Cần đảm bảo interfaces đủ linh hoạt cho các modules khác nhau
- Performance considerations cho large datasets
- Multi-tenant isolation phải được đảm bảo ở tầng utilities
