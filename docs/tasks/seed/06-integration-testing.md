# Task 06: Integration and Testing

## Objective
Integrate seed system với existing application, add verification mechanisms, create tests và ensure system reliability.

## Input
- Complete seed command từ Task 05
- All auth seeders từ Task 04
- Existing auth/rbac modules
- Application bootstrap process

## Output
- Integration với application startup
- Verification scripts và tools
- Unit tests cho seed components
- Integration tests cho full workflow
- Documentation và examples

## Requirements
- Seamless integration với existing modules
- Comprehensive verification mechanisms
- Automated testing coverage
- Performance benchmarks
- Documentation cho usage và maintenance

## Implementation Steps

### 1. Application Integration (30 phút)
- Register auth seed với application
- Integration với module loading process
- Configuration integration
- Database migration coordination
- Error handling integration

### 2. Create Verification Tools (30 phút)
```
scripts/
├── verify-seed.sh              # Verification script
└── seed-health-check.sh        # Health check script
```

### 3. Implement Unit Tests (45 phút)
```
internal/pkg/seed/
├── interface_test.go           # Interface tests
├── config_test.go              # Configuration tests
├── loader_test.go              # Data loading tests
├── database_test.go            # Database utility tests
└── registry_test.go            # Registry tests

modules/auth/seeds/
├── seed_test.go                # Auth seed tests
└── seeders/
    ├── tenant_seeder_test.go   # Tenant seeder tests
    ├── user_seeder_test.go     # User seeder tests
    ├── role_seeder_test.go     # Role seeder tests
    └── permission_seeder_test.go # Permission seeder tests
```

### 4. Implement Integration Tests (30 phút)
```
cmd/seed/
├── integration_test.go         # Full workflow tests
└── commands/
    ├── all_test.go             # All command tests
    ├── module_test.go          # Module command tests
    └── rollback_test.go        # Rollback tests
```

### 5. Performance Testing (20 phút)
- Benchmark tests cho large datasets
- Memory usage profiling
- Database performance testing
- Batch size optimization

### 6. Create Documentation (25 phút)
```
docs/
├── seed-usage.md               # Usage documentation
├── seed-development.md         # Development guide
└── seed-troubleshooting.md     # Troubleshooting guide
```

## Verification Mechanisms

### Data Integrity Checks
```bash
# Verify tenant data
./scripts/verify-seed.sh --check=tenants --env=dev

# Verify user data
./scripts/verify-seed.sh --check=users --env=dev

# Verify role assignments
./scripts/verify-seed.sh --check=roles --env=dev

# Full verification
./scripts/verify-seed.sh --check=all --env=dev
```

### Health Check Script
```bash
# Check seed system health
./scripts/seed-health-check.sh

# Check specific module
./scripts/seed-health-check.sh --module=auth

# Check data consistency
./scripts/seed-health-check.sh --consistency
```

## Test Coverage Requirements

### Unit Tests
- All seed interfaces và implementations
- Configuration loading và validation
- Data loading utilities
- Database operations
- Registry functionality

### Integration Tests
- Full seeding workflow
- Environment switching
- Error handling scenarios
- Rollback operations
- Multi-tenant isolation

### Performance Tests
- Large dataset handling
- Memory usage optimization
- Database performance
- Concurrent operations

## Makefile Integration

### New Targets
```makefile
# Seed targets
.PHONY: build-seed seed-dev seed-staging seed-prod
build-seed:
	go build -o bin/seed cmd/seed/main.go

seed-dev:
	./bin/seed all --env=dev

seed-staging:
	./bin/seed all --env=staging

seed-prod:
	./bin/seed all --env=prod

# Test targets
.PHONY: test-seed test-seed-integration
test-seed:
	go test ./internal/pkg/seed/... ./modules/auth/seeds/...

test-seed-integration:
	go test ./cmd/seed/...

# Verification targets
.PHONY: verify-seed seed-health-check
verify-seed:
	./scripts/verify-seed.sh --check=all --env=dev

seed-health-check:
	./scripts/seed-health-check.sh
```

## Acceptance Criteria
- [ ] Auth seed được register với application successfully
- [ ] Verification scripts hoạt động correctly
- [ ] Unit tests cover all major components
- [ ] Integration tests cover full workflows
- [ ] Performance tests show acceptable performance
- [ ] Documentation comprehensive và accurate
- [ ] Makefile targets hoạt động correctly
- [ ] Error scenarios được handle gracefully
- [ ] Multi-tenant isolation verified
- [ ] Data consistency checks pass
- [ ] Build và test thành công với `make build && make test`

## File Paths
- `scripts/verify-seed.sh`
- `scripts/seed-health-check.sh`
- `internal/pkg/seed/*_test.go`
- `modules/auth/seeds/*_test.go`
- `cmd/seed/*_test.go`
- `docs/seed-usage.md`
- `docs/seed-development.md`
- `docs/seed-troubleshooting.md`

## Dependencies
- Task 05: Seed Command Line Interface

## Estimated Time
3 hours

## Notes
- Focus on reliability và maintainability
- Comprehensive error scenario testing
- Performance optimization cho production use
- Clear documentation cho future maintenance
