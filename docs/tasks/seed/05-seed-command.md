# Task 05: Seed Command Line Interface

## Objective
Tạo command line interface cho seed system, bao gồm entry point, runner logic và các commands cho different operations.

## Input
- Complete auth seeders từ Task 04
- Core seed infrastructure từ Task 01
- Command specifications từ docs/seed.md

## Output
- Seed command entry point
- Runner logic với execution management
- Command handlers cho different operations
- Shell scripts cho easy execution
- Makefile integration

## Requirements
- Support seeding all modules hoặc specific modules
- Environment selection (dev, staging, prod)
- Dry run mode cho testing
- Progress reporting cho large datasets
- Proper error handling và logging
- Rollback support

## Implementation Steps

### 1. Create Command Structure (20 phút)
```
cmd/seed/
├── main.go                     # Entry point
├── runner.go                   # Seed runner logic
└── commands/                   # Command handlers
    ├── all.go                  # Seed all modules
    ├── module.go               # Seed specific module
    ├── list.go                 # List available seeds
    └── rollback.go             # Rollback operations
```

### 2. Implement Main Entry Point (15 phút)
- Command line argument parsing
- Environment configuration
- Logger initialization
- Database connection setup
- Command routing

### 3. Implement Seed Runner (30 phút)
- Module discovery và loading
- Dependency resolution
- Execution ordering
- Progress tracking
- Error handling và recovery
- Transaction management

### 4. Implement Command Handlers (45 phút)
- AllCommand: seed tất cả modules
- ModuleCommand: seed specific module/seeder
- ListCommand: list available seeds
- RollbackCommand: rollback seed data
- Dry run support cho tất cả commands

### 5. Create Shell Scripts (20 phút)
```
scripts/
├── seed.sh                     # Main seed script
└── seed-examples.sh            # Usage examples
```

### 6. Add Makefile Integration (10 phút)
- Add seed targets to Makefile
- Environment-specific targets
- Build và run targets

## Command Interface Specifications

### Basic Commands
```bash
# Build seed command
make build-seed

# Seed all modules
./bin/seed all --env=dev

# Seed specific module
./bin/seed module --name=auth --env=dev

# Seed specific seeder
./bin/seed module --name=auth --seeder=users --env=dev

# List available seeds
./bin/seed list

# Rollback seed data
./bin/seed rollback --module=auth --seeder=users

# Dry run mode
./bin/seed all --env=dev --dry-run
```

### Advanced Options
```bash
# Custom batch size
./bin/seed all --env=dev --batch-size=100

# Skip existing data
./bin/seed all --env=dev --skip-exists

# Verbose logging
./bin/seed all --env=dev --verbose

# Custom data path
./bin/seed all --env=dev --data-path=/custom/path
```

## Implementation Details

### Runner Logic
```go
type Runner struct {
    db       *gorm.DB
    logger   logger.Logger
    config   seed.Config
    registry map[string]seed.ModuleSeed
}

func (r *Runner) SeedAll(ctx context.Context) error
func (r *Runner) SeedModule(ctx context.Context, moduleName string) error
func (r *Runner) SeedSpecific(ctx context.Context, moduleName, seederName string) error
func (r *Runner) ListSeeds() map[string][]string
```

### Progress Reporting
- Progress bars cho large datasets
- ETA calculations
- Success/failure statistics
- Detailed logging

## Acceptance Criteria
- [ ] Command line interface hoạt động với all specified options
- [ ] Environment selection hoạt động correctly
- [ ] Module và seeder selection hoạt động
- [ ] Dry run mode hoạt động without making changes
- [ ] Progress reporting informative và accurate
- [ ] Error handling comprehensive với helpful messages
- [ ] Rollback functionality hoạt động correctly
- [ ] Shell scripts provide easy usage
- [ ] Makefile integration hoạt động
- [ ] Build thành công với `make build`

## File Paths
- `cmd/seed/main.go`
- `cmd/seed/runner.go`
- `cmd/seed/commands/all.go`
- `cmd/seed/commands/module.go`
- `cmd/seed/commands/list.go`
- `cmd/seed/commands/rollback.go`
- `scripts/seed.sh`
- `scripts/seed-examples.sh`

## Dependencies
- Task 04: Auth Seeders Implementation

## Estimated Time
2 hours

## Notes
- Focus on user experience và ease of use
- Comprehensive help text và examples
- Robust error handling với recovery options
- Performance considerations cho large datasets
