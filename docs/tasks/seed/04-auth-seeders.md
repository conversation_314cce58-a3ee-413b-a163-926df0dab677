# Task 04: Auth Seeders Implementation

## Objective
Implement full seeding logic cho auth module seeders, bao gồm data loading, validation, batch processing và error handling.

## Input
- Auth data models từ Task 03
- Auth seed structure từ Task 02
- Core seed infrastructure từ Task 01
- Existing auth/rbac repository patterns

## Output
- Complete seeder implementations
- Data loading và validation logic
- Batch processing với GORM
- Error handling và rollback support
- Multi-tenant data isolation

## Requirements
- Sử dụng GORM cho database operations
- Batch processing cho performance
- Multi-tenant isolation với tenant ID
- Proper error handling với internal/pkg/errors
- Rollback support cho data cleanup
- Idempotent operations (có thể chạy nhiều lần)

## Implementation Steps

### 1. Implement TenantSeeder (30 phút)
- Load tenant data từ JSON files
- Validate tenant data structure
- Check existing tenants để avoid duplicates
- Batch insert với GORM
- Error handling và logging

### 2. Implement UserSeeder (45 phút)
- Load user data từ JSON files
- Password hashing với bcrypt
- Tenant validation và association
- Email uniqueness checking
- Batch insert với proper error handling
- User profile creation nếu cần

### 3. Implement RoleSeeder (30 phút)
- Load role data từ JSON files
- Tenant-specific role creation
- Role code uniqueness checking
- Integration với rbac repository
- Batch processing với dependencies

### 4. Implement PermissionSeeder (30 phút)
- Load permission data từ JSON files
- Permission code uniqueness checking
- Permission group association
- Global vs tenant-specific permissions
- Batch insert với validation

### 5. Implement UserRoleSeeder (20 phút)
- Load user-role mapping data
- Validate user và role existence
- Tenant isolation checking
- Batch assignment với proper error handling

### 6. Implement RolePermissionSeeder (20 phút)
- Load role-permission mapping data
- Validate role và permission existence
- Batch assignment với dependencies
- Error handling cho invalid mappings

### 7. Add Rollback Support (15 phút)
- Implement rollback methods cho tất cả seeders
- Data cleanup logic
- Transaction support
- Proper error handling trong rollback

## Implementation Details

### Base Seeder Pattern
```go
type BaseSeeder struct {
    repo   Repository
    logger logger.Logger
    config seed.Config
}

func (s *BaseSeeder) loadData(dataPath, environment, fileName string) (*seed.SeedData, error)
func (s *BaseSeeder) validateData(data interface{}) error
func (s *BaseSeeder) batchInsert(ctx context.Context, data []interface{}) error
```

### TenantSeeder Implementation
```go
type TenantSeeder struct {
    BaseSeeder
    tenantRepo TenantRepository
}

func (s *TenantSeeder) Run(ctx context.Context) error
func (s *TenantSeeder) Rollback(ctx context.Context) error
```

### Multi-tenant Context Handling
- Extract tenant ID từ context hoặc data
- Validate tenant existence
- Ensure data isolation
- Proper error messages với tenant context

## Acceptance Criteria
- [ ] Tất cả seeders implement Seeder interface correctly
- [ ] Data loading từ JSON files hoạt động với all environments
- [ ] Password hashing được implement cho users
- [ ] Multi-tenant isolation được enforce
- [ ] Batch processing hoạt động với GORM
- [ ] Error handling sử dụng internal/pkg/errors patterns
- [ ] Rollback functionality hoạt động correctly
- [ ] Idempotent operations (có thể chạy nhiều lần)
- [ ] Dependencies được respect trong execution order
- [ ] Logging comprehensive và helpful
- [ ] Build thành công với `make build`

## File Paths
- `modules/auth/seeds/seeders/tenant_seeder.go`
- `modules/auth/seeds/seeders/user_seeder.go`
- `modules/auth/seeds/seeders/role_seeder.go`
- `modules/auth/seeds/seeders/permission_seeder.go`
- `modules/auth/seeds/seeders/user_role_seeder.go`
- `modules/auth/seeds/seeders/role_permission_seeder.go`

## Dependencies
- Task 03: Auth Data Models and JSON Files

## Estimated Time
3 hours

## Notes
- Focus on robust error handling và data validation
- Ensure performance với large datasets
- Consider transaction boundaries cho data consistency
- Test với different environments
