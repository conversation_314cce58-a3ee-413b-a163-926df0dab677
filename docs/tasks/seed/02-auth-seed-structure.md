# Task 02: Auth Module Seed Structure

## Objective
T<PERSON><PERSON> cấu trúc seed cho auth module theo patterns đã thiết lập, bao gồm directory structure, base seeders và module registration.

## Input
- Core seed infrastructure từ Task 01
- Auth module structure từ modules/auth/
- RBAC module patterns từ modules/rbac/
- Multi-tenant architecture patterns

## Output
- Auth module seed directory structure
- AuthSeed module implementation
- Base seeder structures
- Module registration system

## Requirements
- Tuân thủ directory structure từ docs/seed.md
- <PERSON><PERSON> dụ<PERSON> established auth module patterns
- Multi-tenant support với tenant ID parameters
- Integration với core seed infrastructure
- Dependency management cho auth seeders

## Implementation Steps

### 1. Tạo Auth Seed Directory Structure (15 phút)
```
modules/auth/seeds/
├── seed.go                      # AuthSeed module implementation
├── data/                        # JSON data files
│   ├── tenants.json            # Tenant data
│   ├── users.json              # User data
│   ├── roles.json              # Role data
│   └── permissions.json        # Permission data
└── seeders/                     # Individual seeders
    ├── tenant_seeder.go        # Tenant seeder
    ├── user_seeder.go          # User seeder
    ├── role_seeder.go          # Role seeder
    └── permission_seeder.go    # Permission seeder
```

### 2. Implement AuthSeed Module (30 phút)
- AuthSeed struct implementing ModuleSeed interface
- Dependencies injection (repository, logger, config)
- Seeder registration và ordering
- SeedAll và SeedSpecific implementations
- Error handling và logging

### 3. Create Base Seeder Structure (20 phút)
- Base seeder struct với common functionality
- Repository access patterns
- Configuration loading
- Data validation helpers
- Multi-tenant context handling

### 4. Implement Individual Seeder Stubs (30 phút)
- TenantSeeder với basic structure
- UserSeeder với auth repository integration
- RoleSeeder với rbac repository integration
- PermissionSeeder với rbac repository integration
- Proper dependency ordering

### 5. Module Registration (15 phút)
- Auto-registration trong auth module init
- Integration với seed registry
- Module discovery support

## Acceptance Criteria
- [ ] Directory structure tuân thủ docs/seed.md specification
- [ ] AuthSeed implements ModuleSeed interface correctly
- [ ] All seeder stubs được tạo với proper structure
- [ ] Module registration hoạt động với seed registry
- [ ] Dependencies được inject đúng cách
- [ ] Multi-tenant context được handle properly
- [ ] Error handling sử dụng internal/pkg/errors
- [ ] Build thành công với `make build`

## File Paths
- `modules/auth/seeds/seed.go`
- `modules/auth/seeds/seeders/tenant_seeder.go`
- `modules/auth/seeds/seeders/user_seeder.go`
- `modules/auth/seeds/seeders/role_seeder.go`
- `modules/auth/seeds/seeders/permission_seeder.go`

## Dependencies
- Task 01: Seed System Infrastructure

## Estimated Time
2 hours

## Notes
- Focus on structure và integration, chưa implement full seeding logic
- Đảm bảo compatibility với existing auth module
- Prepare cho environment-specific data trong Task 03
