# Task 10: Testing Framework

## Objective
Triển khai comprehensive testing framework cho module cron system, bao gồm unit tests, integration tests và CLI testing commands.

## Input
- Complete module implementations từ Tasks 08-09
- Existing testing patterns trong codebase
- Testing utilities và mocks

## Output
- Unit test framework cho cron handlers
- Integration test suite cho module cron system
- CLI testing commands
- Test utilities và helpers
- Automated test execution

## Requirements

### 1. Unit testing framework
- Test individual cron handlers
- Mock dependencies (database, config, services)
- Test error scenarios và edge cases

### 2. Integration testing
- Test complete module cron workflows
- Test auto-discovery và registration
- Test configuration changes và hot-reload

### 3. CLI testing commands
- Commands để test individual jobs
- Dry-run capabilities
- Performance testing tools

## Implementation Steps

### Step 1: Unit test framework setup (30 phút)

**File**: `modules/auth/cron/handlers/session_cleanup_handler_test.go`

```go
package handlers

import (
    "context"
    "testing"
    "time"
    
    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/mock"
    "gorm.io/gorm"
    
    "wnapi/internal/pkg/config"
    "wnapi/internal/pkg/logger"
    "wnapi/internal/pkg/queue/types"
    "wnapi/modules/auth/models"
    "wnapi/test/mocks"
    "wnapi/test/testutils"
)

func TestAuthSessionCleanupHandler_Handle(t *testing.T) {
    tests := []struct {
        name           string
        setupMocks     func(*mocks.MockDB, *mocks.MockConfig)
        expectedResult *types.CronTaskResult
        expectError    bool
    }{
        {
            name: "successful cleanup",
            setupMocks: func(mockDB *mocks.MockDB, mockConfig *mocks.MockConfig) {
                mockConfig.On("GetInt", "AUTH_SESSION_MAX_INACTIVE_HOURS").Return(168)
                
                // Mock expired sessions cleanup
                mockDB.On("Where", mock.AnythingOfType("string"), mock.Anything).
                    Return(mockDB)
                mockDB.On("Delete", mock.AnythingOfType("*models.UserSession")).
                    Return(&gorm.DB{RowsAffected: 5})
                
                // Mock inactive sessions cleanup
                mockDB.On("Where", mock.AnythingOfType("string"), mock.Anything, mock.Anything).
                    Return(mockDB)
                mockDB.On("Delete", mock.AnythingOfType("*models.UserSession")).
                    Return(&gorm.DB{RowsAffected: 3})
                
                // Mock orphaned sessions cleanup
                mockDB.On("Where", "user_id NOT IN (SELECT id FROM users)").
                    Return(mockDB)
                mockDB.On("Delete", mock.AnythingOfType("*models.UserSession")).
                    Return(&gorm.DB{RowsAffected: 2})
                
                // Mock duplicate sessions cleanup
                mockDB.On("Model", mock.AnythingOfType("*models.UserSession")).
                    Return(mockDB)
                mockDB.On("Select", "MAX(id)").Return(mockDB)
                mockDB.On("Group", "user_id").Return(mockDB)
                mockDB.On("Where", "id NOT IN (?)", mock.Anything).
                    Return(mockDB)
                mockDB.On("Delete", mock.AnythingOfType("*models.UserSession")).
                    Return(&gorm.DB{RowsAffected: 1})
            },
            expectedResult: &types.CronTaskResult{
                TaskType:       types.CronTaskAuthSessionCleanup,
                Success:        true,
                ProcessedCount: 11, // 5 + 3 + 2 + 1
                ErrorCount:     0,
            },
            expectError: false,
        },
        {
            name: "database error",
            setupMocks: func(mockDB *mocks.MockDB, mockConfig *mocks.MockConfig) {
                mockConfig.On("GetInt", "AUTH_SESSION_MAX_INACTIVE_HOURS").Return(168)
                
                mockDB.On("Where", mock.AnythingOfType("string"), mock.Anything).
                    Return(mockDB)
                mockDB.On("Delete", mock.AnythingOfType("*models.UserSession")).
                    Return(&gorm.DB{Error: gorm.ErrInvalidDB})
            },
            expectedResult: &types.CronTaskResult{
                TaskType:   types.CronTaskAuthSessionCleanup,
                Success:    false,
                ErrorCount: 1,
            },
            expectError: false,
        },
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            // Setup mocks
            mockDB := &mocks.MockDB{}
            mockConfig := &mocks.MockConfig{}
            mockLogger := &mocks.MockLogger{}
            
            tt.setupMocks(mockDB, mockConfig)
            
            // Create handler
            handler := NewAuthSessionCleanupHandler(mockDB, mockConfig, mockLogger)
            
            // Create test payload
            payload := &types.AuthSessionCleanupPayload{
                TaskType:   types.CronTaskAuthSessionCleanup,
                ExecutedAt: time.Now(),
            }
            
            // Execute handler
            result, err := handler.Handle(context.Background(), payload)
            
            // Assertions
            if tt.expectError {
                assert.Error(t, err)
            } else {
                assert.NoError(t, err)
                assert.NotNil(t, result)
                assert.Equal(t, tt.expectedResult.TaskType, result.TaskType)
                assert.Equal(t, tt.expectedResult.Success, result.Success)
                assert.Equal(t, tt.expectedResult.ProcessedCount, result.ProcessedCount)
                assert.Equal(t, tt.expectedResult.ErrorCount, result.ErrorCount)
            }
            
            // Verify mocks
            mockDB.AssertExpectations(t)
            mockConfig.AssertExpectations(t)
        })
    }
}

func TestAuthSessionCleanupHandler_GetTaskType(t *testing.T) {
    handler := &AuthSessionCleanupHandler{}
    assert.Equal(t, types.CronTaskAuthSessionCleanup, handler.GetTaskType())
}

func TestAuthSessionCleanupHandler_GetDescription(t *testing.T) {
    handler := &AuthSessionCleanupHandler{}
    description := handler.GetDescription()
    assert.NotEmpty(t, description)
    assert.Contains(t, description, "session")
}
```

### Step 2: Integration test suite (45 phút)

**File**: `test/integration/cron_module_test.go`

```go
package integration

import (
    "context"
    "testing"
    "time"
    
    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/suite"
    
    "wnapi/internal/pkg/config"
    "wnapi/internal/pkg/logger"
    "wnapi/internal/pkg/module"
    "wnapi/internal/pkg/queue/cron"
    "wnapi/modules/auth/models"
    authCron "wnapi/modules/auth/cron"
    "wnapi/test/testutils"
)

type CronModuleTestSuite struct {
    suite.Suite
    db            *gorm.DB
    config        config.Config
    logger        logger.Logger
    cronManager   *cron.Manager
    moduleManager module.ModuleCronManager
}

func (suite *CronModuleTestSuite) SetupSuite() {
    // Setup test database
    suite.db = testutils.SetupTestDB()
    
    // Setup test config
    suite.config = testutils.SetupTestConfig()
    
    // Setup logger
    suite.logger = testutils.SetupTestLogger()
    
    // Setup cron manager
    var err error
    suite.cronManager, err = cron.NewManager(suite.db, suite.config, suite.logger, nil)
    suite.Require().NoError(err)
    
    // Setup module manager
    suite.moduleManager = cron.NewModuleCronManager(
        suite.cronManager,
        suite.cronManager.GetHandlerRegistry(),
        suite.logger,
    )
}

func (suite *CronModuleTestSuite) TearDownSuite() {
    testutils.CleanupTestDB(suite.db)
}

func (suite *CronModuleTestSuite) SetupTest() {
    // Clean up test data before each test
    testutils.CleanupTestData(suite.db)
}

func (suite *CronModuleTestSuite) TestModuleRegistration() {
    // Create auth module registrar
    authRegistrar := authCron.NewAuthCronRegistrar(suite.db, suite.config, suite.logger)
    
    // Test module registration
    err := suite.moduleManager.RegisterModule(authRegistrar)
    suite.NoError(err)
    
    // Verify module is registered
    modules := suite.moduleManager.GetRegisteredModules()
    suite.Contains(modules, "auth")
    
    // Verify jobs are registered
    jobs, err := suite.moduleManager.GetModuleJobs("auth")
    suite.NoError(err)
    suite.NotEmpty(jobs)
    
    // Check specific jobs
    jobIDs := make([]string, len(jobs))
    for i, job := range jobs {
        jobIDs[i] = job.ID
    }
    
    suite.Contains(jobIDs, "auth_session_cleanup")
    suite.Contains(jobIDs, "auth_password_expiry")
    suite.Contains(jobIDs, "auth_token_cleanup")
}

func (suite *CronModuleTestSuite) TestJobExecution() {
    // Setup test data
    suite.setupTestSessionData()
    
    // Register auth module
    authRegistrar := authCron.NewAuthCronRegistrar(suite.db, suite.config, suite.logger)
    err := suite.moduleManager.RegisterModule(authRegistrar)
    suite.NoError(err)
    
    // Get session cleanup job
    jobs, err := suite.moduleManager.GetModuleJobs("auth")
    suite.NoError(err)
    
    var sessionCleanupJob *module.ModuleCronJobDefinition
    for _, job := range jobs {
        if job.ID == "auth_session_cleanup" {
            sessionCleanupJob = job
            break
        }
    }
    suite.NotNil(sessionCleanupJob)
    
    // Execute job manually
    handler, err := suite.cronManager.GetHandlerRegistry().GetHandler(sessionCleanupJob.TaskType)
    suite.NoError(err)
    
    payload := &types.AuthSessionCleanupPayload{
        TaskType:   types.CronTaskAuthSessionCleanup,
        ExecutedAt: time.Now(),
    }
    
    result, err := handler.Handle(context.Background(), payload)
    suite.NoError(err)
    suite.NotNil(result)
    suite.True(result.Success)
    suite.Greater(result.ProcessedCount, 0)
}

func (suite *CronModuleTestSuite) TestConfigurationChanges() {
    // Register auth module
    authRegistrar := authCron.NewAuthCronRegistrar(suite.db, suite.config, suite.logger)
    err := suite.moduleManager.RegisterModule(authRegistrar)
    suite.NoError(err)
    
    // Test enabling/disabling module
    err = suite.cronManager.DisableModuleJob("auth_session_cleanup")
    suite.NoError(err)
    
    err = suite.cronManager.EnableModuleJob("auth_session_cleanup")
    suite.NoError(err)
    
    // Test schedule update
    err = suite.cronManager.GetScheduler().UpdateModuleJobSchedule("auth_session_cleanup", "0 3 * * *")
    suite.NoError(err)
}

func (suite *CronModuleTestSuite) TestModuleValidation() {
    // Test invalid module registration
    invalidRegistrar := &invalidModuleRegistrar{}
    err := suite.moduleManager.RegisterModule(invalidRegistrar)
    suite.Error(err)
    
    // Test duplicate module registration
    authRegistrar := authCron.NewAuthCronRegistrar(suite.db, suite.config, suite.logger)
    err = suite.moduleManager.RegisterModule(authRegistrar)
    suite.NoError(err)
    
    // Try to register again
    err = suite.moduleManager.RegisterModule(authRegistrar)
    suite.Error(err)
}

func (suite *CronModuleTestSuite) TestModuleUnregistration() {
    // Register auth module
    authRegistrar := authCron.NewAuthCronRegistrar(suite.db, suite.config, suite.logger)
    err := suite.moduleManager.RegisterModule(authRegistrar)
    suite.NoError(err)
    
    // Verify registration
    suite.True(suite.moduleManager.IsModuleRegistered("auth"))
    
    // Unregister module
    err = suite.moduleManager.UnregisterModule("auth")
    suite.NoError(err)
    
    // Verify unregistration
    suite.False(suite.moduleManager.IsModuleRegistered("auth"))
}

func (suite *CronModuleTestSuite) setupTestSessionData() {
    // Create test users
    user1 := &models.User{
        Email:     "<EMAIL>",
        Name:      "User 1",
        TenantID:  1,
        IsActive:  true,
    }
    suite.db.Create(user1)
    
    user2 := &models.User{
        Email:     "<EMAIL>",
        Name:      "User 2",
        TenantID:  1,
        IsActive:  true,
    }
    suite.db.Create(user2)
    
    // Create expired sessions
    expiredSession := &models.UserSession{
        UserID:    user1.ID,
        Token:     "expired_token",
        ExpiresAt: time.Now().Add(-24 * time.Hour),
        UpdatedAt: time.Now().Add(-25 * time.Hour),
    }
    suite.db.Create(expiredSession)
    
    // Create inactive sessions
    inactiveSession := &models.UserSession{
        UserID:    user2.ID,
        Token:     "inactive_token",
        ExpiresAt: time.Now().Add(24 * time.Hour),
        UpdatedAt: time.Now().Add(-8 * 24 * time.Hour), // 8 days old
    }
    suite.db.Create(inactiveSession)
}

// Invalid registrar for testing validation
type invalidModuleRegistrar struct{}

func (r *invalidModuleRegistrar) GetModuleName() string { return "" }
func (r *invalidModuleRegistrar) IsEnabled() bool { return true }
func (r *invalidModuleRegistrar) RegisterHandlers(registry *cron.HandlerRegistry) error { return nil }
func (r *invalidModuleRegistrar) GetJobDefinitions() ([]*module.ModuleCronJobDefinition, error) { return nil, nil }

func TestCronModuleTestSuite(t *testing.T) {
    suite.Run(t, new(CronModuleTestSuite))
}
```

### Step 3: CLI testing commands (30 phút)

**File**: `cmd/cli/commands/cron_test.go` (extend existing)

```go
// Add test commands to existing cron.go

// testJobCmd tests a specific cron job
var testJobCmd = &cobra.Command{
    Use:   "test [job-id]",
    Short: "Test a specific cron job",
    Args:  cobra.ExactArgs(1),
    RunE: func(cmd *cobra.Command, args []string) error {
        jobID := args[0]
        
        dryRun, _ := cmd.Flags().GetBool("dry-run")
        verbose, _ := cmd.Flags().GetBool("verbose")
        
        return testCronJob(jobID, dryRun, verbose)
    },
}

// testAllCmd tests all cron jobs
var testAllCmd = &cobra.Command{
    Use:   "test-all",
    Short: "Test all cron jobs",
    RunE: func(cmd *cobra.Command, args []string) error {
        dryRun, _ := cmd.Flags().GetBool("dry-run")
        verbose, _ := cmd.Flags().GetBool("verbose")
        parallel, _ := cmd.Flags().GetBool("parallel")
        
        return testAllCronJobs(dryRun, verbose, parallel)
    },
}

// benchmarkJobCmd benchmarks a specific cron job
var benchmarkJobCmd = &cobra.Command{
    Use:   "benchmark [job-id]",
    Short: "Benchmark a specific cron job performance",
    Args:  cobra.ExactArgs(1),
    RunE: func(cmd *cobra.Command, args []string) error {
        jobID := args[0]
        
        iterations, _ := cmd.Flags().GetInt("iterations")
        if iterations == 0 {
            iterations = 10
        }
        
        return benchmarkCronJob(jobID, iterations)
    },
}

func testCronJob(jobID string, dryRun, verbose bool) error {
    fmt.Printf("Testing cron job: %s\n", jobID)
    
    if dryRun {
        fmt.Println("DRY RUN MODE - No actual execution")
    }
    
    manager, err := initializeCronManager()
    if err != nil {
        return fmt.Errorf("failed to initialize cron manager: %w", err)
    }
    
    // Get job
    job, err := manager.GetJob(jobID)
    if err != nil {
        return fmt.Errorf("job not found: %w", err)
    }
    
    if verbose {
        fmt.Printf("Job Details:\n")
        fmt.Printf("  ID: %s\n", job.ID)
        fmt.Printf("  Name: %s\n", job.Name)
        fmt.Printf("  Schedule: %s\n", job.Schedule)
        fmt.Printf("  Enabled: %v\n", job.Enabled)
        fmt.Printf("  Module: %s\n", job.Module)
        fmt.Printf("  Description: %s\n", job.Description)
        fmt.Println()
    }
    
    if dryRun {
        fmt.Println("✅ Job validation passed")
        return nil
    }
    
    // Execute job
    startTime := time.Now()
    
    payload, err := createPayloadForJob(job.TaskType)
    if err != nil {
        return fmt.Errorf("failed to create payload: %w", err)
    }
    
    result, err := job.Handler.Handle(context.Background(), payload)
    if err != nil {
        fmt.Printf("❌ Job execution failed: %v\n", err)
        return err
    }
    
    duration := time.Since(startTime)
    
    // Display results
    fmt.Printf("✅ Job executed successfully\n")
    fmt.Printf("Duration: %v\n", duration)
    fmt.Printf("Processed: %d\n", result.ProcessedCount)
    fmt.Printf("Errors: %d\n", result.ErrorCount)
    fmt.Printf("Success: %v\n", result.Success)
    
    if verbose && result.Details != nil {
        fmt.Printf("Details:\n")
        for key, value := range result.Details {
            fmt.Printf("  %s: %v\n", key, value)
        }
    }
    
    return nil
}

func testAllCronJobs(dryRun, verbose, parallel bool) error {
    fmt.Println("Testing all cron jobs...")
    
    manager, err := initializeCronManager()
    if err != nil {
        return fmt.Errorf("failed to initialize cron manager: %w", err)
    }
    
    jobs := manager.GetAllJobs()
    if len(jobs) == 0 {
        fmt.Println("No cron jobs found")
        return nil
    }
    
    fmt.Printf("Found %d jobs to test\n\n", len(jobs))
    
    if parallel {
        return testJobsParallel(jobs, dryRun, verbose)
    }
    
    return testJobsSequential(jobs, dryRun, verbose)
}

func testJobsSequential(jobs []*cron.CronJob, dryRun, verbose bool) error {
    var passed, failed int
    
    for i, job := range jobs {
        fmt.Printf("[%d/%d] Testing %s...", i+1, len(jobs), job.ID)
        
        err := testCronJob(job.ID, dryRun, false)
        if err != nil {
            fmt.Printf(" ❌ FAILED: %v\n", err)
            failed++
            if verbose {
                fmt.Printf("  Error details: %v\n", err)
            }
        } else {
            fmt.Printf(" ✅ PASSED\n")
            passed++
        }
    }
    
    fmt.Printf("\nTest Summary:\n")
    fmt.Printf("  Passed: %d\n", passed)
    fmt.Printf("  Failed: %d\n", failed)
    fmt.Printf("  Total: %d\n", len(jobs))
    
    if failed > 0 {
        return fmt.Errorf("%d jobs failed", failed)
    }
    
    return nil
}

func benchmarkCronJob(jobID string, iterations int) error {
    fmt.Printf("Benchmarking cron job: %s (%d iterations)\n", jobID, iterations)
    
    manager, err := initializeCronManager()
    if err != nil {
        return fmt.Errorf("failed to initialize cron manager: %w", err)
    }
    
    job, err := manager.GetJob(jobID)
    if err != nil {
        return fmt.Errorf("job not found: %w", err)
    }
    
    var durations []time.Duration
    var successCount, errorCount int
    
    for i := 0; i < iterations; i++ {
        fmt.Printf("Iteration %d/%d...", i+1, iterations)
        
        payload, err := createPayloadForJob(job.TaskType)
        if err != nil {
            fmt.Printf(" ❌ Payload creation failed\n")
            errorCount++
            continue
        }
        
        startTime := time.Now()
        result, err := job.Handler.Handle(context.Background(), payload)
        duration := time.Since(startTime)
        
        durations = append(durations, duration)
        
        if err != nil || !result.Success {
            fmt.Printf(" ❌ Failed (%v)\n", duration)
            errorCount++
        } else {
            fmt.Printf(" ✅ Success (%v)\n", duration)
            successCount++
        }
    }
    
    // Calculate statistics
    if len(durations) == 0 {
        return fmt.Errorf("no successful executions")
    }
    
    var total time.Duration
    min := durations[0]
    max := durations[0]
    
    for _, d := range durations {
        total += d
        if d < min {
            min = d
        }
        if d > max {
            max = d
        }
    }
    
    avg := total / time.Duration(len(durations))
    
    fmt.Printf("\nBenchmark Results:\n")
    fmt.Printf("  Iterations: %d\n", iterations)
    fmt.Printf("  Successful: %d\n", successCount)
    fmt.Printf("  Failed: %d\n", errorCount)
    fmt.Printf("  Min Duration: %v\n", min)
    fmt.Printf("  Max Duration: %v\n", max)
    fmt.Printf("  Avg Duration: %v\n", avg)
    fmt.Printf("  Total Duration: %v\n", total)
    
    return nil
}

func init() {
    // Add flags
    testJobCmd.Flags().Bool("dry-run", false, "Validate job without executing")
    testJobCmd.Flags().Bool("verbose", false, "Show detailed output")
    
    testAllCmd.Flags().Bool("dry-run", false, "Validate jobs without executing")
    testAllCmd.Flags().Bool("verbose", false, "Show detailed output")
    testAllCmd.Flags().Bool("parallel", false, "Run tests in parallel")
    
    benchmarkJobCmd.Flags().Int("iterations", 10, "Number of benchmark iterations")
    
    // Add commands
    cronCmd.AddCommand(testJobCmd)
    cronCmd.AddCommand(testAllCmd)
    cronCmd.AddCommand(benchmarkJobCmd)
}
```

## File Paths

### Tạo mới:
- `modules/auth/cron/handlers/session_cleanup_handler_test.go`
- `modules/auth/cron/handlers/password_expiry_handler_test.go`
- `modules/auth/cron/handlers/token_cleanup_handler_test.go`
- `modules/notification/cron/handlers/cleanup_handler_test.go`
- `modules/notification/cron/handlers/email_cleanup_handler_test.go`
- `test/integration/cron_module_test.go`
- `test/mocks/cron_mocks.go`
- `test/testutils/cron_utils.go`

### Cập nhật:
- `cmd/cli/commands/cron.go` (add test commands)
- `test/testutils/database.go` (add cron test utilities)

## Acceptance Criteria

1. ✅ Unit tests cover all cron handlers với high coverage
2. ✅ Integration tests validate complete workflows
3. ✅ CLI test commands hoạt động properly
4. ✅ Mock framework setup cho dependencies
5. ✅ Benchmark tools cho performance testing
6. ✅ Test utilities và helpers comprehensive
7. ✅ Automated test execution trong CI/CD

## Dependencies
- Task 09: Notification Module Implementation

## Next Task
[11-integration-testing.md](11-integration-testing.md) - Integration testing với existing system
