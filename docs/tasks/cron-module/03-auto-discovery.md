# Task 03: Auto-Discovery Mechanism

## Objective
Triể<PERSON> khai cơ chế tự động phát hiện và đăng ký cron capabilities của modules khi chúng được load vào system.

## Input
- Enhanced interfaces từ Task 02
- Existing module system trong `internal/pkg/module/`
- Module registration patterns từ auth/rbac modules

## Output
- Auto-discovery system cho module cron capabilities
- Module lifecycle hooks cho cron registration
- Discovery configuration và settings
- Integration với existing module loader

## Requirements

### 1. Auto-discovery mechanism
- Tự động phát hiện modules có cron capabilities
- Load và validate cron configurations
- Register cron handlers tự động

### 2. Module lifecycle integration
- Hook vào module initialization
- Handle module enable/disable
- Graceful shutdown handling

### 3. Configuration-driven discovery
- Enable/disable discovery per module
- Conditional loading based on environment
- Override mechanisms

## Implementation Steps

### Step 1: Module discovery interface (30 phút)

**File**: `internal/pkg/module/discovery.go` (new file)

```go
package module

import (
    "context"
    "fmt"
    "reflect"
    "sync"
    
    "wnapi/internal/pkg/logger"
)

// ModuleDiscovery interface cho module discovery system
type ModuleDiscovery interface {
    // DiscoverModules phát hiện modules có specific capabilities
    DiscoverModules(capability string) ([]ModuleInfo, error)
    
    // RegisterDiscoveryHook đăng ký hook cho discovery events
    RegisterDiscoveryHook(hook DiscoveryHook) error
    
    // EnableAutoDiscovery enable/disable auto discovery
    EnableAutoDiscovery(enabled bool)
    
    // IsAutoDiscoveryEnabled kiểm tra auto discovery status
    IsAutoDiscoveryEnabled() bool
}

// DiscoveryHook interface cho discovery event hooks
type DiscoveryHook interface {
    // OnModuleDiscovered được gọi khi module được phát hiện
    OnModuleDiscovered(ctx context.Context, moduleInfo ModuleInfo) error
    
    // OnModuleRegistered được gọi khi module được đăng ký
    OnModuleRegistered(ctx context.Context, moduleInfo ModuleInfo) error
    
    // OnModuleUnregistered được gọi khi module bị hủy đăng ký
    OnModuleUnregistered(ctx context.Context, moduleInfo ModuleInfo) error
    
    // GetCapability trả về capability mà hook quan tâm
    GetCapability() string
}

// ModuleInfo thông tin về module được discover
type ModuleInfo struct {
    Name         string                 `json:"name"`
    Path         string                 `json:"path"`
    Capabilities []string               `json:"capabilities"`
    Config       map[string]interface{} `json:"config"`
    Metadata     map[string]interface{} `json:"metadata"`
    Instance     interface{}            `json:"-"`
}

// CronCapability constants
const (
    CapabilityCron         = "cron"
    CapabilityQueue        = "queue"
    CapabilityEvent        = "event"
    CapabilityAPI          = "api"
    CapabilityRepository   = "repository"
    CapabilityService      = "service"
)

// ModuleDiscoveryImpl implements ModuleDiscovery
type ModuleDiscoveryImpl struct {
    logger           logger.Logger
    autoDiscovery    bool
    discoveryHooks   map[string][]DiscoveryHook
    discoveredModules map[string]ModuleInfo
    mu               sync.RWMutex
}

func NewModuleDiscovery(logger logger.Logger) *ModuleDiscoveryImpl {
    return &ModuleDiscoveryImpl{
        logger:            logger,
        autoDiscovery:     true,
        discoveryHooks:    make(map[string][]DiscoveryHook),
        discoveredModules: make(map[string]ModuleInfo),
    }
}

func (d *ModuleDiscoveryImpl) DiscoverModules(capability string) ([]ModuleInfo, error) {
    d.mu.RLock()
    defer d.mu.RUnlock()
    
    var modules []ModuleInfo
    for _, moduleInfo := range d.discoveredModules {
        if hasCapability(moduleInfo.Capabilities, capability) {
            modules = append(modules, moduleInfo)
        }
    }
    
    return modules, nil
}

func (d *ModuleDiscoveryImpl) RegisterDiscoveryHook(hook DiscoveryHook) error {
    d.mu.Lock()
    defer d.mu.Unlock()
    
    capability := hook.GetCapability()
    d.discoveryHooks[capability] = append(d.discoveryHooks[capability], hook)
    
    d.logger.Debug("Discovery hook registered", 
        "capability", capability,
        "hook_type", reflect.TypeOf(hook).String())
    
    return nil
}

func (d *ModuleDiscoveryImpl) EnableAutoDiscovery(enabled bool) {
    d.mu.Lock()
    defer d.mu.Unlock()
    
    d.autoDiscovery = enabled
    d.logger.Info("Auto discovery status changed", "enabled", enabled)
}

func (d *ModuleDiscoveryImpl) IsAutoDiscoveryEnabled() bool {
    d.mu.RLock()
    defer d.mu.RUnlock()
    
    return d.autoDiscovery
}

// DiscoverModule phát hiện và đăng ký một module
func (d *ModuleDiscoveryImpl) DiscoverModule(ctx context.Context, moduleInfo ModuleInfo) error {
    d.mu.Lock()
    defer d.mu.Unlock()
    
    if !d.autoDiscovery {
        d.logger.Debug("Auto discovery is disabled, skipping module", "module", moduleInfo.Name)
        return nil
    }
    
    // Store discovered module
    d.discoveredModules[moduleInfo.Name] = moduleInfo
    
    // Trigger discovery hooks
    for _, capability := range moduleInfo.Capabilities {
        hooks, exists := d.discoveryHooks[capability]
        if !exists {
            continue
        }
        
        for _, hook := range hooks {
            if err := hook.OnModuleDiscovered(ctx, moduleInfo); err != nil {
                d.logger.Error("Discovery hook failed", 
                    "module", moduleInfo.Name,
                    "capability", capability,
                    "error", err)
                return fmt.Errorf("discovery hook failed for %s.%s: %w", 
                    moduleInfo.Name, capability, err)
            }
        }
    }
    
    d.logger.Info("Module discovered successfully", 
        "module", moduleInfo.Name,
        "capabilities", moduleInfo.Capabilities)
    
    return nil
}

func hasCapability(capabilities []string, target string) bool {
    for _, cap := range capabilities {
        if cap == target {
            return true
        }
    }
    return false
}
```

### Step 2: Cron discovery hook (45 phút)

**File**: `internal/pkg/queue/cron/discovery_hook.go` (new file)

```go
package cron

import (
    "context"
    "fmt"
    "reflect"
    
    "wnapi/internal/pkg/logger"
    "wnapi/internal/pkg/module"
)

// CronDiscoveryHook implements DiscoveryHook for cron capability
type CronDiscoveryHook struct {
    moduleManager module.ModuleCronManager
    logger        logger.Logger
}

func NewCronDiscoveryHook(moduleManager module.ModuleCronManager, logger logger.Logger) *CronDiscoveryHook {
    return &CronDiscoveryHook{
        moduleManager: moduleManager,
        logger:        logger,
    }
}

func (h *CronDiscoveryHook) GetCapability() string {
    return module.CapabilityCron
}

func (h *CronDiscoveryHook) OnModuleDiscovered(ctx context.Context, moduleInfo module.ModuleInfo) error {
    h.logger.Debug("Cron module discovered", "module", moduleInfo.Name)
    
    // Extract cron registrar from module instance
    registrar, err := h.extractCronRegistrar(moduleInfo)
    if err != nil {
        return fmt.Errorf("failed to extract cron registrar: %w", err)
    }
    
    if registrar == nil {
        h.logger.Debug("Module has no cron registrar", "module", moduleInfo.Name)
        return nil
    }
    
    // Register module with cron manager
    if err := h.moduleManager.RegisterModule(registrar); err != nil {
        return fmt.Errorf("failed to register module cron: %w", err)
    }
    
    h.logger.Info("Module cron registered via discovery", "module", moduleInfo.Name)
    return nil
}

func (h *CronDiscoveryHook) OnModuleRegistered(ctx context.Context, moduleInfo module.ModuleInfo) error {
    h.logger.Debug("Module registered event", "module", moduleInfo.Name)
    return nil
}

func (h *CronDiscoveryHook) OnModuleUnregistered(ctx context.Context, moduleInfo module.ModuleInfo) error {
    h.logger.Debug("Module unregistered event", "module", moduleInfo.Name)
    
    // Unregister from cron manager
    if err := h.moduleManager.UnregisterModule(moduleInfo.Name); err != nil {
        h.logger.Error("Failed to unregister module cron", 
            "module", moduleInfo.Name, 
            "error", err)
        return err
    }
    
    h.logger.Info("Module cron unregistered", "module", moduleInfo.Name)
    return nil
}

func (h *CronDiscoveryHook) extractCronRegistrar(moduleInfo module.ModuleInfo) (module.ModuleCronRegistrar, error) {
    if moduleInfo.Instance == nil {
        return nil, fmt.Errorf("module instance is nil")
    }
    
    // Try to get cron registrar from module instance
    moduleValue := reflect.ValueOf(moduleInfo.Instance)
    
    // Check if module implements ModuleCronRegistrar directly
    if registrar, ok := moduleInfo.Instance.(module.ModuleCronRegistrar); ok {
        return registrar, nil
    }
    
    // Try to find GetCronRegistrar method
    method := moduleValue.MethodByName("GetCronRegistrar")
    if !method.IsValid() {
        return nil, nil // Module doesn't have cron capability
    }
    
    // Call GetCronRegistrar method
    results := method.Call(nil)
    if len(results) != 1 {
        return nil, fmt.Errorf("GetCronRegistrar should return exactly one value")
    }
    
    result := results[0].Interface()
    if result == nil {
        return nil, nil
    }
    
    registrar, ok := result.(module.ModuleCronRegistrar)
    if !ok {
        return nil, fmt.Errorf("GetCronRegistrar returned invalid type: %T", result)
    }
    
    return registrar, nil
}
```

### Step 3: Module loader integration (45 phút)

**File**: `internal/pkg/module/loader.go` (new file)

```go
package module

import (
    "context"
    "fmt"
    "path/filepath"
    "reflect"
    "strings"
    
    "wnapi/internal/pkg/config"
    "wnapi/internal/pkg/logger"
)

// ModuleLoader interface cho loading modules
type ModuleLoader interface {
    // LoadModule load một module từ path
    LoadModule(ctx context.Context, modulePath string) (*ModuleInfo, error)
    
    // LoadModules load tất cả modules từ directory
    LoadModules(ctx context.Context, modulesDir string) ([]*ModuleInfo, error)
    
    // RegisterModule đăng ký module với system
    RegisterModule(ctx context.Context, moduleInfo *ModuleInfo) error
    
    // UnregisterModule hủy đăng ký module
    UnregisterModule(ctx context.Context, moduleName string) error
    
    // GetLoadedModules trả về danh sách modules đã load
    GetLoadedModules() []*ModuleInfo
}

// ModuleLoaderImpl implements ModuleLoader
type ModuleLoaderImpl struct {
    config        config.Config
    logger        logger.Logger
    discovery     ModuleDiscovery
    loadedModules map[string]*ModuleInfo
}

func NewModuleLoader(
    config config.Config,
    logger logger.Logger,
    discovery ModuleDiscovery,
) *ModuleLoaderImpl {
    return &ModuleLoaderImpl{
        config:        config,
        logger:        logger,
        discovery:     discovery,
        loadedModules: make(map[string]*ModuleInfo),
    }
}

func (l *ModuleLoaderImpl) LoadModules(ctx context.Context, modulesDir string) ([]*ModuleInfo, error) {
    l.logger.Info("Loading modules from directory", "dir", modulesDir)
    
    // Get list of module directories
    moduleNames := []string{"auth", "rbac", "notification", "media", "tenant"}
    
    var loadedModules []*ModuleInfo
    
    for _, moduleName := range moduleNames {
        modulePath := filepath.Join(modulesDir, moduleName)
        
        // Check if module is enabled
        if !l.isModuleEnabled(moduleName) {
            l.logger.Debug("Module is disabled, skipping", "module", moduleName)
            continue
        }
        
        moduleInfo, err := l.LoadModule(ctx, modulePath)
        if err != nil {
            l.logger.Error("Failed to load module", 
                "module", moduleName, 
                "path", modulePath, 
                "error", err)
            continue
        }
        
        if moduleInfo != nil {
            loadedModules = append(loadedModules, moduleInfo)
        }
    }
    
    l.logger.Info("Modules loaded successfully", "count", len(loadedModules))
    return loadedModules, nil
}

func (l *ModuleLoaderImpl) LoadModule(ctx context.Context, modulePath string) (*ModuleInfo, error) {
    moduleName := filepath.Base(modulePath)
    
    l.logger.Debug("Loading module", "module", moduleName, "path", modulePath)
    
    // Create module info
    moduleInfo := &ModuleInfo{
        Name:         moduleName,
        Path:         modulePath,
        Capabilities: l.detectCapabilities(modulePath),
        Config:       l.getModuleConfig(moduleName),
        Metadata:     make(map[string]interface{}),
    }
    
    // Try to instantiate module
    instance, err := l.instantiateModule(moduleName, moduleInfo.Config)
    if err != nil {
        return nil, fmt.Errorf("failed to instantiate module %s: %w", moduleName, err)
    }
    
    moduleInfo.Instance = instance
    
    // Register with discovery system
    if l.discovery.IsAutoDiscoveryEnabled() {
        if err := l.discovery.DiscoverModule(ctx, *moduleInfo); err != nil {
            return nil, fmt.Errorf("failed to discover module %s: %w", moduleName, err)
        }
    }
    
    // Store loaded module
    l.loadedModules[moduleName] = moduleInfo
    
    l.logger.Info("Module loaded successfully", 
        "module", moduleName,
        "capabilities", moduleInfo.Capabilities)
    
    return moduleInfo, nil
}

func (l *ModuleLoaderImpl) detectCapabilities(modulePath string) []string {
    var capabilities []string
    
    // Check for cron capability
    cronPath := filepath.Join(modulePath, "cron")
    if l.pathExists(cronPath) {
        capabilities = append(capabilities, CapabilityCron)
    }
    
    // Check for API capability
    apiPath := filepath.Join(modulePath, "api")
    if l.pathExists(apiPath) {
        capabilities = append(capabilities, CapabilityAPI)
    }
    
    // Check for service capability
    servicePath := filepath.Join(modulePath, "service")
    if l.pathExists(servicePath) {
        capabilities = append(capabilities, CapabilityService)
    }
    
    // Check for repository capability
    repoPath := filepath.Join(modulePath, "repository")
    if l.pathExists(repoPath) {
        capabilities = append(capabilities, CapabilityRepository)
    }
    
    return capabilities
}

func (l *ModuleLoaderImpl) isModuleEnabled(moduleName string) bool {
    key := fmt.Sprintf("MODULE_%s_ENABLED", strings.ToUpper(moduleName))
    return l.config.GetBool(key)
}

func (l *ModuleLoaderImpl) getModuleConfig(moduleName string) map[string]interface{} {
    prefix := fmt.Sprintf("MODULE_%s_", strings.ToUpper(moduleName))
    return l.config.GetStringMap(prefix)
}

func (l *ModuleLoaderImpl) pathExists(path string) bool {
    // Implementation would check if path exists
    // For now, return true for demo
    return true
}

func (l *ModuleLoaderImpl) instantiateModule(moduleName string, config map[string]interface{}) (interface{}, error) {
    // This would contain logic to instantiate module based on name
    // For now, return a placeholder
    return &struct {
        Name   string
        Config map[string]interface{}
    }{
        Name:   moduleName,
        Config: config,
    }, nil
}

func (l *ModuleLoaderImpl) GetLoadedModules() []*ModuleInfo {
    var modules []*ModuleInfo
    for _, moduleInfo := range l.loadedModules {
        modules = append(modules, moduleInfo)
    }
    return modules
}
```

## File Paths

### Tạo mới:
- `internal/pkg/module/discovery.go`
- `internal/pkg/module/loader.go`
- `internal/pkg/queue/cron/discovery_hook.go`

### Cập nhật:
- `internal/pkg/module/cron_interfaces.go` (add discovery interfaces)
- `cmd/server/main.go` (integrate auto-discovery)

## Acceptance Criteria

1. ✅ Auto-discovery system phát hiện modules có cron capability
2. ✅ Discovery hooks tự động đăng ký cron handlers
3. ✅ Module loader tích hợp với discovery system
4. ✅ Configuration-driven discovery hoạt động
5. ✅ Lifecycle hooks handle module enable/disable
6. ✅ Error handling và logging comprehensive
7. ✅ Code compile thành công với `make build`

## Dependencies
- Task 02: Cron Interfaces Enhancement

## Next Task
[04-registration-system.md](04-registration-system.md) - Registration system implementation
