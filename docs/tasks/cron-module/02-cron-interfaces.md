# Task 02: Cron Interfaces Enhancement

## Objective
Hoàn thiện và mở rộng interfaces cho module cron system, tích hợp với existing cron infrastructure.

## Input
- Base interfaces từ Task 01
- Existing cron system trong `internal/pkg/queue/cron/`
- Current CronManager và HandlerRegistry

## Output
- Enhanced interfaces với full functionality
- Integration points với existing system
- Error handling và validation interfaces
- Module discovery mechanism interfaces

## Requirements

### 1. Enhanced interfaces
- Module discovery và lifecycle management
- Error handling và validation
- Configuration validation
- Health check capabilities

### 2. Integration với existing system
- Tương thích với current CronManager
- Extend HandlerRegistry cho module support
- Backward compatibility

### 3. Validation và error handling
- Config validation
- Handler validation
- Runtime error handling

## Implementation Steps

### Step 1: Enhance module interfaces (45 phút)

**File**: `internal/pkg/module/cron_interfaces.go` (extend existing)

```go
// ModuleCronManager interface cho module cron lifecycle management
type ModuleCronManager interface {
    // RegisterModule đăng ký module với cron system
    RegisterModule(registrar ModuleCronRegistrar) error
    
    // UnregisterModule hủy đăng ký module
    UnregisterModule(moduleName string) error
    
    // GetRegisteredModules trả về danh sách modules đã đăng ký
    GetRegisteredModules() []string
    
    // IsModuleRegistered kiểm tra module đã đăng ký chưa
    IsModuleRegistered(moduleName string) bool
    
    // GetModuleJobs trả về jobs của module
    GetModuleJobs(moduleName string) ([]*ModuleCronJobDefinition, error)
    
    // ValidateModule validate module configuration
    ValidateModule(moduleName string) error
}

// ModuleCronValidator interface cho validation
type ModuleCronValidator interface {
    // ValidateConfig validate module cron configuration
    ValidateConfig(config ModuleCronConfig) error
    
    // ValidateHandler validate cron handler
    ValidateHandler(handler ModuleCronHandler) error
    
    // ValidateJobDefinition validate job definition
    ValidateJobDefinition(job *ModuleCronJobDefinition) error
    
    // ValidateSchedule validate cron schedule format
    ValidateSchedule(schedule string) error
}

// ModuleCronHealthChecker interface cho health monitoring
type ModuleCronHealthChecker interface {
    // CheckModuleHealth kiểm tra health của module cron
    CheckModuleHealth(moduleName string) (*ModuleCronHealthStatus, error)
    
    // GetModuleMetrics trả về metrics của module
    GetModuleMetrics(moduleName string) (*ModuleCronMetrics, error)
}

// ModuleCronHealthStatus status của module cron health
type ModuleCronHealthStatus struct {
    ModuleName    string                 `json:"module_name"`
    IsHealthy     bool                   `json:"is_healthy"`
    LastCheck     time.Time              `json:"last_check"`
    Issues        []string               `json:"issues,omitempty"`
    JobsStatus    map[string]JobStatus   `json:"jobs_status"`
    Metrics       *ModuleCronMetrics     `json:"metrics,omitempty"`
}

// ModuleCronMetrics metrics của module cron
type ModuleCronMetrics struct {
    ModuleName       string            `json:"module_name"`
    TotalJobs        int               `json:"total_jobs"`
    ActiveJobs       int               `json:"active_jobs"`
    SuccessfulRuns   int64             `json:"successful_runs"`
    FailedRuns       int64             `json:"failed_runs"`
    LastRunTime      time.Time         `json:"last_run_time"`
    AverageRunTime   time.Duration     `json:"average_run_time"`
    JobMetrics       map[string]*JobMetrics `json:"job_metrics"`
}

// JobStatus status của individual job
type JobStatus struct {
    JobID         string        `json:"job_id"`
    IsActive      bool          `json:"is_active"`
    LastRun       time.Time     `json:"last_run"`
    NextRun       time.Time     `json:"next_run"`
    RunCount      int64         `json:"run_count"`
    SuccessCount  int64         `json:"success_count"`
    FailureCount  int64         `json:"failure_count"`
    LastError     string        `json:"last_error,omitempty"`
}

// JobMetrics metrics của individual job
type JobMetrics struct {
    JobID           string        `json:"job_id"`
    TotalRuns       int64         `json:"total_runs"`
    SuccessfulRuns  int64         `json:"successful_runs"`
    FailedRuns      int64         `json:"failed_runs"`
    AverageRunTime  time.Duration `json:"average_run_time"`
    LastRunTime     time.Duration `json:"last_run_time"`
    LastRunStatus   string        `json:"last_run_status"`
    LastError       string        `json:"last_error,omitempty"`
}
```

### Step 2: Extend existing CronManager (45 phút)

**File**: `internal/pkg/queue/cron/module_manager.go` (new file)

```go
package cron

import (
    "fmt"
    "sync"
    "time"
    
    "wnapi/internal/pkg/logger"
    "wnapi/internal/pkg/module"
)

// ModuleCronManagerImpl implements ModuleCronManager
type ModuleCronManagerImpl struct {
    cronManager     *Manager
    handlerRegistry *HandlerRegistry
    logger          logger.Logger
    
    // Module tracking
    registeredModules map[string]module.ModuleCronRegistrar
    moduleJobs        map[string][]*module.ModuleCronJobDefinition
    moduleMetrics     map[string]*module.ModuleCronMetrics
    
    // Synchronization
    mu sync.RWMutex
    
    // Validation
    validator module.ModuleCronValidator
}

func NewModuleCronManager(
    cronManager *Manager,
    handlerRegistry *HandlerRegistry,
    logger logger.Logger,
) *ModuleCronManagerImpl {
    return &ModuleCronManagerImpl{
        cronManager:       cronManager,
        handlerRegistry:   handlerRegistry,
        logger:            logger,
        registeredModules: make(map[string]module.ModuleCronRegistrar),
        moduleJobs:        make(map[string][]*module.ModuleCronJobDefinition),
        moduleMetrics:     make(map[string]*module.ModuleCronMetrics),
        validator:         NewModuleCronValidator(),
    }
}

func (m *ModuleCronManagerImpl) RegisterModule(registrar module.ModuleCronRegistrar) error {
    m.mu.Lock()
    defer m.mu.Unlock()
    
    moduleName := registrar.GetModuleName()
    
    // Validate module
    if err := m.validator.ValidateModule(registrar); err != nil {
        return fmt.Errorf("module validation failed for %s: %w", moduleName, err)
    }
    
    // Check if already registered
    if _, exists := m.registeredModules[moduleName]; exists {
        return fmt.Errorf("module %s is already registered", moduleName)
    }
    
    // Register handlers
    if err := registrar.RegisterHandlers(m.handlerRegistry); err != nil {
        return fmt.Errorf("failed to register handlers for module %s: %w", moduleName, err)
    }
    
    // Get job definitions
    jobs, err := registrar.GetJobDefinitions()
    if err != nil {
        return fmt.Errorf("failed to get job definitions for module %s: %w", moduleName, err)
    }
    
    // Validate jobs
    for _, job := range jobs {
        if err := m.validator.ValidateJobDefinition(job); err != nil {
            return fmt.Errorf("job validation failed for %s.%s: %w", moduleName, job.ID, err)
        }
    }
    
    // Store module info
    m.registeredModules[moduleName] = registrar
    m.moduleJobs[moduleName] = jobs
    m.moduleMetrics[moduleName] = &module.ModuleCronMetrics{
        ModuleName:    moduleName,
        TotalJobs:     len(jobs),
        JobMetrics:    make(map[string]*module.JobMetrics),
    }
    
    // Initialize job metrics
    for _, job := range jobs {
        m.moduleMetrics[moduleName].JobMetrics[job.ID] = &module.JobMetrics{
            JobID: job.ID,
        }
    }
    
    m.logger.Info("Module cron registered successfully", 
        "module", moduleName, 
        "jobs_count", len(jobs))
    
    return nil
}

func (m *ModuleCronManagerImpl) UnregisterModule(moduleName string) error {
    m.mu.Lock()
    defer m.mu.Unlock()
    
    if _, exists := m.registeredModules[moduleName]; !exists {
        return fmt.Errorf("module %s is not registered", moduleName)
    }
    
    // Remove from tracking
    delete(m.registeredModules, moduleName)
    delete(m.moduleJobs, moduleName)
    delete(m.moduleMetrics, moduleName)
    
    m.logger.Info("Module cron unregistered", "module", moduleName)
    return nil
}

func (m *ModuleCronManagerImpl) GetRegisteredModules() []string {
    m.mu.RLock()
    defer m.mu.RUnlock()
    
    modules := make([]string, 0, len(m.registeredModules))
    for moduleName := range m.registeredModules {
        modules = append(modules, moduleName)
    }
    return modules
}

func (m *ModuleCronManagerImpl) IsModuleRegistered(moduleName string) bool {
    m.mu.RLock()
    defer m.mu.RUnlock()
    
    _, exists := m.registeredModules[moduleName]
    return exists
}

func (m *ModuleCronManagerImpl) GetModuleJobs(moduleName string) ([]*module.ModuleCronJobDefinition, error) {
    m.mu.RLock()
    defer m.mu.RUnlock()
    
    jobs, exists := m.moduleJobs[moduleName]
    if !exists {
        return nil, fmt.Errorf("module %s is not registered", moduleName)
    }
    
    return jobs, nil
}

func (m *ModuleCronManagerImpl) ValidateModule(moduleName string) error {
    m.mu.RLock()
    registrar, exists := m.registeredModules[moduleName]
    m.mu.RUnlock()
    
    if !exists {
        return fmt.Errorf("module %s is not registered", moduleName)
    }
    
    return m.validator.ValidateModule(registrar)
}
```

### Step 3: Implement validator (30 phút)

**File**: `internal/pkg/queue/cron/module_validator.go` (new file)

```go
package cron

import (
    "fmt"
    "regexp"
    "strings"
    
    "wnapi/internal/pkg/module"
)

// ModuleCronValidatorImpl implements ModuleCronValidator
type ModuleCronValidatorImpl struct {
    cronRegex *regexp.Regexp
}

func NewModuleCronValidator() *ModuleCronValidatorImpl {
    // Cron expression regex (simplified)
    cronRegex := regexp.MustCompile(`^(\*|[0-5]?\d|\*\/\d+)(\s+(\*|[01]?\d|2[0-3]|\*\/\d+)){4}$`)
    
    return &ModuleCronValidatorImpl{
        cronRegex: cronRegex,
    }
}

func (v *ModuleCronValidatorImpl) ValidateModule(registrar module.ModuleCronRegistrar) error {
    if registrar == nil {
        return fmt.Errorf("registrar cannot be nil")
    }
    
    moduleName := registrar.GetModuleName()
    if moduleName == "" {
        return fmt.Errorf("module name cannot be empty")
    }
    
    if !isValidModuleName(moduleName) {
        return fmt.Errorf("invalid module name: %s", moduleName)
    }
    
    return nil
}

func (v *ModuleCronValidatorImpl) ValidateJobDefinition(job *module.ModuleCronJobDefinition) error {
    if job == nil {
        return fmt.Errorf("job definition cannot be nil")
    }
    
    if job.ID == "" {
        return fmt.Errorf("job ID cannot be empty")
    }
    
    if job.Name == "" {
        return fmt.Errorf("job name cannot be empty")
    }
    
    if job.TaskType == "" {
        return fmt.Errorf("job task type cannot be empty")
    }
    
    if job.Module == "" {
        return fmt.Errorf("job module cannot be empty")
    }
    
    return v.ValidateSchedule(job.Schedule)
}

func (v *ModuleCronValidatorImpl) ValidateSchedule(schedule string) error {
    if schedule == "" {
        return fmt.Errorf("schedule cannot be empty")
    }
    
    if !v.cronRegex.MatchString(schedule) {
        return fmt.Errorf("invalid cron schedule format: %s", schedule)
    }
    
    return nil
}

func isValidModuleName(name string) bool {
    if len(name) == 0 || len(name) > 50 {
        return false
    }
    
    // Module name should contain only lowercase letters, numbers, and underscores
    matched, _ := regexp.MatchString(`^[a-z][a-z0-9_]*$`, name)
    return matched
}
```

## File Paths

### Tạo mới:
- `internal/pkg/queue/cron/module_manager.go`
- `internal/pkg/queue/cron/module_validator.go`
- `internal/pkg/queue/cron/module_health_checker.go`

### Cập nhật:
- `internal/pkg/module/cron_interfaces.go` (extend)
- `internal/pkg/queue/cron/manager.go` (integrate module manager)

## Acceptance Criteria

1. ✅ Enhanced interfaces được implement đầy đủ
2. ✅ ModuleCronManager tích hợp với existing CronManager
3. ✅ Validation system hoạt động cho modules và jobs
4. ✅ Health checking capabilities được implement
5. ✅ Error handling comprehensive
6. ✅ Code compile thành công với `make build`
7. ✅ Unit tests pass

## Dependencies
- Task 01: Module Cron Structure Setup

## Next Task
[03-auto-discovery.md](03-auto-discovery.md) - Auto-discovery mechanism
