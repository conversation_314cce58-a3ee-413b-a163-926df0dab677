# Task 06: Module Configuration Management

## Objective
Triển khai hệ thống quản lý cấu hình cho module cron jobs, cho phép mỗi module có configuration riêng và tích hợp với global config system.

## Input
- Module registrars từ Task 05
- Existing config system trong `internal/pkg/config/`
- Environment variable patterns từ existing modules

## Output
- Module-specific configuration interfaces
- Environment variable management cho module cron
- Configuration validation và defaults
- Dynamic configuration updates

## Requirements

### 1. Module configuration structure
- Hierarchical configuration: Global → Module → Job
- Environment variable naming conventions
- Default values và validation

### 2. Configuration interfaces
- ModuleCronConfig implementation cho mỗi module
- Configuration validation
- Runtime configuration updates

### 3. Integration với existing config
- Extend current config system
- Backward compatibility
- Configuration hot-reload support

## Implementation Steps

### Step 1: Extend global config cho module cron (30 phút)

**File**: `internal/pkg/config/config.go` (extend existing)

```go
// Add to existing Config struct
type Config struct {
    // ... existing fields ...
    
    // Module cron configurations
    ModuleCron struct {
        Auth struct {
            Enabled bool `env:"CRON_AUTH_ENABLED" envDefault:"true"`
            Jobs    struct {
                SessionCleanup struct {
                    Enabled  bool   `env:"CRON_AUTH_SESSION_CLEANUP_ENABLED" envDefault:"true"`
                    Schedule string `env:"CRON_AUTH_SESSION_CLEANUP_SCHEDULE" envDefault:"0 2 * * *"`
                    MaxInactiveHours int `env:"AUTH_SESSION_MAX_INACTIVE_HOURS" envDefault:"168"`
                }
                PasswordExpiry struct {
                    Enabled  bool   `env:"CRON_AUTH_PASSWORD_EXPIRY_ENABLED" envDefault:"true"`
                    Schedule string `env:"CRON_AUTH_PASSWORD_EXPIRY_SCHEDULE" envDefault:"0 9 * * *"`
                    NotifyDays []int `env:"AUTH_PASSWORD_EXPIRY_NOTIFY_DAYS" envDefault:"30,7,1"`
                }
                TokenCleanup struct {
                    Enabled  bool   `env:"CRON_AUTH_TOKEN_CLEANUP_ENABLED" envDefault:"true"`
                    Schedule string `env:"CRON_AUTH_TOKEN_CLEANUP_SCHEDULE" envDefault:"0 3 * * *"`
                    RetentionDays int `env:"AUTH_TOKEN_RETENTION_DAYS" envDefault:"30"`
                }
            }
        }
        
        Notification struct {
            Enabled bool `env:"CRON_NOTIFICATION_ENABLED" envDefault:"true"`
            Jobs    struct {
                Cleanup struct {
                    Enabled  bool   `env:"CRON_NOTIFICATION_CLEANUP_ENABLED" envDefault:"true"`
                    Schedule string `env:"CRON_NOTIFICATION_CLEANUP_SCHEDULE" envDefault:"0 1 * * *"`
                    RetentionDays int `env:"NOTIFICATION_RETENTION_DAYS" envDefault:"90"`
                }
                EmailCleanup struct {
                    Enabled  bool   `env:"CRON_NOTIFICATION_EMAIL_CLEANUP_ENABLED" envDefault:"true"`
                    Schedule string `env:"CRON_NOTIFICATION_EMAIL_CLEANUP_SCHEDULE" envDefault:"0 4 * * *"`
                    RetentionDays int `env:"EMAIL_QUEUE_RETENTION_DAYS" envDefault:"7"`
                }
            }
        }
        
        Media struct {
            Enabled bool `env:"CRON_MEDIA_ENABLED" envDefault:"true"`
            Jobs    struct {
                ImageOptimization struct {
                    Enabled  bool   `env:"CRON_MEDIA_IMAGE_OPTIMIZATION_ENABLED" envDefault:"false"`
                    Schedule string `env:"CRON_MEDIA_IMAGE_OPTIMIZATION_SCHEDULE" envDefault:"0 3 * * *"`
                    BatchSize int  `env:"MEDIA_OPTIMIZATION_BATCH_SIZE" envDefault:"100"`
                }
                TempCleanup struct {
                    Enabled  bool   `env:"CRON_MEDIA_TEMP_CLEANUP_ENABLED" envDefault:"true"`
                    Schedule string `env:"CRON_MEDIA_TEMP_CLEANUP_SCHEDULE" envDefault:"0 */6 * * *"`
                    MaxAge   int    `env:"MEDIA_TEMP_MAX_AGE_HOURS" envDefault:"24"`
                }
            }
        }
    }
}
```

### Step 2: Module config implementations (45 phút)

**File**: `modules/auth/cron/config.go` (complete implementation)

```go
package cron

import (
    "fmt"
    "strings"
    
    "wnapi/internal/pkg/config"
    "wnapi/internal/pkg/module"
)

// AuthCronConfig implements ModuleCronConfig for auth module
type AuthCronConfig struct {
    config config.Config
}

func NewAuthCronConfig(cfg config.Config) *AuthCronConfig {
    return &AuthCronConfig{config: cfg}
}

func (c *AuthCronConfig) GetModuleName() string {
    return "auth"
}

func (c *AuthCronConfig) IsEnabled() bool {
    return c.config.GetBool("CRON_AUTH_ENABLED")
}

func (c *AuthCronConfig) GetJobConfig(jobID string) map[string]interface{} {
    jobConfig := make(map[string]interface{})
    
    switch jobID {
    case "session_cleanup":
        jobConfig["enabled"] = c.config.GetBool("CRON_AUTH_SESSION_CLEANUP_ENABLED")
        jobConfig["schedule"] = c.config.GetString("CRON_AUTH_SESSION_CLEANUP_SCHEDULE")
        jobConfig["max_inactive_hours"] = c.config.GetInt("AUTH_SESSION_MAX_INACTIVE_HOURS")
        
    case "password_expiry":
        jobConfig["enabled"] = c.config.GetBool("CRON_AUTH_PASSWORD_EXPIRY_ENABLED")
        jobConfig["schedule"] = c.config.GetString("CRON_AUTH_PASSWORD_EXPIRY_SCHEDULE")
        jobConfig["notify_days"] = c.config.GetIntSlice("AUTH_PASSWORD_EXPIRY_NOTIFY_DAYS")
        
    case "token_cleanup":
        jobConfig["enabled"] = c.config.GetBool("CRON_AUTH_TOKEN_CLEANUP_ENABLED")
        jobConfig["schedule"] = c.config.GetString("CRON_AUTH_TOKEN_CLEANUP_SCHEDULE")
        jobConfig["retention_days"] = c.config.GetInt("AUTH_TOKEN_RETENTION_DAYS")
    }
    
    return jobConfig
}

func (c *AuthCronConfig) GetSchedule(jobID string) string {
    key := fmt.Sprintf("CRON_AUTH_%s_SCHEDULE", strings.ToUpper(jobID))
    return c.config.GetString(key)
}

func (c *AuthCronConfig) IsJobEnabled(jobID string) bool {
    key := fmt.Sprintf("CRON_AUTH_%s_ENABLED", strings.ToUpper(jobID))
    return c.config.GetBool(key)
}

// GetSessionCleanupConfig returns session cleanup specific configuration
func (c *AuthCronConfig) GetSessionCleanupConfig() SessionCleanupConfig {
    return SessionCleanupConfig{
        Enabled:          c.config.GetBool("CRON_AUTH_SESSION_CLEANUP_ENABLED"),
        Schedule:         c.config.GetString("CRON_AUTH_SESSION_CLEANUP_SCHEDULE"),
        MaxInactiveHours: c.config.GetInt("AUTH_SESSION_MAX_INACTIVE_HOURS"),
    }
}

// GetPasswordExpiryConfig returns password expiry specific configuration
func (c *AuthCronConfig) GetPasswordExpiryConfig() PasswordExpiryConfig {
    return PasswordExpiryConfig{
        Enabled:    c.config.GetBool("CRON_AUTH_PASSWORD_EXPIRY_ENABLED"),
        Schedule:   c.config.GetString("CRON_AUTH_PASSWORD_EXPIRY_SCHEDULE"),
        NotifyDays: c.config.GetIntSlice("AUTH_PASSWORD_EXPIRY_NOTIFY_DAYS"),
    }
}

// GetTokenCleanupConfig returns token cleanup specific configuration
func (c *AuthCronConfig) GetTokenCleanupConfig() TokenCleanupConfig {
    return TokenCleanupConfig{
        Enabled:       c.config.GetBool("CRON_AUTH_TOKEN_CLEANUP_ENABLED"),
        Schedule:      c.config.GetString("CRON_AUTH_TOKEN_CLEANUP_SCHEDULE"),
        RetentionDays: c.config.GetInt("AUTH_TOKEN_RETENTION_DAYS"),
    }
}

// Validate validates the auth cron configuration
func (c *AuthCronConfig) Validate() error {
    if !c.IsEnabled() {
        return nil // Skip validation if disabled
    }
    
    // Validate session cleanup config
    sessionConfig := c.GetSessionCleanupConfig()
    if sessionConfig.Enabled {
        if sessionConfig.Schedule == "" {
            return fmt.Errorf("session cleanup schedule cannot be empty")
        }
        if sessionConfig.MaxInactiveHours <= 0 {
            return fmt.Errorf("max inactive hours must be positive")
        }
    }
    
    // Validate password expiry config
    passwordConfig := c.GetPasswordExpiryConfig()
    if passwordConfig.Enabled {
        if passwordConfig.Schedule == "" {
            return fmt.Errorf("password expiry schedule cannot be empty")
        }
        if len(passwordConfig.NotifyDays) == 0 {
            return fmt.Errorf("notify days cannot be empty")
        }
    }
    
    // Validate token cleanup config
    tokenConfig := c.GetTokenCleanupConfig()
    if tokenConfig.Enabled {
        if tokenConfig.Schedule == "" {
            return fmt.Errorf("token cleanup schedule cannot be empty")
        }
        if tokenConfig.RetentionDays <= 0 {
            return fmt.Errorf("retention days must be positive")
        }
    }
    
    return nil
}

// Configuration structs for type safety
type SessionCleanupConfig struct {
    Enabled          bool
    Schedule         string
    MaxInactiveHours int
}

type PasswordExpiryConfig struct {
    Enabled    bool
    Schedule   string
    NotifyDays []int
}

type TokenCleanupConfig struct {
    Enabled       bool
    Schedule      string
    RetentionDays int
}
```

### Step 3: Configuration validator (30 phút)

**File**: `internal/pkg/module/config_validator.go` (new file)

```go
package module

import (
    "fmt"
    "regexp"
    "strings"
)

// ModuleCronConfigValidator validates module cron configurations
type ModuleCronConfigValidator struct {
    cronRegex *regexp.Regexp
}

func NewModuleCronConfigValidator() *ModuleCronConfigValidator {
    // Enhanced cron expression regex
    cronRegex := regexp.MustCompile(`^(\*|[0-5]?\d|\*\/\d+)(\s+(\*|[01]?\d|2[0-3]|\*\/\d+)){4}$`)
    
    return &ModuleCronConfigValidator{
        cronRegex: cronRegex,
    }
}

// ValidateModuleConfig validates entire module configuration
func (v *ModuleCronConfigValidator) ValidateModuleConfig(config ModuleCronConfig) error {
    if config == nil {
        return fmt.Errorf("config cannot be nil")
    }
    
    moduleName := config.GetModuleName()
    if moduleName == "" {
        return fmt.Errorf("module name cannot be empty")
    }
    
    if !v.isValidModuleName(moduleName) {
        return fmt.Errorf("invalid module name: %s", moduleName)
    }
    
    return nil
}

// ValidateJobConfig validates job-specific configuration
func (v *ModuleCronConfigValidator) ValidateJobConfig(config ModuleCronConfig, jobID string) error {
    if !config.IsJobEnabled(jobID) {
        return nil // Skip validation for disabled jobs
    }
    
    schedule := config.GetSchedule(jobID)
    if err := v.ValidateSchedule(schedule); err != nil {
        return fmt.Errorf("invalid schedule for job %s: %w", jobID, err)
    }
    
    jobConfig := config.GetJobConfig(jobID)
    return v.validateJobSpecificConfig(jobID, jobConfig)
}

// ValidateSchedule validates cron schedule format
func (v *ModuleCronConfigValidator) ValidateSchedule(schedule string) error {
    if schedule == "" {
        return fmt.Errorf("schedule cannot be empty")
    }
    
    // Normalize schedule (remove extra spaces)
    schedule = strings.Join(strings.Fields(schedule), " ")
    
    if !v.cronRegex.MatchString(schedule) {
        return fmt.Errorf("invalid cron schedule format: %s", schedule)
    }
    
    return nil
}

func (v *ModuleCronConfigValidator) validateJobSpecificConfig(jobID string, config map[string]interface{}) error {
    // Validate common fields
    if enabled, ok := config["enabled"].(bool); ok && !enabled {
        return nil // Skip validation for disabled jobs
    }
    
    // Job-specific validation
    switch {
    case strings.Contains(jobID, "cleanup"):
        return v.validateCleanupConfig(config)
    case strings.Contains(jobID, "notification"):
        return v.validateNotificationConfig(config)
    case strings.Contains(jobID, "optimization"):
        return v.validateOptimizationConfig(config)
    default:
        return v.validateGenericConfig(config)
    }
}

func (v *ModuleCronConfigValidator) validateCleanupConfig(config map[string]interface{}) error {
    // Validate retention settings
    if retentionDays, ok := config["retention_days"].(int); ok {
        if retentionDays <= 0 {
            return fmt.Errorf("retention_days must be positive")
        }
        if retentionDays > 365 {
            return fmt.Errorf("retention_days cannot exceed 365 days")
        }
    }
    
    if maxAge, ok := config["max_age_hours"].(int); ok {
        if maxAge <= 0 {
            return fmt.Errorf("max_age_hours must be positive")
        }
    }
    
    return nil
}

func (v *ModuleCronConfigValidator) validateNotificationConfig(config map[string]interface{}) error {
    // Validate notification-specific settings
    if batchSize, ok := config["batch_size"].(int); ok {
        if batchSize <= 0 || batchSize > 1000 {
            return fmt.Errorf("batch_size must be between 1 and 1000")
        }
    }
    
    return nil
}

func (v *ModuleCronConfigValidator) validateOptimizationConfig(config map[string]interface{}) error {
    // Validate optimization-specific settings
    if batchSize, ok := config["batch_size"].(int); ok {
        if batchSize <= 0 || batchSize > 500 {
            return fmt.Errorf("optimization batch_size must be between 1 and 500")
        }
    }
    
    return nil
}

func (v *ModuleCronConfigValidator) validateGenericConfig(config map[string]interface{}) error {
    // Generic validation for unknown job types
    return nil
}

func (v *ModuleCronConfigValidator) isValidModuleName(name string) bool {
    if len(name) == 0 || len(name) > 50 {
        return false
    }
    
    // Module name should contain only lowercase letters, numbers, and underscores
    matched, _ := regexp.MatchString(`^[a-z][a-z0-9_]*$`, name)
    return matched
}
```

### Step 4: Environment configuration (15 phút)

**File**: `.env` (add module cron configurations)

```env
# Module Cron Global Settings
MODULE_AUTH_ENABLED=true
MODULE_NOTIFICATION_ENABLED=true
MODULE_MEDIA_ENABLED=true

# Auth Module Cron Jobs
CRON_AUTH_ENABLED=true
CRON_AUTH_SESSION_CLEANUP_ENABLED=true
CRON_AUTH_SESSION_CLEANUP_SCHEDULE="0 2 * * *"
AUTH_SESSION_MAX_INACTIVE_HOURS=168

CRON_AUTH_PASSWORD_EXPIRY_ENABLED=true
CRON_AUTH_PASSWORD_EXPIRY_SCHEDULE="0 9 * * *"
AUTH_PASSWORD_EXPIRY_NOTIFY_DAYS="30,7,1"

CRON_AUTH_TOKEN_CLEANUP_ENABLED=true
CRON_AUTH_TOKEN_CLEANUP_SCHEDULE="0 3 * * *"
AUTH_TOKEN_RETENTION_DAYS=30

# Notification Module Cron Jobs
CRON_NOTIFICATION_ENABLED=true
CRON_NOTIFICATION_CLEANUP_ENABLED=true
CRON_NOTIFICATION_CLEANUP_SCHEDULE="0 1 * * *"
NOTIFICATION_RETENTION_DAYS=90

CRON_NOTIFICATION_EMAIL_CLEANUP_ENABLED=true
CRON_NOTIFICATION_EMAIL_CLEANUP_SCHEDULE="0 4 * * *"
EMAIL_QUEUE_RETENTION_DAYS=7

# Media Module Cron Jobs
CRON_MEDIA_ENABLED=true
CRON_MEDIA_IMAGE_OPTIMIZATION_ENABLED=false
CRON_MEDIA_IMAGE_OPTIMIZATION_SCHEDULE="0 3 * * *"
MEDIA_OPTIMIZATION_BATCH_SIZE=100

CRON_MEDIA_TEMP_CLEANUP_ENABLED=true
CRON_MEDIA_TEMP_CLEANUP_SCHEDULE="0 */6 * * *"
MEDIA_TEMP_MAX_AGE_HOURS=24
```

## File Paths

### Tạo mới:
- `internal/pkg/module/config_validator.go`
- `modules/auth/cron/config.go`
- `modules/notification/cron/config.go`
- `modules/media/cron/config.go`

### Cập nhật:
- `internal/pkg/config/config.go` (extend for module cron)
- `internal/pkg/config/viperconfig/viper.go` (add module defaults)
- `.env` (add module cron configurations)

## Acceptance Criteria

1. ✅ Module-specific configuration interfaces implemented
2. ✅ Environment variable management hoạt động
3. ✅ Configuration validation comprehensive
4. ✅ Default values được thiết lập properly
5. ✅ Integration với existing config system
6. ✅ Type-safe configuration structs
7. ✅ Code compile thành công với `make build`

## Dependencies
- Task 05: Module-Specific Registrar Implementation

## Next Task
[07-environment-integration.md](07-environment-integration.md) - Environment integration và hot-reload
