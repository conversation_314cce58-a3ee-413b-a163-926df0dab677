# Task 05: Module-Specific Registrar Implementation

## Objective
Triển khai module-specific registrar cho từng module, cho phép mỗi module quản lý cron jobs riêng của mình một cách độc lập.

## Input
- Registration system từ Task 04
- Module structure từ Task 01
- Existing modules: auth, rbac, notification, media, tenant

## Output
- Concrete registrar implementations cho mỗi module
- Module-specific cron handlers
- Job definitions và default schedules
- Module cron configuration management

## Requirements

### 1. Module registrar implementations
- AuthCronRegistrar với session cleanup, password expiry jobs
- NotificationCronRegistrar với email queue cleanup, notification cleanup
- MediaCronRegistrar với image optimization, temp file cleanup
- TenantCronRegistrar với tenant maintenance jobs

### 2. Module-specific handlers
- Implement cron handlers cho từng module
- Follow existing handler patterns
- Proper error handling và logging

### 3. Configuration management
- Module-specific environment variables
- Default schedules và settings
- Enable/disable per job

## Implementation Steps

### Step 1: Auth module registrar implementation (45 phút)

**File**: `modules/auth/cron/registrar.go` (complete implementation)

```go
package cron

import (
    "fmt"
    "gorm.io/gorm"
    
    "wnapi/internal/pkg/config"
    "wnapi/internal/pkg/logger"
    "wnapi/internal/pkg/module"
    "wnapi/internal/pkg/queue/cron"
    "wnapi/internal/pkg/queue/types"
    "wnapi/modules/auth/cron/handlers"
)

// AuthCronRegistrar implements ModuleCronRegistrar for auth module
type AuthCronRegistrar struct {
    db     *gorm.DB
    config config.Config
    logger logger.Logger
}

func NewAuthCronRegistrar(db *gorm.DB, config config.Config, logger logger.Logger) *AuthCronRegistrar {
    return &AuthCronRegistrar{
        db:     db,
        config: config,
        logger: logger,
    }
}

func (r *AuthCronRegistrar) GetModuleName() string {
    return "auth"
}

func (r *AuthCronRegistrar) IsEnabled() bool {
    return r.config.GetBool("CRON_AUTH_ENABLED")
}

func (r *AuthCronRegistrar) RegisterHandlers(registry *cron.HandlerRegistry) error {
    if !r.IsEnabled() {
        r.logger.Debug("Auth cron is disabled, skipping handler registration")
        return nil
    }

    // Register session cleanup handler
    sessionHandler := handlers.NewAuthSessionCleanupHandler(r.db, r.config, r.logger)
    if err := registry.RegisterHandler(sessionHandler); err != nil {
        return fmt.Errorf("failed to register session cleanup handler: %w", err)
    }

    // Register password expiry handler
    passwordHandler := handlers.NewAuthPasswordExpiryHandler(r.db, r.config, r.logger)
    if err := registry.RegisterHandler(passwordHandler); err != nil {
        return fmt.Errorf("failed to register password expiry handler: %w", err)
    }

    // Register token cleanup handler
    tokenHandler := handlers.NewAuthTokenCleanupHandler(r.db, r.config, r.logger)
    if err := registry.RegisterHandler(tokenHandler); err != nil {
        return fmt.Errorf("failed to register token cleanup handler: %w", err)
    }

    r.logger.Info("Auth cron handlers registered successfully")
    return nil
}

func (r *AuthCronRegistrar) GetJobDefinitions() ([]*module.ModuleCronJobDefinition, error) {
    var jobs []*module.ModuleCronJobDefinition

    // Session cleanup job
    if r.config.GetBool("CRON_AUTH_SESSION_CLEANUP_ENABLED") {
        jobs = append(jobs, &module.ModuleCronJobDefinition{
            ID:          "auth_session_cleanup",
            Name:        "Auth Session Cleanup",
            Description: "Clean up expired and inactive user sessions",
            TaskType:    types.CronTaskAuthSessionCleanup,
            Schedule:    r.config.GetString("CRON_AUTH_SESSION_CLEANUP_SCHEDULE"),
            Enabled:     true,
            Module:      "auth",
        })
    }

    // Password expiry job
    if r.config.GetBool("CRON_AUTH_PASSWORD_EXPIRY_ENABLED") {
        jobs = append(jobs, &module.ModuleCronJobDefinition{
            ID:          "auth_password_expiry",
            Name:        "Auth Password Expiry Notification",
            Description: "Send password expiry notifications to users",
            TaskType:    types.CronTaskAuthPasswordExpiry,
            Schedule:    r.config.GetString("CRON_AUTH_PASSWORD_EXPIRY_SCHEDULE"),
            Enabled:     true,
            Module:      "auth",
        })
    }

    // Token cleanup job
    if r.config.GetBool("CRON_AUTH_TOKEN_CLEANUP_ENABLED") {
        jobs = append(jobs, &module.ModuleCronJobDefinition{
            ID:          "auth_token_cleanup",
            Name:        "Auth Token Cleanup",
            Description: "Clean up expired refresh tokens and access tokens",
            TaskType:    types.CronTaskAuthTokenCleanup,
            Schedule:    r.config.GetString("CRON_AUTH_TOKEN_CLEANUP_SCHEDULE"),
            Enabled:     true,
            Module:      "auth",
        })
    }

    return jobs, nil
}
```

### Step 2: Auth module handlers (45 phút)

**File**: `modules/auth/cron/handlers/session_cleanup_handler.go`

```go
package handlers

import (
    "context"
    "fmt"
    "time"
    
    "gorm.io/gorm"
    
    "wnapi/internal/pkg/config"
    "wnapi/internal/pkg/logger"
    "wnapi/internal/pkg/queue/types"
    "wnapi/modules/auth/models"
)

// AuthSessionCleanupHandler handles session cleanup cron job
type AuthSessionCleanupHandler struct {
    db     *gorm.DB
    config config.Config
    logger logger.Logger
}

func NewAuthSessionCleanupHandler(db *gorm.DB, config config.Config, logger logger.Logger) *AuthSessionCleanupHandler {
    return &AuthSessionCleanupHandler{
        db:     db,
        config: config,
        logger: logger,
    }
}

func (h *AuthSessionCleanupHandler) GetTaskType() types.CronTaskType {
    return types.CronTaskAuthSessionCleanup
}

func (h *AuthSessionCleanupHandler) GetDescription() string {
    return "Clean up expired and inactive user sessions"
}

func (h *AuthSessionCleanupHandler) Handle(ctx context.Context, payload types.CronTaskPayload) (*types.CronTaskResult, error) {
    startTime := time.Now()
    
    h.logger.Info("Starting auth session cleanup")
    
    // Get cleanup configuration
    maxInactiveHours := h.config.GetInt("AUTH_SESSION_MAX_INACTIVE_HOURS")
    if maxInactiveHours == 0 {
        maxInactiveHours = 24 * 7 // Default 7 days
    }
    
    cutoffTime := time.Now().Add(-time.Duration(maxInactiveHours) * time.Hour)
    
    // Clean up expired sessions
    var deletedCount int64
    result := h.db.Where("updated_at < ? OR expires_at < ?", cutoffTime, time.Now()).
        Delete(&models.UserSession{})
    
    if result.Error != nil {
        return &types.CronTaskResult{
            TaskType:    types.CronTaskAuthSessionCleanup,
            Success:     false,
            Message:     fmt.Sprintf("Failed to cleanup sessions: %v", result.Error),
            ErrorCount:  1,
            Duration:    time.Since(startTime),
            ExecutedAt:  startTime,
            CompletedAt: time.Now(),
        }, result.Error
    }
    
    deletedCount = result.RowsAffected
    
    // Clean up orphaned session data
    orphanedResult := h.db.Where("user_id NOT IN (SELECT id FROM users)").
        Delete(&models.UserSession{})
    
    if orphanedResult.Error != nil {
        h.logger.Error("Failed to cleanup orphaned sessions", "error", orphanedResult.Error)
    } else {
        deletedCount += orphanedResult.RowsAffected
    }
    
    details := map[string]interface{}{
        "cutoff_time":           cutoffTime.Format("2006-01-02 15:04:05"),
        "max_inactive_hours":    maxInactiveHours,
        "deleted_sessions":      deletedCount,
        "orphaned_sessions":     orphanedResult.RowsAffected,
    }
    
    h.logger.Info("Auth session cleanup completed", 
        "deleted_count", deletedCount,
        "duration", time.Since(startTime))
    
    return &types.CronTaskResult{
        TaskType:       types.CronTaskAuthSessionCleanup,
        Success:        true,
        Message:        fmt.Sprintf("Successfully cleaned up %d sessions", deletedCount),
        ProcessedCount: int(deletedCount),
        ErrorCount:     0,
        Duration:       time.Since(startTime),
        Details:        details,
        ExecutedAt:     startTime,
        CompletedAt:    time.Now(),
    }, nil
}
```

**File**: `modules/auth/cron/handlers/token_cleanup_handler.go`

```go
package handlers

import (
    "context"
    "fmt"
    "time"
    
    "gorm.io/gorm"
    
    "wnapi/internal/pkg/config"
    "wnapi/internal/pkg/logger"
    "wnapi/internal/pkg/queue/types"
    "wnapi/modules/auth/models"
)

// AuthTokenCleanupHandler handles token cleanup cron job
type AuthTokenCleanupHandler struct {
    db     *gorm.DB
    config config.Config
    logger logger.Logger
}

func NewAuthTokenCleanupHandler(db *gorm.DB, config config.Config, logger logger.Logger) *AuthTokenCleanupHandler {
    return &AuthTokenCleanupHandler{
        db:     db,
        config: config,
        logger: logger,
    }
}

func (h *AuthTokenCleanupHandler) GetTaskType() types.CronTaskType {
    return types.CronTaskAuthTokenCleanup
}

func (h *AuthTokenCleanupHandler) GetDescription() string {
    return "Clean up expired refresh tokens and access tokens"
}

func (h *AuthTokenCleanupHandler) Handle(ctx context.Context, payload types.CronTaskPayload) (*types.CronTaskResult, error) {
    startTime := time.Now()
    
    h.logger.Info("Starting auth token cleanup")
    
    var totalDeleted int64
    var details = make(map[string]interface{})
    
    // Clean up expired refresh tokens
    refreshResult := h.db.Where("expires_at < ?", time.Now()).
        Delete(&models.RefreshToken{})
    
    if refreshResult.Error != nil {
        return &types.CronTaskResult{
            TaskType:    types.CronTaskAuthTokenCleanup,
            Success:     false,
            Message:     fmt.Sprintf("Failed to cleanup refresh tokens: %v", refreshResult.Error),
            ErrorCount:  1,
            Duration:    time.Since(startTime),
            ExecutedAt:  startTime,
            CompletedAt: time.Now(),
        }, refreshResult.Error
    }
    
    totalDeleted += refreshResult.RowsAffected
    details["deleted_refresh_tokens"] = refreshResult.RowsAffected
    
    // Clean up revoked tokens older than 30 days
    revokedCutoff := time.Now().AddDate(0, 0, -30)
    revokedResult := h.db.Where("revoked_at IS NOT NULL AND revoked_at < ?", revokedCutoff).
        Delete(&models.RefreshToken{})
    
    if revokedResult.Error != nil {
        h.logger.Error("Failed to cleanup revoked tokens", "error", revokedResult.Error)
    } else {
        totalDeleted += revokedResult.RowsAffected
        details["deleted_revoked_tokens"] = revokedResult.RowsAffected
    }
    
    // Clean up password reset tokens older than 24 hours
    resetCutoff := time.Now().Add(-24 * time.Hour)
    resetResult := h.db.Where("created_at < ?", resetCutoff).
        Delete(&models.PasswordResetToken{})
    
    if resetResult.Error != nil {
        h.logger.Error("Failed to cleanup password reset tokens", "error", resetResult.Error)
    } else {
        totalDeleted += resetResult.RowsAffected
        details["deleted_reset_tokens"] = resetResult.RowsAffected
    }
    
    details["total_deleted"] = totalDeleted
    details["cleanup_time"] = time.Now().Format("2006-01-02 15:04:05")
    
    h.logger.Info("Auth token cleanup completed", 
        "total_deleted", totalDeleted,
        "duration", time.Since(startTime))
    
    return &types.CronTaskResult{
        TaskType:       types.CronTaskAuthTokenCleanup,
        Success:        true,
        Message:        fmt.Sprintf("Successfully cleaned up %d tokens", totalDeleted),
        ProcessedCount: int(totalDeleted),
        ErrorCount:     0,
        Duration:       time.Since(startTime),
        Details:        details,
        ExecutedAt:     startTime,
        CompletedAt:    time.Now(),
    }, nil
}
```

### Step 3: Notification module registrar (30 phút)

**File**: `modules/notification/cron/registrar.go`

```go
package cron

import (
    "fmt"
    "gorm.io/gorm"
    
    "wnapi/internal/pkg/config"
    "wnapi/internal/pkg/logger"
    "wnapi/internal/pkg/module"
    "wnapi/internal/pkg/queue/cron"
    "wnapi/internal/pkg/queue/types"
    "wnapi/modules/notification/cron/handlers"
)

// NotificationCronRegistrar implements ModuleCronRegistrar for notification module
type NotificationCronRegistrar struct {
    db     *gorm.DB
    config config.Config
    logger logger.Logger
}

func NewNotificationCronRegistrar(db *gorm.DB, config config.Config, logger logger.Logger) *NotificationCronRegistrar {
    return &NotificationCronRegistrar{
        db:     db,
        config: config,
        logger: logger,
    }
}

func (r *NotificationCronRegistrar) GetModuleName() string {
    return "notification"
}

func (r *NotificationCronRegistrar) IsEnabled() bool {
    return r.config.GetBool("CRON_NOTIFICATION_ENABLED")
}

func (r *NotificationCronRegistrar) RegisterHandlers(registry *cron.HandlerRegistry) error {
    if !r.IsEnabled() {
        r.logger.Debug("Notification cron is disabled, skipping handler registration")
        return nil
    }

    // Register notification cleanup handler
    cleanupHandler := handlers.NewNotificationCleanupHandler(r.db, r.config, r.logger)
    if err := registry.RegisterHandler(cleanupHandler); err != nil {
        return fmt.Errorf("failed to register notification cleanup handler: %w", err)
    }

    // Register email queue cleanup handler
    emailHandler := handlers.NewEmailQueueCleanupHandler(r.db, r.config, r.logger)
    if err := registry.RegisterHandler(emailHandler); err != nil {
        return fmt.Errorf("failed to register email queue cleanup handler: %w", err)
    }

    r.logger.Info("Notification cron handlers registered successfully")
    return nil
}

func (r *NotificationCronRegistrar) GetJobDefinitions() ([]*module.ModuleCronJobDefinition, error) {
    var jobs []*module.ModuleCronJobDefinition

    // Notification cleanup job
    if r.config.GetBool("CRON_NOTIFICATION_CLEANUP_ENABLED") {
        jobs = append(jobs, &module.ModuleCronJobDefinition{
            ID:          "notification_cleanup",
            Name:        "Notification Cleanup",
            Description: "Clean up old read notifications and failed deliveries",
            TaskType:    types.CronTaskNotificationCleanup,
            Schedule:    r.config.GetString("CRON_NOTIFICATION_CLEANUP_SCHEDULE"),
            Enabled:     true,
            Module:      "notification",
        })
    }

    // Email queue cleanup job
    if r.config.GetBool("CRON_NOTIFICATION_EMAIL_CLEANUP_ENABLED") {
        jobs = append(jobs, &module.ModuleCronJobDefinition{
            ID:          "notification_email_cleanup",
            Name:        "Email Queue Cleanup",
            Description: "Clean up processed email queue items",
            TaskType:    types.CronTaskNotificationEmailCleanup,
            Schedule:    r.config.GetString("CRON_NOTIFICATION_EMAIL_CLEANUP_SCHEDULE"),
            Enabled:     true,
            Module:      "notification",
        })
    }

    return jobs, nil
}
```

## File Paths

### Tạo mới:
- `modules/auth/cron/registrar.go`
- `modules/auth/cron/handlers/session_cleanup_handler.go`
- `modules/auth/cron/handlers/token_cleanup_handler.go`
- `modules/auth/cron/handlers/password_expiry_handler.go`
- `modules/notification/cron/registrar.go`
- `modules/notification/cron/handlers/cleanup_handler.go`
- `modules/notification/cron/handlers/email_cleanup_handler.go`
- `modules/media/cron/registrar.go`
- `modules/media/cron/handlers/optimization_handler.go`
- `modules/media/cron/handlers/temp_cleanup_handler.go`

### Cập nhật:
- `internal/pkg/queue/types/cron.go` (add new task types)
- `.env` (add module cron configurations)

## Acceptance Criteria

1. ✅ Auth module registrar và handlers được implement đầy đủ
2. ✅ Notification module registrar và handlers hoạt động
3. ✅ Media module registrar và handlers được tạo
4. ✅ Module-specific task types được định nghĩa
5. ✅ Configuration management cho từng module
6. ✅ Error handling và logging comprehensive
7. ✅ Code compile thành công với `make build`

## Dependencies
- Task 04: Registration System Implementation

## Next Task
[06-module-config.md](06-module-config.md) - Module configuration management
