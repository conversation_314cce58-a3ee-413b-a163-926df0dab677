# Task 01: Module Cron Structure Setup

## Objective
Thiết lập cấu trúc thư mục cron chuẩn trong các modules để hỗ trợ modular cron job system.

## Input
- Existing module structure trong `modules/`
- Current cron system trong `internal/pkg/queue/cron/`

## Output
- Cấu trúc thư mục `cron/` chuẩn trong modules
- Base interfaces và types cho module cron system
- Documentation về cấu trúc và conventions

## Requirements

### 1. C<PERSON>u trúc thư mục chuẩn
```
modules/{module}/cron/
├── handlers/           # Cron job handlers
│   ├── {module}_cleanup_handler.go
│   ├── {module}_maintenance_handler.go
│   └── {module}_notification_handler.go
├── jobs/              # Job definitions và schedules
│   ├── definitions.go  # Job definitions
│   └── schedules.go   # Default schedules
├── config.go          # Module-specific cron config
├── registrar.go       # Module cron registrar
└── README.md          # Module cron documentation
```

### 2. Base interfaces
- `ModuleCronRegistrar` interface
- `ModuleCronHandler` interface  
- `ModuleCronConfig` interface

### 3. Naming conventions
- Hand<PERSON> files: `{module}_{purpose}_handler.go`
- Job IDs: `{module}_{purpose}`
- Config keys: `CRON_{MODULE}_{JOB}_{SETTING}`

## Implementation Steps

### Step 1: Tạo base interfaces (30 phút)

**File**: `internal/pkg/module/cron_interfaces.go`

```go
package module

import (
    "context"
    "wnapi/internal/pkg/queue/cron"
    "wnapi/internal/pkg/queue/types"
)

// ModuleCronRegistrar interface cho module cron registration
type ModuleCronRegistrar interface {
    // GetModuleName trả về tên module
    GetModuleName() string
    
    // RegisterHandlers đăng ký cron handlers với registry
    RegisterHandlers(registry *cron.HandlerRegistry) error
    
    // GetJobDefinitions trả về danh sách job definitions
    GetJobDefinitions() ([]*ModuleCronJobDefinition, error)
    
    // IsEnabled kiểm tra module cron có enabled không
    IsEnabled() bool
}

// ModuleCronHandler interface cho module cron handlers
type ModuleCronHandler interface {
    cron.CronHandler
    
    // GetModuleName trả về tên module
    GetModuleName() string
    
    // GetJobID trả về unique job ID
    GetJobID() string
    
    // GetDefaultSchedule trả về default cron schedule
    GetDefaultSchedule() string
    
    // IsEnabledByDefault trả về default enabled state
    IsEnabledByDefault() bool
}

// ModuleCronJobDefinition định nghĩa một cron job của module
type ModuleCronJobDefinition struct {
    ID          string
    Name        string
    Description string
    TaskType    types.CronTaskType
    Schedule    string
    Enabled     bool
    Handler     ModuleCronHandler
    Module      string
}

// ModuleCronConfig interface cho module cron configuration
type ModuleCronConfig interface {
    // GetModuleName trả về tên module
    GetModuleName() string
    
    // IsEnabled kiểm tra cron có enabled cho module
    IsEnabled() bool
    
    // GetJobConfig trả về config cho specific job
    GetJobConfig(jobID string) map[string]interface{}
    
    // GetSchedule trả về schedule cho job
    GetSchedule(jobID string) string
    
    // IsJobEnabled kiểm tra job có enabled không
    IsJobEnabled(jobID string) bool
}
```

### Step 2: Tạo cấu trúc cho auth module (45 phút)

**Directory**: `modules/auth/cron/`

**File**: `modules/auth/cron/config.go`
```go
package cron

import (
    "fmt"
    "wnapi/internal/pkg/config"
    "wnapi/internal/pkg/module"
)

// AuthCronConfig implements ModuleCronConfig
type AuthCronConfig struct {
    config config.Config
}

func NewAuthCronConfig(cfg config.Config) *AuthCronConfig {
    return &AuthCronConfig{config: cfg}
}

func (c *AuthCronConfig) GetModuleName() string {
    return "auth"
}

func (c *AuthCronConfig) IsEnabled() bool {
    return c.config.GetBool("CRON_AUTH_ENABLED")
}

func (c *AuthCronConfig) GetJobConfig(jobID string) map[string]interface{} {
    prefix := fmt.Sprintf("CRON_AUTH_%s_", strings.ToUpper(jobID))
    return c.config.GetStringMap(prefix)
}

func (c *AuthCronConfig) GetSchedule(jobID string) string {
    key := fmt.Sprintf("CRON_AUTH_%s_SCHEDULE", strings.ToUpper(jobID))
    return c.config.GetString(key)
}

func (c *AuthCronConfig) IsJobEnabled(jobID string) bool {
    key := fmt.Sprintf("CRON_AUTH_%s_ENABLED", strings.ToUpper(jobID))
    return c.config.GetBool(key)
}
```

### Step 3: Tạo registrar cho auth module (45 phút)

**File**: `modules/auth/cron/registrar.go`
```go
package cron

import (
    "wnapi/internal/pkg/logger"
    "wnapi/internal/pkg/module"
    "wnapi/internal/pkg/queue/cron"
    "wnapi/modules/auth/cron/handlers"
)

// AuthCronRegistrar implements ModuleCronRegistrar
type AuthCronRegistrar struct {
    config module.ModuleCronConfig
    logger logger.Logger
}

func NewAuthCronRegistrar(config module.ModuleCronConfig, logger logger.Logger) *AuthCronRegistrar {
    return &AuthCronRegistrar{
        config: config,
        logger: logger,
    }
}

func (r *AuthCronRegistrar) GetModuleName() string {
    return "auth"
}

func (r *AuthCronRegistrar) IsEnabled() bool {
    return r.config.IsEnabled()
}

func (r *AuthCronRegistrar) RegisterHandlers(registry *cron.HandlerRegistry) error {
    if !r.IsEnabled() {
        r.logger.Debug("Auth cron is disabled, skipping handler registration")
        return nil
    }

    // Register session cleanup handler
    sessionHandler := handlers.NewAuthSessionCleanupHandler(r.config, r.logger)
    if err := registry.RegisterHandler(sessionHandler); err != nil {
        return fmt.Errorf("failed to register session cleanup handler: %w", err)
    }

    // Register password expiry handler
    passwordHandler := handlers.NewAuthPasswordExpiryHandler(r.config, r.logger)
    if err := registry.RegisterHandler(passwordHandler); err != nil {
        return fmt.Errorf("failed to register password expiry handler: %w", err)
    }

    r.logger.Info("Auth cron handlers registered successfully")
    return nil
}

func (r *AuthCronRegistrar) GetJobDefinitions() ([]*module.ModuleCronJobDefinition, error) {
    var jobs []*module.ModuleCronJobDefinition

    // Session cleanup job
    if r.config.IsJobEnabled("session_cleanup") {
        jobs = append(jobs, &module.ModuleCronJobDefinition{
            ID:          "auth_session_cleanup",
            Name:        "Auth Session Cleanup",
            Description: "Clean up expired and inactive user sessions",
            TaskType:    types.CronTaskAuthSessionCleanup,
            Schedule:    r.config.GetSchedule("session_cleanup"),
            Enabled:     true,
            Module:      "auth",
        })
    }

    // Password expiry job
    if r.config.IsJobEnabled("password_expiry") {
        jobs = append(jobs, &module.ModuleCronJobDefinition{
            ID:          "auth_password_expiry",
            Name:        "Auth Password Expiry Notification",
            Description: "Send password expiry notifications to users",
            TaskType:    types.CronTaskAuthPasswordExpiry,
            Schedule:    r.config.GetSchedule("password_expiry"),
            Enabled:     true,
            Module:      "auth",
        })
    }

    return jobs, nil
}
```

## File Paths

### Tạo mới:
- `internal/pkg/module/cron_interfaces.go`
- `modules/auth/cron/config.go`
- `modules/auth/cron/registrar.go`
- `modules/auth/cron/handlers/` (directory)
- `modules/auth/cron/jobs/` (directory)
- `modules/auth/cron/README.md`

### Cập nhật:
- `modules/auth/module.go` (thêm cron registrar)

## Acceptance Criteria

1. ✅ Cấu trúc thư mục `cron/` được tạo trong `modules/auth/`
2. ✅ Base interfaces được định nghĩa trong `internal/pkg/module/`
3. ✅ AuthCronConfig implement ModuleCronConfig interface
4. ✅ AuthCronRegistrar implement ModuleCronRegistrar interface
5. ✅ Naming conventions được tuân thủ
6. ✅ Code compile thành công với `make build`
7. ✅ Documentation được tạo trong README.md

## Dependencies
- None (base infrastructure task)

## Next Task
[02-cron-interfaces.md](02-cron-interfaces.md) - Hoàn thiện interfaces và base types
