# Task 07: Environment Integration & Hot-Reload

## Objective
Triển khai tích hợp với environment variables và hỗ trợ hot-reload configuration cho module cron system.

## Input
- Module configuration từ Task 06
- Existing config system với Viper
- Environment variable patterns

## Output
- Environment variable integration cho module cron
- Hot-reload capability cho cron configurations
- Configuration change detection và handling
- Runtime configuration updates

## Requirements

### 1. Environment integration
- Automatic environment variable loading
- Configuration precedence (env > file > defaults)
- Environment variable validation

### 2. Hot-reload support
- Configuration change detection
- Runtime job schedule updates
- Graceful job restart on config changes

### 3. Configuration management
- Configuration versioning
- Rollback capabilities
- Configuration audit logging

## Implementation Steps

### Step 1: Extend Viper config cho module cron (30 phút)

**File**: `internal/pkg/config/viperconfig/viper.go` (extend existing)

```go
// Add module cron defaults to setDefaults function
func (c *ViperConfig) setDefaults() {
    // ... existing defaults ...
    
    // Module cron defaults
    c.setModuleCronDefaults()
}

func (c *ViperConfig) setModuleCronDefaults() {
    // Auth module defaults
    c.viper.SetDefault("MODULE_AUTH_ENABLED", true)
    c.viper.SetDefault("CRON_AUTH_ENABLED", true)
    
    // Auth session cleanup
    c.viper.SetDefault("CRON_AUTH_SESSION_CLEANUP_ENABLED", true)
    c.viper.SetDefault("CRON_AUTH_SESSION_CLEANUP_SCHEDULE", "0 2 * * *")
    c.viper.SetDefault("AUTH_SESSION_MAX_INACTIVE_HOURS", 168)
    
    // Auth password expiry
    c.viper.SetDefault("CRON_AUTH_PASSWORD_EXPIRY_ENABLED", true)
    c.viper.SetDefault("CRON_AUTH_PASSWORD_EXPIRY_SCHEDULE", "0 9 * * *")
    c.viper.SetDefault("AUTH_PASSWORD_EXPIRY_NOTIFY_DAYS", []int{30, 7, 1})
    
    // Auth token cleanup
    c.viper.SetDefault("CRON_AUTH_TOKEN_CLEANUP_ENABLED", true)
    c.viper.SetDefault("CRON_AUTH_TOKEN_CLEANUP_SCHEDULE", "0 3 * * *")
    c.viper.SetDefault("AUTH_TOKEN_RETENTION_DAYS", 30)
    
    // Notification module defaults
    c.viper.SetDefault("MODULE_NOTIFICATION_ENABLED", true)
    c.viper.SetDefault("CRON_NOTIFICATION_ENABLED", true)
    
    // Notification cleanup
    c.viper.SetDefault("CRON_NOTIFICATION_CLEANUP_ENABLED", true)
    c.viper.SetDefault("CRON_NOTIFICATION_CLEANUP_SCHEDULE", "0 1 * * *")
    c.viper.SetDefault("NOTIFICATION_RETENTION_DAYS", 90)
    
    // Email cleanup
    c.viper.SetDefault("CRON_NOTIFICATION_EMAIL_CLEANUP_ENABLED", true)
    c.viper.SetDefault("CRON_NOTIFICATION_EMAIL_CLEANUP_SCHEDULE", "0 4 * * *")
    c.viper.SetDefault("EMAIL_QUEUE_RETENTION_DAYS", 7)
    
    // Media module defaults
    c.viper.SetDefault("MODULE_MEDIA_ENABLED", true)
    c.viper.SetDefault("CRON_MEDIA_ENABLED", true)
    
    // Media image optimization
    c.viper.SetDefault("CRON_MEDIA_IMAGE_OPTIMIZATION_ENABLED", false)
    c.viper.SetDefault("CRON_MEDIA_IMAGE_OPTIMIZATION_SCHEDULE", "0 3 * * *")
    c.viper.SetDefault("MEDIA_OPTIMIZATION_BATCH_SIZE", 100)
    
    // Media temp cleanup
    c.viper.SetDefault("CRON_MEDIA_TEMP_CLEANUP_ENABLED", true)
    c.viper.SetDefault("CRON_MEDIA_TEMP_CLEANUP_SCHEDULE", "0 */6 * * *")
    c.viper.SetDefault("MEDIA_TEMP_MAX_AGE_HOURS", 24)
}

// GetModuleCronConfig returns module cron configuration
func (c *ViperConfig) GetModuleCronConfig() ModuleCronConfig {
    return ModuleCronConfig{
        Auth: AuthModuleCronConfig{
            Enabled: c.viper.GetBool("CRON_AUTH_ENABLED"),
            SessionCleanup: JobConfig{
                Enabled:  c.viper.GetBool("CRON_AUTH_SESSION_CLEANUP_ENABLED"),
                Schedule: c.viper.GetString("CRON_AUTH_SESSION_CLEANUP_SCHEDULE"),
                Settings: map[string]interface{}{
                    "max_inactive_hours": c.viper.GetInt("AUTH_SESSION_MAX_INACTIVE_HOURS"),
                },
            },
            PasswordExpiry: JobConfig{
                Enabled:  c.viper.GetBool("CRON_AUTH_PASSWORD_EXPIRY_ENABLED"),
                Schedule: c.viper.GetString("CRON_AUTH_PASSWORD_EXPIRY_SCHEDULE"),
                Settings: map[string]interface{}{
                    "notify_days": c.viper.GetIntSlice("AUTH_PASSWORD_EXPIRY_NOTIFY_DAYS"),
                },
            },
            TokenCleanup: JobConfig{
                Enabled:  c.viper.GetBool("CRON_AUTH_TOKEN_CLEANUP_ENABLED"),
                Schedule: c.viper.GetString("CRON_AUTH_TOKEN_CLEANUP_SCHEDULE"),
                Settings: map[string]interface{}{
                    "retention_days": c.viper.GetInt("AUTH_TOKEN_RETENTION_DAYS"),
                },
            },
        },
        Notification: NotificationModuleCronConfig{
            Enabled: c.viper.GetBool("CRON_NOTIFICATION_ENABLED"),
            Cleanup: JobConfig{
                Enabled:  c.viper.GetBool("CRON_NOTIFICATION_CLEANUP_ENABLED"),
                Schedule: c.viper.GetString("CRON_NOTIFICATION_CLEANUP_SCHEDULE"),
                Settings: map[string]interface{}{
                    "retention_days": c.viper.GetInt("NOTIFICATION_RETENTION_DAYS"),
                },
            },
            EmailCleanup: JobConfig{
                Enabled:  c.viper.GetBool("CRON_NOTIFICATION_EMAIL_CLEANUP_ENABLED"),
                Schedule: c.viper.GetString("CRON_NOTIFICATION_EMAIL_CLEANUP_SCHEDULE"),
                Settings: map[string]interface{}{
                    "retention_days": c.viper.GetInt("EMAIL_QUEUE_RETENTION_DAYS"),
                },
            },
        },
        Media: MediaModuleCronConfig{
            Enabled: c.viper.GetBool("CRON_MEDIA_ENABLED"),
            ImageOptimization: JobConfig{
                Enabled:  c.viper.GetBool("CRON_MEDIA_IMAGE_OPTIMIZATION_ENABLED"),
                Schedule: c.viper.GetString("CRON_MEDIA_IMAGE_OPTIMIZATION_SCHEDULE"),
                Settings: map[string]interface{}{
                    "batch_size": c.viper.GetInt("MEDIA_OPTIMIZATION_BATCH_SIZE"),
                },
            },
            TempCleanup: JobConfig{
                Enabled:  c.viper.GetBool("CRON_MEDIA_TEMP_CLEANUP_ENABLED"),
                Schedule: c.viper.GetString("CRON_MEDIA_TEMP_CLEANUP_SCHEDULE"),
                Settings: map[string]interface{}{
                    "max_age_hours": c.viper.GetInt("MEDIA_TEMP_MAX_AGE_HOURS"),
                },
            },
        },
    }
}

// Configuration structs
type ModuleCronConfig struct {
    Auth         AuthModuleCronConfig         `json:"auth"`
    Notification NotificationModuleCronConfig `json:"notification"`
    Media        MediaModuleCronConfig        `json:"media"`
}

type AuthModuleCronConfig struct {
    Enabled        bool      `json:"enabled"`
    SessionCleanup JobConfig `json:"session_cleanup"`
    PasswordExpiry JobConfig `json:"password_expiry"`
    TokenCleanup   JobConfig `json:"token_cleanup"`
}

type NotificationModuleCronConfig struct {
    Enabled      bool      `json:"enabled"`
    Cleanup      JobConfig `json:"cleanup"`
    EmailCleanup JobConfig `json:"email_cleanup"`
}

type MediaModuleCronConfig struct {
    Enabled           bool      `json:"enabled"`
    ImageOptimization JobConfig `json:"image_optimization"`
    TempCleanup       JobConfig `json:"temp_cleanup"`
}

type JobConfig struct {
    Enabled  bool                   `json:"enabled"`
    Schedule string                 `json:"schedule"`
    Settings map[string]interface{} `json:"settings"`
}
```

### Step 2: Configuration hot-reload system (45 phút)

**File**: `internal/pkg/config/hotreload.go` (new file)

```go
package config

import (
    "context"
    "fmt"
    "sync"
    "time"
    
    "github.com/fsnotify/fsnotify"
    "wnapi/internal/pkg/logger"
)

// HotReloadManager manages configuration hot-reload
type HotReloadManager struct {
    config         Config
    logger         logger.Logger
    watcher        *fsnotify.Watcher
    configFile     string
    
    // Callbacks
    callbacks      map[string][]ConfigChangeCallback
    callbacksMu    sync.RWMutex
    
    // State
    isWatching     bool
    watchingMu     sync.RWMutex
    lastReload     time.Time
}

// ConfigChangeCallback function type for configuration change notifications
type ConfigChangeCallback func(ctx context.Context, changes ConfigChanges) error

// ConfigChanges represents configuration changes
type ConfigChanges struct {
    ModuleName    string                 `json:"module_name"`
    ChangedKeys   []string               `json:"changed_keys"`
    OldValues     map[string]interface{} `json:"old_values"`
    NewValues     map[string]interface{} `json:"new_values"`
    Timestamp     time.Time              `json:"timestamp"`
}

func NewHotReloadManager(config Config, logger logger.Logger, configFile string) (*HotReloadManager, error) {
    watcher, err := fsnotify.NewWatcher()
    if err != nil {
        return nil, fmt.Errorf("failed to create file watcher: %w", err)
    }
    
    return &HotReloadManager{
        config:     config,
        logger:     logger,
        watcher:    watcher,
        configFile: configFile,
        callbacks:  make(map[string][]ConfigChangeCallback),
    }, nil
}

// StartWatching starts watching for configuration changes
func (h *HotReloadManager) StartWatching(ctx context.Context) error {
    h.watchingMu.Lock()
    defer h.watchingMu.Unlock()
    
    if h.isWatching {
        return fmt.Errorf("already watching configuration file")
    }
    
    // Add config file to watcher
    if err := h.watcher.Add(h.configFile); err != nil {
        return fmt.Errorf("failed to watch config file: %w", err)
    }
    
    h.isWatching = true
    h.logger.Info("Started configuration hot-reload watching", "file", h.configFile)
    
    // Start watching goroutine
    go h.watchLoop(ctx)
    
    return nil
}

// StopWatching stops watching for configuration changes
func (h *HotReloadManager) StopWatching() error {
    h.watchingMu.Lock()
    defer h.watchingMu.Unlock()
    
    if !h.isWatching {
        return nil
    }
    
    if err := h.watcher.Close(); err != nil {
        return fmt.Errorf("failed to close watcher: %w", err)
    }
    
    h.isWatching = false
    h.logger.Info("Stopped configuration hot-reload watching")
    
    return nil
}

// RegisterCallback registers a callback for configuration changes
func (h *HotReloadManager) RegisterCallback(moduleName string, callback ConfigChangeCallback) {
    h.callbacksMu.Lock()
    defer h.callbacksMu.Unlock()
    
    h.callbacks[moduleName] = append(h.callbacks[moduleName], callback)
    h.logger.Debug("Registered config change callback", "module", moduleName)
}

func (h *HotReloadManager) watchLoop(ctx context.Context) {
    for {
        select {
        case <-ctx.Done():
            h.logger.Info("Configuration watcher stopped due to context cancellation")
            return
            
        case event, ok := <-h.watcher.Events:
            if !ok {
                h.logger.Error("Configuration watcher events channel closed")
                return
            }
            
            if event.Op&fsnotify.Write == fsnotify.Write {
                h.logger.Debug("Configuration file changed", "file", event.Name)
                
                // Debounce rapid changes
                if time.Since(h.lastReload) < 1*time.Second {
                    continue
                }
                
                if err := h.handleConfigChange(ctx); err != nil {
                    h.logger.Error("Failed to handle configuration change", "error", err)
                }
                
                h.lastReload = time.Now()
            }
            
        case err, ok := <-h.watcher.Errors:
            if !ok {
                h.logger.Error("Configuration watcher errors channel closed")
                return
            }
            
            h.logger.Error("Configuration watcher error", "error", err)
        }
    }
}

func (h *HotReloadManager) handleConfigChange(ctx context.Context) error {
    h.logger.Info("Handling configuration change")
    
    // Reload configuration
    oldConfig := h.config
    if err := h.config.Reload(); err != nil {
        return fmt.Errorf("failed to reload configuration: %w", err)
    }
    
    // Detect changes
    changes := h.detectChanges(oldConfig, h.config)
    
    // Notify callbacks
    for moduleName, moduleChanges := range changes {
        callbacks := h.getCallbacks(moduleName)
        for _, callback := range callbacks {
            if err := callback(ctx, moduleChanges); err != nil {
                h.logger.Error("Configuration change callback failed", 
                    "module", moduleName, 
                    "error", err)
            }
        }
    }
    
    h.logger.Info("Configuration change handled successfully", "modules_affected", len(changes))
    return nil
}

func (h *HotReloadManager) detectChanges(oldConfig, newConfig Config) map[string]ConfigChanges {
    changes := make(map[string]ConfigChanges)
    
    // Check auth module changes
    if authChanges := h.detectAuthChanges(oldConfig, newConfig); len(authChanges.ChangedKeys) > 0 {
        changes["auth"] = authChanges
    }
    
    // Check notification module changes
    if notificationChanges := h.detectNotificationChanges(oldConfig, newConfig); len(notificationChanges.ChangedKeys) > 0 {
        changes["notification"] = notificationChanges
    }
    
    // Check media module changes
    if mediaChanges := h.detectMediaChanges(oldConfig, newConfig); len(mediaChanges.ChangedKeys) > 0 {
        changes["media"] = mediaChanges
    }
    
    return changes
}

func (h *HotReloadManager) detectAuthChanges(oldConfig, newConfig Config) ConfigChanges {
    changes := ConfigChanges{
        ModuleName:  "auth",
        ChangedKeys: []string{},
        OldValues:   make(map[string]interface{}),
        NewValues:   make(map[string]interface{}),
        Timestamp:   time.Now(),
    }
    
    // Check auth cron enabled
    if oldConfig.GetBool("CRON_AUTH_ENABLED") != newConfig.GetBool("CRON_AUTH_ENABLED") {
        changes.ChangedKeys = append(changes.ChangedKeys, "CRON_AUTH_ENABLED")
        changes.OldValues["CRON_AUTH_ENABLED"] = oldConfig.GetBool("CRON_AUTH_ENABLED")
        changes.NewValues["CRON_AUTH_ENABLED"] = newConfig.GetBool("CRON_AUTH_ENABLED")
    }
    
    // Check session cleanup changes
    authKeys := []string{
        "CRON_AUTH_SESSION_CLEANUP_ENABLED",
        "CRON_AUTH_SESSION_CLEANUP_SCHEDULE",
        "AUTH_SESSION_MAX_INACTIVE_HOURS",
        "CRON_AUTH_PASSWORD_EXPIRY_ENABLED",
        "CRON_AUTH_PASSWORD_EXPIRY_SCHEDULE",
        "CRON_AUTH_TOKEN_CLEANUP_ENABLED",
        "CRON_AUTH_TOKEN_CLEANUP_SCHEDULE",
        "AUTH_TOKEN_RETENTION_DAYS",
    }
    
    for _, key := range authKeys {
        oldVal := oldConfig.Get(key)
        newVal := newConfig.Get(key)
        
        if fmt.Sprintf("%v", oldVal) != fmt.Sprintf("%v", newVal) {
            changes.ChangedKeys = append(changes.ChangedKeys, key)
            changes.OldValues[key] = oldVal
            changes.NewValues[key] = newVal
        }
    }
    
    return changes
}

func (h *HotReloadManager) detectNotificationChanges(oldConfig, newConfig Config) ConfigChanges {
    // Similar implementation for notification module
    return ConfigChanges{ModuleName: "notification", Timestamp: time.Now()}
}

func (h *HotReloadManager) detectMediaChanges(oldConfig, newConfig Config) ConfigChanges {
    // Similar implementation for media module
    return ConfigChanges{ModuleName: "media", Timestamp: time.Now()}
}

func (h *HotReloadManager) getCallbacks(moduleName string) []ConfigChangeCallback {
    h.callbacksMu.RLock()
    defer h.callbacksMu.RUnlock()
    
    callbacks := make([]ConfigChangeCallback, len(h.callbacks[moduleName]))
    copy(callbacks, h.callbacks[moduleName])
    
    return callbacks
}
```

### Step 3: Cron manager hot-reload integration (30 phút)

**File**: `internal/pkg/queue/cron/hotreload_handler.go` (new file)

```go
package cron

import (
    "context"
    "fmt"
    
    "wnapi/internal/pkg/config"
    "wnapi/internal/pkg/logger"
)

// CronHotReloadHandler handles configuration changes for cron jobs
type CronHotReloadHandler struct {
    manager *Manager
    logger  logger.Logger
}

func NewCronHotReloadHandler(manager *Manager, logger logger.Logger) *CronHotReloadHandler {
    return &CronHotReloadHandler{
        manager: manager,
        logger:  logger,
    }
}

// HandleConfigChange handles configuration changes for cron module
func (h *CronHotReloadHandler) HandleConfigChange(ctx context.Context, changes config.ConfigChanges) error {
    h.logger.Info("Handling cron configuration change", 
        "module", changes.ModuleName,
        "changed_keys", len(changes.ChangedKeys))
    
    switch changes.ModuleName {
    case "auth":
        return h.handleAuthConfigChange(ctx, changes)
    case "notification":
        return h.handleNotificationConfigChange(ctx, changes)
    case "media":
        return h.handleMediaConfigChange(ctx, changes)
    default:
        h.logger.Debug("Unknown module for cron config change", "module", changes.ModuleName)
        return nil
    }
}

func (h *CronHotReloadHandler) handleAuthConfigChange(ctx context.Context, changes config.ConfigChanges) error {
    for _, key := range changes.ChangedKeys {
        switch {
        case key == "CRON_AUTH_ENABLED":
            return h.handleModuleEnabledChange("auth", changes.NewValues[key].(bool))
            
        case key == "CRON_AUTH_SESSION_CLEANUP_SCHEDULE":
            return h.handleJobScheduleChange("auth_session_cleanup", changes.NewValues[key].(string))
            
        case key == "CRON_AUTH_PASSWORD_EXPIRY_SCHEDULE":
            return h.handleJobScheduleChange("auth_password_expiry", changes.NewValues[key].(string))
            
        case key == "CRON_AUTH_TOKEN_CLEANUP_SCHEDULE":
            return h.handleJobScheduleChange("auth_token_cleanup", changes.NewValues[key].(string))
            
        case key == "CRON_AUTH_SESSION_CLEANUP_ENABLED":
            return h.handleJobEnabledChange("auth_session_cleanup", changes.NewValues[key].(bool))
            
        case key == "CRON_AUTH_PASSWORD_EXPIRY_ENABLED":
            return h.handleJobEnabledChange("auth_password_expiry", changes.NewValues[key].(bool))
            
        case key == "CRON_AUTH_TOKEN_CLEANUP_ENABLED":
            return h.handleJobEnabledChange("auth_token_cleanup", changes.NewValues[key].(bool))
        }
    }
    
    return nil
}

func (h *CronHotReloadHandler) handleNotificationConfigChange(ctx context.Context, changes config.ConfigChanges) error {
    // Similar implementation for notification module
    return nil
}

func (h *CronHotReloadHandler) handleMediaConfigChange(ctx context.Context, changes config.ConfigChanges) error {
    // Similar implementation for media module
    return nil
}

func (h *CronHotReloadHandler) handleModuleEnabledChange(moduleName string, enabled bool) error {
    if enabled {
        h.logger.Info("Enabling module cron jobs", "module", moduleName)
        return h.manager.EnableModuleJobs(moduleName)
    } else {
        h.logger.Info("Disabling module cron jobs", "module", moduleName)
        return h.manager.DisableModuleJobs(moduleName)
    }
}

func (h *CronHotReloadHandler) handleJobScheduleChange(jobID, newSchedule string) error {
    h.logger.Info("Updating job schedule", "job", jobID, "schedule", newSchedule)
    
    if err := h.manager.scheduler.UpdateModuleJobSchedule(jobID, newSchedule); err != nil {
        return fmt.Errorf("failed to update job schedule: %w", err)
    }
    
    return nil
}

func (h *CronHotReloadHandler) handleJobEnabledChange(jobID string, enabled bool) error {
    if enabled {
        h.logger.Info("Enabling job", "job", jobID)
        return h.manager.EnableModuleJob(jobID)
    } else {
        h.logger.Info("Disabling job", "job", jobID)
        return h.manager.DisableModuleJob(jobID)
    }
}
```

## File Paths

### Tạo mới:
- `internal/pkg/config/hotreload.go`
- `internal/pkg/queue/cron/hotreload_handler.go`

### Cập nhật:
- `internal/pkg/config/viperconfig/viper.go` (add module defaults)
- `internal/pkg/queue/cron/manager.go` (integrate hot-reload)
- `cmd/server/main.go` (setup hot-reload)

## Acceptance Criteria

1. ✅ Environment variables được load tự động cho module cron
2. ✅ Hot-reload hoạt động khi config file thay đổi
3. ✅ Job schedules được update runtime
4. ✅ Module enable/disable hoạt động dynamically
5. ✅ Configuration change detection accurate
6. ✅ Error handling và rollback capabilities
7. ✅ Code compile thành công với `make build`

## Dependencies
- Task 06: Module Configuration Management

## Next Task
[08-auth-module-example.md](08-auth-module-example.md) - Complete auth module implementation
