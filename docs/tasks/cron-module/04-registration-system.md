# Task 04: Registration System Implementation

## Objective
<PERSON>ể<PERSON> khai hệ thống đăng ký cron jobs tự động cho modules, tích hợp với existing CronManager và scheduler.

## Input
- Auto-discovery system từ Task 03
- Enhanced interfaces từ Task 02
- Existing CronManager và scheduler trong `internal/pkg/queue/cron/`

## Output
- Automated registration system cho module cron jobs
- Integration với existing job scheduler
- Job lifecycle management (start/stop/restart)
- Registration validation và error handling

## Requirements

### 1. Automated registration
- Tự động đăng ký jobs khi module được discover
- Validate job definitions và schedules
- Handle registration conflicts

### 2. Job lifecycle management
- Start/stop individual module jobs
- Restart failed jobs
- Update job schedules dynamically

### 3. Integration với existing system
- Tương thích với current CronManager
- Extend scheduler cho module jobs
- Maintain backward compatibility

## Implementation Steps

### Step 1: Extend CronManager cho module support (45 phút)

**File**: `internal/pkg/queue/cron/manager.go` (extend existing)

```go
// Add to existing Manager struct
type Manager struct {
    // ... existing fields ...
    
    // Module support
    moduleManager    module.ModuleCronManager
    moduleDiscovery  module.ModuleDiscovery
    moduleJobs       map[string]*CronJob  // module job tracking
    moduleJobsMu     sync.RWMutex
}

// Add to NewManager function
func NewManager(
    db *gorm.DB,
    config config.Config,
    logger *slog.Logger,
    queueManager queue.Manager,
) (*Manager, error) {
    // ... existing initialization ...
    
    // Initialize module support
    moduleManager := NewModuleCronManager(manager, registry, logger)
    moduleDiscovery := module.NewModuleDiscovery(logger)
    
    // Register cron discovery hook
    cronHook := NewCronDiscoveryHook(moduleManager, logger)
    if err := moduleDiscovery.RegisterDiscoveryHook(cronHook); err != nil {
        return nil, fmt.Errorf("failed to register cron discovery hook: %w", err)
    }
    
    manager.moduleManager = moduleManager
    manager.moduleDiscovery = moduleDiscovery
    manager.moduleJobs = make(map[string]*CronJob)
    
    return manager, nil
}

// RegisterModuleJobs đăng ký jobs từ modules
func (m *Manager) RegisterModuleJobs(ctx context.Context) error {
    m.logger.Info("Registering module cron jobs")
    
    // Load modules
    moduleLoader := module.NewModuleLoader(m.config, m.logger, m.moduleDiscovery)
    modules, err := moduleLoader.LoadModules(ctx, "modules")
    if err != nil {
        return fmt.Errorf("failed to load modules: %w", err)
    }
    
    // Register jobs from discovered modules
    registeredModules := m.moduleManager.GetRegisteredModules()
    for _, moduleName := range registeredModules {
        if err := m.registerModuleJobsForModule(moduleName); err != nil {
            m.logger.Error("Failed to register jobs for module", 
                "module", moduleName, 
                "error", err)
            continue
        }
    }
    
    m.logger.Info("Module cron jobs registered successfully", 
        "modules_count", len(registeredModules))
    
    return nil
}

func (m *Manager) registerModuleJobsForModule(moduleName string) error {
    jobs, err := m.moduleManager.GetModuleJobs(moduleName)
    if err != nil {
        return fmt.Errorf("failed to get jobs for module %s: %w", moduleName, err)
    }
    
    for _, jobDef := range jobs {
        if !jobDef.Enabled {
            m.logger.Debug("Module job is disabled, skipping", 
                "module", moduleName, 
                "job", jobDef.ID)
            continue
        }
        
        // Get handler from registry
        handler, err := m.registry.GetHandler(jobDef.TaskType)
        if err != nil {
            m.logger.Error("Handler not found for module job", 
                "module", moduleName, 
                "job", jobDef.ID, 
                "task_type", jobDef.TaskType)
            continue
        }
        
        // Create cron job
        cronJob := &CronJob{
            ID:          jobDef.ID,
            Name:        jobDef.Name,
            TaskType:    jobDef.TaskType,
            Schedule:    jobDef.Schedule,
            Enabled:     jobDef.Enabled,
            Handler:     handler,
            Description: jobDef.Description,
            Module:      jobDef.Module,
        }
        
        // Register with scheduler
        if err := m.scheduler.AddJob(cronJob); err != nil {
            return fmt.Errorf("failed to add module job to scheduler: %w", err)
        }
        
        // Track module job
        m.moduleJobsMu.Lock()
        m.moduleJobs[jobDef.ID] = cronJob
        m.moduleJobsMu.Unlock()
        
        m.logger.Info("Module job registered", 
            "module", moduleName, 
            "job", jobDef.ID, 
            "schedule", jobDef.Schedule)
    }
    
    return nil
}

// GetModuleJobs trả về danh sách module jobs
func (m *Manager) GetModuleJobs() []*CronJob {
    m.moduleJobsMu.RLock()
    defer m.moduleJobsMu.RUnlock()
    
    var jobs []*CronJob
    for _, job := range m.moduleJobs {
        jobs = append(jobs, job)
    }
    
    return jobs
}

// GetModuleJobsByModule trả về jobs của specific module
func (m *Manager) GetModuleJobsByModule(moduleName string) []*CronJob {
    m.moduleJobsMu.RLock()
    defer m.moduleJobsMu.RUnlock()
    
    var jobs []*CronJob
    for _, job := range m.moduleJobs {
        if job.Module == moduleName {
            jobs = append(jobs, job)
        }
    }
    
    return jobs
}

// EnableModuleJob enable một module job
func (m *Manager) EnableModuleJob(jobID string) error {
    m.moduleJobsMu.Lock()
    defer m.moduleJobsMu.Unlock()
    
    job, exists := m.moduleJobs[jobID]
    if !exists {
        return fmt.Errorf("module job not found: %s", jobID)
    }
    
    if job.Enabled {
        return fmt.Errorf("module job is already enabled: %s", jobID)
    }
    
    // Enable in scheduler
    if err := m.scheduler.EnableJob(jobID); err != nil {
        return fmt.Errorf("failed to enable job in scheduler: %w", err)
    }
    
    job.Enabled = true
    
    m.logger.Info("Module job enabled", "job", jobID, "module", job.Module)
    return nil
}

// DisableModuleJob disable một module job
func (m *Manager) DisableModuleJob(jobID string) error {
    m.moduleJobsMu.Lock()
    defer m.moduleJobsMu.Unlock()
    
    job, exists := m.moduleJobs[jobID]
    if !exists {
        return fmt.Errorf("module job not found: %s", jobID)
    }
    
    if !job.Enabled {
        return fmt.Errorf("module job is already disabled: %s", jobID)
    }
    
    // Disable in scheduler
    if err := m.scheduler.DisableJob(jobID); err != nil {
        return fmt.Errorf("failed to disable job in scheduler: %w", err)
    }
    
    job.Enabled = false
    
    m.logger.Info("Module job disabled", "job", jobID, "module", job.Module)
    return nil
}
```

### Step 2: Extend scheduler cho module jobs (45 phút)

**File**: `internal/pkg/queue/cron/scheduler.go` (extend existing)

```go
// Add to existing Scheduler struct
type Scheduler struct {
    // ... existing fields ...
    
    // Module job tracking
    moduleJobs    map[string]*cron.Entry
    moduleJobsMu  sync.RWMutex
}

// Extend AddJob method để support module jobs
func (s *Scheduler) AddJob(job *CronJob) error {
    s.mu.Lock()
    defer s.mu.Unlock()
    
    // Check if job already exists
    if _, exists := s.jobs[job.ID]; exists {
        return fmt.Errorf("job already exists: %s", job.ID)
    }
    
    // Create cron job function
    jobFunc := func() {
        s.executeJob(job)
    }
    
    // Add to cron scheduler
    entryID, err := s.cron.AddFunc(job.Schedule, jobFunc)
    if err != nil {
        return fmt.Errorf("failed to add job to cron: %w", err)
    }
    
    // Store job info
    s.jobs[job.ID] = job
    s.entries[job.ID] = entryID
    
    // Track module jobs separately
    if job.Module != "" {
        s.moduleJobsMu.Lock()
        entry := s.cron.Entry(entryID)
        s.moduleJobs[job.ID] = &entry
        s.moduleJobsMu.Unlock()
        
        s.logger.Debug("Module job added to scheduler", 
            "job", job.ID, 
            "module", job.Module,
            "schedule", job.Schedule)
    }
    
    s.logger.Info("Job added to scheduler", 
        "job", job.ID, 
        "schedule", job.Schedule,
        "module", job.Module)
    
    return nil
}

// GetModuleJobEntries trả về cron entries cho module jobs
func (s *Scheduler) GetModuleJobEntries() map[string]*cron.Entry {
    s.moduleJobsMu.RLock()
    defer s.moduleJobsMu.RUnlock()
    
    entries := make(map[string]*cron.Entry)
    for jobID, entry := range s.moduleJobs {
        entries[jobID] = entry
    }
    
    return entries
}

// GetModuleJobStatus trả về status của module jobs
func (s *Scheduler) GetModuleJobStatus() map[string]JobStatus {
    s.moduleJobsMu.RLock()
    defer s.moduleJobsMu.RUnlock()
    
    status := make(map[string]JobStatus)
    
    for jobID, entry := range s.moduleJobs {
        job, exists := s.jobs[jobID]
        if !exists {
            continue
        }
        
        status[jobID] = JobStatus{
            JobID:    jobID,
            Module:   job.Module,
            IsActive: job.Enabled,
            NextRun:  entry.Next,
            Schedule: job.Schedule,
        }
    }
    
    return status
}

// RestartModuleJob restart một module job
func (s *Scheduler) RestartModuleJob(jobID string) error {
    s.mu.Lock()
    defer s.mu.Unlock()
    
    job, exists := s.jobs[jobID]
    if !exists {
        return fmt.Errorf("job not found: %s", jobID)
    }
    
    if job.Module == "" {
        return fmt.Errorf("job is not a module job: %s", jobID)
    }
    
    // Remove existing job
    if err := s.removeJobInternal(jobID); err != nil {
        return fmt.Errorf("failed to remove job: %w", err)
    }
    
    // Re-add job
    if err := s.AddJob(job); err != nil {
        return fmt.Errorf("failed to re-add job: %w", err)
    }
    
    s.logger.Info("Module job restarted", "job", jobID, "module", job.Module)
    return nil
}

// UpdateModuleJobSchedule update schedule của module job
func (s *Scheduler) UpdateModuleJobSchedule(jobID, newSchedule string) error {
    s.mu.Lock()
    defer s.mu.Unlock()
    
    job, exists := s.jobs[jobID]
    if !exists {
        return fmt.Errorf("job not found: %s", jobID)
    }
    
    if job.Module == "" {
        return fmt.Errorf("job is not a module job: %s", jobID)
    }
    
    // Validate new schedule
    if _, err := cron.ParseStandard(newSchedule); err != nil {
        return fmt.Errorf("invalid cron schedule: %w", err)
    }
    
    // Update job schedule
    oldSchedule := job.Schedule
    job.Schedule = newSchedule
    
    // Restart job with new schedule
    if err := s.RestartModuleJob(jobID); err != nil {
        // Rollback on failure
        job.Schedule = oldSchedule
        return fmt.Errorf("failed to restart job with new schedule: %w", err)
    }
    
    s.logger.Info("Module job schedule updated", 
        "job", jobID, 
        "module", job.Module,
        "old_schedule", oldSchedule,
        "new_schedule", newSchedule)
    
    return nil
}
```

### Step 3: CLI integration cho module jobs (30 phút)

**File**: `cmd/cli/commands/cron.go` (extend existing)

```go
// Add module-specific commands

// listModuleJobsCmd lists module cron jobs
var listModuleJobsCmd = &cobra.Command{
    Use:   "list-module [module-name]",
    Short: "List cron jobs for specific module or all modules",
    Args:  cobra.MaximumNArgs(1),
    RunE: func(cmd *cobra.Command, args []string) error {
        manager, err := initializeCronManager()
        if err != nil {
            return err
        }
        
        var moduleName string
        if len(args) > 0 {
            moduleName = args[0]
        }
        
        if moduleName != "" {
            return listModuleJobs(manager, moduleName)
        }
        
        return listAllModuleJobs(manager)
    },
}

// enableModuleJobCmd enables a module job
var enableModuleJobCmd = &cobra.Command{
    Use:   "enable-module-job [job-id]",
    Short: "Enable a module cron job",
    Args:  cobra.ExactArgs(1),
    RunE: func(cmd *cobra.Command, args []string) error {
        manager, err := initializeCronManager()
        if err != nil {
            return err
        }
        
        jobID := args[0]
        if err := manager.EnableModuleJob(jobID); err != nil {
            return fmt.Errorf("failed to enable module job: %w", err)
        }
        
        fmt.Printf("Module job '%s' enabled successfully\n", jobID)
        return nil
    },
}

// disableModuleJobCmd disables a module job
var disableModuleJobCmd = &cobra.Command{
    Use:   "disable-module-job [job-id]",
    Short: "Disable a module cron job",
    Args:  cobra.ExactArgs(1),
    RunE: func(cmd *cobra.Command, args []string) error {
        manager, err := initializeCronManager()
        if err != nil {
            return err
        }
        
        jobID := args[0]
        if err := manager.DisableModuleJob(jobID); err != nil {
            return fmt.Errorf("failed to disable module job: %w", err)
        }
        
        fmt.Printf("Module job '%s' disabled successfully\n", jobID)
        return nil
    },
}

func listModuleJobs(manager *cron.Manager, moduleName string) error {
    jobs := manager.GetModuleJobsByModule(moduleName)
    
    if len(jobs) == 0 {
        fmt.Printf("No cron jobs found for module: %s\n", moduleName)
        return nil
    }
    
    fmt.Printf("Cron jobs for module '%s':\n\n", moduleName)
    
    for _, job := range jobs {
        status := "Disabled"
        if job.Enabled {
            status = "Enabled"
        }
        
        fmt.Printf("ID: %s\n", job.ID)
        fmt.Printf("Name: %s\n", job.Name)
        fmt.Printf("Schedule: %s\n", job.Schedule)
        fmt.Printf("Status: %s\n", status)
        fmt.Printf("Description: %s\n", job.Description)
        fmt.Printf("Task Type: %s\n", job.TaskType)
        fmt.Println("---")
    }
    
    return nil
}

func listAllModuleJobs(manager *cron.Manager) error {
    jobs := manager.GetModuleJobs()
    
    if len(jobs) == 0 {
        fmt.Println("No module cron jobs found")
        return nil
    }
    
    // Group jobs by module
    jobsByModule := make(map[string][]*cron.CronJob)
    for _, job := range jobs {
        jobsByModule[job.Module] = append(jobsByModule[job.Module], job)
    }
    
    fmt.Printf("Module cron jobs (%d total):\n\n", len(jobs))
    
    for moduleName, moduleJobs := range jobsByModule {
        fmt.Printf("Module: %s (%d jobs)\n", moduleName, len(moduleJobs))
        
        for _, job := range moduleJobs {
            status := "Disabled"
            if job.Enabled {
                status = "Enabled"
            }
            
            fmt.Printf("  - %s (%s) - %s\n", job.ID, job.Schedule, status)
        }
        fmt.Println()
    }
    
    return nil
}

// Add commands to root cron command
func init() {
    cronCmd.AddCommand(listModuleJobsCmd)
    cronCmd.AddCommand(enableModuleJobCmd)
    cronCmd.AddCommand(disableModuleJobCmd)
}
```

## File Paths

### Cập nhật:
- `internal/pkg/queue/cron/manager.go` (extend for module support)
- `internal/pkg/queue/cron/scheduler.go` (extend for module jobs)
- `cmd/cli/commands/cron.go` (add module commands)

### Tạo mới:
- `internal/pkg/queue/cron/module_job_manager.go`

## Acceptance Criteria

1. ✅ Module jobs được đăng ký tự động khi modules load
2. ✅ Job lifecycle management (enable/disable/restart) hoạt động
3. ✅ CLI commands hỗ trợ module job management
4. ✅ Integration với existing scheduler không breaking
5. ✅ Validation và error handling comprehensive
6. ✅ Module job tracking và monitoring
7. ✅ Code compile thành công với `make build`

## Dependencies
- Task 03: Auto-Discovery Mechanism

## Next Task
[05-module-registrar.md](05-module-registrar.md) - Module-specific registrar implementation
