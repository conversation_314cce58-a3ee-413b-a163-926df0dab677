# Modular Cron Job System - Overview

## Tổng quan

Hệ thống **Modular Cron Job System** cho phép mỗi module trong ứng dụng định nghĩa và quản lý các cron jobs riêng của mình một cách độc lập, tự động và có thể mở rộng.

## 🎯 Mục tiêu

### Vấn đề hiện tại
- Tất cả cron jobs được định nghĩa tập trung trong `internal/pkg/queue/cron/`
- Thêm cron job mới cần modify nhiều files core
- Khó maintain và scale khi có nhiều modules
- Không có separation of concerns giữa các modules

### Giải pháp
- **Modular Architecture**: Mỗi module quản lý cron jobs riêng
- **Auto-registration**: Tự động đăng ký khi module load
- **Configuration Management**: Module-specific configuration
- **Hot-reload**: Runtime configuration updates
- **Backward Compatibility**: Không breaking existing system

## 🏗️ Kiến trúc

### Cấu trúc Module
```
modules/{module}/
├── cron/
│   ├── handlers/           # Cron job handlers
│   │   ├── cleanup_handler.go
│   │   ├── maintenance_handler.go
│   │   └── notification_handler.go
│   ├── jobs/              # Job definitions và schedules
│   │   ├── definitions.go
│   │   └── schedules.go
│   ├── config.go          # Module-specific cron config
│   ├── registrar.go       # Module cron registrar
│   └── README.md          # Module cron documentation
├── api/
├── service/
└── repository/
```

### Core Components

#### 1. Module Interfaces
```go
// ModuleCronRegistrar - Interface cho module registration
type ModuleCronRegistrar interface {
    GetModuleName() string
    RegisterHandlers(registry *HandlerRegistry) error
    GetJobDefinitions() ([]*ModuleCronJobDefinition, error)
    IsEnabled() bool
}

// ModuleCronHandler - Interface cho module handlers
type ModuleCronHandler interface {
    cron.CronHandler
    GetModuleName() string
    GetJobID() string
    GetDefaultSchedule() string
}
```

#### 2. Auto-Discovery System
```go
// ModuleDiscovery - Tự động phát hiện modules có cron capability
type ModuleDiscovery interface {
    DiscoverModules(capability string) ([]ModuleInfo, error)
    RegisterDiscoveryHook(hook DiscoveryHook) error
    EnableAutoDiscovery(enabled bool)
}
```

#### 3. Configuration Management
```go
// ModuleCronConfig - Module-specific configuration
type ModuleCronConfig interface {
    GetModuleName() string
    IsEnabled() bool
    GetJobConfig(jobID string) map[string]interface{}
    GetSchedule(jobID string) string
    IsJobEnabled(jobID string) bool
}
```

## 🔄 Luồng hoạt động

### 1. Module Initialization
```mermaid
graph TD
    A[Application Start] --> B[Module Loader]
    B --> C[Detect Module Capabilities]
    C --> D{Has Cron Capability?}
    D -->|Yes| E[Create Module Info]
    D -->|No| F[Skip Module]
    E --> G[Trigger Discovery]
    G --> H[Register with Cron System]
```

### 2. Auto-Registration Process
```mermaid
graph TD
    A[Module Discovery] --> B[Validate Module]
    B --> C[Register Handlers]
    C --> D[Get Job Definitions]
    D --> E[Validate Jobs]
    E --> F[Add to Scheduler]
    F --> G[Track Module Jobs]
```

### 3. Job Execution Flow
```mermaid
graph TD
    A[Scheduler Trigger] --> B[Get Job Handler]
    B --> C[Create Payload]
    C --> D[Execute Handler]
    D --> E[Log Result]
    E --> F[Update Metrics]
```

## 📋 Implementation Tasks

### Phase 1: Infrastructure Setup (4-6 giờ)
- **[01-module-cron-structure.md](01-module-cron-structure.md)** - Thiết lập cấu trúc thư mục cron trong modules
- **[02-cron-interfaces.md](02-cron-interfaces.md)** - Định nghĩa interfaces cho module cron system

### Phase 2: Auto-discovery & Registration (6-8 giờ)
- **[03-auto-discovery.md](03-auto-discovery.md)** - Cơ chế tự động phát hiện module cron capabilities
- **[04-registration-system.md](04-registration-system.md)** - Hệ thống đăng ký cron jobs tự động
- **[05-module-registrar.md](05-module-registrar.md)** - Triển khai module-specific registrar

### Phase 3: Configuration Management (4-6 giờ)
- **[06-module-config.md](06-module-config.md)** - Quản lý cấu hình module-specific
- **[07-environment-integration.md](07-environment-integration.md)** - Tích hợp với environment variables

### Phase 4: Implementation Examples (6-8 giờ)
- **[08-auth-module-example.md](08-auth-module-example.md)** - Triển khai cron system cho auth module
- **[09-notification-module-example.md](09-notification-module-example.md)** - Triển khai cho notification module

### Phase 5: Testing & Validation (4-6 giờ)
- **[10-testing-framework.md](10-testing-framework.md)** - Framework testing cho module cron system
- **[11-integration-testing.md](11-integration-testing.md)** - Integration testing với existing system

## 🔧 Configuration

### Environment Variables
```bash
# Module Global Settings
MODULE_AUTH_ENABLED=true
MODULE_NOTIFICATION_ENABLED=true
MODULE_MEDIA_ENABLED=true

# Auth Module Cron Jobs
CRON_AUTH_ENABLED=true
CRON_AUTH_SESSION_CLEANUP_ENABLED=true
CRON_AUTH_SESSION_CLEANUP_SCHEDULE="0 2 * * *"
AUTH_SESSION_MAX_INACTIVE_HOURS=168

CRON_AUTH_PASSWORD_EXPIRY_ENABLED=true
CRON_AUTH_PASSWORD_EXPIRY_SCHEDULE="0 9 * * *"
AUTH_PASSWORD_EXPIRY_NOTIFY_DAYS="30,7,1"

# Notification Module Cron Jobs
CRON_NOTIFICATION_ENABLED=true
CRON_NOTIFICATION_CLEANUP_ENABLED=true
CRON_NOTIFICATION_CLEANUP_SCHEDULE="0 1 * * *"
NOTIFICATION_RETENTION_DAYS=90
```

### Module Configuration Example
```go
// Auth module cron configuration
type AuthCronConfig struct {
    Enabled bool `env:"CRON_AUTH_ENABLED" envDefault:"true"`
    Jobs    struct {
        SessionCleanup struct {
            Enabled  bool   `env:"CRON_AUTH_SESSION_CLEANUP_ENABLED" envDefault:"true"`
            Schedule string `env:"CRON_AUTH_SESSION_CLEANUP_SCHEDULE" envDefault:"0 2 * * *"`
            MaxInactiveHours int `env:"AUTH_SESSION_MAX_INACTIVE_HOURS" envDefault:"168"`
        }
        PasswordExpiry struct {
            Enabled  bool   `env:"CRON_AUTH_PASSWORD_EXPIRY_ENABLED" envDefault:"true"`
            Schedule string `env:"CRON_AUTH_PASSWORD_EXPIRY_SCHEDULE" envDefault:"0 9 * * *"`
            NotifyDays []int `env:"AUTH_PASSWORD_EXPIRY_NOTIFY_DAYS" envDefault:"30,7,1"`
        }
    }
}
```

## 🚀 Usage Examples

### 1. Tạo Module Cron Handler
```go
// modules/auth/cron/handlers/session_cleanup_handler.go
type AuthSessionCleanupHandler struct {
    db     *gorm.DB
    config config.Config
    logger logger.Logger
}

func (h *AuthSessionCleanupHandler) Handle(ctx context.Context, payload types.CronTaskPayload) (*types.CronTaskResult, error) {
    // Implementation logic
    return &types.CronTaskResult{
        TaskType:       types.CronTaskAuthSessionCleanup,
        Success:        true,
        Message:        "Session cleanup completed",
        ProcessedCount: deletedCount,
        Duration:       time.Since(startTime),
    }, nil
}
```

### 2. Module Registrar Implementation
```go
// modules/auth/cron/registrar.go
func (r *AuthCronRegistrar) RegisterHandlers(registry *cron.HandlerRegistry) error {
    sessionHandler := handlers.NewAuthSessionCleanupHandler(r.db, r.config, r.logger)
    return registry.RegisterHandler(sessionHandler)
}

func (r *AuthCronRegistrar) GetJobDefinitions() ([]*module.ModuleCronJobDefinition, error) {
    return []*module.ModuleCronJobDefinition{
        {
            ID:          "auth_session_cleanup",
            Name:        "Auth Session Cleanup",
            Description: "Clean up expired and inactive user sessions",
            TaskType:    types.CronTaskAuthSessionCleanup,
            Schedule:    r.config.GetString("CRON_AUTH_SESSION_CLEANUP_SCHEDULE"),
            Enabled:     true,
            Module:      "auth",
        },
    }, nil
}
```

### 3. CLI Commands
```bash
# List all cron jobs (system + modules)
./wnapi-cli cron list

# List jobs for specific module
./wnapi-cli cron list-module auth

# Test a module job
./wnapi-cli cron test auth_session_cleanup --dry-run

# Enable/disable module job
./wnapi-cli cron enable-module-job auth_session_cleanup
./wnapi-cli cron disable-module-job auth_session_cleanup

# Benchmark job performance
./wnapi-cli cron benchmark auth_session_cleanup --iterations 10
```

## 🧪 Testing

### Unit Tests
```go
func TestAuthSessionCleanupHandler_Handle(t *testing.T) {
    // Setup mocks
    mockDB := &mocks.MockDB{}
    mockConfig := &mocks.MockConfig{}
    
    // Test execution
    handler := NewAuthSessionCleanupHandler(mockDB, mockConfig, logger)
    result, err := handler.Handle(context.Background(), payload)
    
    // Assertions
    assert.NoError(t, err)
    assert.True(t, result.Success)
}
```

### Integration Tests
```go
func (suite *CronModuleTestSuite) TestModuleRegistration() {
    authRegistrar := authCron.NewAuthCronRegistrar(suite.db, suite.config, suite.logger)
    err := suite.moduleManager.RegisterModule(authRegistrar)
    suite.NoError(err)
    
    modules := suite.moduleManager.GetRegisteredModules()
    suite.Contains(modules, "auth")
}
```

## 📊 Benefits

### 1. **Modularity**
- Mỗi module quản lý cron jobs riêng
- Separation of concerns
- Easy maintenance và debugging

### 2. **Scalability**
- Thêm module mới không cần modify core system
- Auto-discovery và registration
- Independent deployment

### 3. **Flexibility**
- Module-specific configuration
- Hot-reload capabilities
- Enable/disable per module hoặc job

### 4. **Maintainability**
- Clear code organization
- Comprehensive testing
- Documentation per module

### 5. **Backward Compatibility**
- Existing cron jobs không bị ảnh hưởng
- Gradual migration path
- No breaking changes

## 🔍 Monitoring & Observability

### Metrics
- Job execution success/failure rates
- Execution duration per module
- Memory usage per module
- Module registration status

### Logging
- Module discovery events
- Job execution logs
- Configuration changes
- Error tracking

### Health Checks
- Module health status
- Job scheduler status
- Configuration validation

## 🚦 Getting Started

1. **Đọc [00-index.md](00-index.md)** để hiểu tổng quan
2. **Bắt đầu với [01-module-cron-structure.md](01-module-cron-structure.md)**
3. **Follow tasks theo thứ tự** từ 01 đến 11
4. **Test từng phase** trước khi chuyển sang phase tiếp theo
5. **Deploy và monitor** system performance

## 📚 Documentation

- **[00-index.md](00-index.md)** - Tổng quan và roadmap
- **Tasks 01-11** - Chi tiết implementation
- **Module READMEs** - Documentation per module
- **API Documentation** - CLI commands và interfaces

---

**Total Estimated Time**: 24-34 giờ  
**Complexity**: Medium-High  
**Impact**: High (Scalable cron architecture)  
**Priority**: High (Foundation for future modules)
