# Task 08: Auth Module Complete Implementation

## Objective
Triển khai hoàn chỉnh cron system cho auth module làm ví dụ mẫu cho các modules kh<PERSON><PERSON>, bao gồm tất cả handlers, jobs và integration.

## Input
- Environment integration từ Task 07
- Auth module structure từ các tasks trước
- Existing auth models và services

## Output
- Complete auth cron implementation
- Working auth cron jobs (session cleanup, password expiry, token cleanup)
- Integration với existing auth system
- Documentation và examples

## Requirements

### 1. Complete handler implementations
- AuthSessionCleanupHandler với full functionality
- AuthPasswordExpiryHandler với notification integration
- AuthTokenCleanupHandler với comprehensive cleanup

### 2. Integration với auth system
- Use existing auth models và repositories
- Integration với notification system
- Proper error handling và logging

### 3. Testing và validation
- Unit tests cho handlers
- Integration tests
- CLI testing commands

## Implementation Steps

### Step 1: Complete session cleanup handler (30 phút)

**File**: `modules/auth/cron/handlers/session_cleanup_handler.go` (complete implementation)

```go
package handlers

import (
    "context"
    "fmt"
    "time"
    
    "gorm.io/gorm"
    
    "wnapi/internal/pkg/config"
    "wnapi/internal/pkg/logger"
    "wnapi/internal/pkg/queue/types"
    "wnapi/modules/auth/models"
)

// AuthSessionCleanupHandler handles session cleanup cron job
type AuthSessionCleanupHandler struct {
    db     *gorm.DB
    config config.Config
    logger logger.Logger
}

func NewAuthSessionCleanupHandler(db *gorm.DB, config config.Config, logger logger.Logger) *AuthSessionCleanupHandler {
    return &AuthSessionCleanupHandler{
        db:     db,
        config: config,
        logger: logger,
    }
}

func (h *AuthSessionCleanupHandler) GetTaskType() types.CronTaskType {
    return types.CronTaskAuthSessionCleanup
}

func (h *AuthSessionCleanupHandler) GetDescription() string {
    return "Clean up expired and inactive user sessions"
}

func (h *AuthSessionCleanupHandler) Handle(ctx context.Context, payload types.CronTaskPayload) (*types.CronTaskResult, error) {
    startTime := time.Now()
    
    h.logger.Info("Starting auth session cleanup")
    
    // Get cleanup configuration
    maxInactiveHours := h.config.GetInt("AUTH_SESSION_MAX_INACTIVE_HOURS")
    if maxInactiveHours == 0 {
        maxInactiveHours = 24 * 7 // Default 7 days
    }
    
    var totalDeleted int64
    var details = make(map[string]interface{})
    var errors []string
    
    // 1. Clean up expired sessions
    expiredCount, err := h.cleanupExpiredSessions()
    if err != nil {
        errors = append(errors, fmt.Sprintf("expired sessions cleanup failed: %v", err))
        h.logger.Error("Failed to cleanup expired sessions", "error", err)
    } else {
        totalDeleted += expiredCount
        details["expired_sessions_deleted"] = expiredCount
    }
    
    // 2. Clean up inactive sessions
    cutoffTime := time.Now().Add(-time.Duration(maxInactiveHours) * time.Hour)
    inactiveCount, err := h.cleanupInactiveSessions(cutoffTime)
    if err != nil {
        errors = append(errors, fmt.Sprintf("inactive sessions cleanup failed: %v", err))
        h.logger.Error("Failed to cleanup inactive sessions", "error", err)
    } else {
        totalDeleted += inactiveCount
        details["inactive_sessions_deleted"] = inactiveCount
    }
    
    // 3. Clean up orphaned sessions
    orphanedCount, err := h.cleanupOrphanedSessions()
    if err != nil {
        errors = append(errors, fmt.Sprintf("orphaned sessions cleanup failed: %v", err))
        h.logger.Error("Failed to cleanup orphaned sessions", "error", err)
    } else {
        totalDeleted += orphanedCount
        details["orphaned_sessions_deleted"] = orphanedCount
    }
    
    // 4. Clean up duplicate sessions (keep only latest per user)
    duplicateCount, err := h.cleanupDuplicateSessions()
    if err != nil {
        errors = append(errors, fmt.Sprintf("duplicate sessions cleanup failed: %v", err))
        h.logger.Error("Failed to cleanup duplicate sessions", "error", err)
    } else {
        totalDeleted += duplicateCount
        details["duplicate_sessions_deleted"] = duplicateCount
    }
    
    // Prepare result
    success := len(errors) == 0
    message := fmt.Sprintf("Session cleanup completed. Deleted: %d sessions", totalDeleted)
    if len(errors) > 0 {
        message = fmt.Sprintf("Session cleanup completed with errors. Deleted: %d sessions", totalDeleted)
        details["errors"] = errors
    }
    
    details["total_deleted"] = totalDeleted
    details["cutoff_time"] = cutoffTime.Format("2006-01-02 15:04:05")
    details["max_inactive_hours"] = maxInactiveHours
    details["cleanup_duration"] = time.Since(startTime).String()
    
    h.logger.Info("Auth session cleanup completed", 
        "total_deleted", totalDeleted,
        "errors_count", len(errors),
        "duration", time.Since(startTime))
    
    return &types.CronTaskResult{
        TaskType:       types.CronTaskAuthSessionCleanup,
        Success:        success,
        Message:        message,
        ProcessedCount: int(totalDeleted),
        ErrorCount:     len(errors),
        Duration:       time.Since(startTime),
        Details:        details,
        ExecutedAt:     startTime,
        CompletedAt:    time.Now(),
    }, nil
}

func (h *AuthSessionCleanupHandler) cleanupExpiredSessions() (int64, error) {
    result := h.db.Where("expires_at < ?", time.Now()).Delete(&models.UserSession{})
    return result.RowsAffected, result.Error
}

func (h *AuthSessionCleanupHandler) cleanupInactiveSessions(cutoffTime time.Time) (int64, error) {
    result := h.db.Where("updated_at < ? AND expires_at > ?", cutoffTime, time.Now()).
        Delete(&models.UserSession{})
    return result.RowsAffected, result.Error
}

func (h *AuthSessionCleanupHandler) cleanupOrphanedSessions() (int64, error) {
    result := h.db.Where("user_id NOT IN (SELECT id FROM users)").
        Delete(&models.UserSession{})
    return result.RowsAffected, result.Error
}

func (h *AuthSessionCleanupHandler) cleanupDuplicateSessions() (int64, error) {
    // Keep only the latest session per user (based on updated_at)
    subquery := h.db.Model(&models.UserSession{}).
        Select("MAX(id)").
        Group("user_id")
    
    result := h.db.Where("id NOT IN (?)", subquery).
        Delete(&models.UserSession{})
    
    return result.RowsAffected, result.Error
}
```

### Step 2: Complete password expiry handler (45 phút)

**File**: `modules/auth/cron/handlers/password_expiry_handler.go`

```go
package handlers

import (
    "context"
    "fmt"
    "time"
    
    "gorm.io/gorm"
    
    "wnapi/internal/pkg/config"
    "wnapi/internal/pkg/logger"
    "wnapi/internal/pkg/queue/types"
    "wnapi/modules/auth/models"
    "wnapi/modules/notification/service"
)

// AuthPasswordExpiryHandler handles password expiry notification cron job
type AuthPasswordExpiryHandler struct {
    db                  *gorm.DB
    config              config.Config
    logger              logger.Logger
    notificationService service.NotificationService
}

func NewAuthPasswordExpiryHandler(
    db *gorm.DB, 
    config config.Config, 
    logger logger.Logger,
    notificationService service.NotificationService,
) *AuthPasswordExpiryHandler {
    return &AuthPasswordExpiryHandler{
        db:                  db,
        config:              config,
        logger:              logger,
        notificationService: notificationService,
    }
}

func (h *AuthPasswordExpiryHandler) GetTaskType() types.CronTaskType {
    return types.CronTaskAuthPasswordExpiry
}

func (h *AuthPasswordExpiryHandler) GetDescription() string {
    return "Send password expiry notifications to users"
}

func (h *AuthPasswordExpiryHandler) Handle(ctx context.Context, payload types.CronTaskPayload) (*types.CronTaskResult, error) {
    startTime := time.Now()
    
    h.logger.Info("Starting auth password expiry notification")
    
    // Get notification configuration
    notifyDays := h.config.GetIntSlice("AUTH_PASSWORD_EXPIRY_NOTIFY_DAYS")
    if len(notifyDays) == 0 {
        notifyDays = []int{30, 7, 1} // Default notification days
    }
    
    passwordMaxAge := h.config.GetInt("AUTH_PASSWORD_MAX_AGE_DAYS")
    if passwordMaxAge == 0 {
        passwordMaxAge = 90 // Default 90 days
    }
    
    var totalNotified int64
    var details = make(map[string]interface{})
    var errors []string
    
    // Process each notification day
    for _, days := range notifyDays {
        count, err := h.processPasswordExpiryNotifications(ctx, days, passwordMaxAge)
        if err != nil {
            errors = append(errors, fmt.Sprintf("notification for %d days failed: %v", days, err))
            h.logger.Error("Failed to process password expiry notifications", 
                "days", days, 
                "error", err)
        } else {
            totalNotified += count
            details[fmt.Sprintf("notified_%d_days", days)] = count
        }
    }
    
    // Process expired passwords (force reset)
    expiredCount, err := h.processExpiredPasswords(ctx, passwordMaxAge)
    if err != nil {
        errors = append(errors, fmt.Sprintf("expired password processing failed: %v", err))
        h.logger.Error("Failed to process expired passwords", "error", err)
    } else {
        details["expired_passwords_processed"] = expiredCount
    }
    
    // Prepare result
    success := len(errors) == 0
    message := fmt.Sprintf("Password expiry processing completed. Notified: %d users", totalNotified)
    if len(errors) > 0 {
        message = fmt.Sprintf("Password expiry processing completed with errors. Notified: %d users", totalNotified)
        details["errors"] = errors
    }
    
    details["total_notified"] = totalNotified
    details["notify_days"] = notifyDays
    details["password_max_age_days"] = passwordMaxAge
    details["processing_duration"] = time.Since(startTime).String()
    
    h.logger.Info("Auth password expiry notification completed", 
        "total_notified", totalNotified,
        "expired_processed", expiredCount,
        "errors_count", len(errors),
        "duration", time.Since(startTime))
    
    return &types.CronTaskResult{
        TaskType:       types.CronTaskAuthPasswordExpiry,
        Success:        success,
        Message:        message,
        ProcessedCount: int(totalNotified),
        ErrorCount:     len(errors),
        Duration:       time.Since(startTime),
        Details:        details,
        ExecutedAt:     startTime,
        CompletedAt:    time.Now(),
    }, nil
}

func (h *AuthPasswordExpiryHandler) processPasswordExpiryNotifications(ctx context.Context, days, maxAge int) (int64, error) {
    // Calculate the date when passwords will expire
    expiryDate := time.Now().AddDate(0, 0, days)
    passwordChangeDate := expiryDate.AddDate(0, 0, -maxAge)
    
    // Find users whose passwords will expire in 'days' days
    var users []models.User
    err := h.db.Where("password_changed_at <= ? AND password_changed_at > ?", 
        passwordChangeDate, 
        passwordChangeDate.AddDate(0, 0, -1)).
        Where("email_verified = ?", true).
        Where("is_active = ?", true).
        Find(&users).Error
    
    if err != nil {
        return 0, fmt.Errorf("failed to query users: %w", err)
    }
    
    var notifiedCount int64
    
    for _, user := range users {
        // Check if notification was already sent for this period
        if h.wasNotificationSent(user.ID, days) {
            continue
        }
        
        // Send notification
        if err := h.sendPasswordExpiryNotification(ctx, user, days); err != nil {
            h.logger.Error("Failed to send password expiry notification", 
                "user_id", user.ID, 
                "email", user.Email,
                "days", days,
                "error", err)
            continue
        }
        
        // Record notification sent
        if err := h.recordNotificationSent(user.ID, days); err != nil {
            h.logger.Error("Failed to record notification sent", 
                "user_id", user.ID, 
                "days", days,
                "error", err)
        }
        
        notifiedCount++
    }
    
    return notifiedCount, nil
}

func (h *AuthPasswordExpiryHandler) processExpiredPasswords(ctx context.Context, maxAge int) (int64, error) {
    // Find users with expired passwords
    expiredDate := time.Now().AddDate(0, 0, -maxAge)
    
    var users []models.User
    err := h.db.Where("password_changed_at < ?", expiredDate).
        Where("password_expired = ?", false).
        Where("is_active = ?", true).
        Find(&users).Error
    
    if err != nil {
        return 0, fmt.Errorf("failed to query expired password users: %w", err)
    }
    
    var processedCount int64
    
    for _, user := range users {
        // Mark password as expired
        if err := h.db.Model(&user).Update("password_expired", true).Error; err != nil {
            h.logger.Error("Failed to mark password as expired", 
                "user_id", user.ID, 
                "error", err)
            continue
        }
        
        // Send password expired notification
        if err := h.sendPasswordExpiredNotification(ctx, user); err != nil {
            h.logger.Error("Failed to send password expired notification", 
                "user_id", user.ID, 
                "error", err)
        }
        
        processedCount++
    }
    
    return processedCount, nil
}

func (h *AuthPasswordExpiryHandler) sendPasswordExpiryNotification(ctx context.Context, user models.User, days int) error {
    // Create notification payload
    notificationData := map[string]interface{}{
        "user_name":    user.Name,
        "days_left":    days,
        "change_url":   h.config.GetString("WEB_URL") + "/auth/change-password",
    }
    
    // Send via notification service
    return h.notificationService.SendNotification(ctx, service.SendNotificationRequest{
        TenantID:     user.TenantID,
        UserID:       &user.ID,
        Type:         "password_expiry_warning",
        Title:        fmt.Sprintf("Password expires in %d days", days),
        Message:      fmt.Sprintf("Your password will expire in %d days. Please change it to maintain account security.", days),
        Data:         notificationData,
        Channels:     []string{"email"},
        Priority:     "medium",
    })
}

func (h *AuthPasswordExpiryHandler) sendPasswordExpiredNotification(ctx context.Context, user models.User) error {
    notificationData := map[string]interface{}{
        "user_name":  user.Name,
        "reset_url":  h.config.GetString("WEB_URL") + "/auth/reset-password",
    }
    
    return h.notificationService.SendNotification(ctx, service.SendNotificationRequest{
        TenantID:     user.TenantID,
        UserID:       &user.ID,
        Type:         "password_expired",
        Title:        "Password has expired",
        Message:      "Your password has expired. Please reset it to continue using your account.",
        Data:         notificationData,
        Channels:     []string{"email"},
        Priority:     "high",
    })
}

func (h *AuthPasswordExpiryHandler) wasNotificationSent(userID uint, days int) bool {
    // Check if notification was sent in the last 24 hours for this user and days
    var count int64
    h.db.Model(&models.PasswordExpiryNotification{}).
        Where("user_id = ? AND days_before_expiry = ? AND created_at > ?", 
            userID, days, time.Now().Add(-24*time.Hour)).
        Count(&count)
    
    return count > 0
}

func (h *AuthPasswordExpiryHandler) recordNotificationSent(userID uint, days int) error {
    notification := &models.PasswordExpiryNotification{
        UserID:             userID,
        DaysBeforeExpiry:   days,
        NotificationSentAt: time.Now(),
    }
    
    return h.db.Create(notification).Error
}
```

### Step 3: Auth module integration (15 phút)

**File**: `modules/auth/module.go` (add cron integration)

```go
package auth

import (
    "wnapi/internal/pkg/config"
    "wnapi/internal/pkg/logger"
    "wnapi/internal/pkg/module"
    authCron "wnapi/modules/auth/cron"
    "gorm.io/gorm"
)

// AuthModule represents the auth module
type AuthModule struct {
    db           *gorm.DB
    config       config.Config
    logger       logger.Logger
    cronRegistrar module.ModuleCronRegistrar
}

func NewAuthModule(db *gorm.DB, config config.Config, logger logger.Logger) *AuthModule {
    cronRegistrar := authCron.NewAuthCronRegistrar(db, config, logger)
    
    return &AuthModule{
        db:           db,
        config:       config,
        logger:       logger,
        cronRegistrar: cronRegistrar,
    }
}

// GetCronRegistrar returns the cron registrar for this module
func (m *AuthModule) GetCronRegistrar() module.ModuleCronRegistrar {
    return m.cronRegistrar
}

// GetModuleName returns the module name
func (m *AuthModule) GetModuleName() string {
    return "auth"
}

// GetCapabilities returns module capabilities
func (m *AuthModule) GetCapabilities() []string {
    return []string{
        module.CapabilityCron,
        module.CapabilityAPI,
        module.CapabilityService,
        module.CapabilityRepository,
    }
}
```

## File Paths

### Hoàn thiện:
- `modules/auth/cron/handlers/session_cleanup_handler.go`
- `modules/auth/cron/handlers/password_expiry_handler.go`
- `modules/auth/cron/handlers/token_cleanup_handler.go`

### Tạo mới:
- `modules/auth/models/password_expiry_notification.go`
- `modules/auth/module.go`

### Cập nhật:
- `internal/pkg/queue/types/cron.go` (add auth task types)
- `modules/auth/cron/registrar.go` (complete implementation)

## Acceptance Criteria

1. ✅ Auth session cleanup handler hoạt động đầy đủ
2. ✅ Password expiry handler với notification integration
3. ✅ Token cleanup handler comprehensive
4. ✅ Integration với existing auth models
5. ✅ Notification service integration
6. ✅ Error handling và logging robust
7. ✅ CLI commands test thành công

## Dependencies
- Task 07: Environment Integration & Hot-Reload

## Next Task
[09-notification-module-example.md](09-notification-module-example.md) - Notification module implementation
