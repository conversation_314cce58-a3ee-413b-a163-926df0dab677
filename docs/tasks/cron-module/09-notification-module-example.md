# Task 09: Notification Module Implementation

## Objective
Triển khai cron system cho notification module, bao gồm cleanup jobs và email queue management.

## Input
- Auth module example từ Task 08
- Existing notification system
- Module patterns đã established

## Output
- Complete notification cron implementation
- Notification cleanup jobs
- Email queue cleanup jobs
- Integration với existing notification service

## Requirements

### 1. Notification cleanup jobs
- Clean up old read notifications
- Remove failed delivery notifications
- Archive important notifications

### 2. Email queue management
- Clean up processed email queue items
- Retry failed email deliveries
- Manage email templates cache

### 3. Integration
- Use existing notification models
- Integration với email service
- Proper error handling

## Implementation Steps

### Step 1: Notification cleanup handler (30 phút)

**File**: `modules/notification/cron/handlers/cleanup_handler.go`

```go
package handlers

import (
    "context"
    "fmt"
    "time"
    
    "gorm.io/gorm"
    
    "wnapi/internal/pkg/config"
    "wnapi/internal/pkg/logger"
    "wnapi/internal/pkg/queue/types"
    "wnapi/modules/notification/models"
)

// NotificationCleanupHandler handles notification cleanup cron job
type NotificationCleanupHandler struct {
    db     *gorm.DB
    config config.Config
    logger logger.Logger
}

func NewNotificationCleanupHandler(db *gorm.DB, config config.Config, logger logger.Logger) *NotificationCleanupHandler {
    return &NotificationCleanupHandler{
        db:     db,
        config: config,
        logger: logger,
    }
}

func (h *NotificationCleanupHandler) GetTaskType() types.CronTaskType {
    return types.CronTaskNotificationCleanup
}

func (h *NotificationCleanupHandler) GetDescription() string {
    return "Clean up old read notifications and failed deliveries"
}

func (h *NotificationCleanupHandler) Handle(ctx context.Context, payload types.CronTaskPayload) (*types.CronTaskResult, error) {
    startTime := time.Now()
    
    h.logger.Info("Starting notification cleanup")
    
    // Get cleanup configuration
    retentionDays := h.config.GetInt("NOTIFICATION_RETENTION_DAYS")
    if retentionDays == 0 {
        retentionDays = 90 // Default 90 days
    }
    
    readRetentionDays := h.config.GetInt("NOTIFICATION_READ_RETENTION_DAYS")
    if readRetentionDays == 0 {
        readRetentionDays = 30 // Default 30 days for read notifications
    }
    
    var totalDeleted int64
    var details = make(map[string]interface{})
    var errors []string
    
    // 1. Clean up old read notifications
    readCount, err := h.cleanupReadNotifications(readRetentionDays)
    if err != nil {
        errors = append(errors, fmt.Sprintf("read notifications cleanup failed: %v", err))
        h.logger.Error("Failed to cleanup read notifications", "error", err)
    } else {
        totalDeleted += readCount
        details["read_notifications_deleted"] = readCount
    }
    
    // 2. Clean up old unread notifications (longer retention)
    unreadCount, err := h.cleanupUnreadNotifications(retentionDays)
    if err != nil {
        errors = append(errors, fmt.Sprintf("unread notifications cleanup failed: %v", err))
        h.logger.Error("Failed to cleanup unread notifications", "error", err)
    } else {
        totalDeleted += unreadCount
        details["unread_notifications_deleted"] = unreadCount
    }
    
    // 3. Clean up failed delivery notifications
    failedCount, err := h.cleanupFailedNotifications()
    if err != nil {
        errors = append(errors, fmt.Sprintf("failed notifications cleanup failed: %v", err))
        h.logger.Error("Failed to cleanup failed notifications", "error", err)
    } else {
        totalDeleted += failedCount
        details["failed_notifications_deleted"] = failedCount
    }
    
    // 4. Archive important notifications
    archivedCount, err := h.archiveImportantNotifications(retentionDays)
    if err != nil {
        errors = append(errors, fmt.Sprintf("notification archiving failed: %v", err))
        h.logger.Error("Failed to archive important notifications", "error", err)
    } else {
        details["notifications_archived"] = archivedCount
    }
    
    // 5. Clean up notification delivery logs
    logCount, err := h.cleanupDeliveryLogs(retentionDays)
    if err != nil {
        errors = append(errors, fmt.Sprintf("delivery logs cleanup failed: %v", err))
        h.logger.Error("Failed to cleanup delivery logs", "error", err)
    } else {
        totalDeleted += logCount
        details["delivery_logs_deleted"] = logCount
    }
    
    // Prepare result
    success := len(errors) == 0
    message := fmt.Sprintf("Notification cleanup completed. Deleted: %d items", totalDeleted)
    if len(errors) > 0 {
        message = fmt.Sprintf("Notification cleanup completed with errors. Deleted: %d items", totalDeleted)
        details["errors"] = errors
    }
    
    details["total_deleted"] = totalDeleted
    details["retention_days"] = retentionDays
    details["read_retention_days"] = readRetentionDays
    details["cleanup_duration"] = time.Since(startTime).String()
    
    h.logger.Info("Notification cleanup completed", 
        "total_deleted", totalDeleted,
        "archived", archivedCount,
        "errors_count", len(errors),
        "duration", time.Since(startTime))
    
    return &types.CronTaskResult{
        TaskType:       types.CronTaskNotificationCleanup,
        Success:        success,
        Message:        message,
        ProcessedCount: int(totalDeleted),
        ErrorCount:     len(errors),
        Duration:       time.Since(startTime),
        Details:        details,
        ExecutedAt:     startTime,
        CompletedAt:    time.Now(),
    }, nil
}

func (h *NotificationCleanupHandler) cleanupReadNotifications(retentionDays int) (int64, error) {
    cutoffDate := time.Now().AddDate(0, 0, -retentionDays)
    
    result := h.db.Where("read_at IS NOT NULL AND read_at < ?", cutoffDate).
        Where("priority != ?", "critical"). // Don't delete critical notifications
        Delete(&models.Notification{})
    
    return result.RowsAffected, result.Error
}

func (h *NotificationCleanupHandler) cleanupUnreadNotifications(retentionDays int) (int64, error) {
    cutoffDate := time.Now().AddDate(0, 0, -retentionDays)
    
    result := h.db.Where("read_at IS NULL AND created_at < ?", cutoffDate).
        Where("priority NOT IN (?)", []string{"critical", "high"}). // Keep important notifications longer
        Delete(&models.Notification{})
    
    return result.RowsAffected, result.Error
}

func (h *NotificationCleanupHandler) cleanupFailedNotifications() (int64, error) {
    // Delete notifications that failed delivery more than 7 days ago
    cutoffDate := time.Now().AddDate(0, 0, -7)
    
    result := h.db.Where("status = ? AND updated_at < ?", "failed", cutoffDate).
        Where("retry_count >= ?", 3). // Only delete after multiple retry attempts
        Delete(&models.Notification{})
    
    return result.RowsAffected, result.Error
}

func (h *NotificationCleanupHandler) archiveImportantNotifications(retentionDays int) (int64, error) {
    cutoffDate := time.Now().AddDate(0, 0, -retentionDays)
    
    // Move critical and high priority notifications to archive table
    var notifications []models.Notification
    err := h.db.Where("priority IN (?) AND created_at < ?", []string{"critical", "high"}, cutoffDate).
        Find(&notifications).Error
    
    if err != nil {
        return 0, err
    }
    
    var archivedCount int64
    
    for _, notification := range notifications {
        // Create archive record
        archive := models.NotificationArchive{
            OriginalID:   notification.ID,
            TenantID:     notification.TenantID,
            UserID:       notification.UserID,
            Type:         notification.Type,
            Title:        notification.Title,
            Message:      notification.Message,
            Data:         notification.Data,
            Priority:     notification.Priority,
            Status:       notification.Status,
            ReadAt:       notification.ReadAt,
            CreatedAt:    notification.CreatedAt,
            ArchivedAt:   time.Now(),
        }
        
        if err := h.db.Create(&archive).Error; err != nil {
            h.logger.Error("Failed to archive notification", 
                "notification_id", notification.ID, 
                "error", err)
            continue
        }
        
        // Delete original notification
        if err := h.db.Delete(&notification).Error; err != nil {
            h.logger.Error("Failed to delete archived notification", 
                "notification_id", notification.ID, 
                "error", err)
            continue
        }
        
        archivedCount++
    }
    
    return archivedCount, nil
}

func (h *NotificationCleanupHandler) cleanupDeliveryLogs(retentionDays int) (int64, error) {
    cutoffDate := time.Now().AddDate(0, 0, -retentionDays)
    
    result := h.db.Where("created_at < ?", cutoffDate).
        Delete(&models.NotificationDeliveryLog{})
    
    return result.RowsAffected, result.Error
}
```

### Step 2: Email queue cleanup handler (30 phút)

**File**: `modules/notification/cron/handlers/email_cleanup_handler.go`

```go
package handlers

import (
    "context"
    "fmt"
    "time"
    
    "gorm.io/gorm"
    
    "wnapi/internal/pkg/config"
    "wnapi/internal/pkg/logger"
    "wnapi/internal/pkg/queue/types"
    "wnapi/modules/notification/models"
)

// EmailQueueCleanupHandler handles email queue cleanup cron job
type EmailQueueCleanupHandler struct {
    db     *gorm.DB
    config config.Config
    logger logger.Logger
}

func NewEmailQueueCleanupHandler(db *gorm.DB, config config.Config, logger logger.Logger) *EmailQueueCleanupHandler {
    return &EmailQueueCleanupHandler{
        db:     db,
        config: config,
        logger: logger,
    }
}

func (h *EmailQueueCleanupHandler) GetTaskType() types.CronTaskType {
    return types.CronTaskNotificationEmailCleanup
}

func (h *EmailQueueCleanupHandler) GetDescription() string {
    return "Clean up processed email queue items and manage email templates cache"
}

func (h *EmailQueueCleanupHandler) Handle(ctx context.Context, payload types.CronTaskPayload) (*types.CronTaskResult, error) {
    startTime := time.Now()
    
    h.logger.Info("Starting email queue cleanup")
    
    // Get cleanup configuration
    retentionDays := h.config.GetInt("EMAIL_QUEUE_RETENTION_DAYS")
    if retentionDays == 0 {
        retentionDays = 7 // Default 7 days
    }
    
    var totalDeleted int64
    var details = make(map[string]interface{})
    var errors []string
    
    // 1. Clean up sent emails
    sentCount, err := h.cleanupSentEmails(retentionDays)
    if err != nil {
        errors = append(errors, fmt.Sprintf("sent emails cleanup failed: %v", err))
        h.logger.Error("Failed to cleanup sent emails", "error", err)
    } else {
        totalDeleted += sentCount
        details["sent_emails_deleted"] = sentCount
    }
    
    // 2. Clean up failed emails (after retry period)
    failedCount, err := h.cleanupFailedEmails()
    if err != nil {
        errors = append(errors, fmt.Sprintf("failed emails cleanup failed: %v", err))
        h.logger.Error("Failed to cleanup failed emails", "error", err)
    } else {
        totalDeleted += failedCount
        details["failed_emails_deleted"] = failedCount
    }
    
    // 3. Retry failed emails (within retry window)
    retriedCount, err := h.retryFailedEmails()
    if err != nil {
        errors = append(errors, fmt.Sprintf("email retry failed: %v", err))
        h.logger.Error("Failed to retry emails", "error", err)
    } else {
        details["emails_retried"] = retriedCount
    }
    
    // 4. Clean up email templates cache
    templatesCount, err := h.cleanupEmailTemplatesCache()
    if err != nil {
        errors = append(errors, fmt.Sprintf("templates cache cleanup failed: %v", err))
        h.logger.Error("Failed to cleanup templates cache", "error", err)
    } else {
        details["templates_cache_cleaned"] = templatesCount
    }
    
    // 5. Clean up email attachments
    attachmentsCount, err := h.cleanupEmailAttachments(retentionDays)
    if err != nil {
        errors = append(errors, fmt.Sprintf("attachments cleanup failed: %v", err))
        h.logger.Error("Failed to cleanup email attachments", "error", err)
    } else {
        totalDeleted += attachmentsCount
        details["attachments_deleted"] = attachmentsCount
    }
    
    // Prepare result
    success := len(errors) == 0
    message := fmt.Sprintf("Email queue cleanup completed. Deleted: %d items, Retried: %d emails", totalDeleted, retriedCount)
    if len(errors) > 0 {
        message = fmt.Sprintf("Email queue cleanup completed with errors. Deleted: %d items", totalDeleted)
        details["errors"] = errors
    }
    
    details["total_deleted"] = totalDeleted
    details["retention_days"] = retentionDays
    details["cleanup_duration"] = time.Since(startTime).String()
    
    h.logger.Info("Email queue cleanup completed", 
        "total_deleted", totalDeleted,
        "retried", retriedCount,
        "errors_count", len(errors),
        "duration", time.Since(startTime))
    
    return &types.CronTaskResult{
        TaskType:       types.CronTaskNotificationEmailCleanup,
        Success:        success,
        Message:        message,
        ProcessedCount: int(totalDeleted + retriedCount),
        ErrorCount:     len(errors),
        Duration:       time.Since(startTime),
        Details:        details,
        ExecutedAt:     startTime,
        CompletedAt:    time.Now(),
    }, nil
}

func (h *EmailQueueCleanupHandler) cleanupSentEmails(retentionDays int) (int64, error) {
    cutoffDate := time.Now().AddDate(0, 0, -retentionDays)
    
    result := h.db.Where("status = ? AND sent_at < ?", "sent", cutoffDate).
        Delete(&models.EmailQueue{})
    
    return result.RowsAffected, result.Error
}

func (h *EmailQueueCleanupHandler) cleanupFailedEmails() (int64, error) {
    // Delete emails that failed more than 3 days ago and exceeded retry limit
    cutoffDate := time.Now().AddDate(0, 0, -3)
    maxRetries := h.config.GetInt("EMAIL_MAX_RETRIES")
    if maxRetries == 0 {
        maxRetries = 3
    }
    
    result := h.db.Where("status = ? AND updated_at < ? AND retry_count >= ?", 
        "failed", cutoffDate, maxRetries).
        Delete(&models.EmailQueue{})
    
    return result.RowsAffected, result.Error
}

func (h *EmailQueueCleanupHandler) retryFailedEmails() (int64, error) {
    // Retry emails that failed less than 24 hours ago and haven't exceeded retry limit
    retryWindow := time.Now().Add(-24 * time.Hour)
    maxRetries := h.config.GetInt("EMAIL_MAX_RETRIES")
    if maxRetries == 0 {
        maxRetries = 3
    }
    
    var failedEmails []models.EmailQueue
    err := h.db.Where("status = ? AND updated_at > ? AND retry_count < ?", 
        "failed", retryWindow, maxRetries).
        Find(&failedEmails).Error
    
    if err != nil {
        return 0, err
    }
    
    var retriedCount int64
    
    for _, email := range failedEmails {
        // Reset status to pending for retry
        email.Status = "pending"
        email.RetryCount++
        email.NextRetryAt = time.Now().Add(time.Duration(email.RetryCount*email.RetryCount) * time.Minute) // Exponential backoff
        
        if err := h.db.Save(&email).Error; err != nil {
            h.logger.Error("Failed to reset email for retry", 
                "email_id", email.ID, 
                "error", err)
            continue
        }
        
        retriedCount++
    }
    
    return retriedCount, nil
}

func (h *EmailQueueCleanupHandler) cleanupEmailTemplatesCache() (int64, error) {
    // Clean up cached email templates older than 1 day
    cutoffDate := time.Now().AddDate(0, 0, -1)
    
    result := h.db.Where("cached_at < ?", cutoffDate).
        Delete(&models.EmailTemplateCache{})
    
    return result.RowsAffected, result.Error
}

func (h *EmailQueueCleanupHandler) cleanupEmailAttachments(retentionDays int) (int64, error) {
    // Clean up email attachments for deleted emails
    cutoffDate := time.Now().AddDate(0, 0, -retentionDays)
    
    // Find attachments for emails that no longer exist
    result := h.db.Where("email_id NOT IN (SELECT id FROM email_queue)").
        Or("created_at < ?", cutoffDate).
        Delete(&models.EmailAttachment{})
    
    return result.RowsAffected, result.Error
}
```

### Step 3: Notification module registrar (15 phút)

**File**: `modules/notification/cron/registrar.go` (complete implementation)

```go
package cron

import (
    "fmt"
    "gorm.io/gorm"
    
    "wnapi/internal/pkg/config"
    "wnapi/internal/pkg/logger"
    "wnapi/internal/pkg/module"
    "wnapi/internal/pkg/queue/cron"
    "wnapi/internal/pkg/queue/types"
    "wnapi/modules/notification/cron/handlers"
)

// NotificationCronRegistrar implements ModuleCronRegistrar for notification module
type NotificationCronRegistrar struct {
    db     *gorm.DB
    config config.Config
    logger logger.Logger
}

func NewNotificationCronRegistrar(db *gorm.DB, config config.Config, logger logger.Logger) *NotificationCronRegistrar {
    return &NotificationCronRegistrar{
        db:     db,
        config: config,
        logger: logger,
    }
}

func (r *NotificationCronRegistrar) GetModuleName() string {
    return "notification"
}

func (r *NotificationCronRegistrar) IsEnabled() bool {
    return r.config.GetBool("CRON_NOTIFICATION_ENABLED")
}

func (r *NotificationCronRegistrar) RegisterHandlers(registry *cron.HandlerRegistry) error {
    if !r.IsEnabled() {
        r.logger.Debug("Notification cron is disabled, skipping handler registration")
        return nil
    }

    // Register notification cleanup handler
    cleanupHandler := handlers.NewNotificationCleanupHandler(r.db, r.config, r.logger)
    if err := registry.RegisterHandler(cleanupHandler); err != nil {
        return fmt.Errorf("failed to register notification cleanup handler: %w", err)
    }

    // Register email queue cleanup handler
    emailHandler := handlers.NewEmailQueueCleanupHandler(r.db, r.config, r.logger)
    if err := registry.RegisterHandler(emailHandler); err != nil {
        return fmt.Errorf("failed to register email queue cleanup handler: %w", err)
    }

    r.logger.Info("Notification cron handlers registered successfully")
    return nil
}

func (r *NotificationCronRegistrar) GetJobDefinitions() ([]*module.ModuleCronJobDefinition, error) {
    var jobs []*module.ModuleCronJobDefinition

    // Notification cleanup job
    if r.config.GetBool("CRON_NOTIFICATION_CLEANUP_ENABLED") {
        jobs = append(jobs, &module.ModuleCronJobDefinition{
            ID:          "notification_cleanup",
            Name:        "Notification Cleanup",
            Description: "Clean up old read notifications and failed deliveries",
            TaskType:    types.CronTaskNotificationCleanup,
            Schedule:    r.config.GetString("CRON_NOTIFICATION_CLEANUP_SCHEDULE"),
            Enabled:     true,
            Module:      "notification",
        })
    }

    // Email queue cleanup job
    if r.config.GetBool("CRON_NOTIFICATION_EMAIL_CLEANUP_ENABLED") {
        jobs = append(jobs, &module.ModuleCronJobDefinition{
            ID:          "notification_email_cleanup",
            Name:        "Email Queue Cleanup",
            Description: "Clean up processed email queue items and retry failed emails",
            TaskType:    types.CronTaskNotificationEmailCleanup,
            Schedule:    r.config.GetString("CRON_NOTIFICATION_EMAIL_CLEANUP_SCHEDULE"),
            Enabled:     true,
            Module:      "notification",
        })
    }

    return jobs, nil
}
```

## File Paths

### Tạo mới:
- `modules/notification/cron/handlers/cleanup_handler.go`
- `modules/notification/cron/handlers/email_cleanup_handler.go`
- `modules/notification/cron/registrar.go`
- `modules/notification/models/notification_archive.go`
- `modules/notification/models/email_template_cache.go`

### Cập nhật:
- `internal/pkg/queue/types/cron.go` (add notification task types)
- `modules/notification/module.go` (add cron integration)

## Acceptance Criteria

1. ✅ Notification cleanup handler hoạt động đầy đủ
2. ✅ Email queue cleanup với retry mechanism
3. ✅ Template cache management
4. ✅ Archive system cho important notifications
5. ✅ Integration với existing notification models
6. ✅ Comprehensive error handling
7. ✅ CLI commands test thành công

## Dependencies
- Task 08: Auth Module Complete Implementation

## Next Task
[10-testing-framework.md](10-testing-framework.md) - Testing framework cho module cron system
