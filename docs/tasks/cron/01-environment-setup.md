# Task 01: Environment Setup

Thiết lập môi trường và cấu hình cần thiết để triển khai cron jobs với AsynqScheduler.

## Mục tiêu

- <PERSON><PERSON><PERSON> hình các biến môi trường cần thiết
- Thiết lập Redis connection
- <PERSON><PERSON>ch hoạt queue system và scheduler
- <PERSON><PERSON><PERSON> minh cấu hình hoạt động đúng

## Biến môi trường bắt buộc

### 1. Queue System Configuration

Thêm vào file `.env`:

```bash
# Kích hoạt hệ thống queue
QUEUE_ENABLED=true

# Chỉ định backend là Asynq
QUEUE_BACKEND=asynq

# Khởi động worker trong server
QUEUE_START_WORKER=true
```

### 2. Redis Configuration

```bash
# Redis connection
QUEUE_REDIS_ADDR=localhost:6379
QUEUE_REDIS_PASSWORD=
QUEUE_REDIS_DB=0

# Redis pool settings (optional)
QUEUE_REDIS_POOL_SIZE=10
QUEUE_REDIS_MIN_IDLE_CONNS=5
```

### 3. Scheduler Configuration

```bash
# Kích hoạt scheduler
QUEUE_SCHEDULER_ENABLED=true

# Múi giờ cho cron expressions
QUEUE_SCHEDULER_TIME_ZONE=Asia/Ho_Chi_Minh

# Scheduler options (optional)
QUEUE_SCHEDULER_LOCATION=Asia/Ho_Chi_Minh
```

### 4. Worker Configuration

```bash
# Worker settings
QUEUE_WORKER_CONCURRENCY=10
QUEUE_WORKER_QUEUES=default,cron_jobs,high_priority
QUEUE_WORKER_STRICT_PRIORITY=false

# Health check
QUEUE_WORKER_HEALTH_CHECK_INTERVAL=15s
```

## Cấu hình Redis

### 1. Cài đặt Redis (nếu chưa có)

**Ubuntu/Debian:**

```bash
sudo apt update
sudo apt install redis-server
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

**macOS:**

```bash
brew install redis
brew services start redis
```

**Docker:**

```bash
docker run -d --name redis -p 6379:6379 redis:7-alpine
```

### 2. Kiểm tra Redis connection

```bash
redis-cli ping
# Expected output: PONG
```

### 3. Redis configuration cho production

Tạo file `redis.conf`:

```conf
# Memory
maxmemory 256mb
maxmemory-policy allkeys-lru

# Persistence
save 900 1
save 300 10
save 60 10000

# Security
requirepass your_redis_password

# Network
bind 127.0.0.1
port 6379
```

## Cấu hình ứng dụng

### 1. Cập nhật config struct

Đảm bảo config struct hỗ trợ các biến môi trường mới:

```go
// internal/config/config.go
type Config struct {
    // ... existing fields
  
    Queue struct {
        Enabled     bool   `env:"QUEUE_ENABLED" envDefault:"false"`
        Backend     string `env:"QUEUE_BACKEND" envDefault:"asynq"`
        StartWorker bool   `env:"QUEUE_START_WORKER" envDefault:"true"`
      
        Redis struct {
            Addr     string `env:"QUEUE_REDIS_ADDR" envDefault:"localhost:6379"`
            Password string `env:"QUEUE_REDIS_PASSWORD"`
            DB       int    `env:"QUEUE_REDIS_DB" envDefault:"0"`
            PoolSize int    `env:"QUEUE_REDIS_POOL_SIZE" envDefault:"10"`
        }
      
        Scheduler struct {
            Enabled  bool   `env:"QUEUE_SCHEDULER_ENABLED" envDefault:"false"`
            TimeZone string `env:"QUEUE_SCHEDULER_TIME_ZONE" envDefault:"UTC"`
        }
      
        Worker struct {
            Concurrency    int      `env:"QUEUE_WORKER_CONCURRENCY" envDefault:"10"`
            Queues         []string `env:"QUEUE_WORKER_QUEUES" envSeparator:","`
            StrictPriority bool     `env:"QUEUE_WORKER_STRICT_PRIORITY" envDefault:"false"`
        }
    }
}
```

### 2. Validation

Thêm validation cho config:

```go
func (c *Config) Validate() error {
    if c.Queue.Enabled {
        if c.Queue.Backend != "asynq" {
            return fmt.Errorf("unsupported queue backend: %s", c.Queue.Backend)
        }
      
        if c.Queue.Redis.Addr == "" {
            return fmt.Errorf("QUEUE_REDIS_ADDR is required when queue is enabled")
        }
      
        if c.Queue.Scheduler.Enabled {
            if _, err := time.LoadLocation(c.Queue.Scheduler.TimeZone); err != nil {
                return fmt.Errorf("invalid timezone: %s", c.Queue.Scheduler.TimeZone)
            }
        }
    }
  
    return nil
}
```

## Kiểm tra cấu hình

### 1. Tạo script kiểm tra

Tạo file `scripts/check-queue-config.sh`:

```bash
#!/bin/bash

echo "Checking queue configuration..."

# Check Redis connection
echo "Testing Redis connection..."
if redis-cli -h ${QUEUE_REDIS_ADDR%:*} -p ${QUEUE_REDIS_ADDR#*:} ping > /dev/null 2>&1; then
    echo "✓ Redis connection successful"
else
    echo "✗ Redis connection failed"
    exit 1
fi

# Check environment variables
required_vars=(
    "QUEUE_ENABLED"
    "QUEUE_BACKEND"
    "QUEUE_REDIS_ADDR"
    "QUEUE_SCHEDULER_ENABLED"
    "QUEUE_SCHEDULER_TIME_ZONE"
)

for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        echo "✗ Missing required environment variable: $var"
        exit 1
    else
        echo "✓ $var = ${!var}"
    fi
done

echo "All queue configuration checks passed!"
```

### 2. Chạy kiểm tra

```bash
chmod +x scripts/check-queue-config.sh
source .env
./scripts/check-queue-config.sh
```

## Troubleshooting

### 1. Redis connection issues

**Lỗi: "connection refused"**

```bash
# Kiểm tra Redis đang chạy
sudo systemctl status redis-server

# Kiểm tra port
netstat -tlnp | grep :6379
```

**Lỗi: "authentication failed"**

```bash
# Kiểm tra password trong Redis config
redis-cli config get requirepass
```

### 2. Environment variables

**Kiểm tra biến môi trường được load:**

```bash
# In ra tất cả biến QUEUE_*
env | grep QUEUE_
```

**Kiểm tra trong ứng dụng:**

```go
func debugConfig() {
    fmt.Printf("QUEUE_ENABLED: %v\n", os.Getenv("QUEUE_ENABLED"))
    fmt.Printf("QUEUE_REDIS_ADDR: %s\n", os.Getenv("QUEUE_REDIS_ADDR"))
    // ... other variables
}
```

### 3. Timezone issues

**Kiểm tra timezone hợp lệ:**

```go
func validateTimezone(tz string) error {
    _, err := time.LoadLocation(tz)
    return err
}
```

## Next Steps

Sau khi hoàn thành environment setup:

1. ✅ Tất cả biến môi trường đã được cấu hình
2. ✅ Redis connection hoạt động
3. ✅ Config validation pass
4. ✅ Timezone được thiết lập đúng

Tiếp theo: [02-project-structure.md](02-project-structure.md) - Thiết lập cấu trúc project
