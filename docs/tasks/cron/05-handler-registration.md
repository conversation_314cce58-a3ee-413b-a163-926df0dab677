# Task 05: Handler Registration

Đăng ký task handlers v<PERSON><PERSON>ger để xử lý cron jobs.

## M<PERSON><PERSON> tiêu

- <PERSON><PERSON>ng ký handlers v<PERSON><PERSON>anager
- Cấu hình task options và queues
- Thiết lập dependency injection
- Tổ chức registration theo modules

## Registration Architecture

### 1. Registration Interface

Tạo file `internal/pkg/queue/registration.go`:

```go
package queue

import (
    "fmt"
    "time"
)

// HandlerRegistrar interface cho đăng ký handlers
type HandlerRegistrar interface {
    RegisterHandlers(manager *QueueManager) error
}

// TaskDefinition định nghĩa một task và handler c<PERSON><PERSON> nó
type TaskDefinition struct {
    Type            string
    Description     string
    Handler         TaskHandler
    Options         *TaskOptions
    PayloadValidator func(payload interface{}) error
}

// TaskOptions cấu hình cho task
type TaskOptions struct {
    Queue           string
    MaxRetry        int
    Timeout         time.Duration
    Priority        int
    ProcessAt       *time.Time
    ProcessIn       time.Duration
    Unique          bool
    UniqueKey       string
    UniqueTTL       time.Duration
}

// DefaultTaskOptions trả về options mặc định
func DefaultTaskOptions() *TaskOptions {
    return &TaskOptions{
        Queue:    "default",
        MaxRetry: 3,
        Timeout:  10 * time.Minute,
        Priority: 5,
    }
}

// CronTaskOptions trả về options cho cron tasks
func CronTaskOptions(queue string) *TaskOptions {
    return &TaskOptions{
        Queue:    queue,
        MaxRetry: 2,
        Timeout:  30 * time.Minute,
        Priority: 5,
        Unique:   true,
        UniqueTTL: 1 * time.Hour,
    }
}
```

### 2. QueueManager Registration Methods

Cập nhật `internal/pkg/queue/manager.go`:

```go
// RegisterTask đăng ký một task definition
func (m *QueueManager) RegisterTask(def *TaskDefinition) error {
    m.mu.Lock()
    defer m.mu.Unlock()
    
    if def.Type == "" {
        return fmt.Errorf("task type cannot be empty")
    }
    
    if def.Handler == nil {
        return fmt.Errorf("task handler cannot be nil")
    }
    
    if def.Options == nil {
        def.Options = DefaultTaskOptions()
    }
    
    // Kiểm tra task type đã được đăng ký chưa
    if _, exists := m.handlers[def.Type]; exists {
        return fmt.Errorf("task type already registered: %s", def.Type)
    }
    
    // Đăng ký handler
    m.handlers[def.Type] = def.Handler
    m.taskDefinitions[def.Type] = def
    
    m.logger.Info("Task handler registered",
        "type", def.Type,
        "description", def.Description,
        "queue", def.Options.Queue,
        "max_retry", def.Options.MaxRetry,
    )
    
    return nil
}

// RegisterBulkTasks đăng ký nhiều tasks cùng lúc
func (m *QueueManager) RegisterBulkTasks(definitions []*TaskDefinition) error {
    for _, def := range definitions {
        if err := m.RegisterTask(def); err != nil {
            return fmt.Errorf("failed to register task %s: %w", def.Type, err)
        }
    }
    return nil
}

// GetTaskDefinition lấy task definition
func (m *QueueManager) GetTaskDefinition(taskType string) (*TaskDefinition, bool) {
    m.mu.RLock()
    defer m.mu.RUnlock()
    
    def, exists := m.taskDefinitions[taskType]
    return def, exists
}

// ListRegisteredTasks liệt kê tất cả registered tasks
func (m *QueueManager) ListRegisteredTasks() map[string]*TaskDefinition {
    m.mu.RLock()
    defer m.mu.RUnlock()
    
    result := make(map[string]*TaskDefinition)
    for k, v := range m.taskDefinitions {
        result[k] = v
    }
    
    return result
}
```

## System Handler Registration

### 1. System Handlers Registration

Tạo file `internal/cron/registration.go`:

```go
package cron

import (
    "time"
    
    "wnapi/internal/cron/handlers"
    "wnapi/internal/pkg/logger"
    "wnapi/internal/pkg/queue"
    "wnapi/internal/pkg/queue/types"
)

// SystemHandlerRegistrar đăng ký system handlers
type SystemHandlerRegistrar struct {
    logger logger.Logger
}

// NewSystemHandlerRegistrar tạo system handler registrar mới
func NewSystemHandlerRegistrar(logger logger.Logger) *SystemHandlerRegistrar {
    return &SystemHandlerRegistrar{
        logger: logger,
    }
}

// RegisterHandlers đăng ký tất cả system handlers
func (r *SystemHandlerRegistrar) RegisterHandlers(manager *queue.QueueManager) error {
    systemHandlers := handlers.NewSystemHandlers(r.logger)
    
    // Định nghĩa tất cả system task definitions
    taskDefinitions := []*queue.TaskDefinition{
        {
            Type:        types.TaskTypeSystemCleanup,
            Description: "System cleanup and maintenance task",
            Handler:     systemHandlers.HandleSystemCleanup,
            Options: &queue.TaskOptions{
                Queue:     types.QueueMaintenance,
                MaxRetry:  2,
                Timeout:   30 * time.Minute,
                Priority:  queue.PriorityNormal,
                Unique:    true,
                UniqueTTL: 1 * time.Hour,
            },
        },
        {
            Type:        types.TaskTypeSystemHealthCheck,
            Description: "System health monitoring task",
            Handler:     systemHandlers.HandleSystemHealthCheck,
            Options: &queue.TaskOptions{
                Queue:     types.QueueCronJobs,
                MaxRetry:  1,
                Timeout:   5 * time.Minute,
                Priority:  queue.PriorityHigh,
                Unique:    true,
                UniqueTTL: 30 * time.Minute,
            },
        },
        {
            Type:        types.TaskTypeSystemBackup,
            Description: "System backup task",
            Handler:     systemHandlers.HandleSystemBackup,
            Options: &queue.TaskOptions{
                Queue:     types.QueueBackup,
                MaxRetry:  1,
                Timeout:   2 * time.Hour,
                Priority:  queue.PriorityLow,
                Unique:    true,
                UniqueTTL: 6 * time.Hour,
            },
        },
        {
            Type:        types.TaskTypeDatabaseCleanup,
            Description: "Database cleanup and optimization task",
            Handler:     systemHandlers.HandleDatabaseCleanup,
            Options: &queue.TaskOptions{
                Queue:     types.QueueMaintenance,
                MaxRetry:  1,
                Timeout:   1 * time.Hour,
                Priority:  queue.PriorityNormal,
                Unique:    true,
                UniqueTTL: 2 * time.Hour,
            },
        },
    }
    
    // Đăng ký tất cả task definitions
    if err := manager.RegisterBulkTasks(taskDefinitions); err != nil {
        return err
    }
    
    r.logger.Info("System cron handlers registered successfully",
        "count", len(taskDefinitions),
    )
    
    return nil
}
```

## Module Handler Registration

### 1. Auth Module Registration

Tạo file `modules/auth/queue/handlers/registration.go`:

```go
package handlers

import (
    "time"
    
    "wnapi/internal/pkg/logger"
    "wnapi/internal/pkg/queue"
    "wnapi/internal/pkg/queue/types"
    "wnapi/modules/auth/queue/types" as authTypes
    "wnapi/modules/auth/service"
)

// AuthHandlerRegistrar đăng ký auth handlers
type AuthHandlerRegistrar struct {
    logger      logger.Logger
    authService *service.AuthService
}

// NewAuthHandlerRegistrar tạo auth handler registrar mới
func NewAuthHandlerRegistrar(logger logger.Logger, authService *service.AuthService) *AuthHandlerRegistrar {
    return &AuthHandlerRegistrar{
        logger:      logger,
        authService: authService,
    }
}

// RegisterHandlers đăng ký tất cả auth handlers
func (r *AuthHandlerRegistrar) RegisterHandlers(manager *queue.QueueManager) error {
    authHandlers := NewAuthCronHandlers(r.logger, r.authService)
    
    taskDefinitions := []*queue.TaskDefinition{
        {
            Type:        authTypes.TaskTypeAuthCleanupSessions,
            Description: "Cleanup expired authentication sessions",
            Handler:     authHandlers.HandleCleanupSessions,
            Options: &queue.TaskOptions{
                Queue:     types.QueueCronJobs,
                MaxRetry:  2,
                Timeout:   15 * time.Minute,
                Priority:  queue.PriorityNormal,
                Unique:    true,
                UniqueTTL: 30 * time.Minute,
            },
            PayloadValidator: func(payload interface{}) error {
                if p, ok := payload.(*authTypes.AuthCleanupSessionsPayload); ok {
                    return p.Validate()
                }
                return nil
            },
        },
        {
            Type:        authTypes.TaskTypeAuthCleanupTokens,
            Description: "Cleanup expired authentication tokens",
            Handler:     authHandlers.HandleCleanupTokens,
            Options: &queue.TaskOptions{
                Queue:     types.QueueCronJobs,
                MaxRetry:  2,
                Timeout:   10 * time.Minute,
                Priority:  queue.PriorityNormal,
                Unique:    true,
                UniqueTTL: 30 * time.Minute,
            },
        },
        {
            Type:        authTypes.TaskTypeAuthPasswordExpiry,
            Description: "Send password expiry notifications",
            Handler:     authHandlers.HandlePasswordExpiry,
            Options: &queue.TaskOptions{
                Queue:     types.QueueNotifications,
                MaxRetry:  3,
                Timeout:   20 * time.Minute,
                Priority:  queue.PriorityHigh,
                Unique:    true,
                UniqueTTL: 1 * time.Hour,
            },
        },
        {
            Type:        authTypes.TaskTypeAuthSecurityReport,
            Description: "Generate security audit report",
            Handler:     authHandlers.HandleSecurityReport,
            Options: &queue.TaskOptions{
                Queue:     types.QueueReports,
                MaxRetry:  2,
                Timeout:   30 * time.Minute,
                Priority:  queue.PriorityNormal,
                Unique:    true,
                UniqueTTL: 2 * time.Hour,
            },
        },
    }
    
    if err := manager.RegisterBulkTasks(taskDefinitions); err != nil {
        return err
    }
    
    r.logger.Info("Auth cron handlers registered successfully",
        "module", "auth",
        "count", len(taskDefinitions),
    )
    
    return nil
}
```

### 2. Media Module Registration

Tạo file `modules/media/queue/handlers/registration.go`:

```go
package handlers

import (
    "time"
    
    "wnapi/internal/pkg/logger"
    "wnapi/internal/pkg/queue"
    "wnapi/internal/pkg/queue/types"
    mediaTypes "wnapi/modules/media/queue/types"
    "wnapi/modules/media/service"
)

// MediaHandlerRegistrar đăng ký media handlers
type MediaHandlerRegistrar struct {
    logger       logger.Logger
    mediaService *service.MediaService
}

// NewMediaHandlerRegistrar tạo media handler registrar mới
func NewMediaHandlerRegistrar(logger logger.Logger, mediaService *service.MediaService) *MediaHandlerRegistrar {
    return &MediaHandlerRegistrar{
        logger:       logger,
        mediaService: mediaService,
    }
}

// RegisterHandlers đăng ký tất cả media handlers
func (r *MediaHandlerRegistrar) RegisterHandlers(manager *queue.QueueManager) error {
    mediaHandlers := NewMediaCronHandlers(r.logger, r.mediaService)
    
    taskDefinitions := []*queue.TaskDefinition{
        {
            Type:        mediaTypes.TaskTypeMediaOptimizeImages,
            Description: "Optimize and compress media images",
            Handler:     mediaHandlers.HandleOptimizeImages,
            Options: &queue.TaskOptions{
                Queue:     types.QueueDataProcessing,
                MaxRetry:  2,
                Timeout:   1 * time.Hour,
                Priority:  queue.PriorityLow,
                Unique:    true,
                UniqueTTL: 2 * time.Hour,
            },
        },
        {
            Type:        mediaTypes.TaskTypeMediaCleanupTemp,
            Description: "Cleanup temporary media files",
            Handler:     mediaHandlers.HandleCleanupTemp,
            Options: &queue.TaskOptions{
                Queue:     types.QueueMaintenance,
                MaxRetry:  1,
                Timeout:   30 * time.Minute,
                Priority:  queue.PriorityNormal,
                Unique:    true,
                UniqueTTL: 1 * time.Hour,
            },
        },
        {
            Type:        mediaTypes.TaskTypeMediaGenerateThumbnails,
            Description: "Generate missing thumbnails for media files",
            Handler:     mediaHandlers.HandleGenerateThumbnails,
            Options: &queue.TaskOptions{
                Queue:     types.QueueDataProcessing,
                MaxRetry:  3,
                Timeout:   45 * time.Minute,
                Priority:  queue.PriorityNormal,
                Unique:    true,
                UniqueTTL: 1 * time.Hour,
            },
        },
    }
    
    if err := manager.RegisterBulkTasks(taskDefinitions); err != nil {
        return err
    }
    
    r.logger.Info("Media cron handlers registered successfully",
        "module", "media",
        "count", len(taskDefinitions),
    )
    
    return nil
}
```

## Global Registration Coordinator

### 1. Registration Coordinator

Tạo file `internal/cron/coordinator.go`:

```go
package cron

import (
    "fmt"
    
    "wnapi/internal/pkg/logger"
    "wnapi/internal/pkg/queue"
)

// RegistrationCoordinator quản lý việc đăng ký tất cả handlers
type RegistrationCoordinator struct {
    logger     logger.Logger
    registrars []queue.HandlerRegistrar
}

// NewRegistrationCoordinator tạo coordinator mới
func NewRegistrationCoordinator(logger logger.Logger) *RegistrationCoordinator {
    return &RegistrationCoordinator{
        logger:     logger,
        registrars: make([]queue.HandlerRegistrar, 0),
    }
}

// AddRegistrar thêm một registrar
func (c *RegistrationCoordinator) AddRegistrar(registrar queue.HandlerRegistrar) {
    c.registrars = append(c.registrars, registrar)
}

// RegisterAll đăng ký tất cả handlers từ các registrars
func (c *RegistrationCoordinator) RegisterAll(manager *queue.QueueManager) error {
    c.logger.Info("Starting cron handler registration",
        "registrars_count", len(c.registrars),
    )
    
    totalRegistered := 0
    
    for i, registrar := range c.registrars {
        c.logger.Debug("Registering handlers",
            "registrar_index", i,
            "registrar_type", fmt.Sprintf("%T", registrar),
        )
        
        beforeCount := len(manager.ListRegisteredTasks())
        
        if err := registrar.RegisterHandlers(manager); err != nil {
            return fmt.Errorf("failed to register handlers from registrar %d: %w", i, err)
        }
        
        afterCount := len(manager.ListRegisteredTasks())
        registered := afterCount - beforeCount
        totalRegistered += registered
        
        c.logger.Debug("Handlers registered",
            "registrar_index", i,
            "handlers_registered", registered,
        )
    }
    
    c.logger.Info("All cron handlers registered successfully",
        "total_registrars", len(c.registrars),
        "total_handlers", totalRegistered,
    )
    
    return nil
}
```

### 2. Bootstrap Integration

Cập nhật `cmd/server/main.go` để tích hợp registration:

```go
// initCronHandlers khởi tạo và đăng ký cron handlers
func initCronHandlers(queueManager *queue.QueueManager, appLogger logger.Logger) error {
    coordinator := cron.NewRegistrationCoordinator(appLogger)
    
    // Đăng ký system handlers
    systemRegistrar := cron.NewSystemHandlerRegistrar(appLogger)
    coordinator.AddRegistrar(systemRegistrar)
    
    // TODO: Thêm module registrars khi modules được khởi tạo
    // Ví dụ:
    // if authService != nil {
    //     authRegistrar := authHandlers.NewAuthHandlerRegistrar(appLogger, authService)
    //     coordinator.AddRegistrar(authRegistrar)
    // }
    
    // Đăng ký tất cả handlers
    if err := coordinator.RegisterAll(queueManager); err != nil {
        return fmt.Errorf("failed to register cron handlers: %w", err)
    }
    
    return nil
}

// Trong hàm main, sau khi khởi tạo QueueManager
func main() {
    // ... existing code ...
    
    // Khởi tạo QueueManager
    if err := initQueueManager(appLogger, cfg); err != nil {
        appLogger.Error("Failed to initialize queue manager", "error", err)
        os.Exit(1)
    }
    
    queueManager := queue.GetGlobalManager()
    if queueManager == nil {
        appLogger.Error("Queue manager not initialized")
        os.Exit(1)
    }
    
    // Đăng ký cron handlers
    if err := initCronHandlers(queueManager, appLogger); err != nil {
        appLogger.Error("Failed to initialize cron handlers", "error", err)
        os.Exit(1)
    }
    
    // ... rest of main function ...
}
```

## Module Integration

### 1. Module Registration Hook

Cập nhật module interface để hỗ trợ handler registration:

```go
// internal/core/module.go
type Module interface {
    Name() string
    Init(ctx context.Context) error
    Cleanup(ctx context.Context) error
    
    // Thêm method cho cron handler registration
    RegisterCronHandlers(manager *queue.QueueManager) error
}

// Implement trong modules
func (m *AuthModule) RegisterCronHandlers(manager *queue.QueueManager) error {
    if m.authService == nil {
        return fmt.Errorf("auth service not initialized")
    }
    
    registrar := handlers.NewAuthHandlerRegistrar(m.logger, m.authService)
    return registrar.RegisterHandlers(manager)
}
```

### 2. Dynamic Registration

Tạo file `internal/cron/dynamic.go`:

```go
package cron

import (
    "fmt"
    
    "wnapi/internal/core"
    "wnapi/internal/pkg/logger"
    "wnapi/internal/pkg/queue"
)

// DynamicRegistrar đăng ký handlers từ modules
type DynamicRegistrar struct {
    logger  logger.Logger
    modules []core.Module
}

// NewDynamicRegistrar tạo dynamic registrar mới
func NewDynamicRegistrar(logger logger.Logger, modules []core.Module) *DynamicRegistrar {
    return &DynamicRegistrar{
        logger:  logger,
        modules: modules,
    }
}

// RegisterHandlers đăng ký handlers từ tất cả modules
func (r *DynamicRegistrar) RegisterHandlers(manager *queue.QueueManager) error {
    for _, module := range r.modules {
        // Kiểm tra module có implement CronHandlerRegistrar không
        if cronModule, ok := module.(interface {
            RegisterCronHandlers(*queue.QueueManager) error
        }); ok {
            r.logger.Debug("Registering cron handlers for module",
                "module", module.Name(),
            )
            
            if err := cronModule.RegisterCronHandlers(manager); err != nil {
                return fmt.Errorf("failed to register cron handlers for module %s: %w", module.Name(), err)
            }
            
            r.logger.Info("Cron handlers registered for module",
                "module", module.Name(),
            )
        }
    }
    
    return nil
}
```

## Validation và Testing

### 1. Registration Validation

```go
// ValidateRegistration kiểm tra registration hợp lệ
func ValidateRegistration(manager *queue.QueueManager) error {
    tasks := manager.ListRegisteredTasks()
    
    if len(tasks) == 0 {
        return fmt.Errorf("no tasks registered")
    }
    
    // Kiểm tra required system tasks
    requiredTasks := []string{
        types.TaskTypeSystemHealthCheck,
        types.TaskTypeSystemCleanup,
    }
    
    for _, required := range requiredTasks {
        if _, exists := tasks[required]; !exists {
            return fmt.Errorf("required task not registered: %s", required)
        }
    }
    
    // Kiểm tra queue configuration
    queues := make(map[string]bool)
    for _, task := range tasks {
        queues[task.Options.Queue] = true
    }
    
    // Đảm bảo có ít nhất một queue được cấu hình
    if len(queues) == 0 {
        return fmt.Errorf("no queues configured")
    }
    
    return nil
}
```

### 2. Registration Tests

```go
func TestRegistrationCoordinator_RegisterAll(t *testing.T) {
    logger := logger.NewTestLogger()
    manager := queue.NewTestQueueManager()
    coordinator := NewRegistrationCoordinator(logger)
    
    // Add test registrar
    testRegistrar := &TestRegistrar{}
    coordinator.AddRegistrar(testRegistrar)
    
    err := coordinator.RegisterAll(manager)
    assert.NoError(t, err)
    
    // Verify tasks were registered
    tasks := manager.ListRegisteredTasks()
    assert.Greater(t, len(tasks), 0)
}
```

## Next Steps

Sau khi hoàn thành handler registration:

1. ✅ Registration interface đã được định nghĩa
2. ✅ System handlers đã được đăng ký
3. ✅ Module registration đã được thiết lập
4. ✅ Coordination system đã được tạo

Tiếp theo: [06-module-integration.md](06-module-integration.md) - Tích hợp với module system
