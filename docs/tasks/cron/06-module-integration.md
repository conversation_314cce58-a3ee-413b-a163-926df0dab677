# Task 06: Module Integration

Tích hợp cron jobs với module system c<PERSON>a ứng dụng.

## <PERSON><PERSON><PERSON> tiêu

- <PERSON><PERSON><PERSON> hợp cron scheduling vào module lifecycle
- <PERSON><PERSON><PERSON><PERSON> lập dependency injection cho handlers
- <PERSON><PERSON><PERSON> hình module-specific cron jobs
- Quản lý module startup và shutdown

## Module Interface Extension

### 1. Cron-aware Module Interface

Cập nhật `internal/core/module.go`:

```go
package core

import (
    "context"
    "wnapi/internal/pkg/queue"
)

// Module interface cơ bản
type Module interface {
    Name() string
    Init(ctx context.Context) error
    Cleanup(ctx context.Context) error
    GetMigrationPath() string
}

// CronModule interface cho modules có cron jobs
type CronModule interface {
    Module
    RegisterCronHandlers(manager *queue.QueueManager) error
    ScheduleCronJobs(scheduler queue.Scheduler) error
}

// ModuleWithDependencies interface cho modules cần dependencies
type ModuleWithDependencies interface {
    Module
    SetDependencies(deps *ModuleDependencies) error
}

// ModuleDependencies chứa các dependencies chung
type ModuleDependencies struct {
    Logger       Logger
    Database     *sql.DB
    GormDB       *gorm.DB
    Redis        *redis.Client
    QueueManager *queue.QueueManager
    Config       Config
}
```

### 2. Base Module Implementation

Tạo file `internal/core/base_module.go`:

```go
package core

import (
    "context"
    "fmt"
    
    "wnapi/internal/pkg/logger"
    "wnapi/internal/pkg/queue"
)

// BaseModule cung cấp implementation cơ bản cho modules
type BaseModule struct {
    name         string
    logger       logger.Logger
    dependencies *ModuleDependencies
    initialized  bool
}

// NewBaseModule tạo base module mới
func NewBaseModule(name string, deps *ModuleDependencies) *BaseModule {
    return &BaseModule{
        name:         name,
        logger:       deps.Logger.With("module", name),
        dependencies: deps,
        initialized:  false,
    }
}

// Name trả về tên module
func (m *BaseModule) Name() string {
    return m.name
}

// GetLogger trả về logger của module
func (m *BaseModule) GetLogger() logger.Logger {
    return m.logger
}

// GetDependencies trả về dependencies
func (m *BaseModule) GetDependencies() *ModuleDependencies {
    return m.dependencies
}

// SetDependencies thiết lập dependencies
func (m *BaseModule) SetDependencies(deps *ModuleDependencies) error {
    if m.initialized {
        return fmt.Errorf("cannot set dependencies after module initialization")
    }
    
    m.dependencies = deps
    m.logger = deps.Logger.With("module", m.name)
    return nil
}

// IsInitialized kiểm tra module đã được khởi tạo chưa
func (m *BaseModule) IsInitialized() bool {
    return m.initialized
}

// MarkInitialized đánh dấu module đã được khởi tạo
func (m *BaseModule) MarkInitialized() {
    m.initialized = true
}

// Init implementation mặc định
func (m *BaseModule) Init(ctx context.Context) error {
    m.logger.Info("Initializing module")
    m.MarkInitialized()
    return nil
}

// Cleanup implementation mặc định
func (m *BaseModule) Cleanup(ctx context.Context) error {
    m.logger.Info("Cleaning up module")
    return nil
}

// GetMigrationPath implementation mặc định
func (m *BaseModule) GetMigrationPath() string {
    return fmt.Sprintf("modules/%s/migrations", m.name)
}
```

## Module Implementation Examples

### 1. Auth Module với Cron Jobs

Cập nhật `modules/auth/module.go`:

```go
package auth

import (
    "context"
    "fmt"
    
    "wnapi/internal/core"
    "wnapi/internal/pkg/queue"
    "wnapi/modules/auth/queue/handlers"
    "wnapi/modules/auth/queue/schedulers"
    "wnapi/modules/auth/service"
)

// Module implements core.CronModule
type Module struct {
    *core.BaseModule
    authService *service.AuthService
    registrar   *handlers.AuthHandlerRegistrar
    scheduler   *schedulers.AuthScheduler
}

// NewModule tạo auth module mới
func NewModule(deps *core.ModuleDependencies, config map[string]interface{}) (core.Module, error) {
    baseModule := core.NewBaseModule("auth", deps)
    
    // Khởi tạo auth service
    authService := service.NewAuthService(
        deps.Database,
        deps.GormDB,
        deps.Redis,
        deps.Logger,
    )
    
    // Khởi tạo handler registrar
    registrar := handlers.NewAuthHandlerRegistrar(
        deps.Logger,
        authService,
    )
    
    // Khởi tạo scheduler
    scheduler := schedulers.NewAuthScheduler(
        deps.Logger,
        authService,
    )
    
    return &Module{
        BaseModule:  baseModule,
        authService: authService,
        registrar:   registrar,
        scheduler:   scheduler,
    }, nil
}

// Init khởi tạo module
func (m *Module) Init(ctx context.Context) error {
    if err := m.BaseModule.Init(ctx); err != nil {
        return err
    }
    
    m.GetLogger().Info("Initializing auth module with cron jobs")
    
    // Khởi tạo auth service
    if err := m.authService.Init(ctx); err != nil {
        return fmt.Errorf("failed to initialize auth service: %w", err)
    }
    
    return nil
}

// RegisterCronHandlers đăng ký cron handlers
func (m *Module) RegisterCronHandlers(manager *queue.QueueManager) error {
    if !m.IsInitialized() {
        return fmt.Errorf("module not initialized")
    }
    
    m.GetLogger().Info("Registering auth cron handlers")
    return m.registrar.RegisterHandlers(manager)
}

// ScheduleCronJobs lên lịch cron jobs
func (m *Module) ScheduleCronJobs(scheduler queue.Scheduler) error {
    if !m.IsInitialized() {
        return fmt.Errorf("module not initialized")
    }
    
    m.GetLogger().Info("Scheduling auth cron jobs")
    return m.scheduler.ScheduleAllJobs(scheduler)
}

// Cleanup dọn dẹp module
func (m *Module) Cleanup(ctx context.Context) error {
    m.GetLogger().Info("Cleaning up auth module")
    
    if m.authService != nil {
        if err := m.authService.Cleanup(ctx); err != nil {
            m.GetLogger().Error("Failed to cleanup auth service", "error", err)
        }
    }
    
    return m.BaseModule.Cleanup(ctx)
}
```

### 2. Media Module với Cron Jobs

Tạo file `modules/media/module.go`:

```go
package media

import (
    "context"
    "fmt"
    
    "wnapi/internal/core"
    "wnapi/internal/pkg/queue"
    "wnapi/modules/media/queue/handlers"
    "wnapi/modules/media/queue/schedulers"
    "wnapi/modules/media/service"
)

// Module implements core.CronModule
type Module struct {
    *core.BaseModule
    mediaService *service.MediaService
    registrar    *handlers.MediaHandlerRegistrar
    scheduler    *schedulers.MediaScheduler
}

// NewModule tạo media module mới
func NewModule(deps *core.ModuleDependencies, config map[string]interface{}) (core.Module, error) {
    baseModule := core.NewBaseModule("media", deps)
    
    // Parse media-specific config
    mediaConfig, err := parseMediaConfig(config)
    if err != nil {
        return nil, fmt.Errorf("invalid media config: %w", err)
    }
    
    // Khởi tạo media service
    mediaService := service.NewMediaService(
        deps.Database,
        deps.GormDB,
        deps.Logger,
        mediaConfig,
    )
    
    // Khởi tạo handler registrar
    registrar := handlers.NewMediaHandlerRegistrar(
        deps.Logger,
        mediaService,
    )
    
    // Khởi tạo scheduler
    scheduler := schedulers.NewMediaScheduler(
        deps.Logger,
        mediaService,
        mediaConfig,
    )
    
    return &Module{
        BaseModule:   baseModule,
        mediaService: mediaService,
        registrar:    registrar,
        scheduler:    scheduler,
    }, nil
}

// Init khởi tạo module
func (m *Module) Init(ctx context.Context) error {
    if err := m.BaseModule.Init(ctx); err != nil {
        return err
    }
    
    m.GetLogger().Info("Initializing media module with cron jobs")
    
    // Khởi tạo media service
    if err := m.mediaService.Init(ctx); err != nil {
        return fmt.Errorf("failed to initialize media service: %w", err)
    }
    
    return nil
}

// RegisterCronHandlers đăng ký cron handlers
func (m *Module) RegisterCronHandlers(manager *queue.QueueManager) error {
    if !m.IsInitialized() {
        return fmt.Errorf("module not initialized")
    }
    
    m.GetLogger().Info("Registering media cron handlers")
    return m.registrar.RegisterHandlers(manager)
}

// ScheduleCronJobs lên lịch cron jobs
func (m *Module) ScheduleCronJobs(scheduler queue.Scheduler) error {
    if !m.IsInitialized() {
        return fmt.Errorf("module not initialized")
    }
    
    m.GetLogger().Info("Scheduling media cron jobs")
    return m.scheduler.ScheduleAllJobs(scheduler)
}

// parseMediaConfig parse media-specific configuration
func parseMediaConfig(config map[string]interface{}) (*service.MediaConfig, error) {
    // TODO: Implement config parsing
    return &service.MediaConfig{}, nil
}
```

## Module Manager Integration

### 1. Cron-aware Module Manager

Cập nhật `internal/core/module_manager.go`:

```go
package core

import (
    "context"
    "fmt"
    
    "wnapi/internal/pkg/logger"
    "wnapi/internal/pkg/queue"
)

// ModuleManager quản lý tất cả modules
type ModuleManager struct {
    logger       logger.Logger
    modules      []Module
    cronModules  []CronModule
    dependencies *ModuleDependencies
    initialized  bool
}

// NewModuleManager tạo module manager mới
func NewModuleManager(logger logger.Logger, deps *ModuleDependencies) *ModuleManager {
    return &ModuleManager{
        logger:       logger,
        modules:      make([]Module, 0),
        cronModules:  make([]CronModule, 0),
        dependencies: deps,
        initialized:  false,
    }
}

// RegisterModule đăng ký một module
func (mm *ModuleManager) RegisterModule(module Module) error {
    if mm.initialized {
        return fmt.Errorf("cannot register module after initialization")
    }
    
    // Set dependencies nếu module support
    if depModule, ok := module.(ModuleWithDependencies); ok {
        if err := depModule.SetDependencies(mm.dependencies); err != nil {
            return fmt.Errorf("failed to set dependencies for module %s: %w", module.Name(), err)
        }
    }
    
    mm.modules = append(mm.modules, module)
    
    // Thêm vào cronModules nếu module implement CronModule
    if cronModule, ok := module.(CronModule); ok {
        mm.cronModules = append(mm.cronModules, cronModule)
        mm.logger.Info("Registered cron module", "module", module.Name())
    }
    
    mm.logger.Info("Registered module", "module", module.Name())
    return nil
}

// InitializeAll khởi tạo tất cả modules
func (mm *ModuleManager) InitializeAll(ctx context.Context) error {
    mm.logger.Info("Initializing all modules", "count", len(mm.modules))
    
    for _, module := range mm.modules {
        mm.logger.Info("Initializing module", "module", module.Name())
        
        if err := module.Init(ctx); err != nil {
            return fmt.Errorf("failed to initialize module %s: %w", module.Name(), err)
        }
        
        mm.logger.Info("Module initialized successfully", "module", module.Name())
    }
    
    mm.initialized = true
    mm.logger.Info("All modules initialized successfully")
    return nil
}

// RegisterAllCronHandlers đăng ký tất cả cron handlers
func (mm *ModuleManager) RegisterAllCronHandlers(manager *queue.QueueManager) error {
    if !mm.initialized {
        return fmt.Errorf("modules not initialized")
    }
    
    mm.logger.Info("Registering cron handlers for all modules", "cron_modules", len(mm.cronModules))
    
    for _, cronModule := range mm.cronModules {
        mm.logger.Info("Registering cron handlers", "module", cronModule.Name())
        
        if err := cronModule.RegisterCronHandlers(manager); err != nil {
            return fmt.Errorf("failed to register cron handlers for module %s: %w", cronModule.Name(), err)
        }
        
        mm.logger.Info("Cron handlers registered", "module", cronModule.Name())
    }
    
    mm.logger.Info("All cron handlers registered successfully")
    return nil
}

// ScheduleAllCronJobs lên lịch tất cả cron jobs
func (mm *ModuleManager) ScheduleAllCronJobs(scheduler queue.Scheduler) error {
    if !mm.initialized {
        return fmt.Errorf("modules not initialized")
    }
    
    mm.logger.Info("Scheduling cron jobs for all modules", "cron_modules", len(mm.cronModules))
    
    for _, cronModule := range mm.cronModules {
        mm.logger.Info("Scheduling cron jobs", "module", cronModule.Name())
        
        if err := cronModule.ScheduleCronJobs(scheduler); err != nil {
            return fmt.Errorf("failed to schedule cron jobs for module %s: %w", cronModule.Name(), err)
        }
        
        mm.logger.Info("Cron jobs scheduled", "module", cronModule.Name())
    }
    
    mm.logger.Info("All cron jobs scheduled successfully")
    return nil
}

// CleanupAll dọn dẹp tất cả modules
func (mm *ModuleManager) CleanupAll(ctx context.Context) error {
    mm.logger.Info("Cleaning up all modules")
    
    // Cleanup theo thứ tự ngược lại
    for i := len(mm.modules) - 1; i >= 0; i-- {
        module := mm.modules[i]
        mm.logger.Info("Cleaning up module", "module", module.Name())
        
        if err := module.Cleanup(ctx); err != nil {
            mm.logger.Error("Failed to cleanup module", "module", module.Name(), "error", err)
            // Continue với modules khác
        }
    }
    
    mm.logger.Info("All modules cleaned up")
    return nil
}

// GetModules trả về danh sách modules
func (mm *ModuleManager) GetModules() []Module {
    return mm.modules
}

// GetCronModules trả về danh sách cron modules
func (mm *ModuleManager) GetCronModules() []CronModule {
    return mm.cronModules
}
```

## Bootstrap Integration

### 1. Application Bootstrap

Cập nhật `cmd/server/main.go`:

```go
func main() {
    // ... existing initialization code ...
    
    // Khởi tạo dependencies
    deps := &core.ModuleDependencies{
        Logger:       appLogger,
        Database:     db,
        GormDB:       gormDB,
        Redis:        redisClient,
        QueueManager: queueManager,
        Config:       cfg,
    }
    
    // Khởi tạo module manager
    moduleManager := core.NewModuleManager(appLogger, deps)
    
    // Đăng ký modules
    if err := registerModules(moduleManager, cfg); err != nil {
        appLogger.Error("Failed to register modules", "error", err)
        os.Exit(1)
    }
    
    // Khởi tạo tất cả modules
    if err := moduleManager.InitializeAll(ctx); err != nil {
        appLogger.Error("Failed to initialize modules", "error", err)
        os.Exit(1)
    }
    
    // Đăng ký cron handlers
    if err := moduleManager.RegisterAllCronHandlers(queueManager); err != nil {
        appLogger.Error("Failed to register cron handlers", "error", err)
        os.Exit(1)
    }
    
    // Lên lịch cron jobs
    scheduler := queueManager.GetScheduler()
    if scheduler != nil {
        if err := moduleManager.ScheduleAllCronJobs(scheduler); err != nil {
            appLogger.Error("Failed to schedule cron jobs", "error", err)
            os.Exit(1)
        }
    }
    
    // ... rest of main function ...
    
    // Graceful shutdown
    defer func() {
        if err := moduleManager.CleanupAll(context.Background()); err != nil {
            appLogger.Error("Failed to cleanup modules", "error", err)
        }
    }()
}

// registerModules đăng ký tất cả modules
func registerModules(manager *core.ModuleManager, cfg config.Config) error {
    // Đăng ký auth module
    authModule, err := auth.NewModule(manager.GetDependencies(), cfg.GetModuleConfig("auth"))
    if err != nil {
        return fmt.Errorf("failed to create auth module: %w", err)
    }
    if err := manager.RegisterModule(authModule); err != nil {
        return fmt.Errorf("failed to register auth module: %w", err)
    }
    
    // Đăng ký media module
    mediaModule, err := media.NewModule(manager.GetDependencies(), cfg.GetModuleConfig("media"))
    if err != nil {
        return fmt.Errorf("failed to create media module: %w", err)
    }
    if err := manager.RegisterModule(mediaModule); err != nil {
        return fmt.Errorf("failed to register media module: %w", err)
    }
    
    // TODO: Đăng ký các modules khác
    
    return nil
}
```

## Configuration Management

### 1. Module-specific Configuration

Tạo file `internal/config/module_config.go`:

```go
package config

// ModuleConfig chứa cấu hình cho modules
type ModuleConfig struct {
    Auth  AuthModuleConfig  `yaml:"auth"`
    Media MediaModuleConfig `yaml:"media"`
    // ... other modules
}

// AuthModuleConfig cấu hình cho auth module
type AuthModuleConfig struct {
    SessionCleanup struct {
        Enabled   bool   `yaml:"enabled" env:"AUTH_SESSION_CLEANUP_ENABLED" envDefault:"true"`
        Schedule  string `yaml:"schedule" env:"AUTH_SESSION_CLEANUP_SCHEDULE" envDefault:"0 2 * * *"`
        MaxAge    string `yaml:"max_age" env:"AUTH_SESSION_CLEANUP_MAX_AGE" envDefault:"24h"`
    } `yaml:"session_cleanup"`
    
    PasswordExpiry struct {
        Enabled     bool     `yaml:"enabled" env:"AUTH_PASSWORD_EXPIRY_ENABLED" envDefault:"true"`
        Schedule    string   `yaml:"schedule" env:"AUTH_PASSWORD_EXPIRY_SCHEDULE" envDefault:"0 9 * * *"`
        NotifyDays  []int    `yaml:"notify_days" env:"AUTH_PASSWORD_EXPIRY_NOTIFY_DAYS" envSeparator:","`
        TemplateID  string   `yaml:"template_id" env:"AUTH_PASSWORD_EXPIRY_TEMPLATE_ID"`
    } `yaml:"password_expiry"`
}

// MediaModuleConfig cấu hình cho media module
type MediaModuleConfig struct {
    ImageOptimization struct {
        Enabled   bool   `yaml:"enabled" env:"MEDIA_IMAGE_OPTIMIZATION_ENABLED" envDefault:"true"`
        Schedule  string `yaml:"schedule" env:"MEDIA_IMAGE_OPTIMIZATION_SCHEDULE" envDefault:"0 3 * * *"`
        Quality   int    `yaml:"quality" env:"MEDIA_IMAGE_OPTIMIZATION_QUALITY" envDefault:"85"`
        BatchSize int    `yaml:"batch_size" env:"MEDIA_IMAGE_OPTIMIZATION_BATCH_SIZE" envDefault:"100"`
    } `yaml:"image_optimization"`
    
    TempCleanup struct {
        Enabled  bool   `yaml:"enabled" env:"MEDIA_TEMP_CLEANUP_ENABLED" envDefault:"true"`
        Schedule string `yaml:"schedule" env:"MEDIA_TEMP_CLEANUP_SCHEDULE" envDefault:"0 1 * * *"`
        MaxAge   string `yaml:"max_age" env:"MEDIA_TEMP_CLEANUP_MAX_AGE" envDefault:"24h"`
    } `yaml:"temp_cleanup"`
}

// GetModuleConfig trả về config cho module cụ thể
func (c *Config) GetModuleConfig(moduleName string) map[string]interface{} {
    switch moduleName {
    case "auth":
        return map[string]interface{}{
            "session_cleanup":  c.Modules.Auth.SessionCleanup,
            "password_expiry":  c.Modules.Auth.PasswordExpiry,
        }
    case "media":
        return map[string]interface{}{
            "image_optimization": c.Modules.Media.ImageOptimization,
            "temp_cleanup":       c.Modules.Media.TempCleanup,
        }
    default:
        return make(map[string]interface{})
    }
}
```

## Testing

### 1. Module Integration Tests

```go
func TestModuleManager_CronIntegration(t *testing.T) {
    logger := logger.NewTestLogger()
    queueManager := queue.NewTestQueueManager()
    
    deps := &core.ModuleDependencies{
        Logger:       logger,
        QueueManager: queueManager,
    }
    
    manager := core.NewModuleManager(logger, deps)
    
    // Register test module
    testModule := &TestCronModule{}
    err := manager.RegisterModule(testModule)
    assert.NoError(t, err)
    
    // Initialize modules
    err = manager.InitializeAll(context.Background())
    assert.NoError(t, err)
    
    // Register cron handlers
    err = manager.RegisterAllCronHandlers(queueManager)
    assert.NoError(t, err)
    
    // Verify handlers were registered
    tasks := queueManager.ListRegisteredTasks()
    assert.Greater(t, len(tasks), 0)
}
```

## Next Steps

Sau khi hoàn thành module integration:

1. ✅ Module interface đã được mở rộng
2. ✅ Base module implementation đã được tạo
3. ✅ Module manager đã được cập nhật
4. ✅ Bootstrap integration đã được thiết lập

Tiếp theo: [07-scheduling-tasks.md](07-scheduling-tasks.md) - Lên lịch thực thi tasks
