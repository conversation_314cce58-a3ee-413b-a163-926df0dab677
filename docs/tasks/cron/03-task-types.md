# Task 03: Task Types and Payloads

<PERSON><PERSON><PERSON> nghĩa task types và payload structures cho cron jobs.

## <PERSON><PERSON><PERSON> ti<PERSON>

- <PERSON><PERSON><PERSON> nghĩa task type constants
- Tạo payload structures
- Thiết lập validation rules
- T<PERSON> chức theo modules và categories

## Task Type Naming Convention

### 1. Pattern

```
cron:{category}:{action}
cron:{module}:{action}
cron:{module}:{category}:{action}
```

### 2. Examples

```go
// System-wide tasks
"cron:system:cleanup"
"cron:system:health_check"
"cron:system:backup"

// Module-specific tasks
"cron:auth:cleanup_sessions"
"cron:notification:send_digest"
"cron:media:optimize_images"

// Category-specific tasks
"cron:reports:daily_summary"
"cron:maintenance:database_cleanup"
"cron:monitoring:collect_metrics"
```

## Core Task Types

### 1. System Tasks

Tạo file `internal/pkg/queue/types/cron.go`:

```go
package types

// System-wide cron task types
const (
    // System maintenance
    TaskTypeSystemCleanup        = "cron:system:cleanup"
    TaskTypeSystemHealthCheck    = "cron:system:health_check"
    TaskTypeSystemBackup         = "cron:system:backup"
    TaskTypeSystemMetrics        = "cron:system:metrics"
    
    // Database maintenance
    TaskTypeDatabaseCleanup      = "cron:database:cleanup"
    TaskTypeDatabaseOptimize     = "cron:database:optimize"
    TaskTypeDatabaseBackup       = "cron:database:backup"
    
    // Log management
    TaskTypeLogRotation          = "cron:log:rotation"
    TaskTypeLogCleanup           = "cron:log:cleanup"
    TaskTypeLogArchival          = "cron:log:archival"
    
    // Security tasks
    TaskTypeSecurityScan         = "cron:security:scan"
    TaskTypeSecurityAudit        = "cron:security:audit"
    TaskTypeSecurityCleanup      = "cron:security:cleanup"
)

// Queue names for different task categories
const (
    QueueCronJobs        = "cron_jobs"
    QueueMaintenance     = "maintenance"
    QueueReports         = "reports"
    QueueNotifications   = "notifications"
    QueueDataProcessing  = "data_processing"
    QueueSecurity        = "security"
    QueueBackup          = "backup"
)

// Task priorities
const (
    PriorityLow    = 1
    PriorityNormal = 5
    PriorityHigh   = 10
    PriorityCritical = 20
)
```

### 2. Module-specific Task Types

#### Auth Module

Tạo file `modules/auth/queue/types/constants.go`:

```go
package types

// Auth module cron task types
const (
    TaskTypeAuthCleanupSessions     = "cron:auth:cleanup_sessions"
    TaskTypeAuthCleanupTokens       = "cron:auth:cleanup_tokens"
    TaskTypeAuthPasswordExpiry      = "cron:auth:password_expiry"
    TaskTypeAuthSecurityReport      = "cron:auth:security_report"
    TaskTypeAuthFailedLoginCleanup  = "cron:auth:failed_login_cleanup"
)
```

#### Notification Module

Tạo file `modules/notification/queue/types/constants.go`:

```go
package types

// Notification module cron task types
const (
    TaskTypeNotificationDigest      = "cron:notification:digest"
    TaskTypeNotificationCleanup     = "cron:notification:cleanup"
    TaskTypeNotificationRetry       = "cron:notification:retry"
    TaskTypeNotificationReport      = "cron:notification:report"
    TaskTypeNotificationTemplateSync = "cron:notification:template_sync"
)
```

#### Media Module

Tạo file `modules/media/queue/types/constants.go`:

```go
package types

// Media module cron task types
const (
    TaskTypeMediaOptimizeImages     = "cron:media:optimize_images"
    TaskTypeMediaCleanupTemp        = "cron:media:cleanup_temp"
    TaskTypeMediaGenerateThumbnails = "cron:media:generate_thumbnails"
    TaskTypeMediaSyncStorage        = "cron:media:sync_storage"
    TaskTypeMediaUsageReport        = "cron:media:usage_report"
)
```

## Payload Structures

### 1. Base Payload

Tạo file `internal/pkg/queue/types/payloads.go`:

```go
package types

import (
    "time"
)

// BaseCronPayload chứa thông tin cơ bản cho tất cả cron jobs
type BaseCronPayload struct {
    JobID       string            `json:"job_id"`
    ScheduledAt time.Time         `json:"scheduled_at"`
    TenantID    *uint             `json:"tenant_id,omitempty"`
    UserID      *uint             `json:"user_id,omitempty"`
    Metadata    map[string]string `json:"metadata,omitempty"`
    DryRun      bool              `json:"dry_run,omitempty"`
}

// SystemCleanupPayload cho system cleanup tasks
type SystemCleanupPayload struct {
    BaseCronPayload
    CleanupType    string   `json:"cleanup_type"`    // "temp_files", "logs", "cache"
    MaxAge         string   `json:"max_age"`         // "7d", "30d", "1y"
    Directories    []string `json:"directories,omitempty"`
    ExcludePattern string   `json:"exclude_pattern,omitempty"`
    ForceCleanup   bool     `json:"force_cleanup,omitempty"`
}

// DatabaseCleanupPayload cho database cleanup tasks
type DatabaseCleanupPayload struct {
    BaseCronPayload
    Tables         []string `json:"tables,omitempty"`
    MaxAge         string   `json:"max_age"`
    BatchSize      int      `json:"batch_size,omitempty"`
    AnalyzeAfter   bool     `json:"analyze_after,omitempty"`
}

// BackupPayload cho backup tasks
type BackupPayload struct {
    BaseCronPayload
    BackupType     string   `json:"backup_type"`     // "full", "incremental"
    Destination    string   `json:"destination"`     // "s3", "local", "ftp"
    Compression    bool     `json:"compression,omitempty"`
    Encryption     bool     `json:"encryption,omitempty"`
    RetentionDays  int      `json:"retention_days,omitempty"`
}

// ReportPayload cho report generation tasks
type ReportPayload struct {
    BaseCronPayload
    ReportType     string            `json:"report_type"`
    Period         string            `json:"period"`         // "daily", "weekly", "monthly"
    Format         string            `json:"format"`         // "pdf", "csv", "json"
    Recipients     []string          `json:"recipients,omitempty"`
    Parameters     map[string]interface{} `json:"parameters,omitempty"`
}
```

### 2. Module-specific Payloads

#### Auth Module Payloads

Tạo file `modules/auth/queue/types/payloads.go`:

```go
package types

import (
    "time"
    commonTypes "wnapi/internal/pkg/queue/types"
)

// AuthCleanupSessionsPayload cho cleanup sessions task
type AuthCleanupSessionsPayload struct {
    commonTypes.BaseCronPayload
    MaxAge          string `json:"max_age"`          // "24h", "7d"
    InactiveOnly    bool   `json:"inactive_only"`
    ExcludeUserIDs  []uint `json:"exclude_user_ids,omitempty"`
}

// AuthPasswordExpiryPayload cho password expiry notification
type AuthPasswordExpiryPayload struct {
    commonTypes.BaseCronPayload
    NotifyDays      []int  `json:"notify_days"`      // [7, 3, 1] days before expiry
    TemplateID      string `json:"template_id,omitempty"`
}

// AuthSecurityReportPayload cho security report generation
type AuthSecurityReportPayload struct {
    commonTypes.BaseCronPayload
    ReportPeriod    string   `json:"report_period"`    // "daily", "weekly"
    IncludeMetrics  []string `json:"include_metrics"`  // ["failed_logins", "new_users"]
    Recipients      []string `json:"recipients"`
}
```

#### Notification Module Payloads

Tạo file `modules/notification/queue/types/payloads.go`:

```go
package types

import (
    commonTypes "wnapi/internal/pkg/queue/types"
)

// NotificationDigestPayload cho digest notifications
type NotificationDigestPayload struct {
    commonTypes.BaseCronPayload
    DigestType      string   `json:"digest_type"`      // "daily", "weekly"
    UserGroups      []string `json:"user_groups,omitempty"`
    TemplateID      string   `json:"template_id"`
    MaxNotifications int     `json:"max_notifications,omitempty"`
}

// NotificationCleanupPayload cho cleanup old notifications
type NotificationCleanupPayload struct {
    commonTypes.BaseCronPayload
    MaxAge          string   `json:"max_age"`          // "30d", "90d"
    Status          []string `json:"status,omitempty"` // ["read", "delivered"]
    BatchSize       int      `json:"batch_size,omitempty"`
}

// NotificationRetryPayload cho retry failed notifications
type NotificationRetryPayload struct {
    commonTypes.BaseCronPayload
    MaxRetries      int      `json:"max_retries"`
    RetryTypes      []string `json:"retry_types,omitempty"` // ["email", "sms"]
    BatchSize       int      `json:"batch_size,omitempty"`
}
```

## Payload Validation

### 1. Validation Interface

Tạo file `internal/pkg/queue/validation.go`:

```go
package queue

import (
    "fmt"
    "regexp"
    "time"
)

// PayloadValidator interface cho validation
type PayloadValidator interface {
    Validate() error
}

// ValidateDuration kiểm tra duration string
func ValidateDuration(duration string) error {
    if duration == "" {
        return fmt.Errorf("duration cannot be empty")
    }
    
    _, err := time.ParseDuration(duration)
    if err != nil {
        // Try parsing as days (e.g., "7d", "30d")
        matched, _ := regexp.MatchString(`^\d+d$`, duration)
        if !matched {
            return fmt.Errorf("invalid duration format: %s", duration)
        }
    }
    
    return nil
}

// ValidateEmail kiểm tra email format
func ValidateEmail(email string) error {
    emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
    if !emailRegex.MatchString(email) {
        return fmt.Errorf("invalid email format: %s", email)
    }
    return nil
}

// ValidateNotEmpty kiểm tra string không rỗng
func ValidateNotEmpty(value, fieldName string) error {
    if value == "" {
        return fmt.Errorf("%s cannot be empty", fieldName)
    }
    return nil
}
```

### 2. Implement Validation

Cập nhật payload structs với validation:

```go
// SystemCleanupPayload với validation
func (p *SystemCleanupPayload) Validate() error {
    if err := ValidateNotEmpty(p.CleanupType, "cleanup_type"); err != nil {
        return err
    }
    
    if err := ValidateDuration(p.MaxAge); err != nil {
        return fmt.Errorf("invalid max_age: %w", err)
    }
    
    validCleanupTypes := []string{"temp_files", "logs", "cache", "uploads"}
    found := false
    for _, validType := range validCleanupTypes {
        if p.CleanupType == validType {
            found = true
            break
        }
    }
    if !found {
        return fmt.Errorf("invalid cleanup_type: %s", p.CleanupType)
    }
    
    return nil
}

// AuthCleanupSessionsPayload với validation
func (p *AuthCleanupSessionsPayload) Validate() error {
    if err := ValidateDuration(p.MaxAge); err != nil {
        return fmt.Errorf("invalid max_age: %w", err)
    }
    
    return nil
}
```

## Task Type Registry

### 1. Registry Implementation

Tạo file `internal/pkg/queue/registry.go`:

```go
package queue

import (
    "fmt"
    "sync"
)

// TaskTypeRegistry quản lý tất cả task types
type TaskTypeRegistry struct {
    mu        sync.RWMutex
    taskTypes map[string]*TaskTypeInfo
}

// TaskTypeInfo chứa thông tin về task type
type TaskTypeInfo struct {
    Type        string
    Category    string
    Module      string
    Description string
    PayloadType string
    Queue       string
    Priority    int
}

var globalRegistry = &TaskTypeRegistry{
    taskTypes: make(map[string]*TaskTypeInfo),
}

// RegisterTaskType đăng ký task type mới
func RegisterTaskType(info *TaskTypeInfo) error {
    globalRegistry.mu.Lock()
    defer globalRegistry.mu.Unlock()
    
    if _, exists := globalRegistry.taskTypes[info.Type]; exists {
        return fmt.Errorf("task type already registered: %s", info.Type)
    }
    
    globalRegistry.taskTypes[info.Type] = info
    return nil
}

// GetTaskType lấy thông tin task type
func GetTaskType(taskType string) (*TaskTypeInfo, bool) {
    globalRegistry.mu.RLock()
    defer globalRegistry.mu.RUnlock()
    
    info, exists := globalRegistry.taskTypes[taskType]
    return info, exists
}

// ListTaskTypes liệt kê tất cả task types
func ListTaskTypes() map[string]*TaskTypeInfo {
    globalRegistry.mu.RLock()
    defer globalRegistry.mu.RUnlock()
    
    result := make(map[string]*TaskTypeInfo)
    for k, v := range globalRegistry.taskTypes {
        result[k] = v
    }
    
    return result
}
```

### 2. Auto Registration

Tạo file `internal/pkg/queue/types/registration.go`:

```go
package types

import (
    "wnapi/internal/pkg/queue"
)

// RegisterAllTaskTypes đăng ký tất cả core task types
func RegisterAllTaskTypes() error {
    taskTypes := []*queue.TaskTypeInfo{
        {
            Type:        TaskTypeSystemCleanup,
            Category:    "system",
            Module:      "core",
            Description: "System cleanup and maintenance",
            PayloadType: "SystemCleanupPayload",
            Queue:       QueueMaintenance,
            Priority:    queue.PriorityNormal,
        },
        {
            Type:        TaskTypeSystemHealthCheck,
            Category:    "system",
            Module:      "core",
            Description: "System health monitoring",
            PayloadType: "BaseCronPayload",
            Queue:       QueueCronJobs,
            Priority:    queue.PriorityHigh,
        },
        // ... thêm các task types khác
    }
    
    for _, taskType := range taskTypes {
        if err := queue.RegisterTaskType(taskType); err != nil {
            return err
        }
    }
    
    return nil
}
```

## Testing

### 1. Payload Validation Tests

Tạo file `internal/pkg/queue/types/payloads_test.go`:

```go
package types

import (
    "testing"
    "time"
)

func TestSystemCleanupPayload_Validate(t *testing.T) {
    tests := []struct {
        name    string
        payload SystemCleanupPayload
        wantErr bool
    }{
        {
            name: "valid payload",
            payload: SystemCleanupPayload{
                BaseCronPayload: BaseCronPayload{
                    JobID:       "test-job",
                    ScheduledAt: time.Now(),
                },
                CleanupType: "temp_files",
                MaxAge:      "7d",
            },
            wantErr: false,
        },
        {
            name: "invalid cleanup type",
            payload: SystemCleanupPayload{
                CleanupType: "invalid_type",
                MaxAge:      "7d",
            },
            wantErr: true,
        },
        {
            name: "invalid max age",
            payload: SystemCleanupPayload{
                CleanupType: "temp_files",
                MaxAge:      "invalid",
            },
            wantErr: true,
        },
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            err := tt.payload.Validate()
            if (err != nil) != tt.wantErr {
                t.Errorf("Validate() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}
```

## Next Steps

Sau khi hoàn thành task types và payloads:

1. ✅ Task type constants đã được định nghĩa
2. ✅ Payload structures đã được tạo
3. ✅ Validation rules đã được thiết lập
4. ✅ Registry system đã được implement

Tiếp theo: [04-task-handlers.md](04-task-handlers.md) - Tạo task handlers
