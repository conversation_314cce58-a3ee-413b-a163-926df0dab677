# Task 04: Task Handlers

Tạo task handlers để xử lý logic nghiệp vụ của cron jobs.

## Mục tiêu

- Tạo handler functions cho các task types
- Implement error handling và logging
- Thiết lập retry logic
- Tổ chức handlers theo modules

## Handler Structure

### 1. Base Handler Interface

Tạo file `internal/pkg/queue/handler.go`:

```go
package queue

import (
    "context"
    "time"
)

// TaskHandler định nghĩa interface cho task handler
type TaskHandler func(ctx *TaskContext) error

// TaskContext chứa thông tin context cho task execution
type TaskContext struct {
    Context   context.Context
    Task      *TaskInfo
    Logger    Logger
    StartTime time.Time
}

// TaskInfo chứa thông tin về task
type TaskInfo struct {
    ID          string
    Type        string
    Payload     []byte
    Queue       string
    MaxRetry    int
    Retried     int
    CreatedAt   time.Time
    ProcessedAt *time.Time
}

// HandlerResult chứa kết quả xử lý task
type HandlerResult struct {
    Success   bool
    Error     error
    Duration  time.Duration
    Metadata  map[string]interface{}
}
```

### 2. Base Handler Struct

Tạo file `internal/cron/handlers/base.go`:

```go
package handlers

import (
    "encoding/json"
    "fmt"
    "time"
    
    "wnapi/internal/pkg/logger"
    "wnapi/internal/pkg/queue"
    "wnapi/internal/pkg/queue/types"
)

// BaseHandler chứa common functionality cho tất cả handlers
type BaseHandler struct {
    logger logger.Logger
}

// NewBaseHandler tạo base handler mới
func NewBaseHandler(logger logger.Logger) *BaseHandler {
    return &BaseHandler{
        logger: logger,
    }
}

// LogStart ghi log khi bắt đầu task
func (h *BaseHandler) LogStart(ctx *queue.TaskContext, taskName string) {
    h.logger.Info("Starting cron task",
        "task_id", ctx.Task.ID,
        "task_type", ctx.Task.Type,
        "task_name", taskName,
        "queue", ctx.Task.Queue,
        "retry_count", ctx.Task.Retried,
        "created_at", ctx.Task.CreatedAt,
    )
}

// LogSuccess ghi log khi task thành công
func (h *BaseHandler) LogSuccess(ctx *queue.TaskContext, taskName string, metadata map[string]interface{}) {
    duration := time.Since(ctx.StartTime)
    
    logFields := []interface{}{
        "task_id", ctx.Task.ID,
        "task_name", taskName,
        "duration", duration,
        "status", "success",
    }
    
    // Thêm metadata vào log
    for key, value := range metadata {
        logFields = append(logFields, key, value)
    }
    
    h.logger.Info("Cron task completed successfully", logFields...)
}

// LogError ghi log khi task thất bại
func (h *BaseHandler) LogError(ctx *queue.TaskContext, taskName string, err error) {
    duration := time.Since(ctx.StartTime)
    
    h.logger.Error("Cron task failed",
        "task_id", ctx.Task.ID,
        "task_name", taskName,
        "duration", duration,
        "retry_count", ctx.Task.Retried,
        "max_retry", ctx.Task.MaxRetry,
        "error", err.Error(),
    )
}

// UnmarshalPayload giải mã payload
func (h *BaseHandler) UnmarshalPayload(ctx *queue.TaskContext, target interface{}) error {
    if len(ctx.Task.Payload) == 0 {
        return nil
    }
    
    if err := json.Unmarshal(ctx.Task.Payload, target); err != nil {
        h.logger.Error("Failed to unmarshal payload",
            "task_id", ctx.Task.ID,
            "task_type", ctx.Task.Type,
            "error", err,
        )
        return fmt.Errorf("invalid payload: %w", err)
    }
    
    return nil
}

// ValidatePayload kiểm tra payload hợp lệ
func (h *BaseHandler) ValidatePayload(payload interface{}) error {
    if validator, ok := payload.(queue.PayloadValidator); ok {
        return validator.Validate()
    }
    return nil
}
```

## System Handlers

### 1. System Maintenance Handlers

Tạo file `internal/cron/handlers/system.go`:

```go
package handlers

import (
    "fmt"
    "os"
    "path/filepath"
    "time"
    
    "wnapi/internal/pkg/logger"
    "wnapi/internal/pkg/queue"
    "wnapi/internal/pkg/queue/types"
)

// SystemHandlers xử lý các system maintenance tasks
type SystemHandlers struct {
    *BaseHandler
}

// NewSystemHandlers tạo system handlers mới
func NewSystemHandlers(logger logger.Logger) *SystemHandlers {
    return &SystemHandlers{
        BaseHandler: NewBaseHandler(logger),
    }
}

// HandleSystemCleanup xử lý system cleanup task
func (h *SystemHandlers) HandleSystemCleanup(ctx *queue.TaskContext) error {
    h.LogStart(ctx, "system_cleanup")
    
    // Unmarshal payload
    var payload types.SystemCleanupPayload
    if err := h.UnmarshalPayload(ctx, &payload); err != nil {
        return err
    }
    
    // Validate payload
    if err := h.ValidatePayload(&payload); err != nil {
        return fmt.Errorf("payload validation failed: %w", err)
    }
    
    // Thực hiện cleanup logic
    result, err := h.performCleanup(&payload)
    if err != nil {
        h.LogError(ctx, "system_cleanup", err)
        return err
    }
    
    // Log success với metadata
    metadata := map[string]interface{}{
        "cleanup_type":   payload.CleanupType,
        "files_deleted":  result.FilesDeleted,
        "bytes_freed":    result.BytesFreed,
        "directories":    len(payload.Directories),
    }
    
    h.LogSuccess(ctx, "system_cleanup", metadata)
    return nil
}

// CleanupResult chứa kết quả cleanup
type CleanupResult struct {
    FilesDeleted int64
    BytesFreed   int64
    Errors       []string
}

// performCleanup thực hiện logic cleanup
func (h *SystemHandlers) performCleanup(payload *types.SystemCleanupPayload) (*CleanupResult, error) {
    result := &CleanupResult{}
    
    // Parse max age
    maxAge, err := time.ParseDuration(payload.MaxAge)
    if err != nil {
        // Try parsing as days
        if payload.MaxAge[len(payload.MaxAge)-1] == 'd' {
            days := payload.MaxAge[:len(payload.MaxAge)-1]
            if d, err := time.ParseDuration(days + "h"); err == nil {
                maxAge = d * 24
            } else {
                return nil, fmt.Errorf("invalid max_age format: %s", payload.MaxAge)
            }
        } else {
            return nil, fmt.Errorf("invalid max_age format: %s", payload.MaxAge)
        }
    }
    
    cutoffTime := time.Now().Add(-maxAge)
    
    // Cleanup theo type
    switch payload.CleanupType {
    case "temp_files":
        return h.cleanupTempFiles(payload.Directories, cutoffTime, payload.ExcludePattern)
    case "logs":
        return h.cleanupLogFiles(payload.Directories, cutoffTime, payload.ExcludePattern)
    case "cache":
        return h.cleanupCacheFiles(payload.Directories, cutoffTime)
    default:
        return nil, fmt.Errorf("unsupported cleanup type: %s", payload.CleanupType)
    }
}

// cleanupTempFiles dọn dẹp temp files
func (h *SystemHandlers) cleanupTempFiles(directories []string, cutoffTime time.Time, excludePattern string) (*CleanupResult, error) {
    result := &CleanupResult{}
    
    defaultDirs := []string{"/tmp", "./tmp", "./temp"}
    if len(directories) == 0 {
        directories = defaultDirs
    }
    
    for _, dir := range directories {
        if err := h.cleanupDirectory(dir, cutoffTime, excludePattern, result); err != nil {
            h.logger.Warn("Failed to cleanup directory",
                "directory", dir,
                "error", err,
            )
            result.Errors = append(result.Errors, fmt.Sprintf("dir %s: %v", dir, err))
        }
    }
    
    return result, nil
}

// cleanupLogFiles dọn dẹp log files
func (h *SystemHandlers) cleanupLogFiles(directories []string, cutoffTime time.Time, excludePattern string) (*CleanupResult, error) {
    result := &CleanupResult{}
    
    defaultDirs := []string{"./logs", "/var/log/app"}
    if len(directories) == 0 {
        directories = defaultDirs
    }
    
    for _, dir := range directories {
        if err := h.cleanupDirectory(dir, cutoffTime, excludePattern, result); err != nil {
            result.Errors = append(result.Errors, fmt.Sprintf("dir %s: %v", dir, err))
        }
    }
    
    return result, nil
}

// cleanupCacheFiles dọn dẹp cache files
func (h *SystemHandlers) cleanupCacheFiles(directories []string, cutoffTime time.Time) (*CleanupResult, error) {
    result := &CleanupResult{}
    
    defaultDirs := []string{"./cache", "./storage/cache"}
    if len(directories) == 0 {
        directories = defaultDirs
    }
    
    for _, dir := range directories {
        if err := h.cleanupDirectory(dir, cutoffTime, "", result); err != nil {
            result.Errors = append(result.Errors, fmt.Sprintf("dir %s: %v", dir, err))
        }
    }
    
    return result, nil
}

// cleanupDirectory dọn dẹp một directory
func (h *SystemHandlers) cleanupDirectory(dir string, cutoffTime time.Time, excludePattern string, result *CleanupResult) error {
    if _, err := os.Stat(dir); os.IsNotExist(err) {
        return nil // Directory không tồn tại, skip
    }
    
    return filepath.Walk(dir, func(path string, info os.FileInfo, err error) error {
        if err != nil {
            return err
        }
        
        // Skip directories
        if info.IsDir() {
            return nil
        }
        
        // Check if file is old enough
        if info.ModTime().After(cutoffTime) {
            return nil
        }
        
        // Check exclude pattern
        if excludePattern != "" {
            matched, _ := filepath.Match(excludePattern, info.Name())
            if matched {
                return nil
            }
        }
        
        // Delete file
        if err := os.Remove(path); err != nil {
            h.logger.Warn("Failed to delete file",
                "file", path,
                "error", err,
            )
            return nil // Continue với files khác
        }
        
        result.FilesDeleted++
        result.BytesFreed += info.Size()
        
        h.logger.Debug("Deleted file",
            "file", path,
            "size", info.Size(),
            "mod_time", info.ModTime(),
        )
        
        return nil
    })
}

// HandleSystemHealthCheck xử lý health check task
func (h *SystemHandlers) HandleSystemHealthCheck(ctx *queue.TaskContext) error {
    h.LogStart(ctx, "system_health_check")
    
    // Thực hiện health checks
    checks := []struct {
        name string
        fn   func() error
    }{
        {"disk_space", h.checkDiskSpace},
        {"memory_usage", h.checkMemoryUsage},
        {"database_connection", h.checkDatabaseConnection},
        {"redis_connection", h.checkRedisConnection},
    }
    
    var errors []string
    successCount := 0
    
    for _, check := range checks {
        if err := check.fn(); err != nil {
            h.logger.Warn("Health check failed",
                "check", check.name,
                "error", err,
            )
            errors = append(errors, fmt.Sprintf("%s: %v", check.name, err))
        } else {
            successCount++
        }
    }
    
    metadata := map[string]interface{}{
        "total_checks":      len(checks),
        "successful_checks": successCount,
        "failed_checks":     len(errors),
    }
    
    if len(errors) > 0 {
        metadata["errors"] = errors
        h.logger.Warn("Some health checks failed", "errors", errors)
    }
    
    h.LogSuccess(ctx, "system_health_check", metadata)
    return nil
}

// Health check functions
func (h *SystemHandlers) checkDiskSpace() error {
    // TODO: Implement disk space check
    return nil
}

func (h *SystemHandlers) checkMemoryUsage() error {
    // TODO: Implement memory usage check
    return nil
}

func (h *SystemHandlers) checkDatabaseConnection() error {
    // TODO: Implement database connection check
    return nil
}

func (h *SystemHandlers) checkRedisConnection() error {
    // TODO: Implement Redis connection check
    return nil
}
```

## Module-specific Handlers

### 1. Auth Module Handlers

Tạo file `modules/auth/queue/handlers/cron_handlers.go`:

```go
package handlers

import (
    "fmt"
    "time"
    
    "wnapi/internal/cron/handlers"
    "wnapi/internal/pkg/logger"
    "wnapi/internal/pkg/queue"
    "wnapi/modules/auth/queue/types"
    "wnapi/modules/auth/service"
)

// AuthCronHandlers xử lý auth-related cron jobs
type AuthCronHandlers struct {
    *handlers.BaseHandler
    authService *service.AuthService
}

// NewAuthCronHandlers tạo auth cron handlers mới
func NewAuthCronHandlers(logger logger.Logger, authService *service.AuthService) *AuthCronHandlers {
    return &AuthCronHandlers{
        BaseHandler: handlers.NewBaseHandler(logger),
        authService: authService,
    }
}

// HandleCleanupSessions xử lý cleanup expired sessions
func (h *AuthCronHandlers) HandleCleanupSessions(ctx *queue.TaskContext) error {
    h.LogStart(ctx, "cleanup_sessions")
    
    // Unmarshal payload
    var payload types.AuthCleanupSessionsPayload
    if err := h.UnmarshalPayload(ctx, &payload); err != nil {
        return err
    }
    
    // Validate payload
    if err := h.ValidatePayload(&payload); err != nil {
        return fmt.Errorf("payload validation failed: %w", err)
    }
    
    // Parse max age
    maxAge, err := time.ParseDuration(payload.MaxAge)
    if err != nil {
        return fmt.Errorf("invalid max_age: %w", err)
    }
    
    // Cleanup sessions
    result, err := h.authService.CleanupExpiredSessions(ctx.Context, maxAge, payload.InactiveOnly, payload.ExcludeUserIDs)
    if err != nil {
        h.LogError(ctx, "cleanup_sessions", err)
        return err
    }
    
    metadata := map[string]interface{}{
        "sessions_deleted": result.DeletedCount,
        "max_age":         payload.MaxAge,
        "inactive_only":   payload.InactiveOnly,
    }
    
    h.LogSuccess(ctx, "cleanup_sessions", metadata)
    return nil
}

// HandlePasswordExpiry xử lý password expiry notifications
func (h *AuthCronHandlers) HandlePasswordExpiry(ctx *queue.TaskContext) error {
    h.LogStart(ctx, "password_expiry")
    
    var payload types.AuthPasswordExpiryPayload
    if err := h.UnmarshalPayload(ctx, &payload); err != nil {
        return err
    }
    
    // Tìm users có password sắp hết hạn
    for _, notifyDays := range payload.NotifyDays {
        users, err := h.authService.FindUsersWithPasswordExpiring(ctx.Context, notifyDays)
        if err != nil {
            h.LogError(ctx, "password_expiry", err)
            return err
        }
        
        // Gửi notification cho từng user
        for _, user := range users {
            if err := h.authService.SendPasswordExpiryNotification(ctx.Context, user, notifyDays, payload.TemplateID); err != nil {
                h.logger.Warn("Failed to send password expiry notification",
                    "user_id", user.ID,
                    "notify_days", notifyDays,
                    "error", err,
                )
            }
        }
    }
    
    metadata := map[string]interface{}{
        "notify_days": payload.NotifyDays,
        "template_id": payload.TemplateID,
    }
    
    h.LogSuccess(ctx, "password_expiry", metadata)
    return nil
}
```

## Error Handling Patterns

### 1. Retry Logic

```go
// RetryableError cho errors có thể retry
type RetryableError struct {
    Err       error
    RetryAfter time.Duration
}

func (e *RetryableError) Error() string {
    return e.Err.Error()
}

func (e *RetryableError) Unwrap() error {
    return e.Err
}

// IsRetryable kiểm tra error có thể retry không
func IsRetryable(err error) bool {
    var retryableErr *RetryableError
    return errors.As(err, &retryableErr)
}

// NewRetryableError tạo retryable error
func NewRetryableError(err error, retryAfter time.Duration) *RetryableError {
    return &RetryableError{
        Err:       err,
        RetryAfter: retryAfter,
    }
}
```

### 2. Circuit Breaker Pattern

```go
// CircuitBreaker cho external service calls
type CircuitBreaker struct {
    maxFailures int
    resetTimeout time.Duration
    failures    int
    lastFailTime time.Time
    state       string // "closed", "open", "half-open"
}

func (cb *CircuitBreaker) Call(fn func() error) error {
    if cb.state == "open" {
        if time.Since(cb.lastFailTime) > cb.resetTimeout {
            cb.state = "half-open"
        } else {
            return fmt.Errorf("circuit breaker is open")
        }
    }
    
    err := fn()
    if err != nil {
        cb.failures++
        cb.lastFailTime = time.Now()
        
        if cb.failures >= cb.maxFailures {
            cb.state = "open"
        }
        
        return err
    }
    
    // Success - reset circuit breaker
    cb.failures = 0
    cb.state = "closed"
    return nil
}
```

## Testing

### 1. Handler Unit Tests

Tạo file `internal/cron/handlers/system_test.go`:

```go
package handlers

import (
    "context"
    "testing"
    "time"
    
    "wnapi/internal/pkg/logger"
    "wnapi/internal/pkg/queue"
    "wnapi/internal/pkg/queue/types"
)

func TestSystemHandlers_HandleSystemCleanup(t *testing.T) {
    logger := logger.NewTestLogger()
    handler := NewSystemHandlers(logger)
    
    tests := []struct {
        name    string
        payload types.SystemCleanupPayload
        wantErr bool
    }{
        {
            name: "valid temp files cleanup",
            payload: types.SystemCleanupPayload{
                CleanupType: "temp_files",
                MaxAge:      "7d",
                Directories: []string{"./test_temp"},
            },
            wantErr: false,
        },
        {
            name: "invalid cleanup type",
            payload: types.SystemCleanupPayload{
                CleanupType: "invalid",
                MaxAge:      "7d",
            },
            wantErr: true,
        },
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            ctx := &queue.TaskContext{
                Context:   context.Background(),
                Task:      &queue.TaskInfo{ID: "test-task"},
                Logger:    logger,
                StartTime: time.Now(),
            }
            
            // Marshal payload
            payload, _ := json.Marshal(tt.payload)
            ctx.Task.Payload = payload
            
            err := handler.HandleSystemCleanup(ctx)
            if (err != nil) != tt.wantErr {
                t.Errorf("HandleSystemCleanup() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}
```

## Next Steps

Sau khi hoàn thành task handlers:

1. ✅ Base handler structure đã được tạo
2. ✅ System handlers đã được implement
3. ✅ Module-specific handlers đã được tạo
4. ✅ Error handling patterns đã được thiết lập

Tiếp theo: [05-handler-registration.md](05-handler-registration.md) - Đăng ký handlers với QueueManager
