# Cron Jobs Implementation Tasks

Tài liệu hướng dẫn triển khai tác vụ định lịch (Cron Jobs) sử dụng AsynqScheduler trong hệ thống WNAPI v2.

## Tổ<PERSON> quan

Hệ thống sử dụng Asynq làm backend cho hàng đợi và AsynqScheduler để đăng ký các tác vụ định kỳ (periodic tasks). <PERSON><PERSON><PERSON> tác vụ này hoạt động tương tự như cron job truyền thống nhưng được quản lý thông qua Redis và Asynq.

## Ki<PERSON><PERSON> trúc

```
[Go App] --Schedule--> [Redis] <--Monitor-- [Asynq Server/Scheduler]
    |                                              |
    |                                              v
    |                                        [Enqueue Task]
    |                                              |
    v                                              v
[Worker] <--Process-- [Task Queue] <--------------+
```

## Danh sách Tasks

### 1. <PERSON><PERSON><PERSON> b<PERSON> và <PERSON>ấ<PERSON> hình
- [01-environment-setup.md](01-environment-setup.md) - <PERSON><PERSON><PERSON><PERSON> lập môi trường và cấu hình
- [02-project-structure.md](02-project-structure.md) - Cấu trúc thư mục và file

### 2. Triển khai Core Components
- [03-task-types.md](03-task-types.md) - Định nghĩa task types và payloads
- [04-task-handlers.md](04-task-handlers.md) - Tạo task handlers
- [05-handler-registration.md](05-handler-registration.md) - Đăng ký handlers với QueueManager

### 3. Scheduling và Module Integration
- [06-module-integration.md](06-module-integration.md) - Tích hợp với module system
- [07-scheduling-tasks.md](07-scheduling-tasks.md) - Lên lịch thực thi tasks
- [08-cron-expressions.md](08-cron-expressions.md) - Hướng dẫn cron expressions

### 4. Vận hành và Deployment
- [09-asynq-server.md](09-asynq-server.md) - Chạy Asynq Server/Scheduler
- [10-monitoring.md](10-monitoring.md) - Giám sát và logging
- [11-error-handling.md](11-error-handling.md) - Xử lý lỗi và retry

### 5. Testing và Best Practices
- [12-testing.md](12-testing.md) - Unit testing và integration testing
- [13-best-practices.md](13-best-practices.md) - Best practices và patterns
- [14-troubleshooting.md](14-troubleshooting.md) - Troubleshooting common issues

## Yêu cầu

- Go 1.19+
- Redis server
- Asynq CLI tool
- Cấu hình môi trường phù hợp

## Quick Start

1. **Cấu hình môi trường**: Thiết lập các biến môi trường cần thiết
2. **Tạo task handler**: Định nghĩa logic xử lý cron job
3. **Đăng ký handler**: Đăng ký với QueueManager
4. **Lên lịch task**: Schedule task trong module init
5. **Chạy Asynq server**: Khởi động scheduler process

## Ví dụ Cron Job đơn giản

```go
// Task Type
const TaskTypeCleanupOldData = "cron:cleanup_old_data"

// Handler
func (h *MaintenanceHandlers) HandleCleanupOldData(ctx *queue.TaskContext) error {
    // Logic dọn dẹp dữ liệu cũ
    return nil
}

// Scheduling (trong module init)
scheduler.Schedule(
    TaskTypeCleanupOldData,
    "0 2 * * *", // 2 giờ sáng mỗi ngày
    nil,
    &queue.ScheduleOptions{Queue: "maintenance"},
)
```

## Lưu ý quan trọng

- **Timezone**: Đảm bảo múi giờ được cấu hình đúng
- **Idempotency**: Task handlers phải có tính idempotent
- **Error Handling**: Xử lý lỗi và retry logic phù hợp
- **Monitoring**: Giám sát queue và task execution
- **Graceful Shutdown**: Đảm bảo shutdown an toàn

## Tài liệu tham khảo

- [Asynq Documentation](https://github.com/hibiken/asynq)
- [Cron Expression Guide](https://crontab.guru/)
- [Redis Configuration](https://redis.io/documentation)
