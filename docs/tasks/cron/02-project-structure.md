# Task 02: Project Structure

Thiết lập cấu trúc thư mục và file cho cron jobs implementation.

## Mục tiêu

- <PERSON><PERSON>o cấu trúc thư mục chuẩn cho cron jobs
- Đ<PERSON><PERSON> nghĩa naming conventions
- Thiết lập file templates
- <PERSON><PERSON> chức code theo modules

## Cấu trúc thư mục

### 1. Core Queue Infrastructure

```
internal/pkg/queue/
├── types/
│   ├── common.go           # Common task types
│   └── cron.go            # Cron-specific task types
├── asynq/
│   ├── scheduler.go       # AsynqScheduler implementation
│   ├── worker.go         # AsynqWorker implementation
│   └── client.go         # AsynqClient implementation
├── manager.go            # QueueManager
├── task.go              # TaskDefinition, TaskContext
├── config.go            # Queue configuration
└── interfaces.go        # Queue interfaces
```

### 2. Module-specific Cron Jobs

Mỗi module có thể có cron jobs riêng:

```
modules/{module_name}/
├── queue/
│   ├── types/
│   │   ├── payloads.go    # Task payloads for this module
│   │   └── constants.go   # Task type constants
│   ├── handlers/
│   │   ├── cron_handlers.go      # Cron job handlers
│   │   ├── periodic_handlers.go  # Periodic task handlers
│   │   └── registration.go       # Handler registration
│   └── schedulers/
│       ├── daily_jobs.go         # Daily scheduled jobs
│       ├── hourly_jobs.go        # Hourly scheduled jobs
│       └── maintenance_jobs.go   # Maintenance jobs
├── module.go            # Module definition with cron scheduling
└── ...
```

### 3. Shared Cron Components

```
internal/cron/
├── types/
│   ├── common.go         # Common cron types
│   └── schedules.go      # Common cron schedules
├── handlers/
│   ├── system.go         # System-wide cron handlers
│   ├── maintenance.go    # Maintenance handlers
│   └── monitoring.go     # Monitoring handlers
├── utils/
│   ├── timezone.go       # Timezone utilities
│   ├── validation.go     # Cron expression validation
│   └── helpers.go        # Helper functions
└── registry.go          # Global cron job registry
```

## File Templates

### 1. Task Types Template

Tạo file `internal/pkg/queue/types/cron.go`:

```go
package types

// Cron job task types
const (
    // System maintenance jobs
    TaskTypeSystemCleanup     = "cron:system:cleanup"
    TaskTypeSystemHealthCheck = "cron:system:health_check"
    TaskTypeSystemBackup      = "cron:system:backup"
    
    // Data processing jobs
    TaskTypeDataAggregation   = "cron:data:aggregation"
    TaskTypeDataArchival      = "cron:data:archival"
    TaskTypeDataValidation    = "cron:data:validation"
    
    // Notification jobs
    TaskTypeNotificationDigest = "cron:notification:digest"
    TaskTypeNotificationCleanup = "cron:notification:cleanup"
    
    // Report generation jobs
    TaskTypeReportDaily       = "cron:report:daily"
    TaskTypeReportWeekly      = "cron:report:weekly"
    TaskTypeReportMonthly     = "cron:report:monthly"
)

// Common cron job queues
const (
    QueueCronJobs        = "cron_jobs"
    QueueMaintenance     = "maintenance"
    QueueReports         = "reports"
    QueueNotifications   = "notifications"
    QueueDataProcessing  = "data_processing"
)
```

### 2. Module Cron Handler Template

Tạo template cho `modules/{module}/queue/handlers/cron_handlers.go`:

```go
package handlers

import (
    "context"
    "fmt"
    "time"
    
    "wnapi/internal/pkg/logger"
    "wnapi/internal/pkg/queue"
    "wnapi/modules/{module}/service"
)

type CronHandlers struct {
    logger  logger.Logger
    service *service.{Module}Service
}

func NewCronHandlers(logger logger.Logger, service *service.{Module}Service) *CronHandlers {
    return &CronHandlers{
        logger:  logger,
        service: service,
    }
}

// HandleDailyTask xử lý tác vụ hàng ngày
func (h *CronHandlers) HandleDailyTask(ctx *queue.TaskContext) error {
    h.logger.Info("Starting daily task", 
        "taskId", ctx.Task.ID,
        "module", "{module}",
        "timestamp", time.Now(),
    )
    
    // TODO: Implement daily task logic
    
    h.logger.Info("Daily task completed successfully",
        "taskId", ctx.Task.ID,
        "duration", time.Since(ctx.Task.CreatedAt),
    )
    
    return nil
}

// HandleHourlyTask xử lý tác vụ hàng giờ
func (h *CronHandlers) HandleHourlyTask(ctx *queue.TaskContext) error {
    h.logger.Info("Starting hourly task",
        "taskId", ctx.Task.ID,
        "module", "{module}",
    )
    
    // TODO: Implement hourly task logic
    
    return nil
}

// HandleMaintenanceTask xử lý tác vụ bảo trì
func (h *CronHandlers) HandleMaintenanceTask(ctx *queue.TaskContext) error {
    h.logger.Info("Starting maintenance task",
        "taskId", ctx.Task.ID,
        "module", "{module}",
    )
    
    // TODO: Implement maintenance logic
    
    return nil
}
```

### 3. Handler Registration Template

Tạo template cho `modules/{module}/queue/handlers/registration.go`:

```go
package handlers

import (
    "fmt"
    "time"
    
    "wnapi/internal/pkg/logger"
    "wnapi/internal/pkg/queue"
    "wnapi/internal/pkg/queue/types"
    "wnapi/modules/{module}/service"
    moduleTypes "wnapi/modules/{module}/queue/types"
)

// RegisterCronHandlers đăng ký tất cả cron handlers của module
func RegisterCronHandlers(
    manager *queue.QueueManager,
    logger logger.Logger,
    service *service.{Module}Service,
) error {
    cronHandlers := NewCronHandlers(logger, service)
    
    // Đăng ký daily task
    dailyTaskDef := &queue.TaskDefinition{
        Type:        moduleTypes.TaskType{Module}Daily,
        Description: "Daily {module} processing task",
        Handler:     cronHandlers.HandleDailyTask,
        Options: &queue.TaskOptions{
            Queue:    types.QueueCronJobs,
            MaxRetry: 3,
            Timeout:  30 * time.Minute,
        },
    }
    
    if err := manager.RegisterTask(dailyTaskDef); err != nil {
        return fmt.Errorf("failed to register daily task: %w", err)
    }
    
    // Đăng ký hourly task
    hourlyTaskDef := &queue.TaskDefinition{
        Type:        moduleTypes.TaskType{Module}Hourly,
        Description: "Hourly {module} processing task",
        Handler:     cronHandlers.HandleHourlyTask,
        Options: &queue.TaskOptions{
            Queue:    types.QueueCronJobs,
            MaxRetry: 2,
            Timeout:  10 * time.Minute,
        },
    }
    
    if err := manager.RegisterTask(hourlyTaskDef); err != nil {
        return fmt.Errorf("failed to register hourly task: %w", err)
    }
    
    logger.Info("Cron handlers registered successfully", "module", "{module}")
    return nil
}
```

### 4. Module Integration Template

Cập nhật `modules/{module}/module.go`:

```go
package {module}

import (
    "context"
    "fmt"
    
    "wnapi/internal/core"
    "wnapi/internal/pkg/logger"
    "wnapi/internal/pkg/queue"
    "wnapi/modules/{module}/queue/handlers"
    "wnapi/modules/{module}/queue/schedulers"
    "wnapi/modules/{module}/service"
)

type Module struct {
    name    string
    logger  logger.Logger
    app     *core.AppBootstrap
    service *service.{Module}Service
}

func NewModule(app *core.AppBootstrap, config map[string]interface{}) (core.Module, error) {
    logger := app.GetLogger().With("module", "{module}")
    
    // Initialize service
    service := service.New{Module}Service(/* dependencies */)
    
    return &Module{
        name:    "{module}",
        logger:  logger,
        app:     app,
        service: service,
    }, nil
}

func (m *Module) Name() string {
    return m.name
}

func (m *Module) Init(ctx context.Context) error {
    m.logger.Info("Initializing {module} module with cron jobs")
    
    // Register cron handlers
    qManager := queue.GetGlobalManager()
    if qManager != nil {
        if err := handlers.RegisterCronHandlers(qManager, m.logger, m.service); err != nil {
            return fmt.Errorf("failed to register cron handlers: %w", err)
        }
        
        // Schedule cron jobs
        if err := schedulers.ScheduleAllJobs(qManager, m.logger); err != nil {
            return fmt.Errorf("failed to schedule cron jobs: %w", err)
        }
    }
    
    return nil
}

func (m *Module) Cleanup(ctx context.Context) error {
    m.logger.Info("Cleaning up {module} module")
    return nil
}
```

## Naming Conventions

### 1. Task Types

```go
// Pattern: "cron:{module}:{task_name}"
const (
    TaskTypeAuthCleanupSessions    = "cron:auth:cleanup_sessions"
    TaskTypeNotificationDigest     = "cron:notification:digest"
    TaskTypeMediaOptimizeImages    = "cron:media:optimize_images"
)
```

### 2. Handler Functions

```go
// Pattern: Handle{TaskName}
func (h *CronHandlers) HandleCleanupSessions(ctx *queue.TaskContext) error
func (h *CronHandlers) HandleOptimizeImages(ctx *queue.TaskContext) error
func (h *CronHandlers) HandleGenerateReport(ctx *queue.TaskContext) error
```

### 3. File Names

```
cron_handlers.go        # Main cron handlers
periodic_handlers.go    # Periodic task handlers
maintenance_handlers.go # Maintenance handlers
registration.go         # Handler registration
schedulers.go          # Job scheduling
```

## Directory Creation Script

Tạo script `scripts/create-cron-structure.sh`:

```bash
#!/bin/bash

MODULE_NAME=$1

if [ -z "$MODULE_NAME" ]; then
    echo "Usage: $0 <module_name>"
    exit 1
fi

BASE_DIR="modules/$MODULE_NAME"

# Create directories
mkdir -p "$BASE_DIR/queue/types"
mkdir -p "$BASE_DIR/queue/handlers"
mkdir -p "$BASE_DIR/queue/schedulers"

# Create template files
echo "Creating cron structure for module: $MODULE_NAME"

# Create types file
cat > "$BASE_DIR/queue/types/constants.go" << EOF
package types

// Task types for $MODULE_NAME module
const (
    TaskType${MODULE_NAME^}Daily       = "cron:${MODULE_NAME}:daily"
    TaskType${MODULE_NAME^}Hourly      = "cron:${MODULE_NAME}:hourly"
    TaskType${MODULE_NAME^}Maintenance = "cron:${MODULE_NAME}:maintenance"
)
EOF

echo "✓ Created cron structure for $MODULE_NAME module"
echo "Next: Implement handlers in $BASE_DIR/queue/handlers/"
```

## Validation

### 1. Structure Check Script

```bash
#!/bin/bash

echo "Validating cron project structure..."

# Check core directories
REQUIRED_DIRS=(
    "internal/pkg/queue/types"
    "internal/pkg/queue/asynq"
)

for dir in "${REQUIRED_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        echo "✓ $dir exists"
    else
        echo "✗ $dir missing"
    fi
done

echo "Structure validation complete"
```

## Next Steps

Sau khi hoàn thành project structure setup:

1. ✅ Cấu trúc thư mục đã được tạo
2. ✅ Templates đã được thiết lập
3. ✅ Naming conventions đã được định nghĩa
4. ✅ Scripts hỗ trợ đã được tạo

Tiếp theo: [03-task-types.md](03-task-types.md) - Định nghĩa task types và payloads
