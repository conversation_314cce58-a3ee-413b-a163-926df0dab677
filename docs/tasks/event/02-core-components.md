# Task 2: <PERSON><PERSON><PERSON>ần Cốt <PERSON>õi

## M<PERSON><PERSON>i<PERSON>
- <PERSON><PERSON><PERSON> khai cấu trúc Config cho event system
- <PERSON>ển khai Publisher để gửi events
- Triển khai Subscriber để nhận events
- Tạo Event Interface cơ bản

## C<PERSON><PERSON>hực <PERSON>n

### 1. Triển Khai Configuration Structure

Tạo file `internal/pkg/events/config.go`:

```go
package events

import (
    "fmt"
    "time"
    
    "github.com/redis/go-redis/v9"
)

type Config struct {
    // Redis configuration
    RedisURL      string `env:"REDIS_URL" envDefault:"redis://localhost:6379/0"`
    RedisHost     string `env:"REDIS_HOST" envDefault:"localhost"`
    RedisPort     int    `env:"REDIS_PORT" envDefault:"6379"`
    RedisDB       int    `env:"REDIS_DB" envDefault:"0"`
    RedisPassword string `env:"REDIS_PASSWORD"`

    // Event system configuration
    Enabled       bool   `env:"EVENT_ENABLED" envDefault:"true"`
    ConsumerGroup string `env:"EVENT_CONSUMER_GROUP" envDefault:"wnapi_consumers"`
    ConsumerID    string `env:"EVENT_CONSUMER_ID" envDefault:"wnapi_consumer_1"`

    // Router configuration
    RouterCloseTimeout      time.Duration `env:"EVENT_ROUTER_CLOSE_TIMEOUT" envDefault:"30s"`
    RouterMiddlewareTimeout time.Duration `env:"EVENT_ROUTER_MIDDLEWARE_TIMEOUT" envDefault:"30s"`

    // Publisher configuration
    PublisherMaxLen int64 `env:"EVENT_PUBLISHER_MAX_LEN" envDefault:"10000"`

    // Retry configuration
    RetryMaxAttempts     int           `env:"EVENT_RETRY_MAX_ATTEMPTS" envDefault:"3"`
    RetryInitialInterval time.Duration `env:"EVENT_RETRY_INITIAL_INTERVAL" envDefault:"1s"`
    RetryMaxInterval     time.Duration `env:"EVENT_RETRY_MAX_INTERVAL" envDefault:"30s"`

    // Monitoring
    LoggingEnabled bool `env:"EVENT_LOGGING_ENABLED" envDefault:"true"`
    MetricsEnabled bool `env:"EVENT_METRICS_ENABLED" envDefault:"true"`
    Debug          bool `env:"EVENT_DEBUG" envDefault:"false"`
}

func (c *Config) GetRedisOptions() *redis.Options {
    return &redis.Options{
        Addr:     fmt.Sprintf("%s:%d", c.RedisHost, c.RedisPort),
        Password: c.RedisPassword,
        DB:       c.RedisDB,
    }
}
```

### 2. Triển Khai Event Interface

Tạo file `internal/pkg/events/message.go`:

```go
package events

import (
    "time"
)

type Event interface {
    Type() string
    TenantID() uint
    WebsiteID() uint
    UserID() *uint
    Payload() interface{}
    Metadata() map[string]interface{}
    Timestamp() time.Time
}

type BaseEvent struct {
    EventType     string                 `json:"event_type"`
    TenantId      uint                   `json:"tenant_id"`
    WebsiteId     uint                   `json:"website_id"`
    UserId        *uint                  `json:"user_id,omitempty"`
    EventPayload  interface{}            `json:"payload"`
    EventMetadata map[string]interface{} `json:"metadata"`
    CreatedAt     time.Time              `json:"created_at"`
}

func (e *BaseEvent) Type() string { return e.EventType }
func (e *BaseEvent) TenantID() uint { return e.TenantId }
func (e *BaseEvent) WebsiteID() uint { return e.WebsiteId }
func (e *BaseEvent) UserID() *uint { return e.UserId }
func (e *BaseEvent) Payload() interface{} { return e.EventPayload }
func (e *BaseEvent) Metadata() map[string]interface{} { return e.EventMetadata }
func (e *BaseEvent) Timestamp() time.Time { return e.CreatedAt }

func NewEvent(eventType string, tenantID, websiteID uint, userID *uint, payload interface{}) Event {
    return &BaseEvent{
        EventType:     eventType,
        TenantId:      tenantID,
        WebsiteId:     websiteID,
        UserId:        userID,
        EventPayload:  payload,
        EventMetadata: make(map[string]interface{}),
        CreatedAt:     time.Now(),
    }
}
```

### 3. Triển Khai Publisher

Tạo file `internal/pkg/events/publisher.go`:

```go
package events

import (
    "context"
    "encoding/json"
    "fmt"
    "time"

    "github.com/ThreeDotsLabs/watermill"
    "github.com/ThreeDotsLabs/watermill-redisstream/pkg/redisstream"
    "github.com/ThreeDotsLabs/watermill/message"
    "github.com/redis/go-redis/v9"
)

type Publisher struct {
    publisher *redisstream.Publisher
    logger    watermill.LoggerAdapter
}

func NewPublisher(config Config) (*Publisher, error) {
    logger := watermill.NewStdLogger(config.Debug, config.Debug)
    
    redisClient := redis.NewClient(config.GetRedisOptions())
    
    publisher, err := redisstream.NewPublisher(
        redisstream.PublisherConfig{
            Client:        redisClient,
            Marshaller:    redisstream.DefaultMarshallerUnmarshaller{},
            DefaultMaxlen: config.PublisherMaxLen,
        },
        logger,
    )
    if err != nil {
        return nil, fmt.Errorf("failed to create redis publisher: %w", err)
    }
    
    return &Publisher{
        publisher: publisher,
        logger:    logger,
    }, nil
}

func (p *Publisher) Publish(ctx context.Context, topic string, payload interface{}) error {
    payloadBytes, err := json.Marshal(payload)
    if err != nil {
        return fmt.Errorf("failed to marshal payload: %w", err)
    }
    
    msg := message.NewMessage(watermill.NewUUID(), payloadBytes)
    
    // Add metadata
    msg.Metadata.Set("published_at", time.Now().Format(time.RFC3339))
    msg.Metadata.Set("topic", topic)
    
    return p.publisher.Publish(topic, msg)
}

func (p *Publisher) PublishEvent(ctx context.Context, event Event) error {
    return p.Publish(ctx, event.Type(), event)
}

func (p *Publisher) Close() error {
    return p.publisher.Close()
}
```

### 4. Triển Khai Subscriber

Tạo file `internal/pkg/events/subscriber.go`:

```go
package events

import (
    "context"
    "fmt"
    "time"

    "github.com/ThreeDotsLabs/watermill"
    "github.com/ThreeDotsLabs/watermill-redisstream/pkg/redisstream"
    "github.com/ThreeDotsLabs/watermill/message"
    "github.com/redis/go-redis/v9"
)

type Subscriber struct {
    subscriber *redisstream.Subscriber
    logger     watermill.LoggerAdapter
}

func NewSubscriber(config Config) (*Subscriber, error) {
    logger := watermill.NewStdLogger(config.Debug, config.Debug)
    
    redisClient := redis.NewClient(config.GetRedisOptions())
    
    subscriber, err := redisstream.NewSubscriber(
        redisstream.SubscriberConfig{
            Client:        redisClient,
            Unmarshaller:  redisstream.DefaultMarshallerUnmarshaller{},
            ConsumerGroup: config.ConsumerGroup,
            Consumer:      config.ConsumerID,
            
            // Performance settings
            BlockTime:               100 * time.Millisecond,
            ClaimInterval:           5 * time.Second,
            ClaimBatchSize:          100,
            MaxIdleTime:             60 * time.Second,
            CheckConsumersInterval:  5 * time.Minute,
            ConsumerTimeout:         10 * time.Minute,
        },
        logger,
    )
    if err != nil {
        return nil, fmt.Errorf("failed to create redis subscriber: %w", err)
    }
    
    return &Subscriber{
        subscriber: subscriber,
        logger:     logger,
    }, nil
}

func (s *Subscriber) Subscribe(ctx context.Context, topic string) (<-chan *message.Message, error) {
    return s.subscriber.Subscribe(ctx, topic)
}

func (s *Subscriber) Close() error {
    return s.subscriber.Close()
}
```

## Điểm Kiểm Tra
- [ ] Config structure đã được triển khai
- [ ] Event interface và BaseEvent đã được triển khai
- [ ] Publisher đã được triển khai với khả năng gửi events
- [ ] Subscriber đã được triển khai với khả năng nhận events
- [ ] Tất cả các thành phần có thể biên dịch mà không có lỗi

## Lưu Ý
- Đảm bảo xử lý lỗi một cách thích hợp trong tất cả các thành phần
- Cấu hình connection pool cho Redis để tối ưu hiệu suất
- Xem xét thêm các tùy chọn cấu hình để tùy chỉnh hành vi của Publisher và Subscriber
- Thực hiện kiểm tra kết nối Redis trước khi tạo Publisher và Subscriber 