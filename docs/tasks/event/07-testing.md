# Task 7: Testing và Validation

## Mục Tiêu
- Triển khai unit tests cho các thành phần cốt lõi của event system
- Triển khai mock cho event publisher để dễ dàng test các modules
- Triển khai integration tests cho event workflow

## Cá<PERSON>c Thực Hiện

### 1. Tri<PERSON><PERSON>ck Publisher

Tạo file `internal/pkg/events/testing/mock_publisher.go`:

```go
package testing

import (
    "context"
    "sync"
    
    "your-project/internal/pkg/events"
)

// PublishedEvent lưu trữ thông tin về event đã phát hành
type PublishedEvent struct {
    Topic   string
    Payload interface{}
}

// MockPublisher là một triển khai mock của event publisher
type MockPublisher struct {
    mu              sync.RWMutex
    publishedEvents []PublishedEvent
}

// NewMockPublisher tạo một instance mới của MockPublisher
func NewMockPublisher() *MockPublisher {
    return &MockPublisher{
        publishedEvents: make([]PublishedEvent, 0),
    }
}

// Publish lưu trữ event đã phát hành
func (m *MockPublisher) Publish(ctx context.Context, topic string, payload interface{}) error {
    m.mu.Lock()
    defer m.mu.Unlock()
    
    m.publishedEvents = append(m.publishedEvents, PublishedEvent{
        Topic:   topic,
        Payload: payload,
    })
    
    return nil
}

// PublishEvent lưu trữ event đã phát hành
func (m *MockPublisher) PublishEvent(ctx context.Context, event events.Event) error {
    return m.Publish(ctx, event.Type(), event)
}

// GetPublishedEvents trả về tất cả các events đã phát hành
func (m *MockPublisher) GetPublishedEvents() []PublishedEvent {
    m.mu.RLock()
    defer m.mu.RUnlock()
    
    result := make([]PublishedEvent, len(m.publishedEvents))
    copy(result, m.publishedEvents)
    return result
}

// GetEventsByType trả về các events đã phát hành theo loại
func (m *MockPublisher) GetEventsByType(eventType string) []PublishedEvent {
    m.mu.RLock()
    defer m.mu.RUnlock()
    
    var result []PublishedEvent
    for _, event := range m.publishedEvents {
        if event.Topic == eventType {
            result = append(result, event)
        }
    }
    return result
}

// Clear xóa tất cả các events đã phát hành
func (m *MockPublisher) Clear() {
    m.mu.Lock()
    defer m.mu.Unlock()
    
    m.publishedEvents = m.publishedEvents[:0]
}

// Close giả lập việc đóng publisher
func (m *MockPublisher) Close() error {
    return nil
}
```

### 2. Triển Khai Helper Functions cho Testing

Tạo file `internal/pkg/events/testing/helpers.go`:

```go
package testing

import (
    "testing"
    
    "github.com/stretchr/testify/assert"
    "your-project/internal/pkg/events/types"
)

// AssertEventPublished kiểm tra xem một event có được phát hành hay không
func AssertEventPublished(t *testing.T, publisher *MockPublisher, eventType string) {
    events := publisher.GetEventsByType(eventType)
    assert.NotEmpty(t, events, "Expected event %s to be published, but it was not found", eventType)
}

// AssertEventNotPublished kiểm tra xem một event không được phát hành
func AssertEventNotPublished(t *testing.T, publisher *MockPublisher, eventType string) {
    events := publisher.GetEventsByType(eventType)
    assert.Empty(t, events, "Expected event %s NOT to be published, but %d were found", eventType, len(events))
}

// AssertEventCount kiểm tra số lượng events đã phát hành theo loại
func AssertEventCount(t *testing.T, publisher *MockPublisher, eventType string, expectedCount int) {
    events := publisher.GetEventsByType(eventType)
    assert.Len(t, events, expectedCount, "Expected %d events of type %s, but found %d", expectedCount, eventType, len(events))
}

// AssertUserCreatedEvent kiểm tra nội dung của user created event
func AssertUserCreatedEvent(t *testing.T, publisher *MockPublisher, userID uint, email string) {
    events := publisher.GetEventsByType(types.AuthUserCreated)
    assert.NotEmpty(t, events, "Expected UserCreated event, but none was found")
    
    if len(events) > 0 {
        event, ok := events[0].Payload.(events.BaseEvent)
        assert.True(t, ok, "Expected event payload to be BaseEvent")
        
        if ok {
            payload, ok := event.Payload().(types.UserCreatedPayload)
            assert.True(t, ok, "Expected event payload to be UserCreatedPayload")
            
            if ok {
                assert.Equal(t, userID, payload.UserID, "Expected UserID to be %d, but got %d", userID, payload.UserID)
                assert.Equal(t, email, payload.Email, "Expected Email to be %s, but got %s", email, payload.Email)
            }
        }
    }
}

// AssertPostPublishedEvent kiểm tra nội dung của post published event
func AssertPostPublishedEvent(t *testing.T, publisher *MockPublisher, postID uint, title string) {
    events := publisher.GetEventsByType(types.BlogPostPublished)
    assert.NotEmpty(t, events, "Expected PostPublished event, but none was found")
    
    if len(events) > 0 {
        event, ok := events[0].Payload.(events.BaseEvent)
        assert.True(t, ok, "Expected event payload to be BaseEvent")
        
        if ok {
            payload, ok := event.Payload().(types.PostPublishedPayload)
            assert.True(t, ok, "Expected event payload to be PostPublishedPayload")
            
            if ok {
                assert.Equal(t, postID, payload.PostID, "Expected PostID to be %d, but got %d", postID, payload.PostID)
                assert.Equal(t, title, payload.Title, "Expected Title to be %s, but got %s", title, payload.Title)
            }
        }
    }
}
```

### 3. Triển Khai Unit Tests cho Event Publisher

Tạo file `internal/pkg/events/publisher_test.go`:

```go
package events_test

import (
    "context"
    "testing"
    "time"

    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/require"
    
    "your-project/internal/pkg/events"
)

func TestPublisher_PublishEvent(t *testing.T) {
    // Setup test config
    config := events.Config{
        RedisHost:     "localhost",
        RedisPort:     6379,
        RedisDB:       1, // Use different DB for testing
        ConsumerGroup: "test_consumers",
        ConsumerID:    "test_consumer",
    }
    
    publisher, err := events.NewPublisher(config)
    require.NoError(t, err)
    defer publisher.Close()
    
    subscriber, err := events.NewSubscriber(config)
    require.NoError(t, err)
    defer subscriber.Close()
    
    ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
    defer cancel()
    
    // Subscribe to test topic
    const testTopic = "test.topic"
    messages, err := subscriber.Subscribe(ctx, testTopic)
    require.NoError(t, err)
    
    // Create test event
    tenantID := uint(1)
    websiteID := uint(1)
    userID := uint(123)
    
    testEvent := events.NewEvent(
        testTopic,
        tenantID,
        websiteID,
        &userID,
        map[string]interface{}{
            "test_key": "test_value",
            "number":   42,
        },
    )
    
    // Publish event
    err = publisher.PublishEvent(ctx, testEvent)
    require.NoError(t, err)
    
    // Wait for message
    select {
    case msg := <-messages:
        assert.NotNil(t, msg)
        
        // Verify message content
        var receivedEvent events.BaseEvent
        err := json.Unmarshal(msg.Payload, &receivedEvent)
        require.NoError(t, err)
        
        assert.Equal(t, testTopic, receivedEvent.Type())
        assert.Equal(t, tenantID, receivedEvent.TenantID())
        assert.Equal(t, websiteID, receivedEvent.WebsiteID())
        assert.Equal(t, &userID, receivedEvent.UserID())
        
        // Acknowledge message
        msg.Ack()
        
    case <-ctx.Done():
        t.Fatal("Timeout waiting for message")
    }
}
```

### 4. Triển Khai Tests cho Module Integration

Tạo file `modules/auth/service/auth_service_test.go`:

```go
package service_test

import (
    "context"
    "testing"
    
    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/mock"
    "github.com/stretchr/testify/require"
    
    "your-project/internal/pkg/events/testing"
    "your-project/internal/pkg/events/types"
    "your-project/modules/auth/events"
    "your-project/modules/auth/repository"
    "your-project/modules/auth/service"
)

// MockUserRepository là một mock của user repository
type MockUserRepository struct {
    mock.Mock
}

func (m *MockUserRepository) Create(ctx context.Context, user *service.User) error {
    args := m.Called(ctx, user)
    return args.Error(0)
}

func (m *MockUserRepository) FindByEmail(ctx context.Context, email string) (*service.User, error) {
    args := m.Called(ctx, email)
    if args.Get(0) == nil {
        return nil, args.Error(1)
    }
    return args.Get(0).(*service.User), args.Error(1)
}

// MockPasswordService là một mock của password service
type MockPasswordService struct {
    mock.Mock
}

func (m *MockPasswordService) Verify(plain, hashed string) bool {
    args := m.Called(plain, hashed)
    return args.Bool(0)
}

func TestAuthService_CreateUser(t *testing.T) {
    // Setup mocks
    mockRepo := new(MockUserRepository)
    mockPublisher := testing.NewMockPublisher()
    eventPublisher := events.NewPublisher(mockPublisher)
    
    // Create service instance
    authService := service.NewAuthService(mockRepo, nil, eventPublisher)
    
    // Setup test data
    tenantID := uint(1)
    websiteID := uint(1)
    userID := uint(123)
    email := "<EMAIL>"
    username := "testuser"
    fullName := "Test User"
    
    // Setup expectations
    mockRepo.On("Create", mock.Anything, mock.MatchedBy(func(user *service.User) bool {
        return user.Email == email && user.Username == username
    })).Return(nil)
    
    // Execute
    req := service.CreateUserRequest{
        TenantID:  tenantID,
        WebsiteID: websiteID,
        Email:     email,
        Username:  username,
        FullName:  fullName,
    }
    
    user, err := authService.CreateUser(context.Background(), req)
    
    // Assert
    require.NoError(t, err)
    assert.NotNil(t, user)
    assert.Equal(t, email, user.Email)
    
    // Verify event was published
    testing.AssertEventPublished(t, mockPublisher, types.AuthUserCreated)
    testing.AssertUserCreatedEvent(t, mockPublisher, userID, email)
    
    // Verify mock expectations
    mockRepo.AssertExpectations(t)
}

func TestAuthService_Login_Success(t *testing.T) {
    // Setup mocks
    mockRepo := new(MockUserRepository)
    mockPasswordService := new(MockPasswordService)
    mockPublisher := testing.NewMockPublisher()
    eventPublisher := events.NewPublisher(mockPublisher)
    
    // Create service instance
    authService := service.NewAuthService(mockRepo, mockPasswordService, eventPublisher)
    
    // Setup test data
    tenantID := uint(1)
    websiteID := uint(1)
    userID := uint(123)
    email := "<EMAIL>"
    password := "password123"
    hashedPassword := "hashed_password"
    ipAddress := "127.0.0.1"
    userAgent := "Mozilla/5.0"
    
    user := &service.User{
        ID:       userID,
        Email:    email,
        Password: hashedPassword,
    }
    
    // Setup expectations
    mockRepo.On("FindByEmail", mock.Anything, email).Return(user, nil)
    mockPasswordService.On("Verify", password, hashedPassword).Return(true)
    
    // Execute
    result, err := authService.Login(
        context.Background(),
        email,
        password,
        ipAddress,
        userAgent,
        tenantID,
        websiteID,
    )
    
    // Assert
    require.NoError(t, err)
    assert.NotNil(t, result)
    assert.Equal(t, userID, result.ID)
    
    // Verify event was published
    testing.AssertEventPublished(t, mockPublisher, types.AuthUserLoginSuccess)
    
    // Verify mock expectations
    mockRepo.AssertExpectations(t)
    mockPasswordService.AssertExpectations(t)
}
```

### 5. Triển Khai Integration Test Suite

Tạo file `internal/pkg/events/integration_test.go`:

```go
package events_test

import (
    "context"
    "testing"
    "time"
    
    "github.com/stretchr/testify/suite"
    
    "your-project/internal/pkg/events"
    "your-project/internal/pkg/events/types"
)

type EventSystemIntegrationSuite struct {
    suite.Suite
    publisher  *events.Publisher
    subscriber *events.Subscriber
    router     *events.EventRouter
    config     events.Config
}

func (suite *EventSystemIntegrationSuite) SetupSuite() {
    suite.config = events.Config{
        RedisHost:     "localhost",
        RedisPort:     6379,
        RedisDB:       2, // Different DB for integration tests
        ConsumerGroup: "integration_test_consumers",
        ConsumerID:    "integration_test_consumer",
        Debug:         true,
    }
    
    var err error
    
    suite.publisher, err = events.NewPublisher(suite.config)
    suite.Require().NoError(err)
    
    suite.subscriber, err = events.NewSubscriber(suite.config)
    suite.Require().NoError(err)
    
    suite.router, err = events.NewEventRouter(suite.config, suite.publisher)
    suite.Require().NoError(err)
}

func (suite *EventSystemIntegrationSuite) TearDownSuite() {
    if suite.publisher != nil {
        suite.publisher.Close()
    }
    if suite.subscriber != nil {
        suite.subscriber.Close()
    }
    if suite.router != nil {
        suite.router.Close()
    }
}

func (suite *EventSystemIntegrationSuite) TestUserCreationWorkflow() {
    ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
    defer cancel()
    
    // Prepare channels to track handler calls
    emailSent := make(chan bool, 1)
    analyticsTracked := make(chan bool, 1)
    
    // Setup test handlers
    emailHandler := func(msg *message.Message) error {
        var event events.BaseEvent
        err := json.Unmarshal(msg.Payload, &event)
        suite.Require().NoError(err)
        
        if event.Type() == types.AuthUserCreated {
            emailSent <- true
        }
        return nil
    }
    
    analyticsHandler := func(msg *message.Message) error {
        var event events.BaseEvent
        err := json.Unmarshal(msg.Payload, &event)
        suite.Require().NoError(err)
        
        if event.Type() == types.AuthUserCreated {
            analyticsTracked <- true
        }
        return nil
    }
    
    // Register handlers
    suite.router.AddNoPublisherHandler(
        "test_email_handler",
        types.AuthUserCreated,
        suite.subscriber.subscriber,
        emailHandler,
    )
    
    suite.router.AddNoPublisherHandler(
        "test_analytics_handler",
        types.AuthUserCreated,
        suite.subscriber.subscriber,
        analyticsHandler,
    )
    
    // Start router
    go suite.router.Run(ctx)
    
    // Wait for router to start
    time.Sleep(200 * time.Millisecond)
    
    // Publish user created event
    tenantID := uint(1)
    websiteID := uint(1)
    userID := uint(123)
    
    payload := types.UserCreatedPayload{
        UserID:    userID,
        Email:     "<EMAIL>",
        Username:  "integrationuser",
        FullName:  "Integration User",
        Status:    "active",
    }
    
    event := events.NewEvent(
        types.AuthUserCreated,
        tenantID,
        websiteID,
        &userID,
        payload,
    )
    
    err := suite.publisher.PublishEvent(ctx, event)
    suite.Require().NoError(err)
    
    // Verify both handlers were called
    select {
    case <-emailSent:
        // Success
    case <-ctx.Done():
        suite.Fail("Email handler was not called")
    }
    
    select {
    case <-analyticsTracked:
        // Success  
    case <-ctx.Done():
        suite.Fail("Analytics handler was not called")
    }
}

func TestEventSystemIntegrationSuite(t *testing.T) {
    if testing.Short() {
        t.Skip("Skipping integration tests in short mode")
    }
    suite.Run(t, new(EventSystemIntegrationSuite))
}
```

## Điểm Kiểm Tra
- [ ] Mock publisher đã được triển khai
- [ ] Helper functions cho testing đã được triển khai
- [ ] Unit tests cho event publisher đã được triển khai
- [ ] Tests cho module integration đã được triển khai
- [ ] Integration test suite đã được triển khai
- [ ] Tất cả các tests có thể chạy mà không có lỗi

## Lưu Ý
- Đảm bảo tests có thể chạy một cách tách biệt và không ảnh hưởng đến dữ liệu production
- Sử dụng các database khác nhau (hoặc Redis DB indexes) cho các loại tests khác nhau
- Cân nhắc sử dụng miniredis cho unit tests để không cần phụ thuộc vào Redis thực
- Cung cấp các helper functions rõ ràng và dễ sử dụng cho team
- Đảm bảo tất cả các error cases đều được test
- Cung cấp documentation đầy đủ về cách chạy tests 