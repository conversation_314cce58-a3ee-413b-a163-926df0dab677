# Task 10: Best Practices và Troubleshooting

## <PERSON><PERSON><PERSON> Tiêu
- <PERSON><PERSON><PERSON> hợp các best practices cho thiết kế và sử dụng event system
- Cung cấp hướng dẫn tối ưu hiệu suất
- X<PERSON>y dựng hướng dẫn xử lý lỗi và troubleshooting

## Các Bước Thực Hiện

### 1. Best Practices cho Event Design

#### 1.1 Nguyên Tắc Thiết Kế Event

```
# Event Design Best Practices

## Đặt Tên Event
- Sử dụng cấu trúc {domain}.{entity}.{action} (ví dụ: auth.user.created)
- <PERSON><PERSON><PERSON> bảo tên rõ ràng và nhất quán trong toàn bộ hệ thống
- Sử dụng động từ quá khứ cho actions (created, updated, deleted)

## Thiết Kế Payload
- Giữ payload nhỏ gọn, chỉ chứa thông tin cần thiết
- <PERSON><PERSON><PERSON> b<PERSON><PERSON> tất cả các events đều có timestamps
- <PERSON><PERSON> gồ<PERSON> đủ thông tin để handlers có thể hoạt động mà không cần truy vấn thêm dữ liệu
- <PERSON><PERSON><PERSON> bao gồm tenant_id, website_id để hỗ trợ multi-tenant
- Cấu trúc JSON nhất quán và sử dụng snake_case cho keys

## Version Events
- Cân nhắc việc thêm version vào events nếu có khả năng cấu trúc sẽ thay đổi
- Đảm bảo backward compatibility trong schema evolution

## Bảo Mật
- Không bao giờ đưa thông tin nhạy cảm vào events (mật khẩu, tokens)
- Nếu cần, sử dụng reference IDs thay vì dữ liệu thực
- Cân nhắc mã hóa payload nếu chứa dữ liệu nhạy cảm

## Mẹo Khác
- Duy trì documentation cho tất cả các loại events và payload
- Cân nhắc việc tạo thư viện client để đảm bảo sử dụng nhất quán
- Thiết lập standard cho các metadata cần thiết
```

#### 1.2 Anti-Patterns để Tránh

```
# Event System Anti-Patterns

## Hiệu Suất
- Payload quá lớn (> 1MB) làm giảm hiệu suất
- Quá nhiều events được tạo (lưu ý về độ chi tiết phù hợp)
- Handler thực hiện xử lý nặng mà không sử dụng worker pools

## Kiến Trúc
- Sử dụng event system cho việc truy vấn đồng bộ
- Phụ thuộc chặt chẽ giữa producers và consumers
- Tạo vòng lặp vô hạn (event A gây ra event B gây ra event A...)

## Xử Lý Lỗi
- Không xử lý lỗi trong handlers
- Rollback database transaction khi event publishing thất bại

## Khác
- Sử dụng events cho tất cả các tương tác, kể cả khi request-response đơn giản là đủ
- Thiếu monitoring và alerting
- Không có kế hoạch xử lý dead-letter queue
```

### 2. Performance Optimization

#### 2.1 Redis Performance Tuning

```
# Redis Performance Tuning cho Event System

## Memory Management
- Cấu hình maxmemory để tránh OOM errors
- Chọn maxmemory-policy phù hợp (allkeys-lru cho cache, noeviction cho queue)
- Sử dụng Redis Enterprise hoặc Redis Cluster cho dataset lớn

## Persistence
- Sử dụng AOF cho data safety với appendfsync=everysec
- Cấu hình auto-aof-rewrite-percentage và auto-aof-rewrite-min-size
- Lên kế hoạch backup thường xuyên

## Connections
- Sử dụng connection pool trong application
- Điều chỉnh tcp-keepalive (300 giây là phù hợp cho hầu hết trường hợp)
- Cấu hình timeout thích hợp

## Stream-Specific
- Điều chỉnh stream-node-max-bytes và stream-node-max-entries
- Xóa messages cũ thường xuyên với XDEL hoặc maxlen
- Tránh quá nhiều consumer groups không sử dụng

## Monitoring
- Giám sát latency với redis-cli --latency
- Sử dụng Redis INFO command để theo dõi memory, clients
- Cấu hình alerting cho slow log entries
```

#### 2.2 Application Performance Optimization

```go
// Batch processing để tăng throughput
func BatchPublisher(publisher *events.Publisher, batchSize int, flushInterval time.Duration) *BatchPublisher {
    return &BatchPublisher{
        publisher:     publisher,
        batchSize:     batchSize,
        flushInterval: flushInterval,
        messages:      make(map[string][]*message.Message),
        mutex:         &sync.Mutex{},
    }
}

// Worker pool pattern cho event handling
func WorkerPoolHandler(workers int, handler message.HandlerFunc) message.HandlerFunc {
    jobs := make(chan *message.Message, workers*10)
    results := make(chan message.HandlerResult, workers*10)
    
    // Start worker pool
    for i := 0; i < workers; i++ {
        go func() {
            for msg := range jobs {
                msgs, err := handler(msg)
                results <- message.HandlerResult{
                    Messages: msgs,
                    Error:    err,
                }
            }
        }()
    }
    
    return func(msg *message.Message) ([]*message.Message, error) {
        jobs <- msg
        result := <-results
        return result.Messages, result.Error
    }
}

// Timeout patterns
func TimeoutMiddleware(timeout time.Duration) message.HandlerMiddleware {
    return func(h message.HandlerFunc) message.HandlerFunc {
        return func(msg *message.Message) ([]*message.Message, error) {
            ctx, cancel := context.WithTimeout(context.Background(), timeout)
            defer cancel()
            
            done := make(chan message.HandlerResult, 1)
            go func() {
                msgs, err := h(msg)
                done <- message.HandlerResult{
                    Messages: msgs,
                    Error:    err,
                }
            }()
            
            select {
            case result := <-done:
                return result.Messages, result.Error
            case <-ctx.Done():
                return nil, fmt.Errorf("handler timeout after %v", timeout)
            }
        }
    }
}
```

### 3. Error Handling Patterns

```go
// Circuit breaker pattern
type CircuitBreaker struct {
    threshold     int
    timeout       time.Duration
    state         int // 0: closed, 1: open, 2: half-open
    failures      int
    lastFailure   time.Time
    mutex         sync.RWMutex
}

func NewCircuitBreaker(threshold int, timeout time.Duration) *CircuitBreaker {
    return &CircuitBreaker{
        threshold: threshold,
        timeout:   timeout,
        state:     0, // closed
    }
}

func (cb *CircuitBreaker) Execute(f func() error) error {
    cb.mutex.RLock()
    state := cb.state
    lastFailure := cb.lastFailure
    cb.mutex.RUnlock()
    
    // Check if circuit is open
    if state == 1 {
        if time.Since(lastFailure) > cb.timeout {
            // Try to half-open the circuit
            cb.mutex.Lock()
            if cb.state == 1 {
                cb.state = 2 // half-open
            }
            cb.mutex.Unlock()
        } else {
            return fmt.Errorf("circuit open")
        }
    }
    
    // Execute function
    err := f()
    
    // Update circuit state
    if err != nil {
        cb.mutex.Lock()
        cb.failures++
        cb.lastFailure = time.Now()
        if (cb.state == 0 && cb.failures >= cb.threshold) || cb.state == 2 {
            cb.state = 1 // open
        }
        cb.mutex.Unlock()
    } else if state == 2 {
        // Success in half-open state, close the circuit
        cb.mutex.Lock()
        cb.failures = 0
        cb.state = 0 // closed
        cb.mutex.Unlock()
    }
    
    return err
}

// Middleware for dead letter queue
func DeadLetterQueue(publisher *events.Publisher, dlqTopic string, maxRetries int) message.HandlerMiddleware {
    return func(h message.HandlerFunc) message.HandlerFunc {
        return func(msg *message.Message) ([]*message.Message, error) {
            // Get retry count
            retryCountStr := msg.Metadata.Get("retry_count")
            retryCount := 0
            if retryCountStr != "" {
                retryCount, _ = strconv.Atoi(retryCountStr)
            }
            
            // Check if max retries reached
            if retryCount >= maxRetries {
                // Send to dead letter queue
                dlqMsg := message.NewMessage(msg.UUID, msg.Payload)
                for k, v := range msg.Metadata {
                    dlqMsg.Metadata.Set(k, v)
                }
                dlqMsg.Metadata.Set("original_topic", msg.Metadata.Get("topic"))
                dlqMsg.Metadata.Set("error_count", strconv.Itoa(retryCount))
                dlqMsg.Metadata.Set("moved_to_dlq_at", time.Now().Format(time.RFC3339))
                
                err := publisher.Publish(context.Background(), dlqTopic, dlqMsg)
                if err != nil {
                    // Log but don't fail - we'll retry the original message
                    log.Printf("Failed to send message to DLQ: %v", err)
                } else {
                    // Successfully sent to DLQ, acknowledge original message
                    return nil, nil
                }
            }
            
            // Process message
            msgs, err := h(msg)
            
            // Increment retry count if error
            if err != nil {
                msg.Metadata.Set("retry_count", strconv.Itoa(retryCount+1))
                msg.Metadata.Set("last_error", err.Error())
                msg.Metadata.Set("last_retry_at", time.Now().Format(time.RFC3339))
            }
            
            return msgs, err
        }
    }
}
```

### 4. Troubleshooting Guide

#### 4.1 Common Issues và Solutions

```
# Event System Troubleshooting Guide

## Messages Not Being Processed

### Symptoms
- Events được published nhưng không được xử lý bởi handlers
- Không có lỗi hiển thị trong logs

### Potential Causes
1. Consumer group không tồn tại hoặc không khớp
2. Consumer ID trùng lặp
3. Redis connection issues
4. Router không chạy

### Solutions
1. Kiểm tra consumer group với lệnh Redis:
   ```
   XINFO GROUPS <stream name>
   ```
2. Đảm bảo mỗi instance có consumer ID duy nhất
3. Kiểm tra Redis connection và auth
4. Kiểm tra router logs và đảm bảo router đang chạy

## High Latency

### Symptoms
- Độ trễ cao giữa thời điểm publish và xử lý
- CPU sử dụng cao

### Potential Causes
1. Quá nhiều messages đang được xử lý
2. Handlers thực hiện tác vụ blocking
3. Redis overloaded
4. Network issues

### Solutions
1. Tăng số lượng consumers hoặc instances
2. Sử dụng worker pool trong handlers
3. Kiểm tra Redis metrics (INFO)
4. Kiểm tra network latency

## Message Loss

### Symptoms
- Messages được published nhưng biến mất
- Số lượng messages không khớp

### Potential Causes
1. ACKs trước khi xử lý thành công
2. Redis persistence không đúng cấu hình
3. Maxlen stream làm mất messages cũ

### Solutions
1. Chỉ ACK sau khi xử lý thành công
2. Cấu hình AOF và RDB phù hợp
3. Kiểm tra và điều chỉnh maxlen

## Redis Connection Issues

### Symptoms
- "Connection refused" errors
- Intermittent failures

### Potential Causes
1. Redis không chạy
2. Sai cấu hình network
3. Auth failure
4. Redis OOM

### Solutions
1. Kiểm tra Redis process
2. Kiểm tra network (telnet)
3. Kiểm tra password
4. Kiểm tra memory usage với INFO command
```

#### 4.2 Useful Debug Commands

```bash
# Redis Commands cho Troubleshooting

# Liệt kê tất cả streams
redis-cli KEYS "*"

# Kiểm tra info của stream
redis-cli XINFO STREAM <stream name>

# Kiểm tra consumer groups
redis-cli XINFO GROUPS <stream name>

# Kiểm tra consumers trong group
redis-cli XINFO CONSUMERS <stream name> <group name>

# Xem pending messages
redis-cli XPENDING <stream name> <group name>

# Xem pending messages chi tiết
redis-cli XPENDING <stream name> <group name> - + 10

# Đọc messages từ stream
redis-cli XRANGE <stream name> - + COUNT 10

# Đọc messages gần đây nhất
redis-cli XREVRANGE <stream name> + - COUNT 10

# Kiểm tra memory usage
redis-cli INFO memory

# Kiểm tra client connections
redis-cli CLIENT LIST

# Kiểm tra slow log
redis-cli SLOWLOG GET 10

# Đặt lại consumer group (cẩn thận!)
redis-cli XGROUP DESTROY <stream name> <group name>
redis-cli XGROUP CREATE <stream name> <group name> $ MKSTREAM
```

### 5. Documentation và References

```
# Redis Streams Documentation
- Watermill Redis Streams Adapter: https://watermill.io/docs/pub-sub-implementations/redis-streams/
- Redis Streams Intro: https://redis.io/topics/streams-intro
- Redis Command Reference: https://redis.io/commands#stream

# Performance Testing Tools
- redis-benchmark: https://redis.io/topics/benchmarks
- Watermill Benchmark: https://github.com/ThreeDotsLabs/watermill/tree/master/tools/mill

# Monitoring Solutions
- Redis Exporter: https://github.com/oliver006/redis_exporter
- Prometheus: https://prometheus.io/
- Grafana: https://grafana.com/

# Additional Resources
- Event-Driven Architecture Patterns: https://martinfowler.com/articles/201701-event-driven.html
- Redis Best Practices: https://redis.io/topics/optimization
- Circuit Breaker Pattern: https://martinfowler.com/bliki/CircuitBreaker.html
```

## Điểm Kiểm Tra
- [ ] Best practices cho thiết kế và sử dụng event đã được tổng hợp
- [ ] Hướng dẫn tối ưu hiệu suất đã được cung cấp
- [ ] Error handling patterns đã được triển khai
- [ ] Troubleshooting guide đã được xây dựng
- [ ] Documentation và references đã được cung cấp

## Lưu Ý
- Đảm bảo tất cả các mẫu code đều được kiểm tra trước khi sử dụng trong production
- Cập nhật troubleshooting guide khi gặp các vấn đề mới
- Đào tạo team về cách xử lý sự cố và tối ưu hệ thống
- Xem xét việc thêm metrics và alerts cho các vấn đề thường gặp
- Thực hiện các load tests trước khi triển khai lên production
- Duy trì changelog cho event schemas để dễ dàng theo dõi các thay đổi 