# Task 4: <PERSON><PERSON><PERSON> Event Types và Payload

## <PERSON><PERSON><PERSON>
- <PERSON><PERSON><PERSON> nghĩa các hằng số cho các loại event
- <PERSON><PERSON><PERSON> nghĩa cấu trúc payload cho mỗi loại event
- Tạo interfaces và structs cần thiết cho event system

## <PERSON><PERSON><PERSON>

### 1. Định Nghĩa Event Constants

Tạo file `internal/pkg/events/types/constants.go`:

```go
package types

// Auth Events
const (
    AuthUserCreated      = "auth.user.created"
    AuthUserUpdated      = "auth.user.updated"
    AuthUserDeleted      = "auth.user.deleted"
    AuthUserLoginSuccess = "auth.user.login_success"
    AuthUserLoginFailed  = "auth.user.login_failed"
    AuthUserLogout       = "auth.user.logout"
    AuthPasswordReset    = "auth.user.password_reset"
    AuthEmailVerified    = "auth.user.email_verified"
)

// Blog Events
const (
    BlogPostCreated   = "blog.post.created"
    BlogPostPublished = "blog.post.published"
    BlogPostUpdated   = "blog.post.updated"
    BlogPostDeleted   = "blog.post.deleted"
    BlogPostViewed    = "blog.post.viewed"
    
    BlogCommentCreated  = "blog.comment.created"
    BlogCommentApproved = "blog.comment.approved"
    BlogCommentRejected = "blog.comment.rejected"
    BlogCommentDeleted  = "blog.comment.deleted"
)

// E-commerce Events
const (
    ProductCreated          = "product.created"
    ProductUpdated          = "product.updated"
    ProductDeleted          = "product.deleted"
    ProductInventoryChanged = "product.inventory_changed"
    ProductPriceChanged     = "product.price_changed"
    
    OrderCreated         = "order.created"
    OrderPaymentReceived = "order.payment_received"
    OrderPaymentFailed   = "order.payment_failed"
    OrderFulfilled       = "order.fulfilled"
    OrderShipped         = "order.shipped"
    OrderDelivered       = "order.delivered"
    OrderCancelled       = "order.cancelled"
    OrderRefunded        = "order.refunded"
)

// System Events
const (
    SystemStarted         = "system.started"
    SystemStopped         = "system.stopped"
    SystemHealthCheck     = "system.health_check"
    SystemMaintenanceMode = "system.maintenance_mode"
)
```

### 2. Định Nghĩa Auth Event Payloads

Tạo file `internal/pkg/events/types/auth_events.go`:

```go
package types

import (
    "time"
)

// Auth Event Payloads
type UserCreatedPayload struct {
    UserID    uint      `json:"user_id"`
    Email     string    `json:"email"`
    Username  string    `json:"username"`
    FullName  string    `json:"full_name"`
    Status    string    `json:"status"`
    CreatedAt time.Time `json:"created_at"`
}

type UserUpdatedPayload struct {
    UserID    uint      `json:"user_id"`
    Email     string    `json:"email"`
    Username  string    `json:"username"`
    FullName  string    `json:"full_name"`
    Status    string    `json:"status"`
    UpdatedAt time.Time `json:"updated_at"`
}

type UserDeletedPayload struct {
    UserID    uint      `json:"user_id"`
    DeletedAt time.Time `json:"deleted_at"`
}

type UserLoginPayload struct {
    UserID    uint      `json:"user_id"`
    Email     string    `json:"email"`
    IPAddress string    `json:"ip_address"`
    UserAgent string    `json:"user_agent"`
    Success   bool      `json:"success"`
    Reason    string    `json:"reason,omitempty"`
    LoginAt   time.Time `json:"login_at"`
}

type PasswordResetPayload struct {
    UserID          uint      `json:"user_id"`
    Email           string    `json:"email"`
    RequestedAt     time.Time `json:"requested_at"`
    CompletedAt     *time.Time `json:"completed_at,omitempty"`
    RequestIPAddress string    `json:"request_ip_address"`
}

type EmailVerifiedPayload struct {
    UserID     uint      `json:"user_id"`
    Email      string    `json:"email"`
    VerifiedAt time.Time `json:"verified_at"`
}
```

### 3. Định Nghĩa Blog Event Payloads

Tạo file `internal/pkg/events/types/blog_events.go`:

```go
package types

import (
    "time"
)

// Blog Event Payloads
type PostCreatedPayload struct {
    PostID     uint      `json:"post_id"`
    Title      string    `json:"title"`
    Slug       string    `json:"slug"`
    AuthorID   uint      `json:"author_id"`
    Status     string    `json:"status"`
    CreatedAt  time.Time `json:"created_at"`
}

type PostPublishedPayload struct {
    PostID      uint      `json:"post_id"`
    Title       string    `json:"title"`
    Slug        string    `json:"slug"`
    AuthorID    uint      `json:"author_id"`
    PublishedAt time.Time `json:"published_at"`
}

type PostUpdatedPayload struct {
    PostID     uint      `json:"post_id"`
    Title      string    `json:"title"`
    Slug       string    `json:"slug"`
    AuthorID   uint      `json:"author_id"`
    Status     string    `json:"status"`
    UpdatedAt  time.Time `json:"updated_at"`
}

type PostDeletedPayload struct {
    PostID     uint      `json:"post_id"`
    Title      string    `json:"title"`
    Slug       string    `json:"slug"`
    AuthorID   uint      `json:"author_id"`
    DeletedAt  time.Time `json:"deleted_at"`
}

type PostViewedPayload struct {
    PostID     uint      `json:"post_id"`
    Slug       string    `json:"slug"`
    ViewerIP   string    `json:"viewer_ip"`
    UserID     *uint     `json:"user_id,omitempty"`
    UserAgent  string    `json:"user_agent"`
    ViewedAt   time.Time `json:"viewed_at"`
}

type CommentCreatedPayload struct {
    CommentID uint      `json:"comment_id"`
    PostID    uint      `json:"post_id"`
    AuthorID  *uint     `json:"author_id,omitempty"`
    Name      string    `json:"name"`
    Email     string    `json:"email"`
    Content   string    `json:"content"`
    Status    string    `json:"status"`
    CreatedAt time.Time `json:"created_at"`
}

type CommentStatusChangedPayload struct {
    CommentID  uint      `json:"comment_id"`
    PostID     uint      `json:"post_id"`
    Status     string    `json:"status"`
    ModeratorID uint     `json:"moderator_id"`
    UpdatedAt  time.Time `json:"updated_at"`
}

type CommentDeletedPayload struct {
    CommentID  uint      `json:"comment_id"`
    PostID     uint      `json:"post_id"`
    DeletedAt  time.Time `json:"deleted_at"`
}
```

### 4. Định Nghĩa E-commerce Event Payloads

Tạo file `internal/pkg/events/types/ecommerce_events.go`:

```go
package types

import (
    "time"
    
    "github.com/shopspring/decimal"
)

// Product Event Payloads
type ProductCreatedPayload struct {
    ProductID   uint            `json:"product_id"`
    Name        string          `json:"name"`
    SKU         string          `json:"sku"`
    Price       decimal.Decimal `json:"price"`
    Inventory   int             `json:"inventory"`
    Status      string          `json:"status"`
    CreatedAt   time.Time       `json:"created_at"`
}

type ProductUpdatedPayload struct {
    ProductID   uint            `json:"product_id"`
    Name        string          `json:"name"`
    SKU         string          `json:"sku"`
    Price       decimal.Decimal `json:"price"`
    Inventory   int             `json:"inventory"`
    Status      string          `json:"status"`
    UpdatedAt   time.Time       `json:"updated_at"`
}

type ProductDeletedPayload struct {
    ProductID   uint      `json:"product_id"`
    SKU         string    `json:"sku"`
    DeletedAt   time.Time `json:"deleted_at"`
}

type ProductInventoryChangedPayload struct {
    ProductID     uint      `json:"product_id"`
    SKU           string    `json:"sku"`
    OldInventory  int       `json:"old_inventory"`
    NewInventory  int       `json:"new_inventory"`
    Reason        string    `json:"reason"`
    ChangedAt     time.Time `json:"changed_at"`
}

type ProductPriceChangedPayload struct {
    ProductID   uint            `json:"product_id"`
    SKU         string          `json:"sku"`
    OldPrice    decimal.Decimal `json:"old_price"`
    NewPrice    decimal.Decimal `json:"new_price"`
    ChangedAt   time.Time       `json:"changed_at"`
}

// Order Event Payloads
type OrderCreatedPayload struct {
    OrderID     uint                `json:"order_id"`
    CustomerID  uint                `json:"customer_id"`
    TotalAmount decimal.Decimal     `json:"total_amount"`
    Currency    string              `json:"currency"`
    Status      string              `json:"status"`
    Items       []OrderItemPayload  `json:"items"`
    CreatedAt   time.Time           `json:"created_at"`
}

type OrderItemPayload struct {
    ProductID uint            `json:"product_id"`
    Quantity  int             `json:"quantity"`
    Price     decimal.Decimal `json:"price"`
    Total     decimal.Decimal `json:"total"`
}

type OrderPaymentPayload struct {
    OrderID     uint            `json:"order_id"`
    CustomerID  uint            `json:"customer_id"`
    Amount      decimal.Decimal `json:"amount"`
    Currency    string          `json:"currency"`
    PaymentID   string          `json:"payment_id"`
    PaymentType string          `json:"payment_type"`
    Success     bool            `json:"success"`
    ErrorCode   *string         `json:"error_code,omitempty"`
    ErrorMsg    *string         `json:"error_msg,omitempty"`
    ProcessedAt time.Time       `json:"processed_at"`
}

type OrderStatusChangedPayload struct {
    OrderID     uint      `json:"order_id"`
    CustomerID  uint      `json:"customer_id"`
    OldStatus   string    `json:"old_status"`
    NewStatus   string    `json:"new_status"`
    ChangedAt   time.Time `json:"changed_at"`
}

type OrderFulfilledPayload struct {
    OrderID     uint      `json:"order_id"`
    CustomerID  uint      `json:"customer_id"`
    FulfilledAt time.Time `json:"fulfilled_at"`
}

type OrderShippedPayload struct {
    OrderID        uint      `json:"order_id"`
    CustomerID     uint      `json:"customer_id"`
    TrackingNumber string    `json:"tracking_number"`
    Carrier        string    `json:"carrier"`
    ShippedAt      time.Time `json:"shipped_at"`
}
```

### 5. Định Nghĩa System Event Payloads

Tạo file `internal/pkg/events/types/system_events.go`:

```go
package types

import (
    "time"
)

// System Event Payloads
type SystemStartedPayload struct {
    ServiceName    string    `json:"service_name"`
    ServiceVersion string    `json:"service_version"`
    Environment    string    `json:"environment"`
    StartedAt      time.Time `json:"started_at"`
}

type SystemStoppedPayload struct {
    ServiceName    string    `json:"service_name"`
    ServiceVersion string    `json:"service_version"`
    Environment    string    `json:"environment"`
    StoppedAt      time.Time `json:"stopped_at"`
    Reason         string    `json:"reason"`
}

type SystemHealthCheckPayload struct {
    ServiceName    string                 `json:"service_name"`
    Status         string                 `json:"status"`
    Metrics        map[string]interface{} `json:"metrics"`
    Dependencies   map[string]string      `json:"dependencies"`
    CheckedAt      time.Time              `json:"checked_at"`
}

type SystemMaintenanceModePayload struct {
    ServiceName     string    `json:"service_name"`
    Enabled         bool      `json:"enabled"`
    Reason          string    `json:"reason"`
    EstimatedEndAt  *time.Time `json:"estimated_end_at,omitempty"`
    ChangedAt       time.Time `json:"changed_at"`
}
```

## Điểm Kiểm Tra
- [ ] Các hằng số cho các loại event đã được định nghĩa
- [ ] Cấu trúc payload cho các event Auth đã được định nghĩa
- [ ] Cấu trúc payload cho các event Blog đã được định nghĩa
- [ ] Cấu trúc payload cho các event E-commerce đã được định nghĩa
- [ ] Cấu trúc payload cho các event System đã được định nghĩa
- [ ] Tất cả các struct sử dụng tag json phù hợp
- [ ] Tất cả các thành phần có thể biên dịch mà không có lỗi

## Lưu Ý
- Đảm bảo mỗi payload có đủ thông tin cần thiết
- Xem xét việc bao gồm timestamps trong mỗi payload
- Đảm bảo các trường trong payload có tên nhất quán và tuân theo quy ước đặt tên snake_case cho JSON
- Xem xét việc thêm documentation cho từng struct để dễ dàng hiểu mục đích sử dụng 