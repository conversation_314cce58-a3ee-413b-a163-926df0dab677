# Task 6: T<PERSON><PERSON>ợp Event System với Các <PERSON>le

## <PERSON><PERSON><PERSON>
- Tích hợp event system với các module trong ứng dụng
- <PERSON><PERSON><PERSON> khai các hàm tiện ích để modules có thể dễ dàng phát hành và xử lý events
- Tạo ví dụ tích hợp cho một số module điển hình

## Các <PERSON>ước Thực Hiện

### 1. Tạo Helper cho Module Auth

Tạo file `modules/auth/events/publisher.go`:

```go
package events

import (
    "context"
    "log"

    "your-project/internal/pkg/events"
    "your-project/internal/pkg/events/types"
)

// Publisher wrapper cho module Auth
type Publisher struct {
    eventPublisher *events.Publisher
}

// NewPublisher tạo một instance của Auth event publisher
func NewPublisher(eventPublisher *events.Publisher) *Publisher {
    return &Publisher{
        eventPublisher: eventPublisher,
    }
}

// PublishUserCreated phát hành event khi user được tạo
func (p *Publisher) PublishUserCreated(ctx context.Context, tenantID, websiteID uint, user interface{}) error {
    // Convert user model to event payload
    userModel := user.(struct {
        ID       uint
        Email    string
        Username string
        FullName string
        Status   string
    })
    
    payload := types.UserCreatedPayload{
        UserID:    userModel.ID,
        Email:     userModel.Email,
        Username:  userModel.Username,
        FullName:  userModel.FullName,
        Status:    userModel.Status,
    }
    
    userID := userModel.ID
    
    event := events.NewEvent(
        types.AuthUserCreated,
        tenantID,
        websiteID,
        &userID,
        payload,
    )
    
    err := p.eventPublisher.PublishEvent(ctx, event)
    if err != nil {
        log.Printf("Failed to publish user created event: %v", err)
    }
    
    return err
}

// PublishUserLogin phát hành event khi user đăng nhập
func (p *Publisher) PublishUserLogin(ctx context.Context, tenantID, websiteID, userID uint, email, ipAddress, userAgent string, success bool, reason string) error {
    payload := types.UserLoginPayload{
        UserID:    userID,
        Email:     email,
        IPAddress: ipAddress,
        UserAgent: userAgent,
        Success:   success,
        Reason:    reason,
    }
    
    eventType := types.AuthUserLoginSuccess
    if !success {
        eventType = types.AuthUserLoginFailed
    }
    
    event := events.NewEvent(
        eventType,
        tenantID,
        websiteID,
        &userID,
        payload,
    )
    
    err := p.eventPublisher.PublishEvent(ctx, event)
    if err != nil {
        log.Printf("Failed to publish user login event: %v", err)
    }
    
    return err
}
```

### 2. Tích Hợp với Auth Service

Cập nhật file `modules/auth/service/auth_service.go` (hoặc tạo mới nếu chưa có):

```go
package service

import (
    "context"
    
    "your-project/modules/auth/events"
)

// AuthService xử lý logic business cho authentication
type AuthService struct {
    // dependencies...
    eventPublisher *events.Publisher
}

// NewAuthService tạo một instance mới của AuthService
func NewAuthService(
    // other dependencies...
    eventPublisher *events.Publisher,
) *AuthService {
    return &AuthService{
        // other dependencies...
        eventPublisher: eventPublisher,
    }
}

// CreateUser tạo user mới và phát hành event
func (s *AuthService) CreateUser(ctx context.Context, req CreateUserRequest) (*User, error) {
    // Create user logic
    user := &User{
        ID:       123, // giả sử ID được tạo
        Email:    req.Email,
        Username: req.Username,
        FullName: req.FullName,
        Status:   "active",
    }
    
    // Save to database
    if err := s.userRepo.Create(ctx, user); err != nil {
        return nil, err
    }
    
    // Publish event
    if err := s.eventPublisher.PublishUserCreated(ctx, req.TenantID, req.WebsiteID, user); err != nil {
        // Log error but don't fail the operation
        log.Printf("Failed to publish user created event: %v", err)
    }
    
    return user, nil
}

// Login xử lý đăng nhập user và phát hành event
func (s *AuthService) Login(ctx context.Context, email, password, ipAddress, userAgent string, tenantID, websiteID uint) (*User, error) {
    user, err := s.userRepo.FindByEmail(ctx, email)
    if err != nil {
        s.eventPublisher.PublishUserLogin(
            ctx, 
            tenantID, 
            websiteID, 
            0, // unknown user ID
            email, 
            ipAddress, 
            userAgent, 
            false, 
            "user not found",
        )
        return nil, err
    }
    
    if !s.passwordService.Verify(password, user.Password) {
        s.eventPublisher.PublishUserLogin(
            ctx, 
            tenantID, 
            websiteID, 
            user.ID, 
            email, 
            ipAddress, 
            userAgent, 
            false, 
            "invalid password",
        )
        return nil, errors.New("invalid credentials")
    }
    
    // Login successful
    s.eventPublisher.PublishUserLogin(
        ctx, 
        tenantID, 
        websiteID, 
        user.ID, 
        email, 
        ipAddress, 
        userAgent, 
        true, 
        "",
    )
    
    return user, nil
}
```

### 3. Tạo Helper cho Module Blog

Tạo file `modules/blog/events/publisher.go`:

```go
package events

import (
    "context"
    "log"

    "your-project/internal/pkg/events"
    "your-project/internal/pkg/events/types"
)

// Publisher wrapper cho module Blog
type Publisher struct {
    eventPublisher *events.Publisher
}

// NewPublisher tạo một instance của Blog event publisher
func NewPublisher(eventPublisher *events.Publisher) *Publisher {
    return &Publisher{
        eventPublisher: eventPublisher,
    }
}

// PublishPostCreated phát hành event khi bài viết được tạo
func (p *Publisher) PublishPostCreated(ctx context.Context, tenantID, websiteID, authorID uint, post interface{}) error {
    // Convert post model to event payload
    postModel := post.(struct {
        ID     uint
        Title  string
        Slug   string
        Status string
    })
    
    payload := types.PostCreatedPayload{
        PostID:   postModel.ID,
        Title:    postModel.Title,
        Slug:     postModel.Slug,
        AuthorID: authorID,
        Status:   postModel.Status,
    }
    
    event := events.NewEvent(
        types.BlogPostCreated,
        tenantID,
        websiteID,
        &authorID,
        payload,
    )
    
    err := p.eventPublisher.PublishEvent(ctx, event)
    if err != nil {
        log.Printf("Failed to publish post created event: %v", err)
    }
    
    return err
}

// PublishPostPublished phát hành event khi bài viết được publish
func (p *Publisher) PublishPostPublished(ctx context.Context, tenantID, websiteID, authorID uint, post interface{}) error {
    // Convert post model to event payload
    postModel := post.(struct {
        ID     uint
        Title  string
        Slug   string
    })
    
    payload := types.PostPublishedPayload{
        PostID:   postModel.ID,
        Title:    postModel.Title,
        Slug:     postModel.Slug,
        AuthorID: authorID,
    }
    
    event := events.NewEvent(
        types.BlogPostPublished,
        tenantID,
        websiteID,
        &authorID,
        payload,
    )
    
    err := p.eventPublisher.PublishEvent(ctx, event)
    if err != nil {
        log.Printf("Failed to publish post published event: %v", err)
    }
    
    return err
}

// PublishCommentCreated phát hành event khi comment được tạo
func (p *Publisher) PublishCommentCreated(ctx context.Context, tenantID, websiteID uint, authorID *uint, comment interface{}) error {
    // Convert comment model to event payload
    commentModel := comment.(struct {
        ID      uint
        PostID  uint
        Name    string
        Email   string
        Content string
        Status  string
    })
    
    payload := types.CommentCreatedPayload{
        CommentID: commentModel.ID,
        PostID:    commentModel.PostID,
        AuthorID:  authorID,
        Name:      commentModel.Name,
        Email:     commentModel.Email,
        Content:   commentModel.Content,
        Status:    commentModel.Status,
    }
    
    event := events.NewEvent(
        types.BlogCommentCreated,
        tenantID,
        websiteID,
        authorID,
        payload,
    )
    
    err := p.eventPublisher.PublishEvent(ctx, event)
    if err != nil {
        log.Printf("Failed to publish comment created event: %v", err)
    }
    
    return err
}
```

### 4. Tích Hợp với Blog Service

Cập nhật file `modules/blog/service/blog_service.go` (hoặc tạo mới nếu chưa có):

```go
package service

import (
    "context"
    
    "your-project/modules/blog/events"
)

// BlogService xử lý logic business cho blog
type BlogService struct {
    // dependencies...
    eventPublisher *events.Publisher
}

// NewBlogService tạo một instance mới của BlogService
func NewBlogService(
    // other dependencies...
    eventPublisher *events.Publisher,
) *BlogService {
    return &BlogService{
        // other dependencies...
        eventPublisher: eventPublisher,
    }
}

// CreatePost tạo bài viết mới và phát hành event
func (s *BlogService) CreatePost(ctx context.Context, tenantID, websiteID, authorID uint, req CreatePostRequest) (*Post, error) {
    // Create post logic
    post := &Post{
        Title:    req.Title,
        Content:  req.Content,
        Slug:     req.Slug,
        Status:   "draft",
        AuthorID: authorID,
    }
    
    // Save to database
    if err := s.postRepo.Create(ctx, post); err != nil {
        return nil, err
    }
    
    // Publish event
    if err := s.eventPublisher.PublishPostCreated(ctx, tenantID, websiteID, authorID, post); err != nil {
        // Log error but don't fail the operation
        log.Printf("Failed to publish post created event: %v", err)
    }
    
    return post, nil
}

// PublishPost cập nhật trạng thái bài viết thành published và phát hành event
func (s *BlogService) PublishPost(ctx context.Context, tenantID, websiteID, authorID, postID uint) (*Post, error) {
    // Get post
    post, err := s.postRepo.FindByID(ctx, postID)
    if err != nil {
        return nil, err
    }
    
    // Update post status
    post.Status = "published"
    post.PublishedAt = time.Now()
    
    // Save to database
    if err := s.postRepo.Update(ctx, post); err != nil {
        return nil, err
    }
    
    // Publish event
    if err := s.eventPublisher.PublishPostPublished(ctx, tenantID, websiteID, authorID, post); err != nil {
        // Log error but don't fail the operation
        log.Printf("Failed to publish post published event: %v", err)
    }
    
    return post, nil
}

// CreateComment tạo comment mới và phát hành event
func (s *BlogService) CreateComment(ctx context.Context, tenantID, websiteID uint, req CreateCommentRequest) (*Comment, error) {
    // Create comment logic
    comment := &Comment{
        PostID:  req.PostID,
        Name:    req.Name,
        Email:   req.Email,
        Content: req.Content,
        Status:  "pending",
    }
    
    // Save to database
    if err := s.commentRepo.Create(ctx, comment); err != nil {
        return nil, err
    }
    
    // Publish event
    var authorID *uint
    if req.UserID != 0 {
        userID := req.UserID
        authorID = &userID
    }
    
    if err := s.eventPublisher.PublishCommentCreated(ctx, tenantID, websiteID, authorID, comment); err != nil {
        // Log error but don't fail the operation
        log.Printf("Failed to publish comment created event: %v", err)
    }
    
    return comment, nil
}
```

### 5. Tạo Factory cho Event Components

Tạo file `internal/pkg/events/factory.go`:

```go
package events

import (
    "log"
    
    "your-project/internal/pkg/events/handlers"
)

// Factory tạo và cấu hình các thành phần event system
type Factory struct {
    config Config
}

// NewFactory tạo một instance mới của Factory
func NewFactory(config Config) *Factory {
    return &Factory{
        config: config,
    }
}

// CreateEventSystem tạo và cấu hình đầy đủ event system
func (f *Factory) CreateEventSystem() (*Publisher, *Subscriber, *EventRouter, error) {
    if !f.config.Enabled {
        log.Println("Event system is disabled")
        return nil, nil, nil, nil
    }
    
    // Create publisher
    publisher, err := NewPublisher(f.config)
    if err != nil {
        return nil, nil, nil, err
    }
    
    // Create subscriber
    subscriber, err := NewSubscriber(f.config)
    if err != nil {
        publisher.Close()
        return nil, nil, nil, err
    }
    
    // Create router
    router, err := NewEventRouter(f.config, publisher)
    if err != nil {
        publisher.Close()
        subscriber.Close()
        return nil, nil, nil, err
    }
    
    // Setup handlers
    if err := handlers.SetupEventHandlers(router, subscriber); err != nil {
        publisher.Close()
        subscriber.Close()
        router.Close()
        return nil, nil, nil, err
    }
    
    return publisher, subscriber, router, nil
}
```

## Điểm Kiểm Tra
- [ ] Helper cho module Auth đã được triển khai
- [ ] Tích hợp với Auth Service đã được triển khai
- [ ] Helper cho module Blog đã được triển khai
- [ ] Tích hợp với Blog Service đã được triển khai
- [ ] Factory cho Event Components đã được triển khai
- [ ] Tất cả các thành phần có thể biên dịch mà không có lỗi

## Lưu Ý
- Đảm bảo xử lý lỗi một cách thích hợp trong tất cả các integration points
- Không để lỗi trong event system ảnh hưởng đến hoạt động chính của ứng dụng
- Xem xét việc thêm tracing để theo dõi luồng events xuyên suốt các modules
- Đảm bảo các module event publishers có thể được mock dễ dàng cho testing
- Cung cấp documentation rõ ràng về cách sử dụng event system trong modules 