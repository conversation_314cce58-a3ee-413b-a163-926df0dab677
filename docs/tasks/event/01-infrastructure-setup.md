# Task 1: <PERSON><PERSON><PERSON> Đặt Cơ Sở Hạ Tầng và Cấu Hình

## M<PERSON>c Tiêu
- Cài đặt và cấu hình Redis cho event system
- Thi<PERSON><PERSON> lập các biến môi trường cần thiết
- <PERSON><PERSON><PERSON> cấu trúc thư mục cho event system

## Các <PERSON> Thực Hiện

### 1. Cài đặt Dependencies

```bash
# Core Watermill
go get github.com/ThreeDotsLabs/watermill

# Redis Streams Pub/Sub
go get github.com/ThreeDotsLabs/watermill-redisstream

# Redis client
go get github.com/redis/go-redis/v9
```

### 2. C<PERSON>u Hình Docker cho Development

Thêm dịch vụ Redis vào file `docker-compose.yml`:

```yaml
# docker-compose.yml
version: '3.8'

services:
  # ... các service khác
  
  redis:
    image: redis:7-alpine
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  # ... các volume khác
  redis_data:
```

### 3. T<PERSON><PERSON>u Trúc <PERSON>

```
internal/pkg/events/
├── config.go                  # Event system configuration
├── publisher.go               # Redis streams publisher wrapper
├── subscriber.go              # Redis streams subscriber wrapper
├── router.go                  # Watermill router setup
├── middleware.go              # Custom middlewares
├── message.go                 # Message creation utilities
├── types/
│   ├── auth_events.go          # Auth domain events
│   ├── blog_events.go          # Blog domain events
│   ├── ecommerce_events.go     # E-commerce domain events
│   └── system_events.go        # System events
└── handlers/
    ├── auth_handlers.go        # Auth event handlers
    ├── blog_handlers.go        # Blog event handlers
    ├── ecommerce_handlers.go   # E-commerce event handlers
    └── notification_handlers.go # Notification handlers

modules/*/events/               # Module-specific event integration
```

### 4. Cấu Hình Environment Variables

Thêm các biến môi trường sau vào file `.env` hoặc `config.yaml`:

```
# Redis Configuration
REDIS_URL=redis://localhost:6379/0
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# Event System Configuration
EVENT_ENABLED=true
EVENT_CONSUMER_GROUP=wnapi_consumers
EVENT_CONSUMER_ID=wnapi_consumer_1

# Router Configuration
EVENT_ROUTER_CLOSE_TIMEOUT=30s
EVENT_ROUTER_MIDDLEWARE_TIMEOUT=30s

# Publisher Configuration
EVENT_PUBLISHER_MAX_LEN=10000

# Retry Configuration
EVENT_RETRY_MAX_ATTEMPTS=3
EVENT_RETRY_INITIAL_INTERVAL=1s
EVENT_RETRY_MAX_INTERVAL=30s

# Monitoring
EVENT_LOGGING_ENABLED=true
EVENT_METRICS_ENABLED=true
EVENT_DEBUG=false
```

## Điểm Kiểm Tra
- [ ] Redis đã được cài đặt và hoạt động (có thể kiểm tra bằng cách chạy `redis-cli ping`)
- [ ] Cấu trúc thư mục đã được tạo
- [ ] Các biến môi trường đã được cấu hình
- [ ] Dependencies đã được cài đặt thành công

## Lưu Ý
- Đảm bảo Redis được bảo mật đúng cách trong môi trường production
- Cấu hình connection pool cho Redis để tối ưu hiệu suất
- Sử dụng các cấu hình phù hợp với môi trường (dev, staging, production) 