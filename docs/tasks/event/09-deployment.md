# Task 9: Triển <PERSON>hai Event System lên Production

## <PERSON><PERSON><PERSON>
- Tạo Dockerfile và docker-compose.yml cho việc triển khai
- <PERSON><PERSON><PERSON> cấu hình <PERSON>bernetes cho việc triển khai
- <PERSON><PERSON><PERSON> khai cơ chế graceful shutdown
- C<PERSON><PERSON> hình Redis cho môi trường production

## Các <PERSON>c Hi<PERSON>n

### 1. Tạo Dockerfile

Cập nhật file `Dockerfile` để bao gồm event system:

```dockerfile
# Builder stage
FROM golang:1.23-alpine AS builder

WORKDIR /app

# Copy go mod files
COPY go.mod go.sum ./
RUN go mod download

# Copy source code
COPY . .

# Build the application
RUN CGO_ENABLED=0 GOOS=linux go build -o wnapi ./cmd/server

# Final stage
FROM alpine:latest

RUN apk --no-cache add ca-certificates
WORKDIR /root/

# Copy the binary
COPY --from=builder /app/wnapi .

# Copy config files
COPY --from=builder /app/config ./config

EXPOSE 8080

# Set environment variables
ENV REDIS_HOST=redis
ENV REDIS_PORT=6379
ENV REDIS_DB=0
ENV EVENT_ENABLED=true

CMD ["./wnapi"]
```

### 2. Cập Nhật Docker Compose

Cập nhật file `docker-compose.yml` để bao gồm Redis và event system:

```yaml
version: '3.8'

services:
  app:
    build: .
    restart: unless-stopped
    depends_on:
      - redis
      - mysql
    ports:
      - "8080:8080"
    environment:
      - REDIS_URL=redis://redis:6379/0
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=0
      - EVENT_ENABLED=true
      - EVENT_CONSUMER_GROUP=wnapi_consumers
      - EVENT_CONSUMER_ID=wnapi_consumer_1
      - EVENT_DEBUG=false
      - EVENT_METRICS_ENABLED=true
    networks:
      - wnapi_network

  redis:
    image: redis:7-alpine
    restart: unless-stopped
    command: redis-server --appendonly yes
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - wnapi_network

  redis_exporter:
    image: oliver006/redis_exporter
    restart: unless-stopped
    environment:
      - REDIS_ADDR=redis://redis:6379
    depends_on:
      - redis
    ports:
      - "9121:9121"
    networks:
      - wnapi_network

  mysql:
    image: mysql:8.0
    restart: unless-stopped
    environment:
      - MYSQL_ROOT_PASSWORD=rootpass
      - MYSQL_DATABASE=wnapi
      - MYSQL_USER=wnapi
      - MYSQL_PASSWORD=wnapipass
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    networks:
      - wnapi_network

volumes:
  redis_data:
  mysql_data:

networks:
  wnapi_network:
    driver: bridge
```

### 3. Tạo Cấu Hình Kubernetes

Tạo file `k8s/redis-deployment.yaml`:

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  labels:
    app: redis
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        args: ["--appendonly", "yes"]
        ports:
        - containerPort: 6379
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 500m
            memory: 512Mi
        volumeMounts:
        - name: redis-data
          mountPath: /data
        livenessProbe:
          tcpSocket:
            port: 6379
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          tcpSocket:
            port: 6379
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: redis-data
        persistentVolumeClaim:
          claimName: redis-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: redis
  labels:
    app: redis
spec:
  selector:
    app: redis
  ports:
  - port: 6379
    targetPort: 6379
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: redis-pvc
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
```

Tạo file `k8s/wnapi-deployment.yaml`:

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: wnapi
  labels:
    app: wnapi
spec:
  replicas: 3
  selector:
    matchLabels:
      app: wnapi
  template:
    metadata:
      labels:
        app: wnapi
    spec:
      containers:
      - name: wnapi
        image: wnapi:latest
        ports:
        - containerPort: 8080
        env:
        - name: REDIS_HOST
          value: "redis"
        - name: REDIS_PORT
          value: "6379"
        - name: REDIS_DB
          value: "0"
        - name: EVENT_ENABLED
          value: "true"
        - name: EVENT_CONSUMER_GROUP
          value: "wnapi_consumers"
        - name: EVENT_CONSUMER_ID
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: EVENT_DEBUG
          value: "false"
        - name: EVENT_METRICS_ENABLED
          value: "true"
        resources:
          requests:
            cpu: 200m
            memory: 256Mi
          limits:
            cpu: 1000m
            memory: 1Gi
        livenessProbe:
          httpGet:
            path: /health/events
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health/events
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: wnapi
  labels:
    app: wnapi
spec:
  selector:
    app: wnapi
  ports:
  - port: 80
    targetPort: 8080
  type: ClusterIP
```

### 4. Triển Khai Graceful Shutdown

Cập nhật file `cmd/server/main.go` để thêm graceful shutdown:

```go
package main

import (
    "context"
    "log"
    "net/http"
    "os"
    "os/signal"
    "syscall"
    "time"
    
    "github.com/gin-gonic/gin"
    
    "your-project/internal/pkg/events"
)

type Application struct {
    server         *http.Server
    eventPublisher *events.Publisher
    eventRouter    *events.EventRouter
}

func (app *Application) Start() error {
    // Start HTTP server
    go func() {
        log.Printf("Starting server on %s", app.server.Addr)
        if err := app.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
            log.Fatal("Server failed to start:", err)
        }
    }()
    
    // Start event router
    go func() {
        if app.eventRouter != nil {
            if err := app.eventRouter.Run(context.Background()); err != nil {
                log.Printf("Event router error: %v", err)
            }
        }
    }()
    
    return nil
}

func (app *Application) Shutdown(ctx context.Context) error {
    log.Println("Initiating graceful shutdown...")
    
    // Create a context with timeout for shutdown
    shutdownCtx, cancel := context.WithTimeout(ctx, 30*time.Second)
    defer cancel()
    
    // Shutdown HTTP server
    if err := app.server.Shutdown(shutdownCtx); err != nil {
        log.Printf("HTTP server shutdown error: %v", err)
    }
    
    // Close event system components
    if app.eventRouter != nil {
        if err := app.eventRouter.Close(); err != nil {
            log.Printf("Event router close error: %v", err)
        }
    }
    
    if app.eventPublisher != nil {
        if err := app.eventPublisher.Close(); err != nil {
            log.Printf("Event publisher close error: %v", err)
        }
    }
    
    log.Println("Graceful shutdown completed")
    return nil
}

func main() {
    // Setup gin router
    router := gin.Default()
    
    // Configure event system
    config := events.Config{
        RedisHost:      os.Getenv("REDIS_HOST"),
        RedisPort:      6379, // Parse from env if needed
        RedisDB:        0,    // Parse from env if needed
        RedisPassword:  os.Getenv("REDIS_PASSWORD"),
        Enabled:        true, // Parse from env if needed
        ConsumerGroup:  os.Getenv("EVENT_CONSUMER_GROUP"),
        ConsumerID:     os.Getenv("EVENT_CONSUMER_ID"),
        Debug:          false, // Parse from env if needed
    }
    
    var publisher *events.Publisher
    var eventRouter *events.EventRouter
    
    if config.Enabled {
        // Create event factory
        factory := events.NewFactory(config)
        
        // Initialize event system
        var subscriber *events.Subscriber
        var err error
        publisher, subscriber, eventRouter, err = factory.CreateEventSystem()
        if err != nil {
            log.Fatalf("Failed to initialize event system: %v", err)
        }
        
        // Setup health check endpoints
        healthChecker := events.NewHealthChecker(config, publisher)
        events.RegisterHealthCheckEndpoints(router, healthChecker)
    }
    
    // Configure HTTP server
    server := &http.Server{
        Addr:    ":8080",
        Handler: router,
    }
    
    // Create application
    app := &Application{
        server:         server,
        eventPublisher: publisher,
        eventRouter:    eventRouter,
    }
    
    // Start application
    if err := app.Start(); err != nil {
        log.Fatal("Failed to start application:", err)
    }
    
    // Wait for interrupt signal
    sigChan := make(chan os.Signal, 1)
    signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
    
    // Block until a signal is received
    sig := <-sigChan
    log.Printf("Received signal %s, initiating shutdown", sig)
    
    // Graceful shutdown
    ctx, cancel := context.WithTimeout(context.Background(), 45*time.Second)
    defer cancel()
    
    if err := app.Shutdown(ctx); err != nil {
        log.Printf("Shutdown error: %v", err)
        os.Exit(1)
    }
}
```

### 5. Cấu Hình Redis cho Production

Tạo file `infrastructure/redis/redis.conf`:

```
# Redis configuration file for WNAPI production environment

# Network
bind 0.0.0.0
protected-mode yes
port 6379

# General
daemonize no
supervised no
pidfile /var/run/redis_6379.pid
loglevel notice
logfile ""

# Persistence
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# Memory Management
maxmemory 1gb
maxmemory-policy allkeys-lru
maxmemory-samples 5

# Security (set a strong password in production)
# requirepass STRONG_PASSWORD_HERE

# Clients
maxclients 10000

# Streams configurations (important for event system)
# Allow more memory for stream entries
stream-node-max-bytes 4096
stream-node-max-entries 100

# Performance tuning
tcp-keepalive 300
```

Cập nhật file `docker-compose.prod.yml` để sử dụng cấu hình Redis:

```yaml
version: '3.8'

services:
  app:
    build: .
    restart: unless-stopped
    depends_on:
      - redis
      - mysql
    ports:
      - "8080:8080"
    environment:
      - REDIS_URL=redis://redis:6379/0
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=0
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - EVENT_ENABLED=true
      - EVENT_CONSUMER_GROUP=wnapi_consumers
      - EVENT_CONSUMER_ID=wnapi_consumer_${HOSTNAME}
      - EVENT_DEBUG=false
      - EVENT_METRICS_ENABLED=true
    networks:
      - wnapi_network
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 1G
        reservations:
          cpus: '0.25'
          memory: 256M

  redis:
    image: redis:7-alpine
    restart: unless-stopped
    command: redis-server /etc/redis/redis.conf
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./infrastructure/redis/redis.conf:/etc/redis/redis.conf
    networks:
      - wnapi_network
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 1G
        reservations:
          cpus: '0.1'
          memory: 128M

  redis_exporter:
    image: oliver006/redis_exporter
    restart: unless-stopped
    environment:
      - REDIS_ADDR=redis://redis:6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
    depends_on:
      - redis
    ports:
      - "9121:9121"
    networks:
      - wnapi_network
    deploy:
      resources:
        limits:
          cpus: '0.1'
          memory: 64M

  mysql:
    image: mysql:8.0
    restart: unless-stopped
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - MYSQL_DATABASE=wnapi
      - MYSQL_USER=wnapi
      - MYSQL_PASSWORD=${MYSQL_PASSWORD}
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    networks:
      - wnapi_network
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 512M

volumes:
  redis_data:
  mysql_data:

networks:
  wnapi_network:
    driver: bridge
```

## Điểm Kiểm Tra
- [ ] Dockerfile đã được cập nhật để bao gồm event system
- [ ] docker-compose.yml đã được cập nhật để bao gồm Redis và event system
- [ ] Cấu hình Kubernetes đã được tạo
- [ ] Cơ chế graceful shutdown đã được triển khai
- [ ] Cấu hình Redis cho production đã được tạo
- [ ] Tất cả các cấu hình có thể hoạt động mà không có lỗi

## Lưu Ý
- Đảm bảo thiết lập mật khẩu mạnh cho Redis trong môi trường production
- Cấu hình tài nguyên hợp lý cho các containers dựa trên nhu cầu thực tế
- Cân nhắc việc sử dụng Redis cluster cho high availability trong môi trường production lớn
- Đảm bảo các biến môi trường nhạy cảm được cung cấp an toàn (sử dụng Kubernetes secrets hoặc Docker secrets)
- Xem xét việc sử dụng Redis Sentinel hoặc Redis Cluster cho môi trường production có yêu cầu cao về tính sẵn sàng
- Kiểm tra kỹ cơ chế graceful shutdown để đảm bảo không mất messages trong quá trình tắt/khởi động lại 