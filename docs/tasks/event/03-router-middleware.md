# Task 3: Triển Khai Router và Middleware

## <PERSON><PERSON><PERSON>
- Triển khai Event Router để quản lý luồng events
- Triển khai các middleware cơ bản cho event processing
- <PERSON><PERSON><PERSON> cơ chế xử lý và định tuyến events

## <PERSON><PERSON><PERSON> Thực Hiện

### 1. Triển Khai Event Router

Tạo file `internal/pkg/events/router.go`:

```go
package events

import (
    "context"
    "time"

    "github.com/ThreeDotsLabs/watermill"
    "github.com/ThreeDotsLabs/watermill/message"
    "github.com/ThreeDotsLabs/watermill/message/router/middleware"
    "github.com/ThreeDotsLabs/watermill/message/router/plugin"
)

type EventRouter struct {
    router    *message.Router
    publisher *Publisher
    config    Config
    logger    watermill.LoggerAdapter
}

func NewEventRouter(config Config, publisher *Publisher) (*EventRouter, error) {
    logger := watermill.NewStdLogger(config.Debug, config.Debug)
    
    routerConfig := message.RouterConfig{
        CloseTimeout: config.RouterCloseTimeout,
    }
    
    router, err := message.NewRouter(routerConfig, logger)
    if err != nil {
        return nil, err
    }
    
    // Add plugins
    router.AddPlugin(plugin.SignalsHandler)
    
    // Add middlewares
    router.AddMiddleware(
        // Correlation ID middleware
        middleware.CorrelationID,
        
        // Retry middleware
        middleware.Retry{
            MaxRetries:      config.RetryMaxAttempts,
            InitialInterval: config.RetryInitialInterval,
            MaxInterval:     config.RetryMaxInterval,
            Multiplier:      2.0,
        }.Middleware,
        
        // Timeout middleware
        middleware.Timeout(config.RouterMiddlewareTimeout),
        
        // Recovery middleware
        middleware.Recoverer,
    )
    
    if config.MetricsEnabled {
        // Add metrics middleware if enabled
        // router.AddMiddleware(middleware.NewMetrics().Middleware)
    }
    
    return &EventRouter{
        router:    router,
        publisher: publisher,
        config:    config,
        logger:    logger,
    }, nil
}

func (r *EventRouter) AddHandler(
    handlerName string,
    topic string,
    subscriber message.Subscriber,
    handlerFunc message.HandlerFunc,
) {
    r.router.AddHandler(
        handlerName,
        topic,
        subscriber,
        topic, // For now, publish to same topic
        r.publisher.publisher,
        handlerFunc,
    )
}

func (r *EventRouter) AddNoPublisherHandler(
    handlerName string,
    topic string,
    subscriber message.Subscriber,
    handlerFunc message.NoPublishHandlerFunc,
) {
    r.router.AddNoPublisherHandler(
        handlerName,
        topic,
        subscriber,
        handlerFunc,
    )
}

func (r *EventRouter) Run(ctx context.Context) error {
    return r.router.Run(ctx)
}

func (r *EventRouter) Close() error {
    return r.router.Close()
}
```

### 2. Triển Khai Custom Middleware

Tạo file `internal/pkg/events/middleware.go`:

```go
package events

import (
    "context"
    "fmt"
    "log"
    "sync"
    "time"
    
    "github.com/ThreeDotsLabs/watermill/message"
)

// Logger middleware
func LoggingMiddleware(h message.HandlerFunc) message.HandlerFunc {
    return func(msg *message.Message) ([]*message.Message, error) {
        start := time.Now()
        
        log.Printf("[Event] Processing message %s on topic %s", 
            msg.UUID, msg.Metadata.Get("topic"))
        
        msgs, err := h(msg)
        
        duration := time.Since(start)
        if err != nil {
            log.Printf("[Event] Error processing message %s: %v (took %v)", 
                msg.UUID, err, duration)
        } else {
            log.Printf("[Event] Successfully processed message %s (took %v)", 
                msg.UUID, duration)
        }
        
        return msgs, err
    }
}

// Tenant isolation middleware
func TenantIsolationMiddleware(h message.HandlerFunc) message.HandlerFunc {
    return func(msg *message.Message) ([]*message.Message, error) {
        // Extract tenant ID from message metadata or payload
        tenantID := msg.Metadata.Get("tenant_id")
        if tenantID == "" {
            // If not in metadata, try to extract from payload
            // Implementation depends on your event structure
        }
        
        // Set tenant context
        ctx := context.WithValue(context.Background(), "tenant_id", tenantID)
        
        // Store context with the message for handlers to use
        msg.SetContext(ctx)
        
        return h(msg)
    }
}

// Idempotent handler middleware
func IdempotentMiddleware(h message.HandlerFunc) message.HandlerFunc {
    processedMessages := sync.Map{}
    
    return func(msg *message.Message) ([]*message.Message, error) {
        messageID := msg.UUID
        
        // Check if already processed
        if _, exists := processedMessages.Load(messageID); exists {
            log.Printf("[Event] Message %s already processed, skipping", messageID)
            return nil, nil
        }
        
        msgs, err := h(msg)
        if err == nil {
            // Mark as processed only on success
            processedMessages.Store(messageID, true)
        }
        
        return msgs, err
    }
}

// Dead letter queue middleware
func DeadLetterQueueMiddleware(maxRetries int, deadLetterTopic string, publisher *Publisher) message.HandlerFunc {
    return func(msg *message.Message) ([]*message.Message, error) {
        // Get retry count from metadata
        retryCountStr := msg.Metadata.Get("retry_count")
        var retryCount int
        
        if retryCountStr != "" {
            fmt.Sscanf(retryCountStr, "%d", &retryCount)
        }
        
        if retryCount >= maxRetries {
            // Send to dead letter queue
            log.Printf("[Event] Message %s exceeded max retries, sending to DLQ", msg.UUID)
            
            // Add failure information to metadata
            msg.Metadata.Set("failed_at", time.Now().Format(time.RFC3339))
            msg.Metadata.Set("original_topic", msg.Metadata.Get("topic"))
            
            // Publish to dead letter queue
            ctx := context.Background()
            if msgCtx := msg.Context(); msgCtx != nil {
                ctx = msgCtx
            }
            
            err := publisher.Publish(ctx, deadLetterTopic, msg.Payload)
            if err != nil {
                log.Printf("[Event] Failed to send message to DLQ: %v", err)
            }
            
            // Acknowledge the message to remove from original queue
            return nil, nil
        }
        
        // Increment retry count
        msg.Metadata.Set("retry_count", fmt.Sprintf("%d", retryCount+1))
        
        // Pass to next handler
        return nil, nil
    }
}
```

### 3. Tạo Hàm Tiện Ích Cho Event Handling

Thêm vào file `internal/pkg/events/message.go`:

```go
// Helper function to extract event from message
func ExtractEvent(msg *message.Message) (*BaseEvent, error) {
    var event BaseEvent
    if err := json.Unmarshal(msg.Payload, &event); err != nil {
        return nil, fmt.Errorf("failed to unmarshal event: %w", err)
    }
    
    return &event, nil
}

// Helper function to extract payload from event
func ExtractPayload(event *BaseEvent, payload interface{}) error {
    payloadBytes, err := json.Marshal(event.Payload())
    if err != nil {
        return fmt.Errorf("failed to marshal payload: %w", err)
    }
    
    if err := json.Unmarshal(payloadBytes, payload); err != nil {
        return fmt.Errorf("failed to unmarshal payload: %w", err)
    }
    
    return nil
}

// Helper function to create a handler that filters by event type
func FilterByEventType(eventType string, handler func(*BaseEvent) error) message.NoPublishHandlerFunc {
    return func(msg *message.Message) error {
        event, err := ExtractEvent(msg)
        if err != nil {
            return err
        }
        
        if event.Type() != eventType {
            return nil // Skip non-relevant events
        }
        
        return handler(event)
    }
}
```

## Điểm Kiểm Tra
- [ ] EventRouter đã được triển khai với các phương thức cần thiết
- [ ] Các middleware cơ bản đã được triển khai (logging, retry, timeout, recovery)
- [ ] Custom middleware đã được triển khai (tenant isolation, idempotency, dead letter queue)
- [ ] Các hàm tiện ích cho event handling đã được triển khai
- [ ] Tất cả các thành phần có thể biên dịch mà không có lỗi

## Lưu Ý
- Đảm bảo xử lý lỗi một cách thích hợp trong tất cả các thành phần
- Middleware nên được thiết kế để có thể kết hợp với nhau
- Xem xét thêm các middleware cho tracing, monitoring, và security
- Đảm bảo các middleware không ảnh hưởng đến hiệu suất của hệ thống quá nhiều 