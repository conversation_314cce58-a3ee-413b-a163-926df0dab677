# Task 8: Monitoring và Health Checks

## <PERSON><PERSON><PERSON>
- <PERSON><PERSON><PERSON>hai health checks cho event system
- Triển khai metrics để theo dõi hiệu suất
- Tạo logging và debugging cho event system

## C<PERSON><PERSON>c Thực Hiện

### 1. Triển <PERSON>hai Health Checker

Tạo file `internal/pkg/events/health.go`:

```go
package events

import (
    "context"
    "fmt"
    "time"
    
    "github.com/redis/go-redis/v9"
)

// HealthChecker kiểm tra tình trạng của event system
type HealthChecker struct {
    redisClient *redis.Client
    publisher   *Publisher
    config      Config
}

// NewHealthChecker tạo một instance mới của HealthChecker
func NewHealthChecker(config Config, publisher *Publisher) *HealthChecker {
    return &HealthChecker{
        redisClient: redis.NewClient(config.GetRedisOptions()),
        publisher:   publisher,
        config:      config,
    }
}

// CheckHealth kiểm tra tình trạng của event system
func (h *HealthChecker) CheckHealth(ctx context.Context) error {
    // Kiểm tra kết nối Redis
    if err := h.checkRedis(ctx); err != nil {
        return fmt.Errorf("redis health check failed: %w", err)
    }
    
    // Kiểm tra chức năng publisher
    if err := h.checkPublisher(ctx); err != nil {
        return fmt.Errorf("publisher health check failed: %w", err)
    }
    
    return nil
}

// checkRedis kiểm tra kết nối Redis
func (h *HealthChecker) checkRedis(ctx context.Context) error {
    ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
    defer cancel()
    
    return h.redisClient.Ping(ctx).Err()
}

// checkPublisher kiểm tra chức năng publisher
func (h *HealthChecker) checkPublisher(ctx context.Context) error {
    ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
    defer cancel()
    
    // Tạo và phát hành event health check
    healthEvent := NewEvent(
        "system.health_check",
        0, 0, nil,
        map[string]interface{}{
            "check_time": time.Now(),
            "component":  "event_system",
        },
    )
    
    return h.publisher.PublishEvent(ctx, healthEvent)
}

// GetStats lấy thông tin về event system
func (h *HealthChecker) GetStats(ctx context.Context) (map[string]interface{}, error) {
    info, err := h.redisClient.Info(ctx, "memory", "clients", "stats").Result()
    if err != nil {
        return nil, err
    }
    
    // Lấy thông tin về số lượng streams và consumers
    streamsInfo, err := h.getStreamsInfo(ctx)
    if err != nil {
        return nil, err
    }
    
    return map[string]interface{}{
        "redis_info":     info,
        "streams_info":   streamsInfo,
        "consumer_group": h.config.ConsumerGroup,
        "consumer_id":    h.config.ConsumerID,
        "timestamp":      time.Now(),
    }, nil
}

// getStreamsInfo lấy thông tin về streams
func (h *HealthChecker) getStreamsInfo(ctx context.Context) (map[string]interface{}, error) {
    // Lấy danh sách streams
    keys, err := h.redisClient.Keys(ctx, "*").Result()
    if err != nil {
        return nil, err
    }
    
    var streams []string
    for _, key := range keys {
        streamType, err := h.redisClient.Type(ctx, key).Result()
        if err == nil && streamType == "stream" {
            streams = append(streams, key)
        }
    }
    
    result := map[string]interface{}{
        "stream_count": len(streams),
        "streams":      streams,
    }
    
    // Lấy thông tin về mỗi stream
    if len(streams) > 0 {
        streamDetails := make(map[string]interface{})
        
        for _, stream := range streams {
            // Lấy thông tin về stream
            info, err := h.redisClient.XInfoStream(ctx, stream).Result()
            if err == nil {
                // Lấy thông tin về consumer groups
                groups, err := h.redisClient.XInfoGroups(ctx, stream).Result()
                if err == nil {
                    streamDetails[stream] = map[string]interface{}{
                        "length":         info.Length,
                        "groups":         len(groups),
                        "first_entry_id": info.FirstEntry.ID,
                        "last_entry_id":  info.LastEntry.ID,
                    }
                }
            }
        }
        
        result["stream_details"] = streamDetails
    }
    
    return result, nil
}
```

### 2. Triển Khai Metrics

Tạo file `internal/pkg/events/metrics.go`:

```go
package events

import (
    "context"
    "fmt"
    "time"
    
    "github.com/prometheus/client_golang/prometheus"
    "github.com/prometheus/client_golang/prometheus/promauto"
    "github.com/ThreeDotsLabs/watermill/message"
)

// Metrics quản lý các metrics cho event system
type Metrics struct {
    eventsPublished   *prometheus.CounterVec
    eventsProcessed   *prometheus.CounterVec
    eventsFailed      *prometheus.CounterVec
    processingTime    *prometheus.HistogramVec
    activeConsumers   prometheus.Gauge
}

// NewMetrics tạo một instance mới của Metrics
func NewMetrics() *Metrics {
    return &Metrics{
        eventsPublished: promauto.NewCounterVec(
            prometheus.CounterOpts{
                Name: "watermill_events_published_total",
                Help: "Tổng số events đã phát hành",
            },
            []string{"topic", "tenant_id"},
        ),
        eventsProcessed: promauto.NewCounterVec(
            prometheus.CounterOpts{
                Name: "watermill_events_processed_total",
                Help: "Tổng số events đã xử lý thành công",
            },
            []string{"topic", "handler"},
        ),
        eventsFailed: promauto.NewCounterVec(
            prometheus.CounterOpts{
                Name: "watermill_events_failed_total",
                Help: "Tổng số events xử lý thất bại",
            },
            []string{"topic", "handler", "error_type"},
        ),
        processingTime: promauto.NewHistogramVec(
            prometheus.HistogramOpts{
                Name:    "watermill_event_processing_duration_seconds",
                Help:    "Thời gian xử lý events",
                Buckets: prometheus.DefBuckets,
            },
            []string{"topic", "handler"},
        ),
        activeConsumers: promauto.NewGauge(
            prometheus.GaugeOpts{
                Name: "watermill_active_consumers",
                Help: "Số lượng consumers đang hoạt động",
            },
        ),
    }
}

// RecordEventPublished ghi nhận việc phát hành event
func (m *Metrics) RecordEventPublished(topic string, tenantID uint) {
    m.eventsPublished.WithLabelValues(topic, fmt.Sprintf("%d", tenantID)).Inc()
}

// RecordEventProcessed ghi nhận việc xử lý event thành công
func (m *Metrics) RecordEventProcessed(topic, handler string, duration time.Duration) {
    m.eventsProcessed.WithLabelValues(topic, handler).Inc()
    m.processingTime.WithLabelValues(topic, handler).Observe(duration.Seconds())
}

// RecordEventFailed ghi nhận việc xử lý event thất bại
func (m *Metrics) RecordEventFailed(topic, handler, errorType string) {
    m.eventsFailed.WithLabelValues(topic, handler, errorType).Inc()
}

// SetActiveConsumers cập nhật số lượng consumers đang hoạt động
func (m *Metrics) SetActiveConsumers(count float64) {
    m.activeConsumers.Set(count)
}

// Middleware tạo middleware cho việc thu thập metrics
func (m *Metrics) Middleware(h message.HandlerFunc) message.HandlerFunc {
    return func(msg *message.Message) ([]*message.Message, error) {
        start := time.Now()
        topic := msg.Metadata.Get("topic")
        handlerName := msg.Metadata.Get("handler_name")
        
        result, err := h(msg)
        
        duration := time.Since(start)
        
        if err != nil {
            errorType := "unknown"
            if err != nil {
                // Trích xuất loại lỗi từ error message
                errorType = fmt.Sprintf("%T", err)
            }
            m.RecordEventFailed(topic, handlerName, errorType)
        } else {
            m.RecordEventProcessed(topic, handlerName, duration)
        }
        
        return result, err
    }
}

// PublishingMiddleware tạo middleware cho việc thu thập metrics khi phát hành
func (m *Metrics) PublishingMiddleware() message.PublisherDecorator {
    return func(pub message.Publisher) message.Publisher {
        return message.PublisherFunc(func(topic string, messages ...*message.Message) error {
            err := pub.Publish(topic, messages...)
            
            for _, msg := range messages {
                tenantIDStr := msg.Metadata.Get("tenant_id")
                var tenantID uint
                fmt.Sscanf(tenantIDStr, "%d", &tenantID)
                
                m.RecordEventPublished(topic, tenantID)
            }
            
            return err
        })
    }
}

// UpdateActiveConsumers cập nhật số lượng consumers đang hoạt động từ Redis
func (m *Metrics) UpdateActiveConsumers(ctx context.Context, redisClient *redis.Client, consumerGroup string) error {
    // Lấy danh sách streams
    keys, err := redisClient.Keys(ctx, "*").Result()
    if err != nil {
        return err
    }
    
    var totalConsumers int
    
    for _, key := range keys {
        streamType, err := redisClient.Type(ctx, key).Result()
        if err == nil && streamType == "stream" {
            // Kiểm tra xem stream có consumer group không
            groups, err := redisClient.XInfoGroups(ctx, key).Result()
            if err != nil {
                continue
            }
            
            for _, group := range groups {
                if group.Name == consumerGroup {
                    consumers, err := redisClient.XInfoConsumers(ctx, key, consumerGroup).Result()
                    if err == nil {
                        totalConsumers += len(consumers)
                    }
                }
            }
        }
    }
    
    m.SetActiveConsumers(float64(totalConsumers))
    return nil
}
```

### 3. Triển Khai Logging Middleware

Cập nhật file `internal/pkg/events/middleware.go` để thêm logging middleware:

```go
// Enhanced logging middleware
func EnhancedLoggingMiddleware(logger watermill.LoggerAdapter) message.HandlerMiddleware {
    return func(h message.HandlerFunc) message.HandlerFunc {
        return func(msg *message.Message) ([]*message.Message, error) {
            msgFields := watermill.LogFields{
                "message_uuid":  msg.UUID,
                "topic":         msg.Metadata.Get("topic"),
                "tenant_id":     msg.Metadata.Get("tenant_id"),
                "website_id":    msg.Metadata.Get("website_id"),
                "published_at":  msg.Metadata.Get("published_at"),
            }
            
            logger.Debug("Processing message", msgFields)
            
            start := time.Now()
            
            msgs, err := h(msg)
            
            duration := time.Since(start)
            
            msgFields["processing_time"] = duration.String()
            
            if err != nil {
                logger.Error("Failed to process message", err, msgFields)
            } else {
                logger.Debug("Successfully processed message", msgFields)
            }
            
            return msgs, err
        }
    }
}
```

### 4. Triển Khai HTTP Health Check Endpoints

Tạo file `internal/pkg/events/http_health.go`:

```go
package events

import (
    "encoding/json"
    "net/http"
    "time"
    
    "github.com/gin-gonic/gin"
)

// RegisterHealthCheckEndpoints đăng ký các endpoints kiểm tra sức khỏe
func RegisterHealthCheckEndpoints(router *gin.Engine, healthChecker *HealthChecker) {
    // Endpoint kiểm tra sức khỏe đơn giản
    router.GET("/health/events", func(c *gin.Context) {
        err := healthChecker.CheckHealth(c.Request.Context())
        if err != nil {
            c.JSON(http.StatusServiceUnavailable, gin.H{
                "status":  "error",
                "message": err.Error(),
            })
            return
        }
        
        c.JSON(http.StatusOK, gin.H{
            "status":    "ok",
            "timestamp": time.Now(),
        })
    })
    
    // Endpoint kiểm tra sức khỏe chi tiết
    router.GET("/health/events/details", func(c *gin.Context) {
        stats, err := healthChecker.GetStats(c.Request.Context())
        if err != nil {
            c.JSON(http.StatusServiceUnavailable, gin.H{
                "status":  "error",
                "message": err.Error(),
            })
            return
        }
        
        c.JSON(http.StatusOK, gin.H{
            "status":    "ok",
            "timestamp": time.Now(),
            "details":   stats,
        })
    })
}

// MetricsHandler xử lý việc thu thập và báo cáo metrics
func MetricsHandler() gin.HandlerFunc {
    return func(c *gin.Context) {
        // Sử dụng Prometheus HTTP handler để phục vụ metrics
        promhttp.Handler().ServeHTTP(c.Writer, c.Request)
    }
}
```

### 5. Triển Khai Debug Tools

Tạo file `internal/pkg/events/debug.go`:

```go
package events

import (
    "context"
    "encoding/json"
    "fmt"
    "os"
    "time"
    
    "github.com/ThreeDotsLabs/watermill/message"
    "github.com/redis/go-redis/v9"
)

// DebugOptions cấu hình cho công cụ debug
type DebugOptions struct {
    LogToConsole bool
    LogToFile    bool
    LogFilePath  string
    VerboseLevel int // 0: minimal, 1: normal, 2: detailed
}

// NewDebugMiddleware tạo middleware để debug events
func NewDebugMiddleware(opts DebugOptions) message.HandlerMiddleware {
    var logFile *os.File
    var err error
    
    if opts.LogToFile {
        if opts.LogFilePath == "" {
            opts.LogFilePath = fmt.Sprintf("event_debug_%s.log", time.Now().Format("20060102_150405"))
        }
        
        logFile, err = os.Create(opts.LogFilePath)
        if err != nil {
            fmt.Printf("Failed to create debug log file: %v\n", err)
            opts.LogToFile = false
        }
    }
    
    return func(h message.HandlerFunc) message.HandlerFunc {
        return func(msg *message.Message) ([]*message.Message, error) {
            // Log message before processing
            logEvent(msg, "RECEIVED", opts, logFile)
            
            start := time.Now()
            msgs, err := h(msg)
            duration := time.Since(start)
            
            // Log processing result
            var resultStatus string
            if err != nil {
                resultStatus = fmt.Sprintf("ERROR: %v", err)
            } else {
                resultStatus = "SUCCESS"
            }
            
            logEvent(msg, fmt.Sprintf("%s (took %v)", resultStatus, duration), opts, logFile)
            
            return msgs, err
        }
    }
}

// logEvent ghi log cho event
func logEvent(msg *message.Message, status string, opts DebugOptions, logFile *os.File) {
    var payload interface{}
    if err := json.Unmarshal(msg.Payload, &payload); err != nil {
        payload = string(msg.Payload)
    }
    
    logEntry := map[string]interface{}{
        "status":     status,
        "uuid":       msg.UUID,
        "metadata":   msg.Metadata,
        "timestamp":  time.Now().Format(time.RFC3339Nano),
    }
    
    if opts.VerboseLevel > 0 {
        logEntry["payload"] = payload
    }
    
    logJSON, _ := json.MarshalIndent(logEntry, "", "  ")
    
    if opts.LogToConsole {
        fmt.Printf("[EVENT DEBUG] %s\n", logJSON)
    }
    
    if opts.LogToFile && logFile != nil {
        logFile.WriteString(string(logJSON) + "\n")
    }
}

// InspectStreams hiển thị thông tin về streams hiện có
func InspectStreams(ctx context.Context, redisClient *redis.Client) (map[string]interface{}, error) {
    // Lấy danh sách streams
    keys, err := redisClient.Keys(ctx, "*").Result()
    if err != nil {
        return nil, err
    }
    
    var streams []string
    for _, key := range keys {
        streamType, err := redisClient.Type(ctx, key).Result()
        if err == nil && streamType == "stream" {
            streams = append(streams, key)
        }
    }
    
    result := map[string]interface{}{
        "stream_count": len(streams),
        "streams":      make(map[string]interface{}),
    }
    
    // Lấy thông tin về mỗi stream
    for _, stream := range streams {
        // Lấy thông tin về stream
        info, err := redisClient.XInfoStream(ctx, stream).Result()
        if err != nil {
            continue
        }
        
        // Lấy thông tin về consumer groups
        groups, err := redisClient.XInfoGroups(ctx, stream).Result()
        if err != nil {
            continue
        }
        
        groupsInfo := make([]map[string]interface{}, 0, len(groups))
        for _, group := range groups {
            // Lấy thông tin về consumers trong group
            consumers, err := redisClient.XInfoConsumers(ctx, stream, group.Name).Result()
            if err != nil {
                continue
            }
            
            consumersInfo := make([]map[string]interface{}, 0, len(consumers))
            for _, consumer := range consumers {
                consumersInfo = append(consumersInfo, map[string]interface{}{
                    "name":            consumer.Name,
                    "pending":         consumer.Pending,
                    "idle":            consumer.Idle,
                    "inactive_since":  time.Now().Add(-time.Duration(consumer.Idle) * time.Millisecond),
                })
            }
            
            groupsInfo = append(groupsInfo, map[string]interface{}{
                "name":           group.Name,
                "consumers":      len(consumers),
                "pending":        group.Pending,
                "last_delivered": group.LastDeliveredID,
                "consumers_info": consumersInfo,
            })
        }
        
        // Đọc một số message gần đây
        messages, _ := redisClient.XRevRange(ctx, stream, "+", "-", 5).Result()
        
        messagesInfo := make([]map[string]interface{}, 0, len(messages))
        for _, message := range messages {
            messagesInfo = append(messagesInfo, map[string]interface{}{
                "id":     message.ID,
                "values": message.Values,
            })
        }
        
        result["streams"].(map[string]interface{})[stream] = map[string]interface{}{
            "length":          info.Length,
            "first_entry_id":  info.FirstEntry.ID,
            "last_entry_id":   info.LastEntry.ID,
            "groups":          groupsInfo,
            "recent_messages": messagesInfo,
        }
    }
    
    return result, nil
}
```

## Điểm Kiểm Tra
- [ ] Health Checker đã được triển khai
- [ ] Metrics đã được triển khai
- [ ] Logging Middleware đã được triển khai
- [ ] HTTP Health Check Endpoints đã được triển khai
- [ ] Debug Tools đã được triển khai
- [ ] Tất cả các thành phần có thể biên dịch mà không có lỗi

## Lưu Ý
- Đảm bảo các thông tin nhạy cảm không được hiển thị trong logs hoặc metrics
- Xem xét việc cung cấp khả năng kích hoạt/vô hiệu hóa các tính năng debug trong production
- Cấu hình log rotation cho các tệp debug log để tránh lấp đầy disk
- Khi triển khai Prometheus metrics, đảm bảo endpoint được bảo vệ đúng cách
- Cân nhắc việc sử dụng các công cụ giám sát như Grafana để trực quan hóa metrics
- Thêm documentation về cách sử dụng các công cụ monitoring 