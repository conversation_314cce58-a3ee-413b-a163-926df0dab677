# Troubleshooting Guide

## Common Issues

### Redis Connection Problems
```go
type RedisHealthChecker struct {
    client redis.Client
    logger logger.Logger
}

func (rhc *RedisHealthChecker) DiagnoseConnection() *DiagnosisResult {
    result := &DiagnosisResult{
        Component: "Redis Connection",
        Checks:    make([]Check, 0),
    }
    
    // Test basic connectivity
    ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
    defer cancel()
    
    err := rhc.client.Ping(ctx).Err()
    if err != nil {
        result.Checks = append(result.Checks, Check{
            Name:    "Basic Connectivity",
            Status:  "FAIL",
            Message: fmt.Sprintf("Cannot ping Redis: %v", err),
            Remediation: []string{
                "Check Redis server is running",
                "Verify network connectivity",
                "Check firewall rules",
                "Verify Redis configuration",
            },
        })
        return result
    }
    
    result.Checks = append(result.Checks, Check{
        Name:   "Basic Connectivity",
        Status: "PASS",
        Message: "Redis server is reachable",
    })
    
    // Test memory usage
    info, err := rhc.client.Info(ctx, "memory").Result()
    if err == nil {
        memInfo := parseRedisMemoryInfo(info)
        if memInfo.UsedMemoryPercent > 90 {
            result.Checks = append(result.Checks, Check{
                Name:   "Memory Usage",
                Status: "WARN",
                Message: fmt.Sprintf("High memory usage: %.1f%%", memInfo.UsedMemoryPercent),
                Remediation: []string{
                    "Consider increasing Redis memory limit",
                    "Review data retention policies",
                    "Check for memory leaks",
                },
            })
        }
    }
    
    return result
}

type DiagnosisResult struct {
    Component string  `json:"component"`
    Checks    []Check `json:"checks"`
}

type Check struct {
    Name        string   `json:"name"`
    Status      string   `json:"status"` // PASS, FAIL, WARN
    Message     string   `json:"message"`
    Remediation []string `json:"remediation,omitempty"`
}
```

### Worker Performance Issues
```go
type WorkerDiagnostics struct {
    server queue.Server
    logger logger.Logger
}

func (wd *WorkerDiagnostics) DiagnosePerformance() *DiagnosisResult {
    result := &DiagnosisResult{
        Component: "Worker Performance",
        Checks:    make([]Check, 0),
    }
    
    stats := wd.server.Stats()
    
    // Check processing rate
    processingRate := float64(stats.Processed) / float64(time.Since(stats.StartTime).Minutes())
    if processingRate < 10 { // Less than 10 tasks per minute
        result.Checks = append(result.Checks, Check{
            Name:   "Processing Rate",
            Status: "WARN",
            Message: fmt.Sprintf("Low processing rate: %.2f tasks/min", processingRate),
            Remediation: []string{
                "Increase worker concurrency",
                "Optimize task handlers",
                "Check for blocking operations",
                "Review database connection pooling",
            },
        })
    }
    
    // Check queue backlog
    totalQueued := int64(0)
    for _, size := range stats.Queues {
        totalQueued += size
    }
    
    if totalQueued > 1000 {
        result.Checks = append(result.Checks, Check{
            Name:   "Queue Backlog",
            Status: "WARN",
            Message: fmt.Sprintf("Large queue backlog: %d tasks", totalQueued),
            Remediation: []string{
                "Scale up workers",
                "Review task priorities",
                "Check for failed tasks",
            },
        })
    }
    
    // Check failure rate
    if stats.Processed > 0 {
        failureRate := float64(stats.Failed) / float64(stats.Processed) * 100
        if failureRate > 5 { // More than 5% failure rate
            result.Checks = append(result.Checks, Check{
                Name:   "Failure Rate",
                Status: "FAIL",
                Message: fmt.Sprintf("High failure rate: %.2f%%", failureRate),
                Remediation: []string{
                    "Review error logs",
                    "Check task payloads",
                    "Verify external service dependencies",
                    "Review retry policies",
                },
            })
        }
    }
    
    return result
}
```

## Debugging Tools

```go
type QueueDebugger struct {
    client queue.Client
    server queue.Server
    logger logger.Logger
}

func (qd *QueueDebugger) InspectTask(taskID string) (*TaskInspection, error) {
    taskInfo, err := qd.client.GetTaskInfo(context.Background(), taskID)
    if err != nil {
        return nil, err
    }
    
    inspection := &TaskInspection{
        TaskInfo: taskInfo,
        Timeline: make([]TimelineEvent, 0),
    }
    
    // Get task timeline from logs
    timeline, err := qd.getTaskTimeline(taskID)
    if err != nil {
        qd.logger.Warn("Failed to get task timeline", "error", err)
    } else {
        inspection.Timeline = timeline
    }
    
    // Analyze common issues
    inspection.Issues = qd.analyzeTaskIssues(taskInfo, timeline)
    
    return inspection, nil
}

type TaskInspection struct {
    TaskInfo *queue.TaskInfo `json:"task_info"`
    Timeline []TimelineEvent `json:"timeline"`
    Issues   []Issue         `json:"issues"`
}

type TimelineEvent struct {
    Timestamp time.Time `json:"timestamp"`
    Event     string    `json:"event"`
    Details   string    `json:"details"`
}

type Issue struct {
    Type        string `json:"type"`
    Severity    string `json:"severity"`
    Description string `json:"description"`
    Suggestion  string `json:"suggestion"`
}

func (qd *QueueDebugger) analyzeTaskIssues(taskInfo *queue.TaskInfo, timeline []TimelineEvent) []Issue {
    issues := make([]Issue, 0)
    
    // Check for high retry count
    if taskInfo.Retried > 10 {
        issues = append(issues, Issue{
            Type:        "high_retry_count",
            Severity:    "warning",
            Description: fmt.Sprintf("Task has been retried %d times", taskInfo.Retried),
            Suggestion:  "Review task handler for persistent errors",
        })
    }
    
    // Check for long processing time
    processingDuration := qd.calculateProcessingDuration(timeline)
    if processingDuration > 30*time.Minute {
        issues = append(issues, Issue{
            Type:        "long_processing_time",
            Severity:    "warning",
            Description: fmt.Sprintf("Task processing took %v", processingDuration),
            Suggestion:  "Optimize task handler or increase timeout",
        })
    }
    
    return issues
}
```

## Monitoring Dashboard

```go
type DashboardData struct {
    Overview     OverviewStats     `json:"overview"`
    Queues       []QueueStats      `json:"queues"`
    Workers      WorkerStats       `json:"workers"`
    Tasks        []TaskTypeStats   `json:"tasks"`
    RecentErrors []ErrorInfo       `json:"recent_errors"`
    Trends       TrendData         `json:"trends"`
}

type OverviewStats struct {
    TotalProcessed int64   `json:"total_processed"`
    TotalFailed    int64   `json:"total_failed"`
    CurrentRate    float64 `json:"current_rate"`
    SuccessRate    float64 `json:"success_rate"`
    ActiveWorkers  int     `json:"active_workers"`
    QueuedTasks    int64   `json:"queued_tasks"`
}

type QueueStats struct {
    Name     string `json:"name"`
    Size     int64  `json:"size"`
    Priority int    `json:"priority"`
    Workers  int    `json:"workers"`
}

type TaskTypeStats struct {
    Type       string  `json:"type"`
    Count      int64   `json:"count"`
    SuccessRate float64 `json:"success_rate"`
    AvgDuration float64 `json:"avg_duration"`
}

func (qd *QueueDebugger) GetDashboardData() *DashboardData {
    stats := qd.server.Stats()
    
    dashboard := &DashboardData{
        Overview: OverviewStats{
            TotalProcessed: stats.Processed,
            TotalFailed:    stats.Failed,
            CurrentRate:    qd.calculateCurrentRate(),
            SuccessRate:    qd.calculateSuccessRate(stats),
            ActiveWorkers:  stats.Workers,
            QueuedTasks:    qd.getTotalQueuedTasks(stats),
        },
        Queues:       qd.getQueueStats(stats),
        Workers:      qd.getWorkerStats(),
        Tasks:        qd.getTaskTypeStats(),
        RecentErrors: qd.getRecentErrors(),
        Trends:       qd.getTrendData(),
    }
    
    return dashboard
}
``` 