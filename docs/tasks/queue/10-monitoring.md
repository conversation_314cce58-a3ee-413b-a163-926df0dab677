# Monitoring & Observability

## Metrics Collection

```go
type QueueMetrics struct {
    TasksEnqueued    prometheus.Counter
    TasksProcessed   prometheus.Counter
    TasksFailed      prometheus.Counter
    TaskDuration     prometheus.Histogram
    QueueSize        prometheus.Gauge
    WorkerCount      prometheus.Gauge
    RetryCount       prometheus.Counter
}

func NewQueueMetrics() *QueueMetrics {
    return &QueueMetrics{
        TasksEnqueued: prometheus.NewCounter(prometheus.CounterOpts{
            Name: "queue_tasks_enqueued_total",
            Help: "Total number of tasks enqueued",
        }),
        TasksProcessed: prometheus.NewCounter(prometheus.CounterOpts{
            Name: "queue_tasks_processed_total", 
            Help: "Total number of tasks processed successfully",
        }),
        TasksFailed: prometheus.NewCounter(prometheus.CounterOpts{
            Name: "queue_tasks_failed_total",
            Help: "Total number of tasks that failed",
        }),
        TaskDuration: prometheus.NewHistogram(prometheus.HistogramOpts{
            Name:    "queue_task_duration_seconds",
            Help:    "Task processing duration in seconds",
            Buckets: prometheus.DefBuckets,
        }),
        QueueSize: prometheus.NewGauge(prometheus.GaugeOpts{
            Name: "queue_size",
            Help: "Current queue size",
        }),
        WorkerCount: prometheus.NewGauge(prometheus.GaugeOpts{
            Name: "queue_workers_active",
            Help: "Number of active workers",
        }),
        RetryCount: prometheus.NewCounter(prometheus.CounterOpts{
            Name: "queue_task_retries_total",
            Help: "Total number of task retries",
        }),
    }
}

func (m *QueueMetrics) Register() {
    prometheus.MustRegister(
        m.TasksEnqueued,
        m.TasksProcessed,
        m.TasksFailed,
        m.TaskDuration,
        m.QueueSize,
        m.WorkerCount,
        m.RetryCount,
    )
}
```

## Tracing Integration

```go
type TracingMiddleware struct {
    tracer opentracing.Tracer
}

func (m *TracingMiddleware) WrapHandler(handler queue.TaskHandler) queue.TaskHandler {
    return &TracedHandler{
        handler: handler,
        tracer:  m.tracer,
    }
}

type TracedHandler struct {
    handler queue.TaskHandler
    tracer  opentracing.Tracer
}

func (h *TracedHandler) Handle(ctx context.Context, task *queue.Task) error {
    span, ctx := opentracing.StartSpanFromContextWithTracer(
        ctx, 
        h.tracer,
        fmt.Sprintf("queue.task.%s", task.Type()),
    )
    defer span.Finish()
    
    span.SetTag("task.type", task.Type())
    span.SetTag("task.id", task.ID())
    span.SetTag("task.queue", task.Options().Queue)
    
    err := h.handler.Handle(ctx, task)
    if err != nil {
        span.SetTag("error", true)
        span.LogFields(log.Error(err))
    }
    
    return err
}
```

## Health Checks

```go
type QueueHealthChecker struct {
    client queue.Client
    server queue.Server
    config HealthConfig
}

type HealthConfig struct {
    MaxQueueSize     int           `json:"max_queue_size"`
    MaxProcessingTime time.Duration `json:"max_processing_time"`
    CheckInterval    time.Duration `json:"check_interval"`
}

type HealthStatus struct {
    Status      string            `json:"status"`
    Checks      map[string]Check  `json:"checks"`
    Timestamp   time.Time         `json:"timestamp"`
}

type Check struct {
    Status    string      `json:"status"`
    Message   string      `json:"message"`
    Details   interface{} `json:"details,omitempty"`
    Duration  string      `json:"duration"`
}

func (hc *QueueHealthChecker) CheckHealth(ctx context.Context) *HealthStatus {
    start := time.Now()
    status := &HealthStatus{
        Status:    "healthy",
        Checks:    make(map[string]Check),
        Timestamp: start,
    }
    
    // Check Redis connectivity
    redisCheck := hc.checkRedisConnection(ctx)
    status.Checks["redis"] = redisCheck
    
    // Check queue sizes
    queueCheck := hc.checkQueueSizes(ctx)
    status.Checks["queues"] = queueCheck
    
    // Check worker status
    workerCheck := hc.checkWorkers(ctx)
    status.Checks["workers"] = workerCheck
    
    // Check dead letter queue
    dlqCheck := hc.checkDeadLetterQueue(ctx)
    status.Checks["dead_letter_queue"] = dlqCheck
    
    // Determine overall status
    for _, check := range status.Checks {
        if check.Status != "healthy" {
            status.Status = "unhealthy"
            break
        }
    }
    
    return status
}

func (hc *QueueHealthChecker) checkRedisConnection(ctx context.Context) Check {
    start := time.Now()
    
    // Try to ping Redis
    err := hc.client.Health()
    duration := time.Since(start)
    
    if err != nil {
        return Check{
            Status:   "unhealthy",
            Message:  "Redis connection failed",
            Details:  err.Error(),
            Duration: duration.String(),
        }
    }
    
    return Check{
        Status:   "healthy",
        Message:  "Redis connection OK",
        Duration: duration.String(),
    }
}

func (hc *QueueHealthChecker) checkQueueSizes(ctx context.Context) Check {
    start := time.Now()
    
    stats := hc.server.Stats()
    duration := time.Since(start)
    
    unhealthyQueues := make([]string, 0)
    for queueName, size := range stats.Queues {
        if int(size) > hc.config.MaxQueueSize {
            unhealthyQueues = append(unhealthyQueues, 
                fmt.Sprintf("%s: %d", queueName, size))
        }
    }
    
    if len(unhealthyQueues) > 0 {
        return Check{
            Status:   "unhealthy",
            Message:  "Some queues are too large",
            Details:  unhealthyQueues,
            Duration: duration.String(),
        }
    }
    
    return Check{
        Status:   "healthy",
        Message:  "All queue sizes are within limits",
        Details:  stats.Queues,
        Duration: duration.String(),
    }
}
``` 