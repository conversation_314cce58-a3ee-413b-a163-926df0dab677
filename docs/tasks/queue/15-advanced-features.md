# Advanced Features

## Task Chains & Workflows

```go
type TaskChain struct {
    tasks   []TaskStep
    options ChainOptions
}

type TaskStep struct {
    Task      queue.Task
    OnSuccess []queue.Task  // Tasks to run on success
    OnFailure []queue.Task  // Tasks to run on failure
}

type ChainOptions struct {
    StopOnFailure bool
    MaxRetries    int
    Timeout       time.Duration
}

func NewTaskChain(opts ChainOptions) *TaskChain {
    return &TaskChain{
        tasks:   make([]TaskStep, 0),
        options: opts,
    }
}

func (tc *TaskChain) AddTask(task queue.Task, onSuccess, onFailure []queue.Task) *TaskChain {
    step := TaskStep{
        Task:      task,
        OnSuccess: onSuccess,
        OnFailure: onFailure,
    }
    tc.tasks = append(tc.tasks, step)
    return tc
}

func (tc *TaskChain) Execute(ctx context.Context, client queue.Client) error {
    for i, step := range tc.tasks {
        taskInfo, err := client.Enqueue(ctx, step.Task)
        if err != nil {
            if tc.options.StopOnFailure {
                return fmt.Errorf("failed to enqueue task %d: %w", i, err)
            }
            continue
        }
        
        // Wait for task completion and handle next steps
        if err := tc.handleTaskCompletion(ctx, client, taskInfo, step); err != nil {
            if tc.options.StopOnFailure {
                return err
            }
        }
    }
    
    return nil
}

func (tc *TaskChain) handleTaskCompletion(ctx context.Context, client queue.Client, 
    taskInfo *queue.TaskInfo, step TaskStep) error {
    
    // Poll for task completion (simplified - in practice, use callbacks)
    for {
        select {
        case <-ctx.Done():
            return ctx.Err()
        case <-time.After(1 * time.Second):
            info, err := client.GetTaskInfo(ctx, taskInfo.ID)
            if err != nil {
                return err
            }
            
            switch info.State {
            case queue.TaskStateCompleted:
                return tc.enqueueNextTasks(ctx, client, step.OnSuccess)
            case queue.TaskStateFailed:
                return tc.enqueueNextTasks(ctx, client, step.OnFailure)
            }
        }
    }
}

func (tc *TaskChain) enqueueNextTasks(ctx context.Context, client queue.Client, tasks []queue.Task) error {
    if len(tasks) == 0 {
        return nil
    }
    
    _, err := client.EnqueueBatch(ctx, tasks)
    return err
}
```

## Conditional Task Execution

```go
type ConditionalTask struct {
    queue.Task
    Condition func(ctx context.Context) (bool, error)
}

func (ct *ConditionalTask) ShouldExecute(ctx context.Context) (bool, error) {
    if ct.Condition == nil {
        return true, nil
    }
    return ct.Condition(ctx)
}

type ConditionalTaskHandler struct {
    baseHandler queue.TaskHandler
}

func (h *ConditionalTaskHandler) Handle(ctx context.Context, task *queue.Task) error {
    conditionalTask, ok := task.(*ConditionalTask)
    if !ok {
        return h.baseHandler.Handle(ctx, task)
    }
    
    shouldExecute, err := conditionalTask.ShouldExecute(ctx)
    if err != nil {
        return fmt.Errorf("condition check failed: %w", err)
    }
    
    if !shouldExecute {
        return nil // Skip execution
    }
    
    return h.baseHandler.Handle(ctx, task)
}

// Example usage
func CreateConditionalEmailTask(userID uint, email string) *ConditionalTask {
    baseTask := queue.NewTask(EmailWelcome, EmailWelcomePayload{
        UserID: userID,
        Email:  email,
    })
    
    return &ConditionalTask{
        Task: baseTask,
        Condition: func(ctx context.Context) (bool, error) {
            // Only send if user hasn't received welcome email
            return !hasReceivedWelcomeEmail(ctx, userID)
        },
    }
}
```

## Task Priority Queues

```go
type PriorityQueueManager struct {
    queues map[Priority]*queue.Client
    config PriorityConfig
}

type Priority int

const (
    PriorityCritical Priority = iota
    PriorityHigh
    PriorityNormal
    PriorityLow
)

type PriorityConfig struct {
    CriticalQueue string
    HighQueue     string
    NormalQueue   string
    LowQueue      string
}

func NewPriorityQueueManager(config PriorityConfig) *PriorityQueueManager {
    return &PriorityQueueManager{
        queues: make(map[Priority]*queue.Client),
        config: config,
    }
}

func (pqm *PriorityQueueManager) EnqueueWithPriority(ctx context.Context, 
    task queue.Task, priority Priority) (*queue.TaskInfo, error) {
    
    queueName := pqm.getQueueName(priority)
    opts := []queue.Option{
        queue.WithQueue(queueName),
        queue.WithPriority(int(priority)),
    }
    
    client := pqm.getClientForPriority(priority)
    return client.Enqueue(ctx, task, opts...)
}

func (pqm *PriorityQueueManager) getQueueName(priority Priority) string {
    switch priority {
    case PriorityCritical:
        return pqm.config.CriticalQueue
    case PriorityHigh:
        return pqm.config.HighQueue
    case PriorityNormal:
        return pqm.config.NormalQueue
    case PriorityLow:
        return pqm.config.LowQueue
    default:
        return pqm.config.NormalQueue
    }
}
``` 