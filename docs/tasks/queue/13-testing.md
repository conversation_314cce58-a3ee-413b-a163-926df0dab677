# Testing Strategies

## Unit Testing

```go
func TestEmailTaskHandler(t *testing.T) {
    tests := []struct {
        name        string
        payload     EmailWelcomePayload
        emailService *MockEmailService
        wantErr     bool
    }{
        {
            name: "successful email send",
            payload: EmailWelcomePayload{
                UserID:    1,
                Email:     "<EMAIL>",
                FirstName: "John",
                Language:  "en",
                Template:  "welcome_en",
            },
            emailService: &MockEmailService{
                SendWelcomeEmailFunc: func(ctx context.Context, payload EmailWelcomePayload) error {
                    return nil
                },
            },
            wantErr: false,
        },
        {
            name: "invalid email address",
            payload: EmailWelcomePayload{
                UserID:    1,
                Email:     "invalid-email",
                FirstName: "John",
                Language:  "en",
                Template:  "welcome_en",
            },
            emailService: &MockEmailService{
                SendWelcomeEmailFunc: func(ctx context.Context, payload EmailWelcomePayload) error {
                    return ErrInvalidEmail
                },
            },
            wantErr: true,
        },
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            handler := &WelcomeEmailHandler{
                emailService: tt.emailService,
                logger:       logger.NewNoop(),
            }
            
            task := queue.NewTask(EmailWelcome, tt.payload)
            err := handler.Handle(context.Background(), task)
            
            if (err != nil) != tt.wantErr {
                t.Errorf("Handle() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}

type MockEmailService struct {
    SendWelcomeEmailFunc func(ctx context.Context, payload EmailWelcomePayload) error
}

func (m *MockEmailService) SendWelcomeEmail(ctx context.Context, payload EmailWelcomePayload) error {
    if m.SendWelcomeEmailFunc != nil {
        return m.SendWelcomeEmailFunc(ctx, payload)
    }
    return nil
}
```

## Integration Testing

```go
func TestQueueIntegration(t *testing.T) {
    // Setup test Redis
    redisContainer, err := testcontainers.GenericContainer(context.Background(), testcontainers.GenericContainerRequest{
        ContainerRequest: testcontainers.ContainerRequest{
            Image:        "redis:7-alpine",
            ExposedPorts: []string{"6379/tcp"},
            WaitingFor:   wait.ForListeningPort("6379/tcp"),
        },
        Started: true,
    })
    require.NoError(t, err)
    defer redisContainer.Terminate(context.Background())
    
    // Get Redis connection details
    host, err := redisContainer.Host(context.Background())
    require.NoError(t, err)
    port, err := redisContainer.MappedPort(context.Background(), "6379")
    require.NoError(t, err)
    
    // Setup queue client and server
    config := queue.Config{
        Enabled:  true,
        Provider: "asynq",
        Redis: queue.RedisConfig{
            Host: host,
            Port: port.Int(),
            DB:   0,
        },
    }
    
    client, err := queue.NewClient(config)
    require.NoError(t, err)
    defer client.Close()
    
    server, err := queue.NewServer(config)
    require.NoError(t, err)
    
    // Register test handler
    handler := &TestTaskHandler{
        processedTasks: make([]string, 0),
    }
    server.RegisterHandler("test:task", handler)
    
    // Start server
    ctx, cancel := context.WithCancel(context.Background())
    defer cancel()
    
    go func() {
        server.Start(ctx)
    }()
    
    // Wait for server to be ready
    time.Sleep(100 * time.Millisecond)
    
    // Test task enqueuing and processing
    task := queue.NewTask("test:task", map[string]string{"message": "hello"})
    info, err := client.Enqueue(context.Background(), task)
    require.NoError(t, err)
    require.NotEmpty(t, info.ID)
    
    // Wait for task to be processed
    time.Sleep(500 * time.Millisecond)
    
    // Verify task was processed
    require.Len(t, handler.processedTasks, 1)
    require.Equal(t, "hello", handler.processedTasks[0])
}

type TestTaskHandler struct {
    processedTasks []string
    mutex         sync.Mutex
}

func (h *TestTaskHandler) Handle(ctx context.Context, task *queue.Task) error {
    h.mutex.Lock()
    defer h.mutex.Unlock()
    
    var payload map[string]string
    json.Unmarshal(task.Payload(), &payload)
    
    h.processedTasks = append(h.processedTasks, payload["message"])
    return nil
}
```

## Load Testing

```go
func BenchmarkQueueThroughput(b *testing.B) {
    // Setup
    client, server := setupTestQueue(b)
    defer client.Close()
    defer server.Stop(context.Background())
    
    // Register simple handler
    server.RegisterHandler("bench:task", &BenchmarkHandler{})
    
    ctx, cancel := context.WithCancel(context.Background())
    defer cancel()
    go server.Start(ctx)
    
    time.Sleep(100 * time.Millisecond) // Wait for server
    
    b.ResetTimer()
    b.RunParallel(func(pb *testing.PB) {
        for pb.Next() {
            task := queue.NewTask("bench:task", map[string]int{"value": rand.Int()})
            _, err := client.Enqueue(context.Background(), task)
            if err != nil {
                b.Error(err)
            }
        }
    })
}

type BenchmarkHandler struct{}

func (h *BenchmarkHandler) Handle(ctx context.Context, task *queue.Task) error {
    // Simulate some work
    time.Sleep(1 * time.Millisecond)
    return nil
}
``` 