# Performance Optimization

## Connection Pooling

```go
type PooledRedisClient struct {
    pool *redis.Pool
}

func NewPooledRedisClient(config RedisConfig) *PooledRedisClient {
    pool := &redis.Pool{
        MaxIdle:     config.MaxIdle,
        MaxActive:   config.MaxActive,
        IdleTimeout: config.IdleTimeout,
        Wait:        true,
        Dial: func() (redis.Conn, error) {
            c, err := redis.Dial("tcp", fmt.Sprintf("%s:%d", config.Host, config.Port))
            if err != nil {
                return nil, err
            }
            
            if config.Password != "" {
                if _, err := c.Do("AUTH", config.Password); err != nil {
                    c.Close()
                    return nil, err
                }
            }
            
            if _, err := c.Do("SELECT", config.DB); err != nil {
                c.Close()
                return nil, err
            }
            
            return c, nil
        },
        TestOnBorrow: func(c redis.Conn, t time.Time) error {
            if time.Since(t) < time.Minute {
                return nil
            }
            _, err := c.Do("PING")
            return err
        },
    }
    
    return &PooledRedisClient{pool: pool}
}
```

## Batch Processing

```go
type BatchProcessor struct {
    client    queue.Client
    batchSize int
    flushTime time.Duration
    buffer    []queue.Task
    mutex     sync.Mutex
    ticker    *time.Ticker
    done      chan struct{}
}

func NewBatchProcessor(client queue.Client, batchSize int, flushTime time.Duration) *BatchProcessor {
    bp := &BatchProcessor{
        client:    client,
        batchSize: batchSize,
        flushTime: flushTime,
        buffer:    make([]queue.Task, 0, batchSize),
        ticker:    time.NewTicker(flushTime),
        done:      make(chan struct{}),
    }
    
    go bp.flushLoop()
    return bp
}

func (bp *BatchProcessor) Add(task queue.Task) error {
    bp.mutex.Lock()
    defer bp.mutex.Unlock()
    
    bp.buffer = append(bp.buffer, task)
    
    if len(bp.buffer) >= bp.batchSize {
        return bp.flush()
    }
    
    return nil
}

func (bp *BatchProcessor) flush() error {
    if len(bp.buffer) == 0 {
        return nil
    }
    
    tasks := make([]queue.Task, len(bp.buffer))
    copy(tasks, bp.buffer)
    bp.buffer = bp.buffer[:0] // Reset buffer
    
    _, err := bp.client.EnqueueBatch(context.Background(), tasks)
    return err
}

func (bp *BatchProcessor) flushLoop() {
    for {
        select {
        case <-bp.ticker.C:
            bp.mutex.Lock()
            bp.flush()
            bp.mutex.Unlock()
        case <-bp.done:
            return
        }
    }
}

func (bp *BatchProcessor) Close() error {
    close(bp.done)
    bp.ticker.Stop()
    
    bp.mutex.Lock()
    defer bp.mutex.Unlock()
    
    return bp.flush()
}
```

## Worker Pool Optimization

```go
type DynamicWorkerPool struct {
    server          queue.Server
    minWorkers      int
    maxWorkers      int
    currentWorkers  int
    targetQueueTime time.Duration
    scaleUpThreshold float64
    scaleDownThreshold float64
    lastScaleTime   time.Time
    cooldownPeriod  time.Duration
    mutex          sync.RWMutex
}

func NewDynamicWorkerPool(server queue.Server, config WorkerPoolConfig) *DynamicWorkerPool {
    return &DynamicWorkerPool{
        server:             server,
        minWorkers:         config.MinWorkers,
        maxWorkers:         config.MaxWorkers,
        currentWorkers:     config.MinWorkers,
        targetQueueTime:    config.TargetQueueTime,
        scaleUpThreshold:   config.ScaleUpThreshold,
        scaleDownThreshold: config.ScaleDownThreshold,
        cooldownPeriod:     config.CooldownPeriod,
        lastScaleTime:      time.Now(),
    }
}

func (dwp *DynamicWorkerPool) Monitor(ctx context.Context) {
    ticker := time.NewTicker(30 * time.Second)
    defer ticker.Stop()
    
    for {
        select {
        case <-ctx.Done():
            return
        case <-ticker.C:
            dwp.adjustWorkers()
        }
    }
}

func (dwp *DynamicWorkerPool) adjustWorkers() {
    dwp.mutex.Lock()
    defer dwp.mutex.Unlock()
    
    // Don't scale too frequently
    if time.Since(dwp.lastScaleTime) < dwp.cooldownPeriod {
        return
    }
    
    stats := dwp.server.Stats()
    avgQueueTime := dwp.calculateAverageQueueTime(stats)
    
    if avgQueueTime > dwp.targetQueueTime && 
       dwp.currentWorkers < dwp.maxWorkers {
        // Scale up
        newWorkers := int(float64(dwp.currentWorkers) * (1 + dwp.scaleUpThreshold))
        if newWorkers > dwp.maxWorkers {
            newWorkers = dwp.maxWorkers
        }
        
        dwp.scaleWorkers(newWorkers)
    } else if avgQueueTime < dwp.targetQueueTime/2 && 
              dwp.currentWorkers > dwp.minWorkers {
        // Scale down
        newWorkers := int(float64(dwp.currentWorkers) * (1 - dwp.scaleDownThreshold))
        if newWorkers < dwp.minWorkers {
            newWorkers = dwp.minWorkers
        }
        
        dwp.scaleWorkers(newWorkers)
    }
}

func (dwp *DynamicWorkerPool) scaleWorkers(targetWorkers int) {
    if targetWorkers == dwp.currentWorkers {
        return
    }
    
    // Update server concurrency
    dwp.server.SetConcurrency(targetWorkers)
    dwp.currentWorkers = targetWorkers
    dwp.lastScaleTime = time.Now()
    
    log.Info("Scaled workers", 
        "previous", dwp.currentWorkers,
        "new", targetWorkers)
}
```

## Memory Optimization

```go
type MemoryOptimizedTask struct {
    taskType string
    payload  []byte
    options  TaskOptions
    
    // Use sync.Pool for payload buffers
    bufferPool *sync.Pool
}

var payloadBufferPool = sync.Pool{
    New: func() interface{} {
        return make([]byte, 0, 1024) // 1KB initial capacity
    },
}

func NewMemoryOptimizedTask(taskType string, payload interface{}, opts ...TaskOption) *MemoryOptimizedTask {
    task := &MemoryOptimizedTask{
        taskType:   taskType,
        bufferPool: &payloadBufferPool,
        options:    DefaultTaskOptions(),
    }
    
    // Apply options
    for _, opt := range opts {
        opt(&task.options)
    }
    
    // Serialize payload efficiently
    task.setPayload(payload)
    
    return task
}

func (t *MemoryOptimizedTask) setPayload(payload interface{}) {
    buffer := t.bufferPool.Get().([]byte)
    buffer = buffer[:0] // Reset length
    
    // Use more efficient serialization
    encoder := msgpack.NewEncoder(bytes.NewBuffer(buffer))
    if err := encoder.Encode(payload); err != nil {
        // Fallback to JSON
        data, _ := json.Marshal(payload)
        t.payload = data
    } else {
        t.payload = buffer
    }
}

func (t *MemoryOptimizedTask) Payload() []byte {
    return t.payload
}

func (t *MemoryOptimizedTask) Release() {
    if t.payload != nil && cap(t.payload) <= 64*1024 { // Don't pool very large buffers
        t.bufferPool.Put(t.payload[:0])
    }
    t.payload = nil
}
``` 