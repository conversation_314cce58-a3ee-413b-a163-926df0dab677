# Task 01: Implement Asynq Scheduler

## Objective
Implement the missing Asynq scheduler component to complete the queue system integration.

## Input
- Existing scheduler interface in `internal/pkg/queue/queue.go`
- Existing cron scheduler implementation in `internal/pkg/queue/scheduler.go`
- Asynq factory reference in `internal/pkg/queue/asynq/factory.go`

## Output
- Complete Asynq scheduler implementation at `internal/pkg/queue/asynq/scheduler.go`
- Integration with existing Asynq components

## Requirements
1. Implement `AsynqScheduler` struct that satisfies the `Scheduler` interface
2. Integrate with Asynq's periodic task functionality
3. Support multi-tenant scheduling with tenant ID parameters
4. Follow the same patterns as other Asynq implementations
5. Include proper error handling using `internal/pkg/errors`
6. Support cron expressions and scheduled tasks
7. Implement task persistence if configured
8. Include comprehensive logging

## Implementation Steps

### Step 1: Create Asynq Scheduler Structure
- Define `AsynqScheduler` struct with required fields
- Include Asynq client, periodic task manager, and configuration
- Add mutex for thread safety
- Include logger and tenant support

### Step 2: Implement Core Scheduler Methods
- `ScheduleTask()` - Schedule tasks using Asynq periodic tasks
- `UnscheduleTask()` - Remove scheduled tasks
- `ListScheduledTasks()` - List all scheduled tasks
- `Start()` and `Stop()` - Lifecycle management

### Step 3: Implement Asynq Integration
- Use Asynq's `PeriodicTaskManager` for cron scheduling
- Convert cron expressions to Asynq periodic task configs
- Handle task payload serialization/deserialization
- Implement tenant-aware task scheduling

### Step 4: Add Utility Methods
- `ValidateCronExpression()` - Validate cron expressions
- `GetStats()` - Return scheduler statistics
- `IsRunning()` - Check scheduler status
- `GetNextRuns()` - Get upcoming scheduled tasks

### Step 5: Error Handling and Logging
- Use `internal/pkg/errors` for error handling
- Add comprehensive logging for all operations
- Handle Asynq-specific errors gracefully
- Include retry logic for failed operations

## Acceptance Criteria
- [ ] `AsynqScheduler` implements all `Scheduler` interface methods
- [ ] Integration with Asynq periodic task manager works correctly
- [ ] Multi-tenant scheduling works with tenant ID parameters
- [ ] Cron expressions are properly validated and converted
- [ ] Error handling follows established patterns
- [ ] Comprehensive logging is included
- [ ] Code follows Go best practices and existing patterns
- [ ] Build passes with `make build`

## File Paths
- `internal/pkg/queue/asynq/scheduler.go` (new file)

## Dependencies
- Must complete after existing Asynq components are verified
- Requires Asynq client and worker implementations

## Estimated Time
1-2 hours

## Success Criteria
- Scheduler can be created and started successfully
- Tasks can be scheduled and unscheduled
- Cron expressions work correctly
- Multi-tenant support functions properly
- No compilation errors
- Follows established code patterns
