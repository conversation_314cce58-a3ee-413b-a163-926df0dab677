# Core Interfaces của Queue System

## Task Interface

```go
type Task interface {
    // Type trả về unique task type identifier
    Type() string
    
    // Payload trả về task payload data
    Payload() []byte
    
    // Options trả về task processing options
    Options() TaskOptions
    
    // Validate kiểm tra tính hợp lệ của task
    Validate() error
}

type TaskOptions struct {
    // Queue name để xử lý task
    Queue string
    
    // Priority của task (higher = more priority)
    Priority int
    
    // MaxRetry số lần retry tối đa
    MaxRetry int
    
    // Timeout cho task processing
    Timeout time.Duration
    
    // ProcessAt thời gian xử lý task (delayed task)
    ProcessAt time.Time
    
    // ProcessIn delay duration trước khi xử lý
    ProcessIn time.Duration
    
    // Unique key để đảm bảo task uniqueness
    Unique string
    
    // UniqueTTL thời gian tồn tại của unique constraint
    UniqueTTL time.Duration
    
    // Metadata bổ sung cho task
    Metadata map[string]string
}
```

## Task Handler Interface

```go
type TaskHandler interface {
    // Handle xử lý task
    Handle(ctx context.Context, task *Task) error
    
    // CanHandle kiểm tra có thể xử lý task type này không
    CanHandle(taskType string) bool
    
    // Name trả về tên của handler
    Name() string
    
    // RetryPolicy trả về retry policy tùy chỉnh
    RetryPolicy() RetryPolicy
}

type RetryPolicy struct {
    // MaxRetry số lần retry tối đa
    MaxRetry int
    
    // RetryDelayFunc function để tính delay giữa các retry
    RetryDelayFunc func(n int, err error, task *Task) time.Duration
    
    // IsRetriable function để quyết định có retry hay không
    IsRetriable func(err error) bool
}
```

## Queue Client Interface

```go
type Client interface {
    // Enqueue thêm task vào queue
    Enqueue(ctx context.Context, task Task, opts ...Option) (*TaskInfo, error)
    
    // EnqueueBatch thêm nhiều tasks cùng lúc
    EnqueueBatch(ctx context.Context, tasks []Task, opts ...Option) ([]*TaskInfo, error)
    
    // Schedule lên lịch task để xử lý sau
    Schedule(ctx context.Context, task Task, when time.Time, opts ...Option) (*TaskInfo, error)
    
    // Cancel hủy một task đang pending
    Cancel(ctx context.Context, taskID string) error
    
    // GetTaskInfo lấy thông tin về task
    GetTaskInfo(ctx context.Context, taskID string) (*TaskInfo, error)
    
    // Close đóng client connection
    Close() error
}

type TaskInfo struct {
    ID        string            `json:"id"`
    Type      string            `json:"type"`
    Payload   []byte            `json:"payload"`
    Queue     string            `json:"queue"`
    MaxRetry  int               `json:"max_retry"`
    Retried   int               `json:"retried"`
    State     TaskState         `json:"state"`
    NextProcessAt time.Time     `json:"next_process_at"`
    Metadata  map[string]string `json:"metadata"`
}

type TaskState string

const (
    TaskStatePending   TaskState = "pending"
    TaskStateActive    TaskState = "active"
    TaskStateScheduled TaskState = "scheduled"
    TaskStateRetry     TaskState = "retry"
    TaskStateArchived  TaskState = "archived"
    TaskStateCompleted TaskState = "completed"
    TaskStateFailed    TaskState = "failed"
)
```

## Queue Server Interface

```go
type Server interface {
    // Start khởi động queue server
    Start(ctx context.Context) error
    
    // Stop dừng queue server gracefully
    Stop(ctx context.Context) error
    
    // RegisterHandler đăng ký task handler
    RegisterHandler(taskType string, handler TaskHandler) error
    
    // RegisterMiddleware đăng ký middleware
    RegisterMiddleware(middleware Middleware) error
    
    // Health kiểm tra trạng thái server
    Health() error
    
    // Stats trả về thống kê server
    Stats() ServerStats
}

type ServerStats struct {
    Processed   int64            `json:"processed"`
    Failed      int64            `json:"failed"`
    Queues      map[string]int64 `json:"queues"`
    InProgress  int64            `json:"in_progress"`
    Scheduled   int64            `json:"scheduled"`
    Retry       int64            `json:"retry"`
    Archived    int64            `json:"archived"`
    Workers     int              `json:"workers"`
    Timestamp   time.Time        `json:"timestamp"`
}
``` 