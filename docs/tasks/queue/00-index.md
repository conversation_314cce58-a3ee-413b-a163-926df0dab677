# Hệ Thống Queue với Asynq - <PERSON><PERSON>ài <PERSON> là danh mục các tài liệu mô tả hệ thống Queue được xây dựng với thư viện Asynq trong Go.

## Nội Dung

1. [Tổ<PERSON>uan](./01-overview.md) - <PERSON><PERSON><PERSON><PERSON> thiệu về hệ thống Queue với Asynq
2. [Cấu Trúc Files](./02-file-structure.md) - <PERSON><PERSON> tả cấu trúc thư mục và file
3. [Environment Config](./03-environment-config.md) - Hướng dẫn cấu hình môi trường và biến môi trường
4. [Core Interfaces](./04-core-interfaces.md) - Các interface chính của hệ thống
5. [Configuration Interface](./05-configuration-interface.md) - <PERSON><PERSON><PERSON> tr<PERSON><PERSON> cấ<PERSON> hình
6. [Task Types](./06-task-types.md) - <PERSON><PERSON><PERSON> lo<PERSON>i task và quy ước đặt tên
7. [Module Integration](./07-module-integration.md) - Mẫu tích hợp module vớ<PERSON> hệ thống queue
8. [Error Handling](./08-error-handling.md) - Xử lý lỗi và đảm bảo độ tin cậy
9. [Multi-tenant](./09-multi-tenant.md) - Hỗ trợ multi-tenant và multi-website
10. [Monitoring](./10-monitoring.md) - Monitoring và observability
11. [Deployment](./11-deployment.md) - Cấu hình triển khai
12. [Security](./12-security.md) - Các tính năng bảo mật
13. [Testing](./13-testing.md) - Chiến lược kiểm thử
14. [Performance](./14-performance.md) - Tối ưu hiệu suất
15. [Advanced Features](./15-advanced-features.md) - Các tính năng nâng cao
16. [Troubleshooting](./16-troubleshooting.md) - Hướng dẫn xử lý sự cố
17. [Best Practices](./17-best-practices.md) - Các phương pháp tốt nhất

## Hướng dẫn sử dụng

Các tài liệu được tổ chức theo thứ tự từ cơ bản đến nâng cao. Người đọc mới nên bắt đầu từ [Tổng Quan](./01-overview.md) và tiếp tục theo thứ tự. Người đọc có kinh nghiệm có thể truy cập trực tiếp phần cần tìm hiểu.

## Tài liệu tham khảo

- [Asynq GitHub](https://github.com/hibiken/asynq)
- [Redis Documentation](https://redis.io/documentation)
- [Go Concurrency Patterns](https://go.dev/blog/concurrency-patterns) 