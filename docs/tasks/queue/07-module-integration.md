# Module Integration Patterns

## Task Creation Pattern

```go
// Task factory pattern trong modules
type EmailTaskFactory struct {
    client queue.Client
    config EmailConfig
}

func (f *EmailTaskFactory) CreateWelcomeEmailTask(userID uint, email, firstName string) queue.Task {
    payload := EmailWelcomePayload{
        UserID:    userID,
        Email:     email,
        FirstName: firstName,
        Language:  "vi",
        Template:  "welcome_vi",
    }
    
    return queue.NewTask(
        EmailWelcome,
        payload,
        queue.WithQueue("email"),
        queue.WithMaxRetry(3),
        queue.WithTimeout(5*time.Minute),
        queue.WithMetadata(map[string]string{
            "user_id": strconv.Itoa(int(userID)),
            "type":    "welcome",
        }),
    )
}

// Usage trong service
func (s *UserService) RegisterUser(ctx context.Context, req RegisterRequest) (*User, error) {
    // Create user
    user, err := s.repository.Create(ctx, req)
    if err != nil {
        return nil, err
    }
    
    // Enqueue welcome email task
    task := s.emailTaskFactory.CreateWelcomeEmailTask(
        user.ID, 
        user.Email, 
        user.FirstName,
    )
    
    if _, err := s.queueClient.Enqueue(ctx, task); err != nil {
        s.logger.Error("Failed to enqueue welcome email", "error", err)
        // Don't fail user creation for email task failure
    }
    
    return user, nil
}
```

## Handler Registration Pattern

```go
// Module queue handler registration
type EmailQueueHandlers struct {
    emailService EmailService
    logger       logger.Logger
}

func (h *EmailQueueHandlers) RegisterHandlers(server queue.Server) error {
    handlers := map[string]queue.TaskHandler{
        EmailWelcome:          h.NewWelcomeEmailHandler(),
        EmailVerification:     h.NewVerificationEmailHandler(),
        EmailPasswordReset:    h.NewPasswordResetHandler(),
        EmailOrderConfirmation: h.NewOrderConfirmationHandler(),
    }
    
    for taskType, handler := range handlers {
        if err := server.RegisterHandler(taskType, handler); err != nil {
            return fmt.Errorf("failed to register handler for %s: %w", taskType, err)
        }
    }
    
    return nil
}

func (h *EmailQueueHandlers) NewWelcomeEmailHandler() queue.TaskHandler {
    return &WelcomeEmailHandler{
        emailService: h.emailService,
        logger:       h.logger,
    }
}

type WelcomeEmailHandler struct {
    emailService EmailService
    logger       logger.Logger
}

func (h *WelcomeEmailHandler) Handle(ctx context.Context, task *queue.Task) error {
    var payload EmailWelcomePayload
    if err := json.Unmarshal(task.Payload(), &payload); err != nil {
        return fmt.Errorf("invalid payload: %w", err)
    }
    
    return h.emailService.SendWelcomeEmail(ctx, payload)
}

func (h *WelcomeEmailHandler) CanHandle(taskType string) bool {
    return taskType == EmailWelcome
}

func (h *WelcomeEmailHandler) Name() string {
    return "WelcomeEmailHandler"
}

func (h *WelcomeEmailHandler) RetryPolicy() queue.RetryPolicy {
    return queue.RetryPolicy{
        MaxRetry: 3,
        RetryDelayFunc: func(n int, err error, task *queue.Task) time.Duration {
            // Exponential backoff: 1s, 2s, 4s
            return time.Duration(math.Pow(2, float64(n))) * time.Second
        },
        IsRetriable: func(err error) bool {
            // Don't retry for certain errors
            if errors.Is(err, ErrInvalidEmail) {
                return false
            }
            return true
        },
    }
}
```

## Event-to-Queue Bridge Pattern

```go
// Bridge events to queue tasks
type EventToQueueBridge struct {
    queueClient  queue.Client
    taskFactories map[string]TaskFactory
    logger       logger.Logger
}

func (b *EventToQueueBridge) HandleUserCreated(ctx context.Context, event Event) error {
    payload := event.Payload().(UserCreatedPayload)
    
    // Create multiple tasks from single event
    tasks := []queue.Task{
        b.taskFactories["email"].CreateWelcomeEmailTask(payload.UserID, payload.Email),
        b.taskFactories["crm"].CreateSyncUserTask(payload.UserID),
        b.taskFactories["analytics"].CreateTrackUserTask(payload.UserID),
    }
    
    // Enqueue batch
    if _, err := b.queueClient.EnqueueBatch(ctx, tasks); err != nil {
        b.logger.Error("Failed to enqueue user created tasks", "error", err)
        return err
    }
    
    return nil
}
``` 