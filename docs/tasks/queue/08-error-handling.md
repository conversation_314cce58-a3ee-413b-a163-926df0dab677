# Error Handling & Resilience

## Retry Strategies

### Exponential Backoff
```go
func ExponentialBackoffRetry(maxRetry int, baseDelay time.Duration) queue.RetryPolicy {
    return queue.RetryPolicy{
        MaxRetry: maxRetry,
        RetryDelayFunc: func(n int, err error, task *queue.Task) time.Duration {
            delay := time.Duration(math.Pow(2, float64(n))) * baseDelay
            // Add jitter to prevent thundering herd
            jitter := time.Duration(rand.Intn(1000)) * time.Millisecond
            return delay + jitter
        },
        IsRetriable: func(err error) bool {
            return !isUnrecoverableError(err)
        },
    }
}

func isUnrecoverableError(err error) bool {
    unrecoverableErrors := []error{
        ErrInvalidPayload,
        ErrInvalidEmail,
        ErrUserNotFound,
        ErrPermissionDenied,
    }
    
    for _, unrecoverable := range unrecoverableErrors {
        if errors.Is(err, unrecoverable) {
            return true
        }
    }
    
    return false
}
```

### Linear Backoff
```go
func LinearBackoffRetry(maxRetry int, delay time.Duration) queue.RetryPolicy {
    return queue.RetryPolicy{
        MaxRetry: maxRetry,
        RetryDelayFunc: func(n int, err error, task *queue.Task) time.Duration {
            return time.Duration(n) * delay
        },
    }
}
```

### Custom Retry Logic
```go
func EmailRetryPolicy() queue.RetryPolicy {
    return queue.RetryPolicy{
        MaxRetry: 5,
        RetryDelayFunc: func(n int, err error, task *queue.Task) time.Duration {
            // Different delays based on error type
            if errors.Is(err, ErrRateLimited) {
                return 15 * time.Minute // Wait longer for rate limits
            }
            if errors.Is(err, ErrTemporaryFailure) {
                return 30 * time.Second // Quick retry for temporary issues
            }
            // Default exponential backoff
            return time.Duration(math.Pow(2, float64(n))) * time.Second
        },
        IsRetriable: func(err error) bool {
            // Don't retry authentication errors
            if errors.Is(err, ErrInvalidCredentials) {
                return false
            }
            return true
        },
    }
}
```

## Dead Letter Queue

```go
type DeadLetterHandler struct {
    logger     logger.Logger
    alerting   AlertingService
    storage    DeadLetterStorage
}

func (h *DeadLetterHandler) HandleDeadTask(ctx context.Context, task *queue.Task, reason error) error {
    deadLetter := &DeadLetter{
        TaskID:     task.ID(),
        TaskType:   task.Type(),
        Payload:    task.Payload(),
        FailReason: reason.Error(),
        FailedAt:   time.Now(),
        RetryCount: task.Retried(),
    }
    
    // Store for later analysis
    if err := h.storage.Store(ctx, deadLetter); err != nil {
        h.logger.Error("Failed to store dead letter", "error", err)
    }
    
    // Send alert for critical tasks
    if isCriticalTask(task.Type()) {
        h.alerting.SendAlert(ctx, fmt.Sprintf("Critical task failed: %s", task.Type()))
    }
    
    return nil
}

type DeadLetter struct {
    TaskID     string    `json:"task_id"`
    TaskType   string    `json:"task_type"`
    Payload    []byte    `json:"payload"`
    FailReason string    `json:"fail_reason"`
    FailedAt   time.Time `json:"failed_at"`
    RetryCount int       `json:"retry_count"`
}
```

## Circuit Breaker Pattern

```go
type CircuitBreakerHandler struct {
    handler        queue.TaskHandler
    circuitBreaker *CircuitBreaker
}

func (h *CircuitBreakerHandler) Handle(ctx context.Context, task *queue.Task) error {
    return h.circuitBreaker.Execute(func() error {
        return h.handler.Handle(ctx, task)
    })
}

type CircuitBreaker struct {
    state           State
    failureCount    int
    successCount    int
    lastFailureTime time.Time
    
    maxFailures     int
    timeout         time.Duration
    resetThreshold  int
}

func (cb *CircuitBreaker) Execute(fn func() error) error {
    if cb.state == StateOpen {
        if time.Since(cb.lastFailureTime) > cb.timeout {
            cb.state = StateHalfOpen
        } else {
            return ErrCircuitBreakerOpen
        }
    }
    
    err := fn()
    if err != nil {
        cb.recordFailure()
        return err
    }
    
    cb.recordSuccess()
    return nil
}
``` 