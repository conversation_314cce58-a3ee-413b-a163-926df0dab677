# Multi-tenant & Multi-website Support

## Tenant-aware Tasks

```go
type TenantAwareClient struct {
    client   queue.Client
    tenantID uint
    logger   logger.Logger
}

func (c *TenantAwareClient) Enqueue(ctx context.Context, task queue.Task, opts ...queue.Option) (*queue.TaskInfo, error) {
    // Inject tenant context
    tenantTask := &TenantTask{
        Task:     task,
        TenantID: c.tenantID,
    }
    
    // Add tenant metadata
    opts = append(opts, queue.WithMetadata(map[string]string{
        "tenant_id": strconv.Itoa(int(c.tenantID)),
    }))
    
    return c.client.Enqueue(ctx, tenantTask, opts...)
}

type TenantTask struct {
    queue.Task
    TenantID uint
}

func (t *TenantTask) Payload() []byte {
    payload := struct {
        TenantID uint        `json:"tenant_id"`
        Data     interface{} `json:"data"`
    }{
        TenantID: t.TenantID,
        Data:     t.Task.Payload(),
    }
    
    bytes, _ := json.Marshal(payload)
    return bytes
}
```

## Queue Isolation by Tenant

```go
func GetTenantQueueName(baseName string, tenantID uint) string {
    return fmt.Sprintf("%s_tenant_%d", baseName, tenantID)
}

type TenantQueueManager struct {
    client queue.Client
    config QueueConfig
}

func (m *TenantQueueManager) EnqueueForTenant(ctx context.Context, tenantID uint, task queue.Task) error {
    queueName := GetTenantQueueName(task.Options().Queue, tenantID)
    
    opts := []queue.Option{
        queue.WithQueue(queueName),
        queue.WithMetadata(map[string]string{
            "tenant_id": strconv.Itoa(int(tenantID)),
        }),
    }
    
    _, err := m.client.Enqueue(ctx, task, opts...)
    return err
}

func (m *TenantQueueManager) CreateTenantWorker(tenantID uint) (*queue.Server, error) {
    config := m.config
    
    // Customize queues for tenant
    tenantQueues := make(map[string]int)
    for queueName, priority := range config.Worker.QueuePriorities {
        tenantQueueName := GetTenantQueueName(queueName, tenantID)
        tenantQueues[tenantQueueName] = priority
    }
    
    config.Worker.QueuePriorities = tenantQueues
    
    return queue.NewServer(config)
}
```

## Resource Limits per Tenant

```go
type TenantResourceLimiter struct {
    limits map[uint]TenantLimits
    usage  map[uint]*TenantUsage
    mutex  sync.RWMutex
}

type TenantLimits struct {
    MaxTasksPerMinute   int
    MaxConcurrentTasks  int
    MaxQueueSize        int
    MaxTaskSize         int64
    AllowedTaskTypes    []string
}

type TenantUsage struct {
    TasksThisMinute    int
    ConcurrentTasks    int32
    CurrentQueueSize   int
    LastResetTime      time.Time
}

func (rl *TenantResourceLimiter) CanEnqueue(tenantID uint, task queue.Task) error {
    rl.mutex.RLock()
    defer rl.mutex.RUnlock()
    
    limits, exists := rl.limits[tenantID]
    if !exists {
        return ErrTenantNotFound
    }
    
    usage := rl.getOrCreateUsage(tenantID)
    
    // Check rate limit
    if usage.TasksThisMinute >= limits.MaxTasksPerMinute {
        return ErrRateLimitExceeded
    }
    
    // Check queue size
    if usage.CurrentQueueSize >= limits.MaxQueueSize {
        return ErrQueueFull
    }
    
    // Check task type allowed
    if !contains(limits.AllowedTaskTypes, task.Type()) {
        return ErrTaskTypeNotAllowed
    }
    
    // Check task size
    if int64(len(task.Payload())) > limits.MaxTaskSize {
        return ErrTaskTooLarge
    }
    
    return nil
}

func (rl *TenantResourceLimiter) RecordTaskEnqueued(tenantID uint) {
    rl.mutex.Lock()
    defer rl.mutex.Unlock()
    
    usage := rl.getOrCreateUsage(tenantID)
    usage.TasksThisMinute++
    usage.CurrentQueueSize++
}
``` 