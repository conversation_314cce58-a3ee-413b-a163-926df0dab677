# Deployment & Production Setup

## Docker Configuration

```dockerfile
# Dockerfile for Queue Worker
FROM golang:1.21-alpine AS builder

WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN CGO_ENABLED=0 GOOS=linux go build -o queue-worker ./cmd/queue-worker

FROM alpine:latest
RUN apk --no-cache add ca-certificates tzdata
WORKDIR /root/

COPY --from=builder /app/queue-worker .
COPY --from=builder /app/configs ./configs

EXPOSE 8080
CMD ["./queue-worker"]
```

```yaml
# docker-compose.yml
version: '3.8'

services:
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    
  queue-worker:
    build: .
    environment:
      - QUEUE_ENABLED=true
      - QUEUE_REDIS_URL=redis://redis:6379/1
      - QUEUE_WORKER_CONCURRENCY=10
      - QUEUE_WEB_UI_ENABLED=true
      - QUEUE_WEB_UI_PORT=8080
    ports:
      - "8080:8080"
    depends_on:
      - redis
    deploy:
      replicas: 3
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  queue-scheduler:
    build: .
    command: ["./queue-worker", "--mode=scheduler"]
    environment:
      - QUEUE_ENABLED=true
      - QUEUE_REDIS_URL=redis://redis:6379/1
      - QUEUE_SCHEDULER_ENABLED=true
    depends_on:
      - redis
    deploy:
      replicas: 1

volumes:
  redis_data:
```

## Kubernetes Deployment

```yaml
# k8s/redis-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        ports:
        - containerPort: 6379
        command: ["redis-server", "--appendonly", "yes"]
        volumeMounts:
        - name: redis-data
          mountPath: /data
      volumes:
      - name: redis-data
        persistentVolumeClaim:
          claimName: redis-pvc

---
apiVersion: v1
kind: Service
metadata:
  name: redis-service
spec:
  selector:
    app: redis
  ports:
  - protocol: TCP
    port: 6379
    targetPort: 6379
```

```yaml
# k8s/queue-worker-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: queue-worker
spec:
  replicas: 3
  selector:
    matchLabels:
      app: queue-worker
  template:
    metadata:
      labels:
        app: queue-worker
    spec:
      containers:
      - name: queue-worker
        image: your-registry/queue-worker:latest
        ports:
        - containerPort: 8080
        env:
        - name: QUEUE_ENABLED
          value: "true"
        - name: QUEUE_REDIS_URL
          value: "redis://redis-service:6379/1"
        - name: QUEUE_WORKER_CONCURRENCY
          value: "10"
        - name: QUEUE_WEB_UI_ENABLED
          value: "true"
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: queue-worker-service
spec:
  selector:
    app: queue-worker
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8080
```

## Horizontal Pod Autoscaler

```yaml
# k8s/queue-worker-hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: queue-worker-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: queue-worker
  minReplicas: 3
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  - type: Pods
    pods:
      metric:
        name: queue_size
      target:
        type: AverageValue
        averageValue: "100"
``` 