# Cấu Tr<PERSON><PERSON> Queue System

```go
type Config struct {
    // Enabled bật/tắt queue system
    Enabled bool `env:"QUEUE_ENABLED" envDefault:"true"`
    
    // Provider loại queue provider (asynq)
    Provider string `env:"QUEUE_PROVIDER" envDefault:"asynq"`
    
    // Redis configuration
    Redis RedisConfig `env:",prefix=QUEUE_REDIS_"`
    
    // Client configuration
    Client ClientConfig `env:",prefix=QUEUE_CLIENT_"`
    
    // Worker configuration
    Worker WorkerConfig `env:",prefix=QUEUE_WORKER_"`
    
    // Task configuration
    Task TaskConfig `env:",prefix=QUEUE_TASK_"`
    
    // Scheduler configuration
    Scheduler SchedulerConfig `env:",prefix=QUEUE_SCHEDULER_"`
    
    // Monitoring configuration
    Monitoring MonitoringConfig `env:",prefix=QUEUE_"`
}

type RedisConfig struct {
    Host     string        `env:"HOST" envDefault:"localhost"`
    Port     int           `env:"PORT" envDefault:"6379"`
    DB       int           `env:"DB" envDefault:"1"`
    Password string        `env:"PASSWORD"`
    PoolSize int           `env:"POOL_SIZE" envDefault:"20"`
    Timeout  time.Duration `env:"TIMEOUT" envDefault:"5s"`
    URL      string        `env:"URL" envDefault:"redis://localhost:6379/1"`
}

type ClientConfig struct {
    RetryMax   int           `env:"RETRY_MAX" envDefault:"3"`
    RetryDelay time.Duration `env:"RETRY_DELAY" envDefault:"1s"`
    Timeout    time.Duration `env:"TIMEOUT" envDefault:"30s"`
}

type WorkerConfig struct {
    Concurrency     int           `env:"CONCURRENCY" envDefault:"10"`
    Queues          string        `env:"QUEUES" envDefault:"critical:6,default:3,low:1"`
    StrictPriority  bool          `env:"STRICT_PRIORITY" envDefault:"false"`
    ShutdownTimeout time.Duration `env:"SHUTDOWN_TIMEOUT" envDefault:"30s"`
}

type TaskConfig struct {
    MaxRetry  int           `env:"MAX_RETRY" envDefault:"25"`
    Timeout   time.Duration `env:"TIMEOUT" envDefault:"30m"`
    Retention time.Duration `env:"RETENTION" envDefault:"24h"`
}

type SchedulerConfig struct {
    Enabled  bool   `env:"ENABLED" envDefault:"true"`
    Location string `env:"LOCATION" envDefault:"Asia/Ho_Chi_Minh"`
}

type MonitoringConfig struct {
    Enabled    bool   `env:"MONITORING_ENABLED" envDefault:"true"`
    WebUI      bool   `env:"WEB_UI_ENABLED" envDefault:"true"`
    WebUIPort  int    `env:"WEB_UI_PORT" envDefault:"8080"`
    Metrics    bool   `env:"METRICS_ENABLED" envDefault:"true"`
    Logging    bool   `env:"LOGGING_ENABLED" envDefault:"true"`
    LogLevel   string `env:"LOG_LEVEL" envDefault:"info"`
}
``` 