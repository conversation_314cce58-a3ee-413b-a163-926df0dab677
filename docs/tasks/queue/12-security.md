# Security Considerations

## Task Payload Encryption

```go
type EncryptedTask struct {
    queue.Task
    encryptor Encryptor
}

func (t *EncryptedTask) Payload() []byte {
    plaintext := t.Task.Payload()
    encrypted, err := t.encryptor.Encrypt(plaintext)
    if err != nil {
        // Log error but don't fail - fallback to unencrypted
        log.Error("Failed to encrypt task payload", "error", err)
        return plaintext
    }
    return encrypted
}

type Encryptor interface {
    Encrypt(data []byte) ([]byte, error)
    Decrypt(data []byte) ([]byte, error)
}

type AESEncryptor struct {
    key []byte
}

func NewAESEncryptor(key string) *AESEncryptor {
    return &AESEncryptor{
        key: []byte(key),
    }
}

func (e *AESEncryptor) Encrypt(data []byte) ([]byte, error) {
    block, err := aes.NewCipher(e.key)
    if err != nil {
        return nil, err
    }
    
    gcm, err := cipher.NewGCM(block)
    if err != nil {
        return nil, err
    }
    
    nonce := make([]byte, gcm.NonceSize())
    if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
        return nil, err
    }
    
    ciphertext := gcm.Seal(nonce, nonce, data, nil)
    return ciphertext, nil
}

func (e *AESEncryptor) Decrypt(data []byte) ([]byte, error) {
    block, err := aes.NewCipher(e.key)
    if err != nil {
        return nil, err
    }
    
    gcm, err := cipher.NewGCM(block)
    if err != nil {
        return nil, err
    }
    
    nonceSize := gcm.NonceSize()
    if len(data) < nonceSize {
        return nil, errors.New("ciphertext too short")
    }
    
    nonce, ciphertext := data[:nonceSize], data[nonceSize:]
    plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
    if err != nil {
        return nil, err
    }
    
    return plaintext, nil
}
```

## Access Control

```go
type TaskAccessController struct {
    permissions map[string][]Permission
}

type Permission struct {
    Action   string // "enqueue", "view", "cancel", "retry"
    Resource string // task type or queue name
    TenantID uint
}

func (ac *TaskAccessController) CanEnqueue(userID string, taskType string, tenantID uint) bool {
    permissions := ac.permissions[userID]
    
    for _, perm := range permissions {
        if perm.Action == "enqueue" && 
           (perm.Resource == "*" || perm.Resource == taskType) &&
           perm.TenantID == tenantID {
            return true
        }
    }
    
    return false
}

func (ac *TaskAccessController) CanViewQueue(userID string, queueName string, tenantID uint) bool {
    permissions := ac.permissions[userID]
    
    for _, perm := range permissions {
        if perm.Action == "view" && 
           (perm.Resource == "*" || perm.Resource == queueName) &&
           perm.TenantID == tenantID {
            return true
        }
    }
    
    return false
}
```

## Rate Limiting

```go
type RateLimiter struct {
    limiters map[string]*rate.Limiter
    mutex    sync.RWMutex
}

func NewRateLimiter() *RateLimiter {
    return &RateLimiter{
        limiters: make(map[string]*rate.Limiter),
    }
}

func (rl *RateLimiter) Allow(key string, limit rate.Limit, burst int) bool {
    rl.mutex.Lock()
    defer rl.mutex.Unlock()
    
    limiter, exists := rl.limiters[key]
    if !exists {
        limiter = rate.NewLimiter(limit, burst)
        rl.limiters[key] = limiter
    }
    
    return limiter.Allow()
}

func (rl *RateLimiter) Wait(ctx context.Context, key string, limit rate.Limit, burst int) error {
    rl.mutex.Lock()
    limiter, exists := rl.limiters[key]
    if !exists {
        limiter = rate.NewLimiter(limit, burst)
        rl.limiters[key] = limiter
    }
    rl.mutex.Unlock()
    
    return limiter.Wait(ctx)
}

// Rate limiting middleware
type RateLimitingClient struct {
    client      queue.Client
    rateLimiter *RateLimiter
    config      RateLimitConfig
}

type RateLimitConfig struct {
    TasksPerSecond int
    BurstSize      int
    PerTenant      bool
}

func (c *RateLimitingClient) Enqueue(ctx context.Context, task queue.Task, opts ...queue.Option) (*queue.TaskInfo, error) {
    key := "global"
    if c.config.PerTenant {
        if tenantID := task.GetTenantID(); tenantID > 0 {
            key = fmt.Sprintf("tenant_%d", tenantID)
        }
    }
    
    if !c.rateLimiter.Allow(key, rate.Limit(c.config.TasksPerSecond), c.config.BurstSize) {
        return nil, ErrRateLimitExceeded
    }
    
    return c.client.Enqueue(ctx, task, opts...)
}
``` 