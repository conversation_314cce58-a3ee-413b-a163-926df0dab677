# Task Types & Naming Convention

## Task Naming Standards
Tasks được đặt tên theo pattern: `{domain}:{entity}:{action}`

## Core Domain Tasks

### Email Tasks
```go
const (
    EmailWelcome          = "email:welcome:send"
    EmailVerification     = "email:verification:send" 
    EmailPasswordReset    = "email:password:reset"
    EmailOrderConfirmation = "email:order:confirmation"
    EmailInvoice          = "email:invoice:send"
    EmailNewsletter       = "email:newsletter:send"
    EmailBulkCampaign     = "email:campaign:send"
)
```

### Media Processing Tasks
```go
const (
    MediaImageResize      = "media:image:resize"
    MediaImageOptimize    = "media:image:optimize"
    MediaVideoTranscode   = "media:video:transcode"
    MediaThumbnailGenerate = "media:thumbnail:generate"
    MediaFileCompress     = "media:file:compress"
    MediaMetadataExtract  = "media:metadata:extract"
)
```

### Notification Tasks
```go
const (
    NotificationPushSend  = "notification:push:send"
    NotificationSMSSend   = "notification:sms:send"
    NotificationWebhook   = "notification:webhook:send"
    NotificationSlack     = "notification:slack:send"
    NotificationDiscord   = "notification:discord:send"
)
```

### System Tasks
```go
const (
    SystemBackupDatabase  = "system:backup:database"
    SystemCleanupFiles    = "system:cleanup:files"
    SystemGenerateReport  = "system:report:generate"
    SystemHealthCheck     = "system:health:check"
    SystemCacheWarmup     = "system:cache:warmup"
    SystemIndexRebuild    = "system:index:rebuild"
)
```

### E-commerce Tasks
```go
const (
    OrderProcessPayment   = "order:payment:process"
    OrderUpdateInventory  = "order:inventory:update"
    OrderGenerateInvoice  = "order:invoice:generate"
    OrderSyncCRM          = "order:crm:sync"
    OrderShippingLabel    = "order:shipping:label"
)
```

## Task Payload Standards

### Standard Task Structure
```go
type BaseTask struct {
    TaskID     string                 `json:"task_id"`
    TaskType   string                 `json:"task_type"`
    TenantID   uint                   `json:"tenant_id,omitempty"`
    WebsiteID  uint                   `json:"website_id,omitempty"`
    UserID     uint                   `json:"user_id,omitempty"`
    Metadata   map[string]interface{} `json:"metadata,omitempty"`
    Payload    interface{}            `json:"payload"`
    CreatedAt  time.Time              `json:"created_at"`
}
```

### Example Task Payloads

```go
// Email Welcome Task
type EmailWelcomePayload struct {
    UserID    uint   `json:"user_id"`
    Email     string `json:"email"`
    FirstName string `json:"first_name"`
    Language  string `json:"language"`
    Template  string `json:"template"`
}

// Image Resize Task
type ImageResizePayload struct {
    SourceURL    string `json:"source_url"`
    TargetURL    string `json:"target_url"`
    Width        int    `json:"width"`
    Height       int    `json:"height"`
    Quality      int    `json:"quality"`
    Format       string `json:"format"`
    PreserveAspect bool `json:"preserve_aspect"`
}

// Order Payment Task
type OrderPaymentPayload struct {
    OrderID       uint            `json:"order_id"`
    CustomerID    uint            `json:"customer_id"`
    Amount        decimal.Decimal `json:"amount"`
    Currency      string          `json:"currency"`
    PaymentMethod string          `json:"payment_method"`
    Gateway       string          `json:"gateway"`
}
``` 