# Cấu Trúc Files của Queue System

```
internal/pkg/queue/
├── queue.go                    # Core queue interfaces và types
├── client.go                   # Queue client implementation
├── worker.go                   # Worker server implementation
├── task.go                     # Task definitions và utilities
├── config.go                   # Configuration structures
├── middleware.go               # Queue middleware (logging, tracing, metrics)
├── registry.go                 # Task type registry
├── scheduler.go                # Task scheduling utilities
├── inspector.go                # Queue inspection utilities
├── asynq/
│   ├── client.go               # Asynq client wrapper
│   ├── server.go               # Asynq server wrapper
│   ├── config.go               # Asynq-specific configuration
│   └── middleware.go           # Asynq middleware implementations
├── types/
│   ├── email_tasks.go          # Email task definitions
│   ├── media_tasks.go          # Media processing task definitions
│   ├── system_tasks.go         # System maintenance task definitions
│   └── notification_tasks.go   # Notification task definitions
└── testing/
    ├── mock_client.go          # Mock client for testing
    ├── mock_worker.go          # Mock worker for testing
    └── test_tasks.go           # Test task utilities

modules/*/queue/                # Module-specific queue definitions
├── auth/queue/
│   ├── tasks.go                # Auth queue tasks
│   ├── handlers.go             # Auth task handlers
│   └── client.go               # Auth queue client utilities
├── blog/queue/
│   ├── tasks.go                # Blog queue tasks
│   ├── handlers.go             # Blog task handlers
│   └── client.go               # Blog queue client utilities
└── product/queue/
    ├── tasks.go                # Product queue tasks
    ├── handlers.go             # Product task handlers
    └── client.go               # Product queue client utilities
```

Cấu trúc thư mục được thiết kế để:

1. Tách biệt core queue system (trong `internal/pkg/queue`) từ các module cụ thể
2. Phân chia rõ ràng giữa client và worker implementations
3. Hỗ trợ kiểm thử với các mock và test utilities
4. Hỗ trợ nhiều loại task cho các domain khác nhau
5. Cho phép mỗi module định nghĩa task và handler riêng 