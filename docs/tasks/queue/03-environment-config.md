# Cấu Hình Environment Variables cho Queue System

## Core Queue Configuration

```bash
# Queue System Configuration
QUEUE_ENABLED=true
QUEUE_PROVIDER=asynq
QUEUE_REDIS_URL=redis://localhost:6379/1

# Redis Configuration
QUEUE_REDIS_HOST=localhost
QUEUE_REDIS_PORT=6379
QUEUE_REDIS_DB=1
QUEUE_REDIS_PASSWORD=
QUEUE_REDIS_POOL_SIZE=20
QUEUE_REDIS_TIMEOUT=5s

# Client Configuration
QUEUE_CLIENT_RETRY_MAX=3
QUEUE_CLIENT_RETRY_DELAY=1s
QUEUE_CLIENT_TIMEOUT=30s

# Worker Configuration
QUEUE_WORKER_CONCURRENCY=10
QUEUE_WORKER_QUEUES=critical:6,default:3,low:1
QUEUE_WORKER_STRICT_PRIORITY=false
QUEUE_WORKER_SHUTDOWN_TIMEOUT=30s

# Task Configuration
QUEUE_TASK_MAX_RETRY=25
QUEUE_TASK_TIMEOUT=30m
QUEUE_TASK_RETENTION=24h

# Scheduler Configuration
QUEUE_SCHEDULER_ENABLED=true
QUEUE_SCHEDULER_LOCATION=Asia/Ho_Chi_Minh

# Monitoring Configuration
QUEUE_MONITORING_ENABLED=true
QUEUE_WEB_UI_ENABLED=true
QUEUE_WEB_UI_PORT=8080
QUEUE_METRICS_ENABLED=true
QUEUE_LOGGING_ENABLED=true
QUEUE_LOG_LEVEL=info
```

## Task-specific Configuration

```bash
# Email Tasks
EMAIL_QUEUE_ENABLED=true
EMAIL_QUEUE_NAME=email
EMAIL_QUEUE_PRIORITY=high
EMAIL_QUEUE_MAX_RETRY=3
EMAIL_QUEUE_TIMEOUT=5m

# Media Processing Tasks
MEDIA_QUEUE_ENABLED=true
MEDIA_QUEUE_NAME=media
MEDIA_QUEUE_PRIORITY=low
MEDIA_QUEUE_MAX_RETRY=2
MEDIA_QUEUE_TIMEOUT=30m

# Notification Tasks
NOTIFICATION_QUEUE_ENABLED=true
NOTIFICATION_QUEUE_NAME=notification
NOTIFICATION_QUEUE_PRIORITY=high
NOTIFICATION_QUEUE_MAX_RETRY=5
NOTIFICATION_QUEUE_TIMEOUT=10s

# System Tasks
SYSTEM_QUEUE_ENABLED=true
SYSTEM_QUEUE_NAME=system
SYSTEM_QUEUE_PRIORITY=low
SYSTEM_QUEUE_MAX_RETRY=1
SYSTEM_QUEUE_TIMEOUT=1h
```

## Ví dụ File .env Hoàn Chỉnh

```bash
# ===========================================
# Queue System Configuration
# ===========================================

# Core Settings
QUEUE_ENABLED=true
QUEUE_PROVIDER=asynq
QUEUE_REDIS_URL=redis://localhost:6379/1

# Redis Connection
QUEUE_REDIS_HOST=localhost
QUEUE_REDIS_PORT=6379
QUEUE_REDIS_DB=1
QUEUE_REDIS_PASSWORD=your_redis_password_here
QUEUE_REDIS_POOL_SIZE=20
QUEUE_REDIS_TIMEOUT=5s

# Worker Settings
QUEUE_WORKER_CONCURRENCY=10
QUEUE_WORKER_QUEUES=critical:6,default:3,low:1
QUEUE_WORKER_STRICT_PRIORITY=false
QUEUE_WORKER_SHUTDOWN_TIMEOUT=30s

# Task Settings
QUEUE_TASK_MAX_RETRY=25
QUEUE_TASK_TIMEOUT=30m
QUEUE_TASK_RETENTION=24h

# Monitoring
QUEUE_MONITORING_ENABLED=true
QUEUE_WEB_UI_ENABLED=true
QUEUE_WEB_UI_PORT=8080
QUEUE_METRICS_ENABLED=true

# Task Types
EMAIL_QUEUE_ENABLED=true
MEDIA_QUEUE_ENABLED=true
NOTIFICATION_QUEUE_ENABLED=true
SYSTEM_QUEUE_ENABLED=true
``` 