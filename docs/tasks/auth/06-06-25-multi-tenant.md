Tuyệt vời! Đ<PERSON>y là một câu hỏi rất quan trọng và làm thay đổi đáng kể cách chúng ta thiết kế hệ thống. <PERSON><PERSON> hình "một người dùng thuộc nhiều tenant" thường được gọi là mô hình **many-to-many** giữa User và Tenant.

Khi một người dùng có thể thuộc nhiều tenant, kiến trúc của bạn cần được điều chỉnh ở nhiều cấp độ: cơ sở dữ liệu, logic nghiệp vụ, xác thực/phân quyền (JWT, RBAC), và cả cách bạn xử lý các sự kiện.

### 1. Thiết kế Cơ sở dữ liệu (Quan trọng nhất)

Mối quan hệ 1-N (một user thuộc một tenant) trong bảng `users` (`tenant_id`) không còn phù hợp. Bạn cần một bảng trung gian để thể hiện mối quan hệ N-N.

**Bảng mới: `user_tenants` 

Bảng này sẽ liên kết người dùng với các tenant mà họ thuộc về, và cũng là nơi lý tưởng để lưu trữ vai trò của người dùng *trong từng tenant cụ thể*.

| Cột | Kiểu dữ liệu | Mô tả |
| :--- | :--- | :--- |
| `id` | `INT` / `Unsigned` | Khóa chính, tự tăng. |
| `user_id` | `INT` / `Unsigned` | Khóa ngoại, tham chiếu đến `users.user_id`. |
| `tenant_id` | `INT` / `Unsigned` | Khóa ngoại, tham chiếu đến `tenants.tenant_id`. |
| `role_id` | `INT` / `Unsigned` | Khóa ngoại, tham chiếu đến `rbac_roles.role_id`. Đây là vai trò của user trong tenant này. |
| `status` | `VARCHAR(20)` | Trạng thái thành viên (ví dụ: `active`, `pending_invite`, `inactive`). |
| `created_at` | `DATETIME` | |
| `updated_at` | `DATETIME` | |

**Cập nhật các bảng hiện có:**

*   **`users` table:**
    *   **Loại bỏ cột `tenant_id`**. Bảng `users` giờ đây chỉ chứa thông tin cốt lõi, độc lập với tenant (email, password hash, full name, etc.). Một người dùng tồn tại duy nhất trong hệ thống, không bị nhân bản.
*   **`tenants` table:** Giữ nguyên.
*   **`rbac_roles` table:** Giữ nguyên. Vai trò (`Admin`, `Editor`, `Member`) vẫn được định nghĩa chung.
*   **`rbac_permissions` table:** Giữ nguyên.

**Sơ đồ quan hệ mới:**
`users` <--(1-N)-- `user_tenants` --(N-1)--> `tenants`
`user_tenants` --(N-1)--> `rbac_roles`

### 2. Thay đổi về Logic Xác thực và Phân quyền (JWT & RBAC)

Đây là phần phức tạp và cần thay đổi lớn. Người dùng không còn đăng nhập vào "hệ thống" chung chung nữa, họ cần **chọn tenant mà họ muốn làm việc** sau khi đăng nhập.

**Luồng đăng nhập và JWT:**

1.  **Đăng nhập lần đầu (Login Step 1):**
    *   Người dùng cung cấp `email` và `password`.
    *   Hệ thống xác thực thông tin này từ bảng `users`.
    *   Nếu thành công, **thay vì cấp ngay JWT**, hệ thống sẽ truy vấn bảng `user_tenants` để lấy danh sách tất cả các tenant mà người dùng này thuộc về.
    *   API trả về danh sách các tenant này cho người dùng (ví dụ: `[{tenant_id: 1, tenant_name: "Công ty A"}, {tenant_id: 5, tenant_name: "Dự án B"}]`).

2.  **Chọn Tenant và Cấp JWT (Login Step 2):**
    *   Giao diện người dùng hiển thị danh sách tenant.
    *   Người dùng chọn một tenant để làm việc.
    *   Giao diện gửi một request thứ hai đến một endpoint mới, ví dụ `/api/v1/auth/select-tenant`, với `tenant_id` đã chọn.
    *   Server kiểm tra xem người dùng có thực sự thuộc `tenant_id` này không (kiểm tra trong bảng `user_tenants`).
    *   Nếu hợp lệ, server sẽ:
        *   Lấy `role_id` của người dùng *trong tenant đó* từ bảng `user_tenants`.
        *   **Tạo JWT với các thông tin quan trọng trong `claims`:**
            *   `user_id`: ID của người dùng.
            *   **`tenant_id`**: ID của tenant **đã được chọn**.
            *   `role`: Tên vai trò của người dùng **trong tenant đó**.
        *   Trả về cặp Access Token và Refresh Token cho client.

**Hệ quả đối với JWT:**
Mỗi JWT giờ đây đại diện cho một **phiên làm việc (session)** của người dùng *trong một tenant cụ thể*. Nếu người dùng muốn chuyển sang làm việc ở một tenant khác, họ cần thực hiện lại quy trình "chọn tenant", và nhận một JWT mới với `tenant_id` và `role` tương ứng.

**Middleware và RBAC:**

*   **`authMiddleware`**: Không thay đổi nhiều, vẫn xác thực JWT và đưa `userID`, `tenantID`, `claims` vào context.
*   **`permission.MiddlewareFactory`**:
    *   Logic kiểm tra quyền giờ đây sẽ dựa trên `userID` và **`tenantID` lấy từ JWT**.
    *   `checker.UserHasPermission(ctx, tenantID, userID, permission)` sẽ truy vấn như sau: "Liệu người dùng `userID` có vai trò nào trong `tenantID` mà vai trò đó được cấp quyền `permission` không?".
    *   Truy vấn sẽ join các bảng: `user_tenants` -> `rbac_role_permissions` -> `rbac_permissions`.

### 3. Thay đổi về API và Logic nghiệp vụ

*   **Tất cả các API nghiệp vụ** (ví dụ: `/api/v1/products`, `/api/v1/posts`) giờ đây phải hoạt động trong ngữ cảnh của `tenant_id` được lấy từ JWT. Mọi truy vấn DB phải có điều kiện `WHERE tenant_id = ?`.
*   Bạn sẽ cần các API mới để quản lý mối quan hệ này:
    *   `POST /api/v1/tenants/{tenant_id}/users`: Mời/thêm một người dùng (bằng email) vào một tenant với một vai trò cụ thể.
    *   `GET /api/v1/tenants/{tenant_id}/users`: Lấy danh sách người dùng trong một tenant.
    *   `PUT /api/v1/tenants/{tenant_id}/users/{user_id}`: Thay đổi vai trò của một người dùng trong tenant.
    *   `DELETE /api/v1/tenants/{tenant_id}/users/{user_id}`: Xóa người dùng khỏi tenant.
    *   `GET /api/v1/users/me/tenants`: API để lấy danh sách các tenant mà người dùng hiện tại thuộc về (dùng cho bước 1 của luồng đăng nhập).

### 4. Thay đổi về Xử lý Sự kiện

*   **Sự kiện `AuthUserCreated`:** Sự kiện này vẫn như cũ, vì nó chỉ thông báo rằng một thực thể người dùng mới đã tồn tại trong hệ thống. Nó chưa thuộc tenant nào cả.
*   **Sự kiện mới: `TenantMemberAdded`**
    *   Khi một người dùng được thêm vào một tenant (qua API mời), bạn sẽ phát ra sự kiện này.
    *   **Payload:** `{ user_id, tenant_id, role_id, added_by_user_id }`.
    *   Đây là sự kiện quan trọng để các module khác có thể xử lý, ví dụ:
        *   **Notification Service:** Gửi email thông báo "Bạn đã được thêm vào Công ty A".
        *   **Audit Service:** Ghi log hành động mời.
*   **Sự kiện mới: `TenantMemberRoleChanged`**
    *   Khi vai trò của người dùng trong một tenant thay đổi.
    *   **Payload:** `{ user_id, tenant_id, old_role_id, new_role_id, changed_by_user_id }`.
*   **Sự kiện mới: `TenantMemberRemoved`**
    *   Khi người dùng bị xóa khỏi một tenant.
    *   **Payload:** `{ user_id, tenant_id, removed_by_user_id }`.

**Luồng đăng ký mới sẽ trông như thế nào?**

Quy trình đăng ký có thể được thiết kế theo nhiều cách:

*   **Cách 1: Đăng ký và tạo tenant mới cùng lúc (phổ biến nhất cho SaaS):**
    1.  Người dùng điền form đăng ký (email, pass, tên công ty).
    2.  **Service `Register`:**
        a. Tạo `User` mới.
        b. Tạo `Tenant` mới với tên công ty.
        c. Tạo một bản ghi trong `user_tenants` để liên kết `User` và `Tenant` vừa tạo, gán vai trò là "owner" hoặc "admin".
        d. Phát ra cả hai sự kiện: `AuthUserCreated` và `TenantMemberAdded` (hoặc một sự kiện tổng hợp như `UserSignedUpAndCreatedTenant`).
        e. Trả về JWT cho phiên làm việc đầu tiên của người dùng trong tenant mới này.
*   **Cách 2: Chỉ đăng ký tài khoản, chưa thuộc tenant nào:**
    1.  Người dùng chỉ đăng ký `email`, `password`.
    2.  Chỉ có bản ghi `users` được tạo.
    3.  Người dùng này sau đó phải được mời vào một tenant đã có, hoặc tự tạo một tenant mới (nếu hệ thống cho phép).
    4.  Sự kiện duy nhất được phát là `AuthUserCreated`.

**Tóm lại, để hỗ trợ một người dùng thuộc nhiều tenant:**

1.  **Thay đổi CSDL:** Bỏ `tenant_id` khỏi bảng `users`, tạo bảng trung gian `user_tenants` (với `user_id`, `tenant_id`, `role_id`).
2.  **Thay đổi Luồng Đăng Nhập:** Tách thành 2 bước: xác thực credential, sau đó chọn tenant để có JWT tương ứng.
3.  **Cập nhật JWT Claims:** JWT phải chứa `tenant_id` và `role` của phiên làm việc hiện tại.
4.  **Cập nhật Logic Phân Quyền (RBAC):** Mọi kiểm tra quyền đều phải dựa trên `(userID, tenantID)` từ JWT.
5.  **Thêm API mới:** Để quản lý thành viên trong tenant (mời, xóa, đổi vai trò).
6.  **Thêm Sự kiện mới:** `TenantMemberAdded`, `TenantMemberRoleChanged`, `TenantMemberRemoved` để thông báo các thay đổi về quan hệ user-tenant.

Đây là một sự thay đổi cấu trúc lớn nhưng rất cần thiết để hỗ trợ mô hình kinh doanh linh hoạt hơn.