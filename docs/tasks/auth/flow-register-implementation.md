# Registration Flow Implementation

## Overview
This document describes the implemented registration flow that enforces email verification before login.

## Flow Requirements (from flow-register.md)
> "đăng ký thì is_email_verified = true, status = 'active' mới login được"
> 
> Translation: "For registration, users can only login when is_email_verified = true AND status = 'active'"

## Implementation Details

### 1. Registration Process
When a user registers:
1. **User Creation**: User is created with `status = 'active'` and `is_email_verified = false`
2. **Automatic Email Sending**: Verification email is automatically sent during registration
3. **Event Publishing**: User creation event is published for other modules

### 2. Login Validation
When a user attempts to login:
1. **Credential Check**: Username/email and password are validated
2. **Status Check**: User status must be 'active'
3. **Email Verification Check**: User's email must be verified (`is_email_verified = true`)
4. **Token Generation**: Only if both conditions are met, JWT tokens are generated

### 3. Email Verification Process
1. **Token Creation**: UUID token is generated with 24-hour expiration
2. **Database Storage**: Token is stored in `email_verifications` table
3. **Email Sending**: Verification email with link is sent to user
4. **Verification**: When user clicks link, `is_email_verified` is set to `true`

## API Endpoints

### Registration
```http
POST /api/v1/auth/register
Content-Type: application/json

{
  "username": "testuser",
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "full_name": "Test User"
}
```

**Response (Success):**
```json
{
  "success": true,
  "data": {
    "user_id": 1,
    "tenant_id": 1,
    "username": "testuser",
    "email": "<EMAIL>"
  }
}
```

### Login (Before Email Verification)
```http
POST /api/v1/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "SecurePassword123!"
}
```

**Response (Email Not Verified):**
```json
{
  "success": false,
  "error": {
    "code": "EMAIL_NOT_VERIFIED",
    "message": "Email chưa được xác thực"
  }
}
```

### Email Verification
```http
POST /api/v1/auth/verify-email
Content-Type: application/json

{
  "token": "verification_token_from_email"
}
```

**Response (Success):**
```json
{
  "success": true,
  "data": {
    "email": "<EMAIL>",
    "verified": true,
    "message": "Email đã được xác thực thành công"
  }
}
```

### Login (After Email Verification)
```http
POST /api/v1/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "SecurePassword123!"
}
```

**Response (Success):**
```json
{
  "success": true,
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "access_token_expires_in": 900,
    "refresh_token": "uuid-refresh-token",
    "refresh_token_expires_in": 604800,
    "token_type": "Bearer",
    "user_id": 1,
    "email": "<EMAIL>",
    "tenant_id": 1
  }
}
```

## Error Codes

| Error Code | HTTP Status | Description |
|------------|-------------|-------------|
| `EMAIL_NOT_VERIFIED` | 403 | Email chưa được xác thực |
| `ACCOUNT_INACTIVE` | 403 | Tài khoản không hoạt động |
| `INVALID_CREDENTIALS` | 401 | Thông tin đăng nhập không hợp lệ |
| `USER_ALREADY_EXISTS` | 409 | Người dùng đã tồn tại |

## Database Schema

### Users Table
```sql
CREATE TABLE users (
  user_id INT PRIMARY KEY AUTO_INCREMENT,
  tenant_id INT NOT NULL,
  username VARCHAR(255) UNIQUE,
  email VARCHAR(255) UNIQUE,
  password_hash VARCHAR(255),
  full_name VARCHAR(255),
  status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
  is_email_verified BOOLEAN DEFAULT FALSE,
  user_type VARCHAR(50) DEFAULT 'admin',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  last_login TIMESTAMP NULL
);
```

### Email Verifications Table
```sql
CREATE TABLE email_verifications (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  user_id BIGINT NOT NULL,
  email VARCHAR(255) NOT NULL,
  token VARCHAR(255) UNIQUE NOT NULL,
  verified BOOLEAN DEFAULT FALSE,
  expires_at TIMESTAMP NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_email (email),
  INDEX idx_token (token),
  INDEX idx_expires_at (expires_at)
);
```

## Testing the Flow

### 1. Test Registration
```bash
curl -X POST http://localhost:8080/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>", 
    "password": "SecurePassword123!",
    "full_name": "Test User"
  }'
```

### 2. Test Login Before Verification (Should Fail)
```bash
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SecurePassword123!"
  }'
```

### 3. Check Email for Verification Link
- Check email inbox for verification email
- Extract token from verification URL

### 4. Verify Email
```bash
curl -X POST http://localhost:8080/api/v1/auth/verify-email \
  -H "Content-Type: application/json" \
  -d '{
    "token": "extracted_token_from_email"
  }'
```

### 5. Test Login After Verification (Should Succeed)
```bash
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SecurePassword123!"
  }'
```

## Implementation Notes

### Automatic Email Sending
- Verification email is sent automatically during registration
- If email sending fails, registration still succeeds (user can resend later)
- Email service must be properly configured for automatic sending

### Security Considerations
- Verification tokens expire after 24 hours
- Tokens are UUIDs for security
- Password is hashed using bcrypt
- JWT tokens have configurable expiration

### Tracing and Logging
- All operations are traced with OpenTelemetry
- Comprehensive logging for debugging
- Span attributes include user_id, email, and operation status

## Configuration Requirements

### Email Service
- SMTP configuration must be set up
- Notification module must be enabled
- Email templates must be configured

### Environment Variables
```bash
# Email Configuration
NOTIFICATION_EMAIL_HOST=smtp.gmail.com
NOTIFICATION_EMAIL_PORT=587
NOTIFICATION_EMAIL_USERNAME=<EMAIL>
NOTIFICATION_EMAIL_PASSWORD=your-app-password
NOTIFICATION_EMAIL_FROM=<EMAIL>

# Web URL for verification links
WEB_URL=http://localhost:9200

# Queue Configuration (for email sending)
QUEUE_ENABLED=true
NOTIFICATION_WORKER_ENABLED=true
```
