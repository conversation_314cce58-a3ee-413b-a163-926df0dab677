Việc tạo tenant khi tạo tài khoản người dùng thành công phụ thuộc rất nhiều vào **luồng nghiệp vụ (business flow)** và **kiến trúc multi-tenancy** của ứng dụng bạn. Dưới đây là một số kịch bản phổ biến và cách tiếp cận tương ứng:

**Kịch bản và Cách tiếp cận:**

1.  **Mỗi User Mới Tự Động Có Một Tenant Riêng (Mô hình Isolation Cao):**
    *   **Luồng:** Khi một user đăng ký, hệ thống tự động tạo một tenant mới và gán user đó làm chủ sở hữu (hoặc admin) của tenant đó.
    *   **Cách thực hiện:**
        *   **Trong `AuthService.Register` (hoặc một service cấp cao hơn điều phối việc đăng ký):**
            1.  <PERSON>u khi tạo user thành công trong bảng `users`.
            2.  <PERSON><PERSON><PERSON> một `TenantService.CreateTenant` với thông tin từ user (ví dụ: tên tenant có thể dựa trên tên user hoặc tên công ty user cung cấp, nếu có).
            3.  Gán `tenant_id` vừa tạo vào bản ghi `user` vừa tạo (cập nhật lại user).
            4.  Gán vai trò (ví dụ: "tenant_admin" hoặc "owner") cho user đó trong phạm vi tenant mới tạo.
            5.  (Tùy chọn) Thực hiện các tác vụ khởi tạo tenant khác (tạo schema riêng, cấu hình mặc định cho tenant,...).
        *   **Sử dụng Events (Khuyến khích nếu logic phức tạp):**
            1.  Sau khi `AuthService.Register` tạo user thành công, nó sẽ phát ra một sự kiện `UserCreatedEvent` (như bạn đã có).
            2.  Một `EventHandler` (ví dụ: `TenantCreationHandler`) lắng nghe sự kiện này.
            3.  `TenantCreationHandler` sẽ gọi `TenantService.CreateTenant` và thực hiện các bước gán user, vai trò.
            *   **Ưu điểm của event:** Tách rời logic, dễ mở rộng, tăng khả năng phục hồi nếu một bước nào đó thất bại (có thể retry).

2.  **User Đăng Ký và Được Mời Vào Một Tenant Hiện Có:**
    *   **Luồng:** Admin của một tenant hiện có mời một user mới. User đăng ký (hoặc chấp nhận lời mời) và được liên kết với tenant đó.
    *   **Cách thực hiện:**
        *   **Hệ thống invitación (Invitation System):**
            1.  Admin tenant tạo lời mời (lưu token mời, email người được mời, tenant_id).
            2.  User nhấp vào link mời, được chuyển đến trang đăng ký.
            3.  Trang đăng ký truyền token mời.
            4.  Trong `AuthService.Register`:
                *   Xác thực token mời.
                *   Nếu hợp lệ, tạo user và gán `tenant_id` từ lời mời.
                *   Gán vai trò mặc định cho user trong tenant đó (ví dụ: "member").
        *   **Không nhất thiết phải tạo tenant mới khi user đăng ký.**

3.  **User Đăng Ký và Chọn/Tạo Tenant Sau Đó:**
    *   **Luồng:** User đăng ký tài khoản chung. Sau khi đăng nhập lần đầu (hoặc trong một bước riêng), user có thể tạo một tenant mới hoặc yêu cầu tham gia một tenant hiện có.
    *   **Cách thực hiện:**
        *   `AuthService.Register` chỉ tạo user mà **không** gán `tenant_id` (hoặc gán một `tenant_id` mặc định/tạm thời cho các user chưa thuộc tenant nào).
        *   Sau khi đăng nhập, UI sẽ hướng dẫn user:
            *   Tạo tenant mới (gọi `TenantService.CreateTenant`).
            *   Tìm kiếm và yêu cầu tham gia tenant hiện có.

4.  **Mô hình "Freemium" hoặc "Trial" - Tenant được tạo với gói mặc định:**
    *   **Luồng:** Tương tự Kịch bản 1, nhưng tenant mới được tạo sẽ có một gói dịch vụ (plan) mặc định (ví dụ: "free" hoặc "trial").
    *   **Cách thực hiện:**
        *   Khi gọi `TenantService.CreateTenant`, truyền thêm thông tin về `plan_id` mặc định.

**Vị trí Code để Thực Hiện:**

*   **`AuthService.Register`:** Đây là nơi hợp lý nhất để điều phối logic tạo user và tenant nếu chúng diễn ra đồng thời hoặc ngay sau nhau.
    *   Bạn sẽ inject `TenantService` vào `AuthService`.
    *   Thực hiện các thao tác trong một **transaction** để đảm bảo tính nhất quán (nếu tạo user lỗi thì không tạo tenant, và ngược lại).

    ```go
    // modules/auth/service/auth_service.go
    // (trong phương thức Register của Service)

    // ... (sau khi tạo user thành công trong DB)
    // createdUser, err = s.repo.CreateUser(ctx, user, req.Password)
    // if err != nil { /* ... */ }

    // Kịch bản 1: Tự động tạo tenant mới cho user
    if s.shouldAutoCreateTenantForNewUser(req) { // Hàm kiểm tra logic nghiệp vụ
        tenantName := fmt.Sprintf("%s's Team", createdUser.FullName) // Ví dụ
        if req.CompanyName != "" { // Giả sử có trường CompanyName trong RegisterRequest
            tenantName = req.CompanyName
        }

        createTenantReq := tenantDto.CreateTenantRequest{
            Name:        tenantName,
            OwnerUserID: createdUser.UserID,
            // ... các thông tin khác cho tenant
        }

        // Giả sử bạn có tenantService
        newTenant, err := s.tenantService.CreateTenant(ctx, createTenantReq)
        if err != nil {
            s.logger.Error("Failed to create tenant for new user", "userID", createdUser.UserID, "error", err)
            // Cân nhắc rollback việc tạo user ở đây nếu cần, hoặc xử lý lỗi phù hợp
            // (Ví dụ: đánh dấu user là "pending_tenant_creation" và thử lại qua một job)
            // Hoặc đơn giản là trả lỗi cho người dùng.
            return nil, fmt.Errorf("failed to create associated tenant: %w", err)
        }

        // Cập nhật user với tenant_id mới
        createdUser.TenantID = newTenant.ID
        if err := s.repo.UpdateUser(ctx, createdUser); err != nil {
            s.logger.Error("Failed to update user with new tenantID", "userID", createdUser.UserID, "tenantID", newTenant.ID, "error", err)
            // Xử lý lỗi nghiêm trọng, có thể cần rollback tạo tenant
            return nil, fmt.Errorf("failed to associate user with tenant: %w", err)
        }

        // (Tùy chọn) Gán vai trò owner/admin cho user trong tenant này
        // err = s.rbacService.AssignRoleToUserInTenant(ctx, createdUser.UserID, newTenant.ID, "owner")
        // ...
    }

    // Phát sự kiện UserCreated (nếu dùng)
    // ...

    return &internal.UserInfo{ /* ... */ }, nil
    ```

*   **`Event Handlers`:**
    *   Nếu bạn chọn cách tiếp cận dựa trên sự kiện, `AuthService.Register` chỉ tạo user và phát ra `UserCreatedEvent`.
    *   Một `TenantCreationHandler` sẽ lắng nghe sự kiện này.
        ```go
        // modules/tenant/eventhandlers/user_event_handler.go (ví dụ)
        package eventhandlers

        type UserCreatedEventHandler struct {
            tenantService TenantService
            // ... other dependencies
        }

        func (h *UserCreatedEventHandler) Handle(ctx context.Context, event events.Event) error {
            payload, ok := event.Payload().(types.UserCreatedPayload) // types từ internal/pkg/events/types
            if !ok {
                return fmt.Errorf("invalid payload type for UserCreatedEvent")
            }

            // Kiểm tra xem có nên tạo tenant không (dựa vào UserType, hoặc cờ nào đó trong payload)
            if payload.ShouldCreateTenant { // Giả sử có cờ này
                tenantName := fmt.Sprintf("%s's Organization", payload.FullName)
                createTenantReq := tenantDto.CreateTenantRequest{
                    Name:        tenantName,
                    OwnerUserID: payload.UserID,
                    // ...
                }
                newTenant, err := h.tenantService.CreateTenant(ctx, createTenantReq)
                if err != nil {
                    // Xử lý lỗi, có thể retry
                    return err
                }

                // Cập nhật user với tenant_id (có thể cần gọi UserService hoặc Repo)
                // Gán vai trò
            }
            return nil
        }
        ```

**Lưu ý quan trọng:**

*   **Transaction:** Nếu tạo user và tenant là một thao tác nguyên tử, hãy đảm bảo chúng được thực hiện trong một database transaction. Nếu `AuthService` và `TenantService` sử dụng các repository khác nhau nhưng cùng một DB instance, bạn có thể truyền transaction vào các phương thức repository.
*   **Service Dependencies:** `AuthService` sẽ cần inject `TenantService` (và có thể cả `RBACService`). Hoặc `Event Handlers` sẽ cần các service tương ứng.
*   **Cấu hình:** Luồng tạo tenant có thể được điều khiển bởi cấu hình (ví dụ: `AUTO_CREATE_TENANT_ON_REGISTER=true`).
*   **Tenant ID trong User:** Bảng `users` của bạn đã có cột `tenant_id`. Điều này rất quan trọng.
*   **Vai trò (Roles):** Sau khi tạo tenant và gán user, việc gán vai trò ban đầu cho user trong tenant đó (ví dụ: "owner", "admin") là bước cần thiết.
*   **Module `tenant`:** Bạn có module `tenant` (theo `MODULES_ENABLED`). Logic tạo tenant và quản lý tenant nên nằm trong service của module này.

**Chọn cách nào?**

*   **Kịch bản 1 (tự động tạo tenant riêng):** Sử dụng `AuthService.Register` để điều phối hoặc dùng `Event Handler` cho `UserCreatedEvent` là phổ biến. Event handler giúp tách biệt logic tốt hơn.
*   **Các kịch bản khác:** Logic sẽ phức tạp hơn và thường liên quan đến các tính năng như "Invitations" hoặc luồng "Onboarding" sau đăng ký.

Hãy chọn cách tiếp cận phù hợp nhất với yêu cầu cụ thể của ứng dụng bạn. Nếu luồng đơn giản (như kịch bản 1), việc tích hợp trực tiếp vào `AuthService.Register` có thể chấp nhận được, nhưng sử dụng events sẽ linh hoạt hơn cho tương lai.