# Hệ thống Seed Permission cho RBAC Module

Tài liệu này mô tả hệ thống seed permission tự động cho RBAC module, cho phép quét và seed tất cả permission definitions từ các modules trong hệ thống.

## Tổng quan

Hệ thống permission seeding được thiết kế để:

1. **Tự động discovery**: Quét tất cả modules để tìm file `internal/permission.go`
2. **Phân tích code**: Parse Go source code để trích xuất permission constants
3. **Seed database**: Tạo hoặc cập nhật permissions trong RBAC database
4. **Quản lý tập trung**: Cung cấp CLI commands để quản lý permissions
5. **Hỗ trợ incremental**: Cho phép cập nhật mà không tạo duplicate

## Cấu trúc Commands

### 1. Seed Permissions Command

```bash
wnapi seed-permissions [flags]
```

**Ch<PERSON><PERSON> năng**: Seed tất cả permission definitions từ modules vào RBAC database

**Flags**:
- `--module=<name>`: Chỉ seed permissions từ module cụ thể
- `--dry-run`: Hiển thị permissions sẽ được seed mà không thực thi
- `--force-update`: Cập nhật permissions đã tồn tại
- `--default-group-id=<id>`: Group ID mặc định cho permissions (default: 1)

**Ví dụ**:
```bash
# Seed tất cả permissions
wnapi seed-permissions

# Seed permissions từ module auth
wnapi seed-permissions --module=auth

# Dry run để xem trước
wnapi seed-permissions --dry-run

# Force update permissions đã tồn tại
wnapi seed-permissions --force-update
```

### 2. List Permissions Command

```bash
wnapi list-permissions [flags]
```

**Chức năng**: Liệt kê tất cả permissions trong RBAC database

**Flags**:
- `--module=<name>`: Filter theo module cụ thể
- `--pattern=<pattern>`: Filter theo pattern (hỗ trợ wildcard *)
- `--group-id=<id>`: Filter theo group ID
- `--limit=<number>`: Số lượng permissions hiển thị (default: 50)
- `--show-stats`: Hiển thị thống kê (default: true)

**Ví dụ**:
```bash
# Liệt kê tất cả permissions
wnapi list-permissions

# Liệt kê permissions của module auth
wnapi list-permissions --module=auth

# Liệt kê permissions theo pattern
wnapi list-permissions --pattern="*.create"

# Liệt kê permissions của group 1
wnapi list-permissions --group-id=1
```

### 3. Discover Permissions Command

```bash
wnapi discover-permissions [flags]
```

**Chức năng**: Discovery permissions từ modules mà không seed vào database

**Flags**:
- `--module=<name>`: Chỉ discover từ module cụ thể
- `--verbose`: Hiển thị thông tin chi tiết
- `--output=<file>`: Export kết quả ra file JSON
- `--include-functions`: Bao gồm permissions từ functions (default: true)
- `--include-constants`: Bao gồm permissions từ constants (default: true)

**Ví dụ**:
```bash
# Discover tất cả permissions
wnapi discover-permissions

# Discover với thông tin chi tiết
wnapi discover-permissions --verbose

# Export ra file JSON
wnapi discover-permissions --output=permissions.json
```

## Cấu trúc Permission Files

### Format chuẩn cho `internal/permission.go`

```go
package internal

import "wnapi/internal/pkg/permission"

// Module name
const ModuleName = "auth"

// Permission constants
const (
    // Standard CRUD permissions
    CreateUserPermission = ModuleName + permission.PermissionSeparator + "users" + permission.PermissionSeparator + permission.ActionCreate
    ReadUserPermission   = ModuleName + permission.PermissionSeparator + "users" + permission.PermissionSeparator + permission.ActionRead
    UpdateUserPermission = ModuleName + permission.PermissionSeparator + "users" + permission.PermissionSeparator + permission.ActionUpdate
    DeleteUserPermission = ModuleName + permission.PermissionSeparator + "users" + permission.PermissionSeparator + permission.ActionDelete
    ListUserPermission   = ModuleName + permission.PermissionSeparator + "users" + permission.PermissionSeparator + permission.ActionList

    // Special permissions
    LoginPermission    = ModuleName + permission.PermissionSeparator + "login"
    LogoutPermission   = ModuleName + permission.PermissionSeparator + "logout"
    RegisterPermission = ModuleName + permission.PermissionSeparator + "register"

    // Full management permission
    ManagePermission = ModuleName + permission.PermissionSeparator + permission.ActionManage
)

// Helper functions
func GetAllPermissions() []string {
    return []string{
        CreateUserPermission,
        ReadUserPermission,
        UpdateUserPermission,
        DeleteUserPermission,
        ListUserPermission,
        LoginPermission,
        LogoutPermission,
        RegisterPermission,
        ManagePermission,
    }
}
```

### Naming Convention

Permissions tuân theo format: `module.action.resource` hoặc `module.action`

**Ví dụ**:
- `auth.users.create` - Tạo user trong module auth
- `auth.users.read` - Đọc user trong module auth
- `auth.login` - Đăng nhập trong module auth
- `rbac.roles.manage` - Quản lý roles trong module rbac

### Standard Actions

Sử dụng các actions chuẩn từ `internal/pkg/permission/constants.go`:

- `create` - Tạo mới
- `read` - Đọc/xem
- `update` - Cập nhật
- `delete` - Xóa
- `list` - Liệt kê
- `manage` - Quản lý toàn bộ

## Permission Parser

### Cách thức hoạt động

1. **File Discovery**: Quét thư mục `modules/*/internal/permission.go`
2. **AST Parsing**: Sử dụng Go AST để parse source code
3. **Constant Extraction**: Trích xuất constants có tên chứa "Permission"
4. **Expression Evaluation**: Đánh giá expressions để lấy permission code
5. **Metadata Generation**: Tạo tên và mô tả từ constant names

### Supported Patterns

Parser hỗ trợ các patterns sau:

```go
// String literal
const SimplePermission = "auth.login"

// Concatenation với constants
const CreateUserPermission = ModuleName + "." + "users" + "." + "create"

// Sử dụng permission package
const ReadUserPermission = ModuleName + permission.PermissionSeparator + "users" + permission.PermissionSeparator + permission.ActionRead
```

## Group Mapping

Permissions được tự động gán vào groups dựa trên category:

| Category | Group ID | Mô tả |
|----------|----------|-------|
| user_management | 1 | Quản lý người dùng |
| role_management | 2 | Quản lý vai trò |
| permission_management | 2 | Quản lý quyền |
| profile_management | 1 | Quản lý profile |
| password_management | 1 | Quản lý mật khẩu |
| email_management | 1 | Quản lý email |
| media_management | 5 | Quản lý media |
| product_management | 3 | Quản lý sản phẩm |
| notification_management | 6 | Quản lý thông báo |
| general | 1 | Chung |

## Integration với Module System

### Module Registration

Modules tự động đăng ký commands thông qua `RegisterConsoleCommands`:

```go
// modules/rbac/module.go
func (m *Module) RegisterConsoleCommands(registry *console.Registry) error {
    m.logger.Info("Registering RBAC module console commands")
    return commands.RegisterRBACCommands(registry, m.permissionService)
}
```

### CLI Integration

Commands được tự động discover và đăng ký trong `cmd/cli/main.go`:

```go
// Import modules để đăng ký với registry
_ "wnapi/modules/rbac"
```

## Error Handling

### Common Errors

1. **Module không có permission file**: Bỏ qua và log warning
2. **Parse error**: Log error và tiếp tục với modules khác
3. **Permission đã tồn tại**: Bỏ qua hoặc update nếu có `--force-update`
4. **Database error**: Dừng và báo lỗi

### Logging

Hệ thống sử dụng structured logging với các levels:

- `INFO`: Thông tin chung về quá trình seed
- `WARNING`: Modules không có permission file hoặc parse errors
- `ERROR`: Database errors hoặc system errors
- `SUCCESS`: Thông báo thành công

## Best Practices

### 1. Tổ chức Permission Files

- Đặt tất cả permissions trong `internal/permission.go`
- Sử dụng constants thay vì string literals
- Nhóm permissions theo chức năng
- Cung cấp helper functions để lấy groups

### 2. Naming Conventions

- Sử dụng format `module.action.resource`
- Constant names kết thúc bằng "Permission"
- Sử dụng CamelCase cho constant names
- Sử dụng lowercase cho permission codes

### 3. Documentation

- Thêm comments cho permission groups
- Mô tả rõ ràng chức năng của từng permission
- Cung cấp examples trong comments

### 4. Testing

- Test permission parsing với unit tests
- Verify permission codes được generate đúng
- Test integration với RBAC database

## Troubleshooting

### Permission không được seed

1. Kiểm tra file `internal/permission.go` có tồn tại
2. Verify constant names chứa "Permission"
3. Check permission code format
4. Xem logs để tìm parse errors

### Duplicate permissions

1. Sử dụng `--force-update` để update
2. Kiểm tra permission codes có unique không
3. Verify database constraints

### Performance issues

1. Giới hạn số modules được scan
2. Sử dụng `--module` flag cho specific modules
3. Optimize database operations

## Roadmap

### Planned Features

1. **Function-based discovery**: Parse `GetAllPermissions()` functions
2. **JSON export/import**: Export permissions ra JSON format
3. **Permission validation**: Validate permission format và dependencies
4. **Batch operations**: Optimize database operations
5. **Permission templates**: Templates cho common permission patterns
