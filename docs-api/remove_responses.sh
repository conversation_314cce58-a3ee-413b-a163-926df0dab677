#!/bin/bash
# Script to remove response sections from all .bru files in docs-api directory

find /Users/<USER>/Desktop/Workspace/Webnew/wnapi/docs-api -name "*.bru" -type f -not -name "folder.bru" | while read file; do
  # Create a backup of the original file
  cp "$file" "${file}.bak"
  
  # Remove response section (everything from "response {" to the end of file)
  # Using perl because macOS sed has limitations with pattern matching
  perl -i -ne 'print unless /^response \{/ .. eof' "$file"
  
  echo "Processed: $file"
done

echo "Done removing response sections from all .bru files."
