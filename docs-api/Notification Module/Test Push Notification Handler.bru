meta {
  name: Test Push Notification Handler
  type: http
  seq: 13
}

post {
  url: {{api_url}}/api/admin/v1/notifications/test-push
  body: json
  auth: none
}

headers {
  Content-Type: application/json
  X-Tenant-ID: {{tenant_id}}
  X-Tenant-Code: {{tenant_code
  Authorization: Bearer {{access_token}}
}}
}

body:json {
  {
    "tenant_id": 1,
    "user_id": 1,
    "device_id": "device_123",
    "device_type": "android",
    "title": "New Message",
    "body": "You have received a new message from <PERSON>",
    "device_tokens": [
      "fcm_token_1",
      "fcm_token_2"
    ],
    "icon": "notification_icon",
    "sound": "default",
    "badge": "1",
    "click_action": "OPEN_CHAT",
    "data": {
      "chat_id": "chat_123",
      "sender_id": "user_456",
      "message_type": "text"
    },
    "options": {
      "priority": "high",
      "time_to_live": 3600
    }
  }
}

docs {
  # Test Push Notification Handler
  
  **Mô tả**: Test endpoint để gửi push notification thông qua queue system.
  
  ## Request Body
  
  | Field | Type | Required | Description |
  |-------|------|----------|-------------|
  | tenant_id | int | ✅ | ID của tenant |
  | user_id | int | ✅ | ID của user |
  | title | string | ✅ | Tiêu đề notification |
  | body | string | ✅ | Nội dung notification |
  | device_id | string | ❌ | ID của device |
  | device_type | string | ❌ | Loại device (android/ios) |
  | device_tokens | array | ❌ | Danh sách FCM/APNS tokens |
  | icon | string | ❌ | Icon notification |
  | sound | string | ❌ | Âm thanh notification |
  | badge | string | ❌ | Badge count |
  | click_action | string | ❌ | Action khi click notification |
  | data | object | ❌ | Custom data payload |
  | options | object | ❌ | Tùy chọn gửi (priority, ttl, etc.) |
  
  ## Response Success (200)
  
  ```json
  {
    "status": "ok",
    "task_id": "uuid-string",
    "task_type": "notification.send.push",
    "message": "Push notification task enqueued successfully",
    "payload": {
      "tenant_id": 1,
      "user_id": 1,
      "title": "New Message",
      "body": "You have received a new message...",
      "device_tokens": ["fcm_token_1", "fcm_token_2"],
      "data": {
        "chat_id": "chat_123"
      }
    }
  }
  ```
  
  ## Response Error (400)
  
  ```json
  {
    "error": "Invalid request body",
    "details": "validation error details"
  }
  ```
  
  ## Response Error (500)
  
  ```json
  {
    "error": "Failed to enqueue push notification task",
    "details": "error details"
  }
  ```
  
  ## Cách sử dụng
  
  1. **Chuẩn bị device tokens**: Lấy FCM/APNS tokens từ client apps
  2. **Gửi request**: POST với payload chứa thông tin notification
  3. **Kiểm tra queue**: Task sẽ được đưa vào queue "push" để xử lý
  4. **Xem logs**: Kiểm tra logs để theo dõi quá trình gửi
  
  ## Device Types
  
  - **android**: Sử dụng Firebase Cloud Messaging (FCM)
  - **ios**: Sử dụng Apple Push Notification Service (APNS)
  - **web**: Sử dụng Web Push Protocol
  
  ## Custom Data
  
  Data payload sẽ được gửi kèm notification:
  ```json
  {
    "data": {
      "chat_id": "chat_123",
      "sender_id": "user_456",
      "action": "open_chat"
    }
  }
  ```
  
  ## Queue Processing
  
  Task sẽ được xử lý bởi `PushHandler` với các bước:
  1. Validate payload và device tokens
  2. Tạo push notification
  3. Gửi qua FCM/APNS gateway
  4. Log kết quả và delivery status
}
