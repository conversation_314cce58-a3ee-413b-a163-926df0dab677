meta {
  name: Test Email Template Handler
  type: http
  seq: 10
}

post {
  url: {{api_url}}/api/admin/v1/notifications/test-email-template
  body: json
  auth: none
}

headers {
  Content-Type: application/json
  X-Tenant-ID: {{tenant_id}}
  X-Tenant-Code: {{tenant_code
  Authorization: Bearer {{access_token}}
}}
}

body:json {
  {
    "tenant_id": 1,
    "user_id": 1,
    "to": "<EMAIL>",
    "subject": "Test Email Template",
    "template_id": "welcome_email",
    "variables": {
      "user_name": "<PERSON>",
      "company_name": "ACME Corp",
      "activation_link": "https://example.com/activate/123",
      "support_email": "<EMAIL>"
    }
  }
}

docs {
  # Test Email Template Handler
  
  **Mô tả**: Test endpoint để gửi email sử dụng template thông qua queue system.
  
  ## Request Body
  
  | Field | Type | Required | Description |
  |-------|------|----------|-------------|
  | tenant_id | int | ✅ | ID của tenant |
  | user_id | int | ✅ | ID của user |
  | to | string | ✅ | Email ng<PERSON>ời nhận (phải là email hợp lệ) |
  | subject | string | ✅ | Tiêu đề email |
  | template_id | string | ✅ | ID của template email |
  | variables | object | ❌ | Các biến để thay thế trong template |
  
  ## Response Success (200)
  
  ```json
  {
    "status": "ok",
    "task_id": "uuid-string",
    "task_type": "notification.send.email_template",
    "message": "Email template task enqueued successfully",
    "payload": {
      "tenant_id": 1,
      "user_id": 1,
      "to": "<EMAIL>",
      "subject": "Test Email Template",
      "template_id": "welcome_email",
      "variables": {
        "user_name": "John Doe",
        "company_name": "ACME Corp"
      }
    }
  }
  ```
  
  ## Response Error (400)
  
  ```json
  {
    "error": "Invalid request body",
    "details": "validation error details"
  }
  ```
  
  ## Response Error (500)
  
  ```json
  {
    "error": "Failed to enqueue email template task",
    "details": "error details"
  }
  ```
  
  ## Cách sử dụng
  
  1. **Chuẩn bị template**: Đảm bảo template với `template_id` đã tồn tại trong database
  2. **Gửi request**: POST với payload chứa thông tin email và template
  3. **Kiểm tra queue**: Task sẽ được đưa vào queue "emails" để xử lý
  4. **Xem logs**: Kiểm tra logs để theo dõi quá trình xử lý
  
  ## Template Variables
  
  Variables trong request sẽ được sử dụng để thay thế placeholders trong template:
  - `{{user_name}}` → "John Doe"
  - `{{company_name}}` → "ACME Corp"
  - `{{activation_link}}` → "https://example.com/activate/123"
  
  ## Queue Processing
  
  Task sẽ được xử lý bởi `EmailTemplateHandler` với các bước:
  1. Validate payload
  2. Tạo notification từ template
  3. Gửi notification qua email service
  4. Log kết quả
}
