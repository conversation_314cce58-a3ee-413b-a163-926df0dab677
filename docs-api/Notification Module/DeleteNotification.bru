meta {
  name: DeleteNotification
  type: http
  seq: 5
}

delete {
  url: {{api_url}}/api/v1/notifications/{{notification_id}}
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true) {
    // Store the deleted notification ID
    const deletedNotificationId = bru.getEnvVar("notification_id");
    
    // Save to environment variables
    bru.setEnvVar("deleted_notification_id", deletedNotificationId);
    
    console.log(`Successfully deleted notification with ID: ${deletedNotificationId}`);
  }
}

docs {
  title: "Delete Notification"
  desc: "Delete a specific notification by its ID"
}
