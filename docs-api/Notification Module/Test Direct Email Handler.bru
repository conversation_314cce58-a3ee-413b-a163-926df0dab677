meta {
  name: Test Direct Email Handler
  type: http
  seq: 11
}

post {
  url: {{api_url}}/api/admin/v1/notifications/test-email-direct
  body: json
  auth: none
}

headers {
  Content-Type: application/json
  X-Tenant-ID: {{tenant_id}}
  X-Tenant-Code: {{tenant_code
  Authorization: Bearer {{access_token}}
}}
}

body:json {
  {
    "tenant_id": 1,
    "user_id": 1,
    "to": "<EMAIL>",
    "subject": "Test Direct Email",
    "body": "<h1>Hello World!</h1><p>This is a test email sent directly without template.</p><p>Best regards,<br>The Team</p>",
    "cc": ["<EMAIL>"],
    "bcc": ["<EMAIL>"],
    "variables": {
      "custom_var": "custom_value"
    }
  }
}

docs {
  # Test Direct Email Handler
  
  **Mô tả**: Test endpoint để gửi email trực tiếp (không sử dụng template) thông qua queue system.
  
  ## Request Body
  
  | Field | Type | Required | Description |
  |-------|------|----------|-------------|
  | tenant_id | int | ✅ | ID của tenant |
  | user_id | int | ✅ | ID của user |
  | to | string | ✅ | Email người nhận (phải là email hợp lệ) |
  | subject | string | ✅ | Tiêu đề email |
  | body | string | ✅ | Nội dung email (HTML hoặc text) |
  | cc | array | ❌ | Danh sách email CC |
  | bcc | array | ❌ | Danh sách email BCC |
  | variables | object | ❌ | Các biến tùy chỉnh |
  
  ## Response Success (200)
  
  ```json
  {
    "status": "ok",
    "task_id": "uuid-string",
    "task_type": "notification.send.email",
    "message": "Direct email task enqueued successfully",
    "payload": {
      "tenant_id": 1,
      "user_id": 1,
      "to": "<EMAIL>",
      "subject": "Test Direct Email",
      "body": "<h1>Hello World!</h1>...",
      "cc": ["<EMAIL>"],
      "bcc": ["<EMAIL>"],
      "variables": {
        "custom_var": "custom_value"
      }
    }
  }
  ```
  
  ## Response Error (400)
  
  ```json
  {
    "error": "Invalid request body",
    "details": "validation error details"
  }
  ```
  
  ## Response Error (500)
  
  ```json
  {
    "error": "Failed to enqueue direct email task",
    "details": "error details"
  }
  ```
  
  ## Cách sử dụng
  
  1. **Chuẩn bị nội dung**: Tạo nội dung email (HTML hoặc text)
  2. **Gửi request**: POST với payload chứa thông tin email đầy đủ
  3. **Kiểm tra queue**: Task sẽ được đưa vào queue "emails" để xử lý
  4. **Xem logs**: Kiểm tra logs để theo dõi quá trình xử lý
  
  ## Email Content
  
  - **HTML Support**: Body có thể chứa HTML tags
  - **Plain Text**: Hoặc chỉ là plain text
  - **Variables**: Có thể sử dụng variables tùy chỉnh
  
  ## Queue Processing
  
  Task sẽ được xử lý bởi `EmailHandler` với các bước:
  1. Validate payload
  2. Tạo notification trực tiếp
  3. Gửi email qua email service
  4. Log kết quả
}
