meta {
  name: Test SMS Handler
  type: http
  seq: 12
}

post {
  url: {{api_url}}/api/admin/v1/notifications/test-sms
  body: json
  auth: none
}

headers {
  Content-Type: application/json
  X-Tenant-ID: {{tenant_id}}
  X-Tenant-Code: {{tenant_code
  Authorization: Bearer {{access_token}}
}}
}

body:json {
  {
    "tenant_id": 1,
    "user_id": 1,
    "to": "+84901234567",
    "message": "Hello! This is a test SMS message from our notification system.",
    "template_id": "sms_verification",
    "from": "ACME",
    "variables": {
      "verification_code": "123456",
      "company_name": "ACME Corp",
      "expires_in": "5 minutes"
    }
  }
}

docs {
  # Test SMS Handler
  
  **Mô tả**: Test endpoint để gửi SMS thông qua queue system.
  
  ## Request Body
  
  | Field | Type | Required | Description |
  |-------|------|----------|-------------|
  | tenant_id | int | ✅ | ID của tenant |
  | user_id | int | ✅ | ID của user |
  | to | string | ✅ | Số điện thoại người nhận (format: +84901234567) |
  | message | string | ✅ | Nội dung SMS |
  | template_id | string | ❌ | ID của template SMS |
  | from | string | ❌ | Tên người gửi (brand name) |
  | variables | object | ❌ | Các biến để thay thế trong template |
  
  ## Response Success (200)
  
  ```json
  {
    "status": "ok",
    "task_id": "uuid-string",
    "task_type": "notification.send.sms",
    "message": "SMS task enqueued successfully",
    "payload": {
      "tenant_id": 1,
      "user_id": 1,
      "to": "+84901234567",
      "message": "Hello! This is a test SMS...",
      "template_id": "sms_verification",
      "from": "ACME",
      "variables": {
        "verification_code": "123456"
      }
    }
  }
  ```
  
  ## Response Error (400)
  
  ```json
  {
    "error": "Invalid request body",
    "details": "validation error details"
  }
  ```
  
  ## Response Error (500)
  
  ```json
  {
    "error": "Failed to enqueue SMS task",
    "details": "error details"
  }
  ```
  
  ## Cách sử dụng
  
  1. **Chuẩn bị số điện thoại**: Đảm bảo số điện thoại đúng format quốc tế
  2. **Gửi request**: POST với payload chứa thông tin SMS
  3. **Kiểm tra queue**: Task sẽ được đưa vào queue "sms" để xử lý
  4. **Xem logs**: Kiểm tra logs để theo dõi quá trình xử lý
  
  ## Phone Number Format
  
  - **International**: +84901234567 (Vietnam)
  - **Other countries**: +1234567890, +44123456789
  - **Validation**: Hệ thống sẽ validate format số điện thoại
  
  ## Template Variables
  
  Nếu sử dụng template, variables sẽ thay thế placeholders:
  - `{{verification_code}}` → "123456"
  - `{{company_name}}` → "ACME Corp"
  - `{{expires_in}}` → "5 minutes"
  
  ## Queue Processing
  
  Task sẽ được xử lý bởi `SMSHandler` với các bước:
  1. Validate payload và số điện thoại
  2. Tạo SMS notification
  3. Gửi SMS qua SMS gateway
  4. Log kết quả và trạng thái gửi
}
