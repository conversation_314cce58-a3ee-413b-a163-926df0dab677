meta {
  name: GetAllNotifications
  type: http
  seq: 1
}

get {
  url: {{api_url}}/api/v1/notifications
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
  X-Tenant-ID: {{tenant_id}}
}

query {
  page: 1
  limit: 20
  status: all
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    // Check if there are notifications
    if (responseJson.data.notifications && responseJson.data.notifications.length > 0) {
      // Extract details from the first notification
      const firstNotification = responseJson.data.notifications[0];
      const notificationId = firstNotification.notification_id;
      const templateCode = firstNotification.template_code;
      
      // Save to environment variables
      bru.setEnvVar("notification_id", notificationId);
      bru.setEnvVar("notification_template_code", templateCode);
      
      console.log("Notification data saved:");
      console.log(`Notification ID: ${notificationId}`);
      console.log(`Template Code: ${templateCode}`);
      console.log(`Total Notifications: ${responseJson.data.notifications.length}`);
    }
    
    // Save pagination info if available
    if (responseJson.data.meta) {
      const currentPage = responseJson.data.meta.current_page;
      const totalPages = responseJson.data.meta.total_pages;
      const totalItems = responseJson.data.meta.total_items;
      
      console.log(`Page ${currentPage} of ${totalPages}, Total: ${totalItems} items`);
    }
  }
}

docs {
  title: "Get All Notifications"
  desc: "Retrieve all notifications for the authenticated user with pagination"
}
