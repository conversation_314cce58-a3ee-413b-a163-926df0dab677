# Notification Module - Queue Testing APIs

## Tổng quan

Tài liệu này mô tả các API endpoints để test các queue handlers trong Notification Module, đặc biệt là `email_template_handler.go` và các handlers khác.

## Danh sách API Test Endpoints

### 1. Test Email Template Handler
- **Endpoint**: `POST /api/admin/v1/notifications/test-email-template`
- **File**: `Test Email Template Handler.bru`
- **M<PERSON>c đích**: Test gửi email sử dụng template
- **Queue**: `emails`
- **Task Type**: `notification.send.email_template`

### 2. Test Direct Email Handler  
- **Endpoint**: `POST /api/admin/v1/notifications/test-email-direct`
- **File**: `Test Direct Email Handler.bru`
- **Mục đích**: Test gửi email trực tiếp (không template)
- **Queue**: `emails`
- **Task Type**: `notification.send.email`

### 3. Test SMS Handler
- **Endpoint**: `POST /api/admin/v1/notifications/test-sms`
- **File**: `Test SMS Handler.bru`
- **Mục đích**: Test gửi SMS
- **Queue**: `sms`
- **Task Type**: `notification.send.sms`

### 4. Test Push Notification Handler
- **Endpoint**: `POST /api/admin/v1/notifications/test-push`
- **File**: `Test Push Notification Handler.bru`
- **Mục đích**: Test gửi push notification
- **Queue**: `push`
- **Task Type**: `notification.send.push`

### 5. Test Queue System
- **Endpoint**: `POST /api/admin/v1/notifications/test-queue`
- **File**: `Test Queue System.bru`
- **Mục đích**: Test queue system cơ bản
- **Queue**: `default`
- **Task Type**: `test_task`

## Cách sử dụng

### Bước 1: Khởi động server
```bash
go run cmd/fx-server/main.go -config .env
```

### Bước 2: Kiểm tra queue system
Trước tiên test endpoint cơ bản:
```bash
curl -X POST http://localhost:9033/api/admin/v1/notifications/test-queue \
  -H "Content-Type: application/json" \
  -d '{"message": "test"}'
```

### Bước 3: Test Email Template Handler
```bash
curl -X POST http://localhost:9033/api/admin/v1/notifications/test-email-template \
  -H "Content-Type: application/json" \
  -H "X-Tenant-Code: demo" \
  -d '{
    "tenant_id": 1,
    "user_id": 1,
    "to": "<EMAIL>",
    "subject": "Test Email Template",
    "template_id": "welcome_email",
    "variables": {
      "user_name": "John Doe",
      "company_name": "ACME Corp"
    }
  }'
```

### Bước 4: Kiểm tra logs
Xem logs trong console để theo dõi quá trình xử lý:
- Queue enqueue logs
- Handler processing logs
- Email sending logs
- Error logs (nếu có)

## Cấu trúc Queue System

### Queue Names
- `emails`: Xử lý email tasks
- `sms`: Xử lý SMS tasks  
- `push`: Xử lý push notification tasks
- `default`: Xử lý general tasks

### Task Types
- `notification.send.email_template`: Email với template
- `notification.send.email`: Email trực tiếp
- `notification.send.sms`: SMS
- `notification.send.push`: Push notification
- `test_task`: Test task

### Handler Files
- `email_template_handler.go`: Xử lý email template
- `email_handler.go`: Xử lý email trực tiếp
- `sms_handler.go`: Xử lý SMS
- `push_handler.go`: Xử lý push notification

## Debugging

### Kiểm tra Queue Status
```bash
# Xem queue stats (nếu có monitoring endpoint)
curl http://localhost:9033/api/admin/v1/queue/stats
```

### Xem Logs
- **Queue logs**: Logs từ queue manager và client
- **Handler logs**: Logs từ các handlers
- **Service logs**: Logs từ notification service

### Common Issues
1. **Queue manager not available**: Kiểm tra queue configuration
2. **Handler not found**: Đảm bảo handlers được register đúng
3. **Payload validation error**: Kiểm tra format request body
4. **Email/SMS service error**: Kiểm tra service configuration

## Environment Variables

Đảm bảo các env vars sau được cấu hình:
```env
# Queue
QUEUE_ENABLED=true
QUEUE_REDIS_URL=redis://localhost:6379

# Email
EMAIL_ENABLED=true
SMTP_HOST=localhost
SMTP_PORT=1025

# SMS (nếu test SMS)
SMS_ENABLED=true
SMS_PROVIDER=twilio

# Push (nếu test Push)
PUSH_ENABLED=true
FCM_SERVER_KEY=your_fcm_key
```

## Kết quả mong đợi

### Success Response
```json
{
  "status": "ok",
  "task_id": "uuid-string",
  "task_type": "notification.send.email_template",
  "message": "Email template task enqueued successfully",
  "payload": { ... }
}
```

### Error Response
```json
{
  "error": "Failed to enqueue email template task",
  "details": "error details"
}
```

### Logs mong đợi
```
[INFO] Task enqueued: notification.send.email_template
[INFO] Đang xử lý task gửi email với template
[INFO] Đã phân tích payload email template
[INFO] Tạo notification từ template
[INFO] Gửi notification
[INFO] Email template notification đã được gửi thành công
```
