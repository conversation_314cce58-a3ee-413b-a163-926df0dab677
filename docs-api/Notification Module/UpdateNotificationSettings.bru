meta {
  name: UpdateNotificationSettings
  type: http
  seq: 7
}

put {
  url: {{api_url}}/api/v1/notifications/settings
  body: json
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

body:json {
  {
    "emailNotifications": true,
    "pushNotifications": true,
    "inAppNotifications": true,
    "notificationTypes": {
      "system": true,
      "message": true,
      "activity": true,
      "marketing": false
    }
  }
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    // Extract settings
    const settings = responseJson.data;
    
    // Save to environment variables
    if (settings.emailNotifications !== undefined) {
      bru.setEnvVar("notification_email_enabled", settings.emailNotifications);
    }
    
    if (settings.pushNotifications !== undefined) {
      bru.setEnvVar("notification_push_enabled", settings.pushNotifications);
    }
    
    if (settings.inAppNotifications !== undefined) {
      bru.setEnvVar("notification_inapp_enabled", settings.inAppNotifications);
    }
    
    console.log("Notification settings updated:");
    console.log(`Email notifications: ${settings.emailNotifications}`);
    console.log(`Push notifications: ${settings.pushNotifications}`);
    console.log(`In-app notifications: ${settings.inAppNotifications}`);
    
    if (settings.notificationTypes) {
      console.log("Notification types:");
      Object.entries(settings.notificationTypes).forEach(([type, enabled]) => {
        console.log(`  ${type}: ${enabled}`);
      });
    }
  }
}

docs {
  title: "Update Notification Settings"
  desc: "Update notification preferences for the authenticated user"
}
