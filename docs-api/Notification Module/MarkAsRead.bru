meta {
  name: MarkAsRead
  type: http
  seq: 3
}

patch {
  url: {{api_url}}/api/v1/notifications/{{notification_id}}/read
  body: none
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true) {
    // Extract notification details if available
    if (responseJson.data && responseJson.data.notification_id) {
      const notificationId = responseJson.data.notification_id;
      
      // Save to environment variables
      bru.setEnvVar("notification_id", notificationId);
      bru.setEnvVar("notification_is_read", true);
      
      console.log("Notification marked as read:");
      console.log(`Notification ID: ${notificationId}`);
    } else {
      console.log("Notification successfully marked as read");
    }
  }
}

docs {
  title: "Mark Notification As Read"
  desc: "Mark a specific notification as read by its ID"
}
