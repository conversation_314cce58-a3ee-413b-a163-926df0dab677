meta {
  name: GetUnreadCount
  type: http
  seq: 6
}

get {
  url: {{api_url}}/api/v1/notifications/unread-count
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    // Extract unread count
    const unreadCount = responseJson.data.unread_count;
    
    // Save to environment variables
    bru.setEnvVar("notification_unread_count", unreadCount);
    
    console.log(`Unread notifications count: ${unreadCount}`);
  }
}

docs {
  title: "Get Unread Notifications Count"
  desc: "Get the count of unread notifications for the authenticated user"
}
