meta {
  name: GetNotificationById
  type: http
  seq: 2
}

get {
  url: {{api_url}}/api/v1/notifications/{{notification_id}}
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
  X-Tenant-ID: {{tenant_id}}
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    // Extract notification details
    const notificationId = responseJson.data.notification_id;
    const templateCode = responseJson.data.template_code;
    const isRead = responseJson.data.is_read;
    
    // Save to environment variables
    bru.setEnvVar("notification_id", notificationId);
    bru.setEnvVar("notification_template_code", templateCode);
    bru.setEnvVar("notification_is_read", isRead);
    
    console.log("Notification data saved:");
    console.log(`Notification ID: ${notificationId}`);
    console.log(`Template Code: ${templateCode}`);
    console.log(`Is Read: ${isRead}`);
  }
}

docs {
  title: "Get Notification By ID"
  desc: "Retrieve a specific notification by its ID"
}
