meta {
  name: Test Queue System
  type: http
  seq: 14
}

post {
  url: {{api_url}}/api/admin/v1/notifications/test-queue
  body: json
  auth: none
}

headers {
  Content-Type: application/json
  X-Tenant-ID: {{tenant_id}}
  X-Tenant-Code: {{tenant_code
  Authorization: Bearer {{access_token}}
}}
}

body:json {
  {
    "message": "Testing queue system functionality"
  }
}

docs {
  # Test Queue System
  
  **Mô tả**: Test endpoint cơ bản để kiểm tra queue system hoạt động.
  
  ## Request Body
  
  | Field | Type | Required | Description |
  |-------|------|----------|-------------|
  | message | string | ❌ | Tin nhắn test |
  
  ## Response Success (200)
  
  ```json
  {
    "status": "ok",
    "task_id": "uuid-string",
    "message": "Task enqueued successfully"
  }
  ```
  
  ## Response Error (500)
  
  ```json
  {
    "error": "Queue manager not available"
  }
  ```
  
  ## <PERSON><PERSON><PERSON> sử dụng
  
  1. **Kiểm tra queue**: Endpoint này để test queue system cơ bản
  2. **<PERSON><PERSON><PERSON> request**: POST với payload đơn giản
  3. **Xem response**: Kiểm tra task_id được trả về
  4. **Xem logs**: Kiểm tra logs để đảm bảo task được xử lý
  
  ## Queue Processing
  
  - Task type: `test_task`
  - Queue: `default`
  - Timeout: 10 seconds
  - Max retry: 3
  
  Endpoint này chỉ để test queue system, không thực hiện logic nghiệp vụ cụ thể.
}
