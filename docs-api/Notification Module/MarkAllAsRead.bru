meta {
  name: MarkAllAsRead
  type: http
  seq: 4
}

patch {
  url: {{api_url}}/api/v1/notifications/mark-all-read
  body: none
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true) {
    // Extract count information if available
    if (responseJson.data && responseJson.data.count !== undefined) {
      const count = responseJson.data.count;
      
      // Save to environment variables
      bru.setEnvVar("notifications_marked_read_count", count);
      
      console.log(`Successfully marked ${count} notifications as read`);
    } else {
      console.log("All notifications successfully marked as read");
    }
  }
}

docs {
  title: "Mark All Notifications As Read"
  desc: "Mark all notifications of the authenticated user as read"
}
