meta {
  name: Delete Media
  type: http
  seq: 6
}

delete {
  url: {{base_url}}/api/admin/v1/media/{{media_id}}
  body: none
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true) {
    console.log("Media deleted successfully");
    console.log(`Media ID ${bru.getEnvVar("media_id")} has been removed`);
    
    // Clear the media_id from environment since it's deleted
    bru.setEnvVar("media_id", "");
  }
}

docs {
  title: "Delete Media"
  desc: "Permanently delete a media file from the system. This will remove both the database record and the actual file from storage."
}
