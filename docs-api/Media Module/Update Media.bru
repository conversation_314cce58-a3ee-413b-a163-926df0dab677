meta {
  name: Update Media
  type: http
  seq: 5
}

put {
  url: {{base_url}}/api/admin/v1/media/{{media_id}}
  body: json
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

body:json {
  {
    "is_public": false
  }
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    console.log("Media updated successfully:");
    console.log(`ID: ${responseJson.data.id}`);
    console.log(`Is Public: ${responseJson.data.is_public}`);
    console.log(`Updated At: ${responseJson.data.updated_at}`);
  }
}

docs {
  title: "Update Media"
  desc: "Update media file properties such as public visibility status. Only certain fields can be updated after upload."
}
