meta {
  name: Update Folder
  type: http
  seq: 11
}

put {
  url: {{base_url}}/api/admin/v1/media/folders/{{folder_id}}
  body: json
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

body:json {
  {
    "name": "Updated Folder Name",
    "description": "Updated description for the folder",
    "is_public": false
  }
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    console.log("Folder updated successfully:");
    console.log(`ID: ${responseJson.data.folder_id}`);
    console.log(`Name: ${responseJson.data.name}`);
    console.log(`Slug: ${responseJson.data.slug}`);
    console.log(`Description: ${responseJson.data.description}`);
    console.log(`Is Public: ${responseJson.data.is_public}`);
    console.log(`Updated At: ${responseJson.data.updated_at}`);
  }
}

docs {
  title: "Update Folder"
  desc: "Update folder properties such as name, description, parent folder, and public visibility. The slug will be automatically regenerated if the name changes."
}
