meta {
  name: Get Media by ID
  type: http
  seq: 3
}

get {
  url: {{base_url}}/api/admin/v1/media/{{media_id}}
  body: none
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    console.log("Media details retrieved:");
    console.log(`ID: ${responseJson.data.id}`);
    console.log(`Filename: ${responseJson.data.filename}`);
    console.log(`Media Type: ${responseJson.data.media_type}`);
    console.log(`Status: ${responseJson.data.status}`);
    console.log(`Public URL: ${responseJson.data.public_url}`);
  }
}

docs {
  title: "Get Media by ID"
  desc: "Retrieve detailed information about a specific media file by its ID. Returns media metadata, properties, and public URL if available."
}
