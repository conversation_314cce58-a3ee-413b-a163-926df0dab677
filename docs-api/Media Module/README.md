# Media Module API Documentation

This collection contains Bruno API requests for testing the Media Module endpoints in the WNAPI v2 system.

## Overview

The Media Module provides comprehensive file management capabilities including:
- File upload and storage (local or S3)
- Media organization with folders
- File metadata and properties management
- Public/private access control
- Multiple media types support (image, video, audio, document)

## Base URL
- **Local Development**: `http://localhost:9033`
- **Staging**: Configure in environment
- **Production**: Configure in environment

## Authentication

All endpoints (except health check) require JWT Bearer token authentication:
```
Authorization: Bearer {access_token}
```

## Media Types Supported

- **image**: JPEG, PNG, GIF, WebP, etc.
- **video**: MP4, AVI, MOV, etc.
- **audio**: MP3, WAV, AAC, etc.
- **document**: PDF, DOC, DOCX, TXT, etc.
- **other**: Any other file type

## Media Status

- **pending**: File uploaded but not yet processed
- **processing**: File is being processed (resize, optimization, etc.)
- **ready**: File is ready for use
- **failed**: Processing failed

## Endpoints

### Media File Operations

1. **Health Check** - `GET /api/admin/v1/media/health`
   - Check if media module is running
   - No authentication required

2. **Upload File** - `POST /api/admin/v1/media/upload`
   - Upload a new media file
   - Supports multipart form data
   - Requires JWT authentication

3. **Get Media by ID** - `GET /api/admin/v1/media/{id}`
   - Retrieve specific media file details
   - Requires JWT authentication

4. **Get Media List** - `GET /api/admin/v1/media`
   - List media files with filtering and pagination
   - Query parameters: limit, cursor, media_type, folder_id, is_public, search
   - Requires JWT authentication

5. **Update Media** - `PUT /api/admin/v1/media/{id}`
   - Update media file metadata
   - Requires JWT authentication

6. **Delete Media** - `DELETE /api/admin/v1/media/{id}`
   - Delete media file and its storage
   - Requires JWT authentication

7. **Get Media URL** - `GET /api/admin/v1/media/{id}/url`
   - Get direct access URL for media file
   - Requires JWT authentication

### Media Folder Operations

1. **Create Folder** - `POST /api/admin/v1/media/folders`
   - Create a new media folder
   - Requires JWT authentication

2. **Get Folder by ID** - `GET /api/admin/v1/media/folders/{id}`
   - Retrieve specific folder details
   - Requires JWT authentication

3. **Get Folder List** - `GET /api/admin/v1/media/folders`
   - List folders with filtering and pagination
   - Query parameters: limit, cursor, parent_id, is_public, search
   - Requires JWT authentication

4. **Update Folder** - `PUT /api/admin/v1/media/folders/{id}`
   - Update folder metadata
   - Requires JWT authentication

5. **Delete Folder** - `DELETE /api/admin/v1/media/folders/{id}`
   - Delete folder (must be empty)
   - Requires JWT authentication
   - Optional folder assignment

3. **Get Media by ID** - `GET /api/admin/v1/media/{id}`
   - Retrieve specific media file details
   - Returns metadata, properties, and URLs

4. **Get Media List** - `GET /api/admin/v1/media`
   - List media files with pagination
   - Filter by type, folder, public status
   - Search functionality

5. **Update Media** - `PUT /api/admin/v1/media/{id}`
   - Update media properties
   - Currently supports public status changes

6. **Delete Media** - `DELETE /api/admin/v1/media/{id}`
   - Permanently delete media file
   - Removes both database record and file

7. **Get Media URL** - `GET /api/admin/v1/media/{id}/url`
   - Get direct access URL for media file
   - Returns public or signed URL

### Folder Operations

8. **Create Folder** - `POST /api/admin/v1/media/folders`
   - Create new media folder
   - Supports nested folder structure

9. **Get Folder by ID** - `GET /api/admin/v1/media/folders/{id}`
   - Retrieve specific folder details

10. **Get Folder List** - `GET /api/admin/v1/media/folders`
    - List folders with pagination
    - Filter by parent, public status
    - Search functionality

11. **Update Folder** - `PUT /api/admin/v1/media/folders/{id}`
    - Update folder properties
    - Auto-generates slug from name

12. **Delete Folder** - `DELETE /api/admin/v1/media/folders/{id}`
    - Delete empty folder
    - May fail if folder contains files

## Environment Variables

The following environment variables are used and set by the API requests:

### Media Variables
- `media_id` - ID of uploaded/selected media
- `media_filename` - Generated filename
- `media_original_filename` - Original uploaded filename
- `media_public_url` - Public access URL
- `media_type` - Media type (image, video, etc.)
- `media_content_type` - MIME content type
- `media_size` - File size in bytes
- `media_status` - Processing status
- `media_access_url` - Direct access URL

### Folder Variables
- `folder_id` - ID of created/selected folder
- `folder_name` - Folder name
- `folder_slug` - URL-friendly folder slug

### Pagination Variables
- `media_list_next_cursor` - Next page cursor for media list
- `folder_list_next_cursor` - Next page cursor for folder list

## Usage Examples

### Basic File Upload
1. Run "Upload File" request with a sample file
2. The response will populate environment variables
3. Use "Get Media by ID" to verify upload
4. Use "Get Media URL" to get access URL

### Folder Management
1. Run "Create Folder" to create a new folder
2. Use the folder_id in "Upload File" request
3. List folders to see the hierarchy
4. Update or delete folders as needed

### File Organization
1. Create folders for different media types
2. Upload files to specific folders
3. Use folder filtering in media list
4. Organize content hierarchically

## Error Handling

All endpoints return standardized error responses:
```json
{
  "status": {
    "success": false,
    "message": "Error description",
    "code": "ERROR_CODE"
  },
  "details": [
    {
      "message": "Detailed error information"
    }
  ]
}
```

## Configuration

The media module supports various configuration options:
- Storage type (local/S3)
- Maximum file size
- Allowed file types
- Image quality settings
- Processing options

## Notes

- File uploads are limited to 10MB by default
- Images may be automatically processed/optimized
- Public files are accessible without authentication
- Private files require authentication to access
- Folder structure supports unlimited nesting
- Slugs are auto-generated from folder names
