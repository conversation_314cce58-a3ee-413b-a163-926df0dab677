meta {
  name: Create Folder
  type: http
  seq: 8
}

post {
  url: {{base_url}}/api/admin/v1/media/folders
  body: json
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

body:json {
  {
    "name": "Sample Folder",
    "description": "A sample folder for organizing media files",
    "parent_id": null,
    "is_public": true
  }
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    // Extract folder details
    const folderId = responseJson.data.folder_id;
    const folderName = responseJson.data.name;
    const folderSlug = responseJson.data.slug;
    const isPublic = responseJson.data.is_public;
    const parentId = responseJson.data.parent_id;
    
    // Save to environment variables
    bru.setEnvVar("folder_id", folderId);
    bru.setEnvVar("folder_name", folderName);
    bru.setEnvVar("folder_slug", folderSlug);
    
    console.log("Folder created successfully:");
    console.log(`Folder ID: ${folderId}`);
    console.log(`Name: ${folderName}`);
    console.log(`Slug: ${folderSlug}`);
    console.log(`Is Public: ${isPublic}`);
    console.log(`Parent ID: ${parentId}`);
  }
}

docs {
  title: "Create Media Folder"
  desc: "Create a new folder for organizing media files. Folders can be nested by specifying a parent_id and can be public or private."
}
