meta {
  name: Upload File
  type: http
  seq: 1
}

post {
  url: {{base_url}}/api/admin/v1/media/upload
  body: multipartForm
  auth: inherit
}

headers {
  Authorization: Bearer {{access_token}}
}

body:multipart-form {
  file: @file(sample.jpg)
  folder_id: 1
  alt_text: Sample image description
  title: Sample Image Title
  description: File description
  is_public: true
}

docs {
  # Upload Media File
  
  Upload một file media (image, video, document) lên hệ thống.
  
  ## Request Body (multipart/form-data)
  - `file`: File cần upload (required)
  - `folder_id`: ID của folder chứa file (optional)
  - `alt_text`: Text mô tả cho accessibility (optional)
  - `title`: Ti<PERSON>u đề file (optional)
  - `description`: <PERSON><PERSON> tả file (optional)
  - `is_public`: File có public không (boolean, default: true)
  
  ## Supported File Types
  ### Images
  - JPEG (.jpg, .jpeg)
  - PNG (.png)
  - GIF (.gif)
  - WebP (.webp)
  - SVG (.svg)
  
  ### Videos
  - MP4 (.mp4)
  - WebM (.webm)
  - AVI (.avi)
  - MOV (.mov)
  
  ### Documents
  - PDF (.pdf)
  - DOC/DOCX (.doc, .docx)
  - XLS/XLSX (.xls, .xlsx)
  - PPT/PPTX (.ppt, .pptx)
  - TXT (.txt)
  
  ## File Size Limits
  - Images: Max 10MB
  - Videos: Max 100MB
  - Documents: Max 50MB
  
  ## Response Format
  ```json
  {
    "status": "success",
    "data": {
      "id": 1,
      "filename": "sample.jpg",
      "original_filename": "my-image.jpg",
      "title": "Sample Image Title",
      "alt_text": "Sample image description",
      "description": "File description",
      "mime_type": "image/jpeg",
      "file_size": 1024576,
      "file_extension": "jpg",
      "storage_path": "/uploads/2024/01/sample.jpg",
      "url": "https://example.com/uploads/2024/01/sample.jpg",
      "thumbnail_url": "https://example.com/uploads/2024/01/thumbs/sample.jpg",
      "is_public": true,
      "folder": {
        "id": 1,
        "name": "Images",
        "path": "/Images"
      },
      "metadata": {
        "width": 1920,
        "height": 1080,
        "duration": null,
        "exif": {
          "camera": "Canon EOS 5D",
          "lens": "24-70mm f/2.8"
        }
      },
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  }
  ```
  
  ## Required Permission
  - `media.upload`
  
  ## Error Responses
  - 400: Bad Request - File không hợp lệ hoặc quá dung lượng
  - 415: Unsupported Media Type - Định dạng file không được hỗ trợ
  - 422: Validation Error - Dữ liệu không hợp lệ
  - 507: Insufficient Storage - Không đủ dung lượng lưu trữ
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    // Extract media details
    const mediaId = responseJson.data.id;
    const filename = responseJson.data.filename;
    const originalFilename = responseJson.data.original_filename;
    const publicUrl = responseJson.data.public_url;
    const mediaType = responseJson.data.media_type;
    const contentType = responseJson.data.content_type;
    const size = responseJson.data.size;
    const status = responseJson.data.status;
    
    // Save to environment variables
    bru.setEnvVar("media_id", mediaId);
    bru.setEnvVar("media_filename", filename);
    bru.setEnvVar("media_original_filename", originalFilename);
    bru.setEnvVar("media_public_url", publicUrl);
    bru.setEnvVar("media_type", mediaType);
    bru.setEnvVar("media_content_type", contentType);
    bru.setEnvVar("media_size", size);
    bru.setEnvVar("media_status", status);
    
    console.log("Media upload data saved:");
    console.log(`Media ID: ${mediaId}`);
    console.log(`Filename: ${filename}`);
    console.log(`Original Filename: ${originalFilename}`);
    console.log(`Public URL: ${publicUrl}`);
    console.log(`Media Type: ${mediaType}`);
    console.log(`Content Type: ${contentType}`);
    console.log(`Size: ${size} bytes`);
    console.log(`Status: ${status}`);
  }
}

docs {
  title: "Upload Media File"
  desc: "Upload a media file (image, video, audio, document) to the server. The file will be processed and stored based on the configured storage type (local or S3)."
}
