meta {
  name: Get Folder List
  type: http
  seq: 10
}

get {
  url: {{base_url}}/api/admin/v1/media/folders?limit=10&cursor=&parent_id=&is_public=&search=
  body: none
  auth: none
}

params:query {
  limit: 10
  cursor: 
  parent_id: 
  is_public: 
  search: 
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    const folderList = responseJson.data;
    const meta = responseJson.meta;
    
    console.log(`Retrieved ${folderList.length} folders`);
    
    if (meta && meta.next_cursor) {
      bru.setEnvVar("folder_list_next_cursor", meta.next_cursor);
      console.log(`Next cursor: ${meta.next_cursor}`);
    }
    
    // Save first folder ID if available
    if (folderList.length > 0) {
      bru.setEnvVar("first_folder_id", folderList[0].folder_id);
      console.log(`First folder ID: ${folderList[0].folder_id}`);
      console.log(`First folder name: ${folderList[0].name}`);
    }
  }
}

docs {
  title: "Get Folder List"
  desc: "Retrieve a paginated list of media folders with optional filtering by parent folder, public status, and search query. Supports cursor-based pagination."
}
