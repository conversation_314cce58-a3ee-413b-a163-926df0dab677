meta {
  name: Get Folder by ID
  type: http
  seq: 9
}

get {
  url: {{base_url}}/api/admin/v1/media/folders/{{folder_id}}
  body: none
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    console.log("Folder details retrieved:");
    console.log(`ID: ${responseJson.data.folder_id}`);
    console.log(`Name: ${responseJson.data.name}`);
    console.log(`Slug: ${responseJson.data.slug}`);
    console.log(`Description: ${responseJson.data.description}`);
    console.log(`Is Public: ${responseJson.data.is_public}`);
    console.log(`Parent ID: ${responseJson.data.parent_id}`);
    console.log(`Created At: ${responseJson.data.created_at}`);
  }
}

docs {
  title: "Get Folder by ID"
  desc: "Retrieve detailed information about a specific media folder by its ID. Returns folder metadata including name, slug, description, and hierarchy information."
}
