meta {
  name: Delete Folder
  type: http
  seq: 12
}

delete {
  url: {{base_url}}/api/admin/v1/media/folders/{{folder_id}}
  body: none
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true) {
    console.log("Folder deleted successfully");
    console.log(`Folder ID ${bru.getEnvVar("folder_id")} has been removed`);
    
    // Clear the folder_id from environment since it's deleted
    bru.setEnvVar("folder_id", "");
  }
}

docs {
  title: "Delete Folder"
  desc: "Permanently delete a media folder from the system. Note: This operation may fail if the folder contains media files or subfolders."
}
