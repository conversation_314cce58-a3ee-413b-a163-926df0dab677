meta {
  name: Get Media URL
  type: http
  seq: 7
}

get {
  url: {{base_url}}/api/admin/v1/media/{{media_id}}/url
  body: none
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    const mediaUrl = responseJson.data.url;
    
    // Save URL to environment
    bru.setEnvVar("media_access_url", mediaUrl);
    
    console.log("Media URL retrieved:");
    console.log(`URL: ${mediaUrl}`);
  }
}

docs {
  title: "Get Media URL"
  desc: "Get the public or signed URL for accessing a media file. This endpoint returns the direct URL that can be used to view or download the media file."
}
