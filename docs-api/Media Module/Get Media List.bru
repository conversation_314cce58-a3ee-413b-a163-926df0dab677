meta {
  name: Get Media List
  type: http
  seq: 4
}

get {
  url: {{base_url}}/api/admin/v1/media?limit=10&cursor=&media_type=&folder_id=&is_public=&search=
  body: none
  auth: none
}

params:query {
  limit: 10
  cursor: 
  media_type: 
  folder_id: 
  is_public: 
  search: 
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    const mediaList = responseJson.data;
    const meta = responseJson.meta;
    
    console.log(`Retrieved ${mediaList.length} media files`);
    
    if (meta && meta.next_cursor) {
      bru.setEnvVar("media_list_next_cursor", meta.next_cursor);
      console.log(`Next cursor: ${meta.next_cursor}`);
    }
    
    // Save first media ID if available
    if (mediaList.length > 0) {
      bru.setEnvVar("first_media_id", mediaList[0].id);
      console.log(`First media ID: ${mediaList[0].id}`);
    }
  }
}

docs {
  title: "Get Media List"
  desc: "Retrieve a paginated list of media files with optional filtering by media type, folder, public status, and search query. Supports cursor-based pagination."
}
