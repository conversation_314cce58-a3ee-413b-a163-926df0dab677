meta {
  name: Register
  type: http
  seq: 1
}

post {
  url: {{api_url}}/api/admin/v1/auth/register
  body: json
  auth: inherit
}

body:json {
  {
    "username": "admin{{userIdCount}}",
    "email": "admin{{userIdCount}}@gmail.com",
    "password": "12345678",
    "full_name": "Doe1"
  }
}

script:pre-request {
  let current = bru.getEnvVar('userIdCount') || 1
  current = current + 1
  //current = ''
  bru.setEnvVar("userIdCount", current);
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    // Extract user registration details
    const userId = responseJson.data.user_id;
    const email = responseJson.data.email;
    const status = responseJson.data.status;
    
    // Save to environment variables
    bru.setEnvVar("registered_user_id", userId);
    bru.setEnvVar("registered_email", email);
    bru.setEnvVar("registered_status", status);
    
    console.log("Registration data saved:");
    console.log(`User ID: ${userId}`);
    console.log(`Email: ${email}`);
    console.log(`Status: ${status}`);
  }
}

docs {
  title: "User Registration"
  desc: "Register a new user account with email and password"
}
