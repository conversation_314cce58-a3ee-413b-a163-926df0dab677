meta {
  name: RefreshToken
  type: http
  seq: 5
}

post {
  url: {{api_url}}/api/admin/v1/auth/refresh-token
  body: json
  auth: none

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
  X-Tenant-ID: {{tenant_id}}
}
}

body:json {
  {
    "refresh_token": "{{refresh_token}}"
  }
}

docs {
  title: "Refresh Token"
  desc: "Get a new access token using a refresh token"
}
