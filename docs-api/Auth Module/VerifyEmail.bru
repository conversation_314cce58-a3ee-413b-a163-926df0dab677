meta {
  name: VerifyEmail
  type: http
  seq: 8
}

post {
  url: {{api_url}}/api/admin/v1/auth/verify-email
  body: json
  auth: none

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
  X-Tenant-ID: {{tenant_id}}
}
}

body:json {
  {
    "token": "verification_token_from_email"
  }
}

docs {
  title: "Verify Email"
  desc: "Verify user email address using token received by email"
}
