meta {
  name: ResetPassword
  type: http
  seq: 7
}

post {
  url: {{api_url}}/api/admin/v1/auth/reset-password
  body: json
  auth: none

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
  X-Tenant-ID: {{tenant_id}}
}
}

body:json {
  {
    "token": "reset_token_from_email",
    "password": "NewPassword123!",
    "confirmPassword": "NewPassword123!"
  }
}

docs {
  title: "Reset Password"
  desc: "Reset user password using token received by email"
}
