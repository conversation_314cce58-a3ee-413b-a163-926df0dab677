meta {
  name: UpdateUser
  type: http
  seq: 3
}

put {
  url: {{base_url}}/api/v1/users/{{user_id}}
  body: json
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

body:json {
  {
    "full_name": "John Updated Doe",
    "status": "active",
    "role": "admin",
    "profile": {
      "avatar_url": "https://example.com/avatars/john_updated.jpg",
      "phone_number": "+84987654321",
      "address": "456 New Street",
      "city": "Hanoi",
      "country": "Vietnam",
      "bio": "Updated biography information"
    }
  }
}

docs {
  title: "Update User"
  desc: "Update a user's information (admin or owner)"
}

