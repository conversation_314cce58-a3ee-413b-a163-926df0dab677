meta {
  name: GetAllUsers
  type: http
  seq: 1
}

get {
  url: {{base_url}}/api/v1/users?limit=1
  body: none
  auth: none
}

params:query {
  limit: 1
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

docs {
  title: "Get All Users"
  desc: "Retrieve a list of users with pagination and filtering (admin only)

## Response Format

The API returns a standardized response with the following structure:

```json
{
  \"status\": {
    \"code\": 200,
    \"message\": \"Operation completed successfully\",
    \"success\": true,
    \"error_code\": \"\",
    \"path\": \"/api/v1/users\",
    \"timestamp\": \"2024-01-15T10:30:00Z\",
    \"details\": null
  },
  \"data\": [
    {
      \"user_id\": 1,
      \"tenant_id\": 1,
      \"username\": \"admin\",
      \"email\": \"<EMAIL>\",
      \"full_name\": \"Administrator\",
      \"status\": \"active\",
      \"last_login\": \"2024-01-15T09:30:00Z\",
      \"created_at\": \"2024-01-01T00:00:00Z\",
      \"updated_at\": \"2024-01-15T09:30:00Z\",
      \"is_email_verified\": true,
      \"user_type\": \"admin\"
    }
  ],
  \"meta\": {
    \"next_cursor\": \"eyJpZCI6MX0=\",
    \"has_more\": true
  }
}
```

## Key Points

- **data**: Contains the array of users directly (not nested under a \"users\" field)
- **meta**: Contains pagination metadata at the top level
- **Cursor-based pagination**: Use the `next_cursor` value for the next request
- **has_more**: Indicates if there are more results available"
}
