meta {
  name: GetUserById
  type: http
  seq: 2
}

get {
  url: {{base_url}}/api/v1/users/{{user_id}}
  body: none
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    // Extract user details
    const userId = responseJson.data.user_id;
    const username = responseJson.data.username;
    const email = responseJson.data.email;
    const userType = responseJson.data.user_type;
    
    // Save to environment variables
    bru.setEnvVar("last_user_id", userId);
    bru.setEnvVar("last_username", username);
    bru.setEnvVar("last_email", email);
    bru.setEnvVar("last_user_type", userType);
    
    console.log("User data saved:");
    console.log(`User ID: ${userId}`);
    console.log(`Username: ${username}`);
    console.log(`Email: ${email}`);
    console.log(`User Type: ${userType}`);
  }
}

docs {
  title: "Get User By ID"
  desc: "Retrieve a specific user by ID (admin or owner)"
}

