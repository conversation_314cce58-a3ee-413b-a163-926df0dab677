meta {
  name: CreateOrder
  type: http
  seq: 3
}

post {
  url: {{base_url}}/api/v1/orders
  body: json
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

body:json {
  {
    "items": [
      {
        "product_id": "PROD-001",
        "quantity": 1,
        "unit_price": 199.99
      },
      {
        "product_id": "PROD-002",
        "quantity": 2,
        "unit_price": 49.99
      }
    ],
    "shipping_address": {
      "full_name": "John Doe",
      "phone": "+84123456789",
      "address_line1": "123 Example Street",
      "address_line2": "Apt 4B",
      "city": "Ho Chi Minh",
      "state": "",
      "postal_code": "70000",
      "country": "Vietnam"
    },
    "payment_method": "credit_card",
    "coupon_code": "SUMMER10"
  }
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    // Extract order details
    const orderId = responseJson.data.order_id;
    const orderNumber = responseJson.data.order_number;
    const orderStatus = responseJson.data.status;
    const totalAmount = responseJson.data.total_amount;
    
    // Save to environment variables
    bru.setEnvVar("order_id", orderId);
    if (orderNumber) {
      bru.setEnvVar("order_number", orderNumber);
    }
    bru.setEnvVar("order_status", orderStatus);
    
    console.log("Order data saved:");
    console.log(`Order ID: ${orderId}`);
    if (orderNumber) {
      console.log(`Order Number: ${orderNumber}`);
    }
    console.log(`Order Status: ${orderStatus}`);
    console.log(`Total Amount: ${totalAmount}`);
  }
}

docs {
  title: "Create Order"
  desc: "Create a new order from cart items or specific products"
}

