meta {
  name: UpdateOrderStatus
  type: http
  seq: 4
}

patch {
  url: {{base_url}}/api/v1/orders/{{order_id}}/status
  body: json
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

body:json {
  {
    "status": "shipped",
    "tracking_number": "TRK123456789",
    "carrier": "DHL",
    "notes": "Package was shipped today"
  }
}

docs {
  title: "Update Order Status"
  desc: "Update the status of an existing order (admin only)"
}

