meta {
  name: GetAllOrders
  type: http
  seq: 1
}

get {
  url: {{base_url}}/api/v1/orders
  body: none
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

query {
  page: 1
  limit: 20
  status: all
  start_date: 2025-01-01
  end_date: 2025-06-01
  sort: created_at_desc
}

docs {
  title: "Get All Orders"
  desc: "Retrieve a list of orders with pagination and filtering (admin sees all, users see only their own)"
}

