meta {
  name: ProcessPayment
  type: http
  seq: 1
}

post {
  url: {{base_url}}/api/v1/payments/process
  body: json
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

body:json {
  {
    "order_id": "ORD-12345",
    "payment_method": "credit_card",
    "payment_details": {
      "card_number": "****************",
      "expiry_month": 12,
      "expiry_year": 2025,
      "cvv": "123",
      "card_holder_name": "<PERSON>"
    },
    "billing_address": {
      "full_name": "<PERSON>",
      "phone": "+84123456789",
      "address_line1": "123 Example Street",
      "address_line2": "Apt 4B",
      "city": "Ho Chi Minh",
      "state": "",
      "postal_code": "70000",
      "country": "Vietnam"
    }
  }
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    // Extract payment details
    const paymentId = responseJson.data.payment_id;
    const transactionId = responseJson.data.transaction_id;
    const orderId = responseJson.data.order_id;
    const status = responseJson.data.status;
    const amount = responseJson.data.amount;
    
    // Save to environment variables
    bru.setEnvVar("payment_id", paymentId);
    bru.setEnvVar("transaction_id", transactionId);
    bru.setEnvVar("payment_order_id", orderId);
    bru.setEnvVar("payment_status", status);
    
    console.log("Payment data saved:");
    console.log(`Payment ID: ${paymentId}`);
    console.log(`Transaction ID: ${transactionId}`);
    console.log(`Order ID: ${orderId}`);
    console.log(`Status: ${status}`);
    console.log(`Amount: ${amount}`);
  }
}

docs {
  title: "Process Payment"
  desc: "Process a payment for an order"
}

