meta {
  name: RefundPayment
  type: http
  seq: 3
}

post {
  url: {{base_url}}/api/v1/payments/{{payment_id}}/refund
  body: json
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

body:json {
  {
    "amount": 299.97,
    "reason": "Customer request",
    "full_refund": true
  }
}

docs {
  title: "Refund Payment"
  desc: "Process a refund for a completed payment (admin only)"
}

