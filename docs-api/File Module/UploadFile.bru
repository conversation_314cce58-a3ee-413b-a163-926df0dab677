meta {
  name: UploadFile
  type: http
  seq: 1
}

post {
  url: {{base_url}}/api/v1/files/upload
  body: multipartForm
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}



script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    // Extract file details
    const fileId = responseJson.data.file_id;
    const filePath = responseJson.data.file_path;
    const fileUrl = responseJson.data.url;
    const fileName = responseJson.data.file_name;
    
    // Save to environment variables
    bru.setEnvVar("file_id", fileId);
    bru.setEnvVar("file_path", filePath);
    bru.setEnvVar("file_url", fileUrl);
    bru.setEnvVar("file_name", fileName);
    
    console.log("File upload data saved:");
    console.log(`File ID: ${fileId}`);
    console.log(`File Path: ${filePath}`);
    console.log(`File URL: ${fileUrl}`);
    console.log(`File Name: ${fileName}`);
  }
}

docs {
  title: "Upload File"
  desc: "Upload a file to the server"
}

