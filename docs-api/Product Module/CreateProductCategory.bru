meta {
  name: CreateProductCategory
  type: http
  seq: 8
}

post {
  url: {{base_url}}/api/v1/products/categories
  body: json
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

body:json {
  {
    "name": "Smart Home",
    "description": "Smart home devices and accessories",
    "slug": "smart-home",
    "parentId": null,
    "image": "https://example.com/categories/smart-home.jpg",
    "isActive": true
  }
}

docs {
  title: "Create Product Category"
  desc: "Create a new product category (requires admin authentication)"
}
