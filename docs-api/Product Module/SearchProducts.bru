meta {
  name: SearchProducts
  type: http
  seq: 6
}

get {
  url: {{api_url}}/api/admin/v1/products/search
  body: none
  auth: none

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
  X-Tenant-ID: {{tenant_id}}
}
}

query {
  q: smartphone
  page: 1
  limit: 20
  category: electronics
  minPrice: 100
  maxPrice: 1000
  sort: price_asc
}

docs {
  title: "Search Products"
  desc: "Search for products by keyword with advanced filtering options"
}
