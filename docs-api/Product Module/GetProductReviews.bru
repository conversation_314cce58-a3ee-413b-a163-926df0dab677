meta {
  name: GetProductReviews
  type: http
  seq: 9
}

get {
  url: {{api_url}}/api/admin/v1/products/{{product_id}}/reviews
  body: none
  auth: none

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
  X-Tenant-ID: {{tenant_id}}
}
}

query {
  page: 1
  limit: 20
  sort: newest
}

docs {
  title: "Get Product Reviews"
  desc: "Retrieve all reviews for a specific product"
}
