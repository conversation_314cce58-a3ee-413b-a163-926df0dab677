meta {
  name: UpdateProduct
  type: http
  seq: 4
}

put {
  url: {{base_url}}/api/v1/products/{{product_id}}
  body: json
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

body:json {
  {
    "name": "Updated Product Name",
    "description": "Updated product description",
    "price": 89.99,
    "salePrice": 69.99,
    "categories": ["electronics", "gadgets", "new-category"],
    "images": [
      {
        "url": "https://example.com/new-image1.jpg",
        "isMain": true
      }
    ],
    "attributes": [
      {
        "name": "Color",
        "value": "Blue"
      },
      {
        "name": "Size",
        "value": "Large"
      }
    ],
    "sku": "PROD-12345-UPDATED",
    "stock": 75,
    "isActive": true
  }
}

docs {
  title: "Update Product"
  desc: "Update an existing product by ID (requires admin/seller authentication)"
}
