meta {
  name: CreateProductVariant
  type: http
  seq: 12
}

post {
  url: {{base_url}}/api/v1/products/{{product_id}}/variants
  body: json
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

body:json {
  {
    "sku": "PROD-12345-RED-L",
    "attributes": [
      {
        "name": "Color",
        "value": "Red"
      },
      {
        "name": "Size",
        "value": "Large"
      }
    ],
    "price": 109.99,
    "salePrice": 89.99,
    "stock": 25,
    "images": [
      "https://example.com/product-red-large-1.jpg",
      "https://example.com/product-red-large-2.jpg"
    ],
    "isActive": true
  }
}

docs {
  title: "Create Product Variant"
  desc: "Create a new variant for an existing product (requires admin/seller authentication)"
}
