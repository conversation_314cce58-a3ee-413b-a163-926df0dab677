meta {
  name: CreateProductReview
  type: http
  seq: 10
}

post {
  url: {{base_url}}/api/v1/products/{{product_id}}/reviews
  body: json
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

body:json {
  {
    "rating": 4.5,
    "title": "Great product!",
    "content": "This product exceeded my expectations. It's well built and works perfectly.",
    "images": [
      "https://example.com/review-image1.jpg",
      "https://example.com/review-image2.jpg"
    ]
  }
}

docs {
  title: "Create Product Review"
  desc: "Add a review for a product (requires authentication and purchase verification)"
}
