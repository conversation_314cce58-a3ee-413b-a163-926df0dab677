meta {
  name: CreateProduct
  type: http
  seq: 3
}

post {
  url: {{base_url}}/api/v1/products
  body: json
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

body:json {
  {
    "name": "Sample Product",
    "description": "This is a detailed description of the product",
    "price": 99.99,
    "salePrice": 79.99,
    "categories": ["electronics", "gadgets"],
    "images": [
      {
        "url": "https://example.com/image1.jpg",
        "isMain": true
      },
      {
        "url": "https://example.com/image2.jpg",
        "isMain": false
      }
    ],
    "attributes": [
      {
        "name": "Color",
        "value": "Black"
      },
      {
        "name": "Size",
        "value": "Medium"
      }
    ],
    "sku": "PROD-12345",
    "stock": 100,
    "isActive": true,
    "weight": 1.5,
    "dimensions": {
      "length": 10,
      "width": 5,
      "height": 2
    }
  }
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    // Extract product details
    const productId = responseJson.data.product_id;
    const productName = responseJson.data.name;
    const productSlug = responseJson.data.slug;
    
    // Save to environment variables
    bru.setEnvVar("product_id", productId);
    bru.setEnvVar("product_name", productName);
    if (productSlug) {
      bru.setEnvVar("product_slug", productSlug);
    }
    
    console.log("Product data saved:");
    console.log(`Product ID: ${productId}`);
    console.log(`Product Name: ${productName}`);
    if (productSlug) {
      console.log(`Product Slug: ${productSlug}`);
    }
  }
}

docs {
  title: "Create Product"
  desc: "Create a new product (requires admin/seller authentication)"
}
