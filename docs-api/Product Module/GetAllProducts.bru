meta {
  name: GetAllProducts
  type: http
  seq: 1
}

get {
  url: {{api_url}}/api/admin/v1/products
  body: none
  auth: none

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
  X-Tenant-ID: {{tenant_id}}
}
}

query {
  page: 1
  limit: 20
  category: 
  sort: newest
  minPrice: 
  maxPrice: 
  inStock: true
}

docs {
  title: "Get All Products"
  desc: "Retrieve a paginated list of products with optional filtering and sorting"
}
