# Bruno API Post-Response Scripts Reference

This document provides information about the post-response scripts used in the Bruno API collection. These scripts automatically extract data from API responses and store them as environment variables for use in subsequent requests.

## Purpose

The post-response scripts serve several purposes:
1. Extract important IDs and values from API responses
2. Store these values as environment variables
3. Allow referencing these values in subsequent API calls
4. Improve the testing workflow by reducing manual copying of IDs

## How It Works

Each script follows this general pattern:
1. Parse the response JSON
2. Check if the response was successful
3. Extract relevant data (IDs, names, statuses, etc.)
4. Save the data to environment variables using `bru.setEnvVar()`
5. Log information about what was saved

Example:
```javascript
script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    // Extract data
    const someId = responseJson.data.some_id;
    
    // Save to environment variables
    bru.setEnvVar("some_id", someId);
    
    // Log what was saved
    console.log("Data saved:");
    console.log(`Some ID: ${someId}`);
  }
}
```

## Environment Variables

The following environment variables are set by the post-response scripts:

### Authentication
- `access_token` - The JWT access token
- `refresh_token` - The refresh token
- `token_type` - The token type (usually "Bearer")
- `access_token_expires_at` - Timestamp when the access token expires
- `refresh_token_expires_at` - Timestamp when the refresh token expires

### User
- `last_user_id` - ID of the last retrieved or created user
- `last_username` - Username of the last retrieved user
- `last_email` - Email of the last retrieved user
- `last_user_type` - Type of the last retrieved user
- `registered_user_id` - ID of the newly registered user
- `registered_email` - Email of the newly registered user
- `registered_status` - Status of the newly registered user

### RBAC
- `last_tenant_id` - ID of the last tenant
- `last_role_id` - ID of the last role
- `last_role_code` - Code of the last role
- `role_id` - ID of the last created or retrieved role
- `role_code` - Code of the last created or retrieved role
- `role_name` - Name of the last created or retrieved role
- `updated_role_id` - ID of the updated role
- `updated_role_code` - Code of the updated role
- `updated_role_name` - Name of the updated role
- `deleted_role_id` - ID of the deleted role
- `permission_id` - ID of the last created or retrieved permission
- `permission_code` - Code of the last created or retrieved permission
- `permission_group_id` - ID of the permission group
- `permission_group_name` - Name of the permission group
- `updated_permission_id` - ID of the updated permission
- `updated_permission_code` - Code of the updated permission
- `deleted_permission_id` - ID of the deleted permission
- `deleted_permission_group_id` - ID of the deleted permission group
- `first_role_id` - ID of the first role in a list
- `first_role_code` - Code of the first role in a list
- `first_permission_id` - ID of the first permission in a list
- `first_permission_code` - Code of the first permission in a list
- `first_group_id` - ID of the first permission group in a list
- `first_group_name` - Name of the first permission group in a list
- `role_permissions_count` - Number of permissions assigned to a role
- `user_roles_count` - Number of roles assigned to a user
- `role_users_count` - Number of users assigned to a role
- `revoked_user_id` - ID of the user from whom a role was revoked
- `revoked_role_id` - ID of the role that was revoked
- `check_user_id` - ID of the user checked for a role
- `check_role_id` - ID of the role checked for a user
- `user_has_role` - Boolean indicating if a user has a specific role
- `role_id` - ID of the last created or retrieved role
- `role_code` - Code of the last created or retrieved role
- `role_name` - Name of the last created or retrieved role
- `updated_role_id` - ID of the updated role
- `updated_role_code` - Code of the updated role
- `updated_role_name` - Name of the updated role
- `deleted_role_id` - ID of the deleted role
- `permission_id` - ID of the last created or retrieved permission
- `permission_code` - Code of the last created or retrieved permission
- `permission_group_id` - ID of the permission group
- `permission_group_name` - Name of the permission group
- `updated_permission_id` - ID of the updated permission
- `updated_permission_code` - Code of the updated permission
- `deleted_permission_id` - ID of the deleted permission
- `deleted_permission_group_id` - ID of the deleted permission group
- `first_role_id` - ID of the first role in a list
- `first_role_code` - Code of the first role in a list
- `first_permission_id` - ID of the first permission in a list
- `first_permission_code` - Code of the first permission in a list
- `first_group_id` - ID of the first permission group in a list
- `first_group_name` - Name of the first permission group in a list
- `role_permissions_count` - Number of permissions assigned to a role
- `user_roles_count` - Number of roles assigned to a user
- `role_users_count` - Number of users assigned to a role
- `revoked_user_id` - ID of the user from whom a role was revoked
- `revoked_role_id` - ID of the role that was revoked
- `check_user_id` - ID of the user checked for a role
- `check_role_id` - ID of the role checked for a user
- `user_has_role` - Boolean indicating if a user has a specific role

### Product
- `product_id` - ID of the last created or retrieved product
- `product_name` - Name of the last product
- `product_slug` - Slug of the last product
- `last_product_id` - ID of a product referenced in other operations

### Cart
- `cart_id` - ID of the current cart
- `item_id` - ID of the last added/updated cart item

### Order
- `order_id` - ID of the last created order
- `order_number` - Number/reference of the last order
- `order_status` - Status of the last order

### Payment
- `payment_id` - ID of the last payment
- `transaction_id` - ID of the last transaction
- `payment_order_id` - Order ID associated with the payment
- `payment_status` - Status of the last payment

### File
- `file_id` - ID of the last uploaded file
- `file_path` - Path of the last uploaded file
- `file_url` - URL of the last uploaded file
- `file_name` - Name of the last uploaded file

### Notification
- `notification_id` - ID of the last notification
- `notification_template_code` - Template code of the last notification
- `notification_is_read` - Boolean indicating if the notification is read
- `notification_unread_count` - Count of unread notifications
- `notifications_marked_read_count` - Count of notifications marked as read in bulk
- `deleted_notification_id` - ID of the deleted notification
- `notification_email_enabled` - Boolean indicating if email notifications are enabled
- `notification_push_enabled` - Boolean indicating if push notifications are enabled
- `notification_inapp_enabled` - Boolean indicating if in-app notifications are enabled

## Usage in Requests

To use these environment variables in your requests, reference them using the `{{variable_name}}` syntax:

```
GET {{api_url}}/api/admin/v1/users/{{last_user_id}}
```

```json
{
  "order_id": "{{order_id}}",
  "payment_method": "credit_card"
}
```

## Modules With Post-Response Scripts

The following Bruno API files have post-response scripts implemented:

1. Auth Module:
   - `Dang Nhap.bru` - Login
   - `Register.bru` - User registration

2. RBAC Module:
   - `AssignUserRole.bru` - Assign a role to a user
   - `CreateRole.bru` - Create a new role
   - `GetRole.bru` - Get role details
   - `UpdateRole.bru` - Update role details
   - `DeleteRole.bru` - Delete a role
   - `ListRoles.bru` - List all roles
   - `CreatePermission.bru` - Create a new permission
   - `GetPermission.bru` - Get permission details
   - `UpdatePermission.bru` - Update permission details
   - `DeletePermission.bru` - Delete a permission
   - `ListPermissions.bru` - List all permissions
   - `CreatePermissionGroup.bru` - Create a new permission group
   - `GetPermissionGroup.bru` - Get permission group details
   - `UpdatePermissionGroup.bru` - Update permission group details
   - `DeletePermissionGroup.bru` - Delete a permission group
   - `ListPermissionGroups.bru` - List all permission groups
   - `GetRolePermissions.bru` - Get permissions of a role
   - `GetUserRoles.bru` - Get roles of a user
   - `GetUsersWithRole.bru` - Get users with a specific role
   - `RevokeUserRole.bru` - Revoke a role from a user
   - `CheckUserRole.bru` - Check if a user has a specific role

3. Cart Module:
   - `AddToCart.bru` - Add a product to cart

4. Product Module:
   - `CreateProduct.bru` - Create a new product

5. Order Module:
   - `CreateOrder.bru` - Create a new order

6. Payment Module:
   - `ProcessPayment.bru` - Process a payment

7. User Module:
   - `GetUserById.bru` - Get user information

8. File Module:
   - `UploadFile.bru` - Upload a file

9. Notification Module:
   - `GetAllNotifications.bru` - Get all notifications
   - `GetNotificationById.bru` - Get notification details
   - `MarkAsRead.bru` - Mark notification as read
   - `MarkAllAsRead.bru` - Mark all notifications as read
   - `DeleteNotification.bru` - Delete notification
   - `GetUnreadCount.bru` - Get unread notifications count
   - `UpdateNotificationSettings.bru` - Update notification settings
