meta {
  name: User - Get Tenant by Code
  type: http
  seq: 7
}

get {
  url: {{base_url}}/api/v1/tenants/{{tenant_code}}
  body: none
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    // Extract tenant details
    const tenant = responseJson.data;
    
    console.log("Tenant details retrieved successfully:");
    console.log(`Tenant ID: ${tenant.id}`);
    console.log(`Tenant Code: ${tenant.tenant_code}`);
    console.log(`Tenant Name: ${tenant.tenant_name}`);
    console.log(`Status: ${tenant.status}`);
    console.log(`Plan Type: ${tenant.plan_type}`);
    console.log(`Created At: ${tenant.created_at}`);
    console.log(`Updated At: ${tenant.updated_at}`);
    
    if (tenant.subscription_expires_at) {
      console.log(`Subscription Expires At: ${tenant.subscription_expires_at}`);
    }
    
    // Update environment variables with current tenant info
    bru.setEnvVar("tenant_id", tenant.id);
    bru.setEnvVar("tenant_code", tenant.tenant_code);
  } else {
    console.log("Failed to retrieve tenant details");
    if (responseJson.status) {
      console.log(`Error: ${responseJson.status.message}`);
      console.log(`Error Code: ${responseJson.status.error_code}`);
    }
  }
}

docs {
  title: "Lấy thông tin tenant theo code (User)"
  desc: "Lấy thông tin chi tiết của một tenant theo tenant code. User chỉ có thể truy cập thông tin của các tenants mà họ có quyền.
  
  **Authentication:** Bearer token required
  
  **Path parameters:**
  - `tenant_code`: Code của tenant cần lấy thông tin (ví dụ: 'abc-corp')
  
  **Multi-tenant context:**
  - Endpoint này kiểm tra quyền truy cập của user đối với tenant
  - Chỉ trả về thông tin nếu user có quyền đọc tenant
  - Tenant code là unique identifier thân thiện với người dùng
  
  **Success Response (200):**
  ```json
  {
    \"status\": {
      \"code\": 200,
      \"message\": \"Operation completed successfully\",
      \"success\": true
    },
    \"data\": {
      \"id\": 1,
      \"tenant_name\": \"Công ty ABC\",
      \"tenant_code\": \"abc-corp\",
      \"status\": \"active\",
      \"plan_type\": \"premium\",
      \"created_at\": \"2024-01-01T00:00:00Z\",
      \"updated_at\": \"2024-01-01T00:00:00Z\",
      \"subscription_expires_at\": \"2024-12-31T23:59:59Z\"
    }
  }
  ```
  
  **Error Response (404):**
  ```json
  {
    \"status\": {
      \"code\": 404,
      \"message\": \"Không tìm thấy tenant\",
      \"success\": false,
      \"error_code\": \"TENANT_NOT_FOUND\",
      \"path\": \"/api/v1/tenants/invalid-code\",
      \"timestamp\": \"2024-01-01T00:00:00Z\"
    }
  }
  ```
  
  **Error Response (403):**
  ```json
  {
    \"status\": {
      \"code\": 403,
      \"message\": \"Không có quyền truy cập tenant này\",
      \"success\": false,
      \"error_code\": \"PERMISSION_DENIED\",
      \"path\": \"/api/v1/tenants/restricted-tenant\",
      \"timestamp\": \"2024-01-01T00:00:00Z\"
    }
  }
  ```
  
  **Error Response (401):**
  ```json
  {
    \"status\": {
      \"code\": 401,
      \"message\": \"Token đã hết hạn\",
      \"success\": false,
      \"error_code\": \"TOKEN_EXPIRED\"
    }
  }
  ```"
}
