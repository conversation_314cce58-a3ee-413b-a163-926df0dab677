meta {
  name: Error Examples - Forbidden
  type: http
  seq: 10
}

delete {
  url: {{base_url}}/api/v1/tenants/1
  body: none
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
  X-User-Role: user
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  console.log("=== FORBIDDEN ERROR EXAMPLE ===");
  console.log(`Status Code: ${response.status}`);
  console.log(`Response:`, JSON.stringify(responseJson, null, 2));
  
  if (responseJson.status) {
    console.log(`Error Code: ${responseJson.status.error_code}`);
    console.log(`Message: ${responseJson.status.message}`);
    console.log(`Success: ${responseJson.status.success}`);
    console.log(`Path: ${responseJson.status.path}`);
    console.log(`Timestamp: ${responseJson.status.timestamp}`);
  }
}

docs {
  title: "Ví dụ lỗi Forbidden (403)"
  desc: "Endpoint này minh họa lỗi phân quyền khi user có token hợp lệ nhưng không có quyền thực hiện thao tác.
  
  **Mục đích:** Demonstration endpoint để hiểu cách xử lý lỗi authorization
  
  **Scenario:** User thường cố gắng xóa tenant (chỉ admin mới có quyền)
  
  **Expected Error Response (403):**
  ```json
  {
    \"status\": {
      \"code\": 403,
      \"message\": \"Không có quyền thực hiện thao tác này\",
      \"success\": false,
      \"error_code\": \"PERMISSION_DENIED\",
      \"path\": \"/api/v1/tenants/1\",
      \"timestamp\": \"2024-01-01T00:00:00Z\",
      \"details\": {
        \"required_permission\": \"tenants.delete\",
        \"user_permissions\": [\"tenants.read\"]
      }
    },
    \"data\": null
  }
  ```
  
  **Các loại authorization errors:**
  
  1. **PERMISSION_DENIED (403):**
     - User không có quyền cụ thể (ví dụ: tenants.delete)
     - Role của user không được phép thực hiện thao tác
     
  2. **INSUFFICIENT_ROLE (403):**
     - Role của user thấp hơn yêu cầu
     - Ví dụ: user role cố truy cập admin endpoint
     
  3. **TENANT_ACCESS_DENIED (403):**
     - User không có quyền truy cập tenant cụ thể
     - Multi-tenant isolation violation
     
  4. **RESOURCE_OWNER_ONLY (403):**
     - Chỉ owner của resource mới có quyền
     - Ví dụ: chỉ người tạo mới có quyền xóa
  
  **Permission system trong WNAPI:**
  - Sử dụng format: `module.action.resource`
  - Ví dụ: `tenants.create`, `tenants.read`, `tenants.update`, `tenants.delete`
  - Middleware kiểm tra quyền trước khi thực hiện thao tác
  - Multi-tenant: quyền được kiểm tra trong context của tenant
  
  **Cách xử lý:**
  - Hiển thị thông báo lỗi phù hợp
  - Ẩn các chức năng user không có quyền
  - Redirect về trang phù hợp với role của user"
}
