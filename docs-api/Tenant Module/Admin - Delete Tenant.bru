meta {
  name: Admin - Delete Tenant
  type: http
  seq: 5
}

delete {
  url: {{base_url}}/api/v1/tenants/{{tenant_id}}
  body: none
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true) {
    console.log("Tenant deletion successful:");
    console.log(`Tenant ID ${bru.getEnvVar("tenant_id")} has been deleted`);
    console.log(`Message: ${responseJson.status.message}`);
    
    // Clear tenant-related environment variables
    bru.setEnvVar("tenant_id", "");
    bru.setEnvVar("tenant_code", "");
  } else {
    console.log("Failed to delete tenant");
    if (responseJson.status) {
      console.log(`Error: ${responseJson.status.message}`);
      console.log(`Error Code: ${responseJson.status.error_code}`);
    }
  }
}

docs {
  title: "Xóa tenant (Admin)"
  desc: "<PERSON><PERSON>a một tenant khỏi hệ thống. Đ<PERSON>y là thao tác nguy hiểm và không thể hoàn tác. Chỉ admin mới có quyền thực hiện.
  
  **Quyền yêu cầu:** `tenants.delete`
  
  **Path parameters:**
  - `tenant_id`: ID của tenant cần xóa
  
  **Lưu ý quan trọng:**
  - Thao tác này sẽ xóa vĩnh viễn tenant và tất cả dữ liệu liên quan
  - Không thể hoàn tác sau khi thực hiện
  - Cần cân nhắc kỹ trước khi thực hiện
  - Nên backup dữ liệu trước khi xóa
  
  **Success Response (200):**
  ```json
  {
    \"status\": {
      \"code\": 200,
      \"message\": \"Tenant deleted successfully\",
      \"success\": true,
      \"path\": \"/api/v1/tenants/1\",
      \"timestamp\": \"2024-01-01T00:00:00Z\"
    },
    \"data\": null
  }
  ```
  
  **Error Response (404):**
  ```json
  {
    \"status\": {
      \"code\": 404,
      \"message\": \"Không tìm thấy tenant\",
      \"success\": false,
      \"error_code\": \"TENANT_NOT_FOUND\",
      \"path\": \"/api/v1/tenants/999\",
      \"timestamp\": \"2024-01-01T00:00:00Z\"
    }
  }
  ```
  
  **Error Response (403):**
  ```json
  {
    \"status\": {
      \"code\": 403,
      \"message\": \"Không có quyền xóa tenant\",
      \"success\": false,
      \"error_code\": \"PERMISSION_DENIED\",
      \"path\": \"/api/v1/tenants/1\",
      \"timestamp\": \"2024-01-01T00:00:00Z\"
    }
  }
  ```
  
  **Error Response (409):**
  ```json
  {
    \"status\": {
      \"code\": 409,
      \"message\": \"Không thể xóa tenant có dữ liệu liên quan\",
      \"success\": false,
      \"error_code\": \"TENANT_HAS_DEPENDENCIES\",
      \"path\": \"/api/v1/tenants/1\",
      \"timestamp\": \"2024-01-01T00:00:00Z\",
      \"details\": {
        \"dependencies\": [\"users\", \"orders\", \"products\"]
      }
    }
  }
  ```"
}
