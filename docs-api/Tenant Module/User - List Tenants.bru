meta {
  name: User - List Tenants
  type: http
  seq: 6
}

get {
  url: {{base_url}}/api/v1/tenants?limit=10&cursor=
  body: none
  auth: none
}

params:query {
  limit: 10
  cursor: 
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    console.log("User accessible tenants retrieved successfully:");
    console.log(`Total accessible tenants: ${responseJson.data.length}`);
    
    // Save pagination info if available
    if (responseJson.meta) {
      if (responseJson.meta.next_cursor) {
        bru.setEnvVar("next_cursor", responseJson.meta.next_cursor);
        console.log(`Next cursor: ${responseJson.meta.next_cursor}`);
      }
      console.log(`Has more: ${responseJson.meta.has_more}`);
    }
    
    // Save first tenant info for other requests
    if (responseJson.data.length > 0) {
      const firstTenant = responseJson.data[0];
      bru.setEnvVar("tenant_id", firstTenant.id);
      bru.setEnvVar("tenant_code", firstTenant.tenant_code);
      console.log(`First accessible tenant ID: ${firstTenant.id}`);
      console.log(`First accessible tenant code: ${firstTenant.tenant_code}`);
    }
    
    // Log all accessible tenants
    responseJson.data.forEach((tenant, index) => {
      console.log(`Tenant ${index + 1}: ${tenant.tenant_name} (${tenant.tenant_code}) - ${tenant.status}`);
    });
  } else {
    console.log("Failed to retrieve accessible tenants");
    if (responseJson.status) {
      console.log(`Error: ${responseJson.status.message}`);
    }
  }
}

docs {
  title: "Danh sách tenants có quyền truy cập (User)"
  desc: "Lấy danh sách các tenants mà user hiện tại có quyền truy cập. Endpoint này chỉ trả về các tenants mà user có quyền.
  
  **Authentication:** Bearer token required
  
  **Tham số query:**
  - `limit`: Số lượng tenants tối đa trả về (mặc định: 10, tối đa: 100)
  - `cursor`: Cursor để phân trang (để trống cho trang đầu tiên)
  
  **Multi-tenant context:**
  - Endpoint này tự động lọc tenants dựa trên quyền của user
  - Chỉ trả về tenants mà user có ít nhất quyền đọc
  - Kết quả được sắp xếp theo thời gian tạo (mới nhất trước)
  
  **Success Response (200):**
  ```json
  {
    \"status\": {
      \"code\": 200,
      \"message\": \"Operation completed successfully\",
      \"success\": true
    },
    \"data\": [
      {
        \"id\": 1,
        \"tenant_name\": \"Công ty ABC\",
        \"tenant_code\": \"abc-corp\",
        \"status\": \"active\",
        \"plan_type\": \"premium\",
        \"created_at\": \"2024-01-01T00:00:00Z\",
        \"updated_at\": \"2024-01-01T00:00:00Z\",
        \"subscription_expires_at\": \"2024-12-31T23:59:59Z\"
      }
    ],
    \"meta\": {
      \"next_cursor\": \"eyJpZCI6MX0=\",
      \"has_more\": false
    }
  }
  ```
  
  **Error Response (401):**
  ```json
  {
    \"status\": {
      \"code\": 401,
      \"message\": \"Không tìm thấy token xác thực\",
      \"success\": false,
      \"error_code\": \"TOKEN_NOT_FOUND\"
    }
  }
  ```"
}
