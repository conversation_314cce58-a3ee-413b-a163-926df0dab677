meta {
  name: Admin - Get Tenant
  type: http
  seq: 3
}

get {
  url: {{base_url}}/api/v1/tenants/{{tenant_id}}
  body: none
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    // Extract tenant details
    const tenant = responseJson.data;
    
    console.log("Tenant details retrieved successfully:");
    console.log(`Tenant ID: ${tenant.id}`);
    console.log(`Tenant Code: ${tenant.tenant_code}`);
    console.log(`Tenant Name: ${tenant.tenant_name}`);
    console.log(`Status: ${tenant.status}`);
    console.log(`Plan Type: ${tenant.plan_type}`);
    console.log(`Created At: ${tenant.created_at}`);
    console.log(`Updated At: ${tenant.updated_at}`);
    
    if (tenant.subscription_expires_at) {
      console.log(`Subscription Expires At: ${tenant.subscription_expires_at}`);
    }
  } else {
    console.log("Failed to retrieve tenant details");
    if (responseJson.status) {
      console.log(`Error: ${responseJson.status.message}`);
      console.log(`Error Code: ${responseJson.status.error_code}`);
    }
  }
}

docs {
  title: "Lấy thông tin chi tiết tenant (Admin)"
  desc: "Lấy thông tin chi tiết của một tenant theo ID. Chỉ admin mới có quyền truy cập endpoint này.
  
  **Quyền yêu cầu:** `tenants.read`
  
  **Path parameters:**
  - `tenant_id`: ID của tenant cần lấy thông tin
  
  **Success Response (200):**
  ```json
  {
    \"status\": {
      \"code\": 200,
      \"message\": \"Operation completed successfully\",
      \"success\": true
    },
    \"data\": {
      \"id\": 1,
      \"tenant_name\": \"Công ty ABC\",
      \"tenant_code\": \"abc-corp\",
      \"status\": \"active\",
      \"plan_type\": \"premium\",
      \"created_at\": \"2024-01-01T00:00:00Z\",
      \"updated_at\": \"2024-01-01T00:00:00Z\",
      \"subscription_expires_at\": \"2024-12-31T23:59:59Z\"
    }
  }
  ```
  
  **Error Response (404):**
  ```json
  {
    \"status\": {
      \"code\": 404,
      \"message\": \"Không tìm thấy tenant\",
      \"success\": false,
      \"error_code\": \"TENANT_NOT_FOUND\",
      \"path\": \"/api/v1/tenants/999\",
      \"timestamp\": \"2024-01-01T00:00:00Z\"
    }
  }
  ```
  
  **Error Response (403):**
  ```json
  {
    \"status\": {
      \"code\": 403,
      \"message\": \"Không có quyền truy cập\",
      \"success\": false,
      \"error_code\": \"PERMISSION_DENIED\",
      \"path\": \"/api/v1/tenants/1\",
      \"timestamp\": \"2024-01-01T00:00:00Z\"
    }
  }
  ```"
}
