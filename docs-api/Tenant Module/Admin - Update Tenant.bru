meta {
  name: Admin - Update Tenant
  type: http
  seq: 4
}

put {
  url: {{base_url}}/api/v1/tenants/{{tenant_id}}
  body: json
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

body:json {
  {
    "tenant_name": "Công ty ABC - Updated",
    "status": "active",
    "plan_type": "enterprise",
    "subscription_expires_at": "2025-12-31T23:59:59Z"
  }
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    // Extract updated tenant details
    const tenant = responseJson.data;
    
    console.log("Tenant update successful:");
    console.log(`Tenant ID: ${tenant.id}`);
    console.log(`Tenant Code: ${tenant.tenant_code}`);
    console.log(`Tenant Name: ${tenant.tenant_name}`);
    console.log(`Status: ${tenant.status}`);
    console.log(`Plan Type: ${tenant.plan_type}`);
    console.log(`Updated At: ${tenant.updated_at}`);
    
    if (tenant.subscription_expires_at) {
      console.log(`Subscription Expires At: ${tenant.subscription_expires_at}`);
    }
  } else {
    console.log("Failed to update tenant");
    if (responseJson.status) {
      console.log(`Error: ${responseJson.status.message}`);
      console.log(`Error Code: ${responseJson.status.error_code}`);
      if (responseJson.status.details) {
        console.log("Validation errors:", responseJson.status.details);
      }
    }
  }
}

docs {
  title: "Cập nhật thông tin tenant (Admin)"
  desc: "Cập nhật thông tin của một tenant. Chỉ admin mới có quyền thực hiện thao tác này.
  
  **Quyền yêu cầu:** `tenants.update`
  
  **Path parameters:**
  - `tenant_id`: ID của tenant cần cập nhật
  
  **Request body (tất cả fields đều optional):**
  ```json
  {
    \"tenant_name\": \"string (optional)\",
    \"status\": \"enum (optional: active|inactive|suspended|trial)\",
    \"plan_type\": \"string (optional)\",
    \"subscription_expires_at\": \"datetime (optional)\"
  }
  ```
  
  **Lưu ý:**
  - `tenant_code` không thể thay đổi sau khi tạo
  - Chỉ cần gửi các fields muốn cập nhật
  - Validation rules tương tự như khi tạo tenant
  
  **Success Response (200):**
  ```json
  {
    \"status\": {
      \"code\": 200,
      \"message\": \"Operation completed successfully\",
      \"success\": true
    },
    \"data\": {
      \"id\": 1,
      \"tenant_name\": \"Công ty ABC - Updated\",
      \"tenant_code\": \"abc-corp\",
      \"status\": \"active\",
      \"plan_type\": \"enterprise\",
      \"created_at\": \"2024-01-01T00:00:00Z\",
      \"updated_at\": \"2024-01-02T00:00:00Z\",
      \"subscription_expires_at\": \"2025-12-31T23:59:59Z\"
    }
  }
  ```
  
  **Error Response (400):**
  ```json
  {
    \"status\": {
      \"code\": 400,
      \"message\": \"Validation failed\",
      \"success\": false,
      \"error_code\": \"VALIDATION_FAILED\",
      \"details\": [
        {
          \"field\": \"status\",
          \"message\": \"Invalid status value\"
        }
      ]
    }
  }
  ```
  
  **Error Response (404):**
  ```json
  {
    \"status\": {
      \"code\": 404,
      \"message\": \"Không tìm thấy tenant\",
      \"success\": false,
      \"error_code\": \"TENANT_NOT_FOUND\"
    }
  }
  ```"
}
