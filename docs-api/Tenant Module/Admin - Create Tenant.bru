meta {
  name: Admin - Create Tenant
  type: http
  seq: 2
}

post {
  url: {{base_url}}/api/v1/tenants
  body: json
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

body:json {
  {
    "tenant_name": "Công ty XYZ",
    "tenant_code": "xyz-corp",
    "status": "active",
    "plan_type": "premium",
    "subscription_expires_at": "2024-12-31T23:59:59Z"
  }
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    // Extract tenant details
    const tenantId = responseJson.data.id;
    const tenantCode = responseJson.data.tenant_code;
    const tenantName = responseJson.data.tenant_name;
    const status = responseJson.data.status;
    const planType = responseJson.data.plan_type;
    
    // Save to environment variables
    bru.setEnvVar("tenant_id", tenantId);
    bru.setEnvVar("tenant_code", tenantCode);
    
    console.log("Tenant creation successful:");
    console.log(`Tenant ID: ${tenantId}`);
    console.log(`Tenant Code: ${tenantCode}`);
    console.log(`Tenant Name: ${tenantName}`);
    console.log(`Status: ${status}`);
    console.log(`Plan Type: ${planType}`);
  } else {
    console.log("Failed to create tenant");
    if (responseJson.status) {
      console.log(`Error: ${responseJson.status.message}`);
      if (responseJson.status.details) {
        console.log("Validation errors:", responseJson.status.details);
      }
    }
  }
}

docs {
  title: "Tạo tenant mới (Admin)"
  desc: "Tạo một tenant mới trong hệ thống. Chỉ admin mới có quyền thực hiện thao tác này.
  
  **Quyền yêu cầu:** `tenants.create`
  
  **Request body:**
  ```json
  {
    \"tenant_name\": \"string (required)\",
    \"tenant_code\": \"string (required, alphanum, unique)\",
    \"status\": \"enum (required: active|inactive|suspended|trial)\",
    \"plan_type\": \"string (required)\",
    \"subscription_expires_at\": \"datetime (optional)\"
  }
  ```
  
  **Validation rules:**
  - `tenant_name`: Bắt buộc, tối đa 255 ký tự
  - `tenant_code`: Bắt buộc, chỉ chứa chữ và số, duy nhất trong hệ thống
  - `status`: Bắt buộc, một trong các giá trị: active, inactive, suspended, trial
  - `plan_type`: Bắt buộc, loại gói dịch vụ
  - `subscription_expires_at`: Tùy chọn, thời gian hết hạn subscription
  
  **Success Response (201):**
  ```json
  {
    \"status\": {
      \"code\": 201,
      \"message\": \"Resource created successfully\",
      \"success\": true
    },
    \"data\": {
      \"id\": 1,
      \"tenant_name\": \"Công ty XYZ\",
      \"tenant_code\": \"xyz-corp\",
      \"status\": \"active\",
      \"plan_type\": \"premium\",
      \"created_at\": \"2024-01-01T00:00:00Z\",
      \"updated_at\": \"2024-01-01T00:00:00Z\",
      \"subscription_expires_at\": \"2024-12-31T23:59:59Z\"
    }
  }
  ```
  
  **Error Response (400):**
  ```json
  {
    \"status\": {
      \"code\": 400,
      \"message\": \"Validation failed\",
      \"success\": false,
      \"error_code\": \"VALIDATION_FAILED\",
      \"details\": [
        {
          \"field\": \"tenant_code\",
          \"message\": \"Tenant code already exists\"
        }
      ]
    }
  }
  ```"
}
