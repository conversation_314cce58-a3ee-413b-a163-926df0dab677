meta {
  name: Error Examples - Unauthorized
  type: http
  seq: 9
}

get {
  url: {{api_url}}/api/admin/v1/tenants
  body: none
  auth: none
}

headers {
  Content-Type: application/json
  X-Tenant-ID: {{tenant_id}}

  Authorization: Bearer {{access_token}}
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  console.log("=== UNAUTHORIZED ERROR EXAMPLE ===");
  console.log(`Status Code: ${response.status}`);
  console.log(`Response:`, JSON.stringify(responseJson, null, 2));
  
  if (responseJson.status) {
    console.log(`Error Code: ${responseJson.status.error_code}`);
    console.log(`Message: ${responseJson.status.message}`);
    console.log(`Success: ${responseJson.status.success}`);
    console.log(`Path: ${responseJson.status.path}`);
    console.log(`Timestamp: ${responseJson.status.timestamp}`);
  }
}

docs {
  title: "<PERSON><PERSON> dụ lỗi Unauthorized (401)"
  desc: "Endpoint này minh họa lỗi xác thực khi truy cập API mà không có token hoặc token không hợp lệ.
  
  **Mục đích:** Demonstration endpoint để hiểu cách xử lý lỗi authentication
  
  **Scenario:** Truy cập admin endpoint mà không có Authorization header
  
  **Expected Error Response (401):**
  ```json
  {
    \"status\": {
      \"code\": 401,
      \"message\": \"Không tìm thấy token xác thực\",
      \"success\": false,
      \"error_code\": \"TOKEN_NOT_FOUND\",
      \"path\": \"/api/admin/v1/tenants\",
      \"timestamp\": \"2024-01-01T00:00:00Z\"
    },
    \"data\": null
  }
  ```
  
  **Các loại authentication errors:**
  
  1. **TOKEN_NOT_FOUND (401):**
     - Không có Authorization header
     - Header không bắt đầu bằng 'Bearer '
     
  2. **TOKEN_EXPIRED (401):**
     - Token đã hết hạn
     - Cần refresh token hoặc đăng nhập lại
     
  3. **INVALID_TOKEN (401):**
     - Token không đúng định dạng
     - Token bị thay đổi hoặc không hợp lệ
     - Signature không khớp
     
  4. **TOKEN_REVOKED (401):**
     - Token đã bị thu hồi
     - User đã logout hoặc token bị blacklist
  
  **Cách xử lý:**
  - Client nên redirect về trang login
  - Hoặc tự động refresh token nếu có refresh token
  - Hiển thị thông báo lỗi phù hợp cho user"
}
