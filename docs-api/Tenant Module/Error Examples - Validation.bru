meta {
  name: Error Examples - Validation
  type: http
  seq: 8
}

post {
  url: {{base_url}}/api/v1/tenants
  body: json
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

body:json {
  {
    "tenant_name": "",
    "tenant_code": "invalid-code-with-special-chars!@#",
    "status": "invalid_status",
    "plan_type": ""
  }
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  console.log("=== VALIDATION ERROR EXAMPLE ===");
  console.log(`Status Code: ${response.status}`);
  console.log(`Response:`, JSON.stringify(responseJson, null, 2));
  
  if (responseJson.status) {
    console.log(`Error Code: ${responseJson.status.error_code}`);
    console.log(`Message: ${responseJson.status.message}`);
    console.log(`Success: ${responseJson.status.success}`);
    
    if (responseJson.status.details) {
      console.log("Validation Details:");
      responseJson.status.details.forEach((detail, index) => {
        console.log(`  ${index + 1}. Field: ${detail.field || 'N/A'}, Message: ${detail.message}`);
      });
    }
  }
}

docs {
  title: "Ví dụ lỗi Validation"
  desc: "Endpoint này minh họa các lỗi validation thường gặp khi tạo tenant với dữ liệu không hợp lệ.
  
  **Mục đích:** Demonstration endpoint để hiểu cách xử lý lỗi validation
  
  **Request body có lỗi:**
  ```json
  {
    \"tenant_name\": \"\",                                    // Lỗi: Bắt buộc
    \"tenant_code\": \"invalid-code-with-special-chars!@#\",  // Lỗi: Chỉ cho phép chữ và số
    \"status\": \"invalid_status\",                           // Lỗi: Giá trị không hợp lệ
    \"plan_type\": \"\"                                       // Lỗi: Bắt buộc
  }
  ```
  
  **Expected Error Response (400):**
  ```json
  {
    \"status\": {
      \"code\": 400,
      \"message\": \"Validation failed\",
      \"success\": false,
      \"error_code\": \"VALIDATION_FAILED\",
      \"path\": \"/api/v1/tenants\",
      \"timestamp\": \"2024-01-01T00:00:00Z\",
      \"details\": [
        {
          \"field\": \"tenant_name\",
          \"message\": \"Tenant name is required\"
        },
        {
          \"field\": \"tenant_code\",
          \"message\": \"Tenant code must contain only alphanumeric characters\"
        },
        {
          \"field\": \"status\",
          \"message\": \"Status must be one of: active, inactive, suspended, trial\"
        },
        {
          \"field\": \"plan_type\",
          \"message\": \"Plan type is required\"
        }
      ]
    },
    \"data\": null
  }
  ```
  
  **Các loại validation errors phổ biến:**
  1. **Required fields:** Các trường bắt buộc bị thiếu
  2. **Format validation:** Định dạng không đúng (email, URL, etc.)
  3. **Length validation:** Độ dài không hợp lệ
  4. **Enum validation:** Giá trị không nằm trong danh sách cho phép
  5. **Unique validation:** Giá trị đã tồn tại trong hệ thống
  6. **Pattern validation:** Không khớp với pattern yêu cầu"
}
