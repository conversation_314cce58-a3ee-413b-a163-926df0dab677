meta {
  name: Admin - List Tenants
  type: http
  seq: 1
}

get {
  url: {{base_url}}/api/v1/tenants?limit=10&cursor=&sort_field=created_at&sort_order=desc
  body: none
  auth: none
}

params:query {
  limit: 10
  cursor: 
  sort_field: created_at
  sort_order: desc
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    console.log("Tenants list retrieved successfully:");
    console.log(`Total tenants: ${responseJson.data.length}`);
    
    // Save pagination info if available
    if (responseJson.meta) {
      if (responseJson.meta.next_cursor) {
        bru.setEnvVar("next_cursor", responseJson.meta.next_cursor);
        console.log(`Next cursor: ${responseJson.meta.next_cursor}`);
      }
      console.log(`Has more: ${responseJson.meta.has_more}`);
    }
    
    // Save first tenant info for other requests
    if (responseJson.data.length > 0) {
      const firstTenant = responseJson.data[0];
      bru.setEnvVar("tenant_id", firstTenant.id);
      bru.setEnvVar("tenant_code", firstTenant.tenant_code);
      console.log(`First tenant ID: ${firstTenant.id}`);
      console.log(`First tenant code: ${firstTenant.tenant_code}`);
    }
  } else {
    console.log("Failed to retrieve tenants list");
    if (responseJson.status) {
      console.log(`Error: ${responseJson.status.message}`);
    }
  }
}

docs {
  title: "Danh sách tenants (Admin)"
  desc: "Lấy danh sách tất cả tenants với phân trang cursor-based. Chỉ admin mới có quyền truy cập endpoint này.
  
  **Tham số query:**
  - `limit`: Số lượng tenants tối đa trả về (mặc định: 10, tối đa: 100)
  - `cursor`: Cursor để phân trang (để trống cho trang đầu tiên)
  - `sort_field`: Trường sắp xếp (created_at, updated_at, tenant_name)
  - `sort_order`: Thứ tự sắp xếp (asc, desc)
  
  **Quyền yêu cầu:** `tenants.read`
  
  **Response format:**
  ```json
  {
    \"status\": {
      \"code\": 200,
      \"message\": \"Operation completed successfully\",
      \"success\": true
    },
    \"data\": [
      {
        \"id\": 1,
        \"tenant_name\": \"Công ty ABC\",
        \"tenant_code\": \"abc-corp\",
        \"status\": \"active\",
        \"plan_type\": \"premium\",
        \"created_at\": \"2024-01-01T00:00:00Z\",
        \"updated_at\": \"2024-01-01T00:00:00Z\",
        \"subscription_expires_at\": \"2024-12-31T23:59:59Z\"
      }
    ],
    \"meta\": {
      \"next_cursor\": \"eyJpZCI6MX0=\",
      \"has_more\": true
    }
  }
  ```"
}
