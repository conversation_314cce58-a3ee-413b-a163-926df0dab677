meta {
  name: AddToCart
  type: http
  seq: 2
}

post {
  url: {{base_url}}/api/v1/cart/items
  body: json
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

body:json {
  {
    "product_id": "PROD-003",
    "quantity": 1,
    "attributes": {
      "color": "White",
      "size": "Medium"
    }
  }
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    // Extract cart and item IDs
    const cartId = responseJson.data.cart_id;
    const itemId = responseJson.data.item?.id;
    const productId = responseJson.data.item?.product_id;
    
    // Save to environment variables
    bru.setEnvVar("cart_id", cartId);
    if (itemId) {
      bru.setEnvVar("item_id", itemId);
    }
    if (productId) {
      bru.setEnvVar("last_product_id", productId);
    }
    
    console.log("Cart data saved:");
    console.log(`Cart ID: ${cartId}`);
    if (itemId) {
      console.log(`Item ID: ${itemId}`);
    }
  }
}

docs {
  title: "Add To Cart"
  desc: "Add a product to the user's shopping cart"
}


