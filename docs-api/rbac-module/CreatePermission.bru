meta {
  name: CreatePermission
  type: http
  seq: 2
}

post {
  url: {{base_url}}/api/v1/rbac/permissions
  body: json
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

body:json {
  {
    "permission_code": "products.create.abc",
    "tenant_id": 1,
    "group_id": 2,
    "permission_name": "Tạo sản phẩm",
    "permission_description": "Quyền tạo sản phẩm mới"
  }
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    // Extract permission details
    const permissionId = responseJson.data.permission_id;
    const permissionCode = responseJson.data.permission_code;
    const permissionName = responseJson.data.permission_name;
    const groupId = responseJson.data.group_id;
    
    // Save to environment variables
    bru.setEnvVar("permission_id", permissionId);
    bru.setEnvVar("permission_code", permissionCode);
    bru.setEnvVar("permission_group_id", groupId);
    
    console.log("Permission creation saved:");
    console.log(`Permission ID: ${permissionId}`);
    console.log(`Permission Code: ${permissionCode}`);
    console.log(`Permission Name: ${permissionName}`);
    console.log(`Group ID: ${groupId}`);
  }
}

docs {
  title: "Tạo quyền mới"
  desc: "Tạo một quyền mới trong hệ thống"
}
