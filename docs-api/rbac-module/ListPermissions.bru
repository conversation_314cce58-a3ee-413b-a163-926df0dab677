meta {
  name: ListPermissions
  type: http
  seq: 1
}

get {
  url: {{base_url}}/api/v1/rbac/permissions?limit=20&cursor=&group_id=1&search=user
  body: none
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    // Check if there are permissions
    if (responseJson.data.permissions && responseJson.data.permissions.length > 0) {
      // Extract details from the first permission
      const firstPermission = responseJson.data.permissions[0];
      const permissionId = firstPermission.permission_id;
      const permissionCode = firstPermission.permission_code;
      const permissionName = firstPermission.permission_name;
      
      // Save to environment variables
      bru.setEnvVar("first_permission_id", permissionId);
      bru.setEnvVar("first_permission_code", permissionCode);
      
      console.log("Permission list saved:");
      console.log(`First Permission ID: ${permissionId}`);
      console.log(`First Permission Code: ${permissionCode}`);
      console.log(`First Permission Name: ${permissionName}`);
      console.log(`Total Permissions: ${responseJson.data.permissions.length}`);
    }
    
    // Save pagination info if available
    if (responseJson.data.next_cursor) {
      bru.setEnvVar("permissions_next_cursor", responseJson.data.next_cursor);
      console.log(`Next Cursor: ${responseJson.data.next_cursor}`);
    }
  }
}

docs {
  title: "Lấy danh sách quyền"
  desc: "Lấy danh sách quyền với phân trang và bộ lọc"
}

