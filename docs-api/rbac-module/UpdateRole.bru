meta {
  name: UpdateRole
  type: http
  seq: 10
}

put {
  url: {{base_url}}/api/v1/rbac/roles/{{role_id}}
  body: json
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

body:json {
  {
    "role_code": "super_admin",
    "role_name": "Quản trị viên cấp cao",
    "role_description": "Vai trò quản trị viên cấp cao với toàn quyền",
    "permission_ids": [1, 2, 3, 4, 5, 6, 7]
  }
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    // Extract updated role details
    const roleId = responseJson.data.role_id;
    const roleCode = responseJson.data.role_code;
    const roleName = responseJson.data.role_name;
    const permissions = responseJson.data.permissions;
    
    // Save to environment variables
    bru.setEnvVar("updated_role_id", roleId);
    bru.setEnvVar("updated_role_code", roleCode);
    bru.setEnvVar("updated_role_name", roleName);
    
    console.log("Updated role details saved:");
    console.log(`Role ID: ${roleId}`);
    console.log(`Role Code: ${roleCode}`);
    console.log(`Role Name: ${roleName}`);
    if (permissions) {
      console.log(`Permissions: ${permissions.join(', ')}`);
    }
  }
}

docs {
  title: "Cập nhật vai trò"
  desc: "Cập nhật thông tin và quyền của một vai trò"
}

