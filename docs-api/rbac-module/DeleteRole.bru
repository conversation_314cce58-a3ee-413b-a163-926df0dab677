meta {
  name: DeleteRole
  type: http
  seq: 11
}

delete {
  url: {{base_url}}/api/v1/rbac/roles/{{role_id}}
  body: none
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true) {
    // Get the role ID from the URL
    const url = req.url;
    const urlParts = url.split('/');
    const roleId = urlParts[urlParts.length - 1];
    
    // Save to environment variables for confirmation
    bru.setEnvVar("deleted_role_id", roleId);
    
    console.log("Role deletion confirmed:");
    console.log(`Role ID: ${roleId}`);
    console.log(`Status: ${responseJson.status.message}`);
  }
}

docs {
  title: "Xóa vai trò"
  desc: "X<PERSON>a một vai trò khỏi hệ thống"
}

