meta {
  name: GetRolePermissions
  type: http
  seq: 9
}

get {
  url: {{base_url}}/api/v1/rbac/roles/{{role_id}}/permissions
  body: none
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    // Check if there are permissions
    if (responseJson.data.permissions && responseJson.data.permissions.length > 0) {
      // Extract role details
      const roleId = responseJson.data.role_id;
      const roleName = responseJson.data.role_name;
      
      // Save to environment variables
      bru.setEnvVar("role_permissions_count", responseJson.data.permissions.length);
      
      // Optionally, save first permission if needed
      if (responseJson.data.permissions.length > 0) {
        const firstPermission = responseJson.data.permissions[0];
        bru.setEnvVar("role_first_permission_id", firstPermission.permission_id);
        bru.setEnvVar("role_first_permission_code", firstPermission.permission_code);
      }
      
      console.log("Role permissions saved:");
      console.log(`Role ID: ${roleId}`);
      console.log(`Role Name: ${roleName}`);
      console.log(`Permission Count: ${responseJson.data.permissions.length}`);
    }
  }
}

docs {
  title: "Lấy quyền của vai trò"
  desc: "Lấy danh sách quyền của một vai trò"
}

