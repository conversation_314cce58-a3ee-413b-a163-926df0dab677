meta {
  name: DeletePermissionGroup
  type: http
  seq: 21
}

delete {
  url: {{base_url}}/api/v1/rbac/permission-groups/{{group_id}}
  body: none
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true) {
    // Get the group ID from the URL
    const url = req.url;
    const urlParts = url.split('/');
    const groupId = urlParts[urlParts.length - 1];
    
    // Save to environment variables for confirmation
    bru.setEnvVar("deleted_permission_group_id", groupId);
    
    console.log("Permission group deletion confirmed:");
    console.log(`Group ID: ${groupId}`);
    console.log(`Status: ${responseJson.status.message}`);
  }
}

docs {
  title: "<PERSON><PERSON><PERSON> nhóm quyền"
  desc: "<PERSON><PERSON><PERSON> một nhóm quyền khỏi hệ thống"
}

