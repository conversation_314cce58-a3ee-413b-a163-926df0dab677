meta {
  name: GetPermission
  type: http
  seq: 3
}

get {
  url: {{base_url}}/api/v1/rbac/permissions/{{permission_id}}
  body: none
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    // Extract permission details
    const permissionId = responseJson.data.permission_id;
    const permissionCode = responseJson.data.permission_code;
    const permissionName = responseJson.data.permission_name;
    const groupId = responseJson.data.group_id;
    const groupName = responseJson.data.group_name;
    
    // Save to environment variables
    bru.setEnvVar("permission_id", permissionId);
    bru.setEnvVar("permission_code", permissionCode);
    bru.setEnvVar("permission_group_id", groupId);
    if (groupName) {
      bru.setEnvVar("permission_group_name", groupName);
    }
    
    console.log("Permission details saved:");
    console.log(`Permission ID: ${permissionId}`);
    console.log(`Permission Code: ${permissionCode}`);
    console.log(`Permission Name: ${permissionName}`);
    console.log(`Group ID: ${groupId}`);
    if (groupName) {
      console.log(`Group Name: ${groupName}`);
    }
  }
}

docs {
  title: "Lấy chi tiết quyền"
  desc: "Lấy thông tin chi tiết của một quyền theo ID"
}

