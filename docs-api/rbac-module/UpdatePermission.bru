meta {
  name: UpdatePermission
  type: http
  seq: 4
}

put {
  url: {{base_url}}/api/v1/rbac/permissions/{{permission_id}}
  body: json
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

body:json {
  {
    "permission_code": "users.create",
    "group_id": 1,
    "permission_name": "Tạo và quản lý người dùng",
    "permission_description": "Quyền tạo và quản lý người dùng trong hệ thống"
  }
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    // Extract updated permission details
    const permissionId = responseJson.data.permission_id;
    const permissionCode = responseJson.data.permission_code;
    const permissionName = responseJson.data.permission_name;
    const groupId = responseJson.data.group_id;
    
    // Save to environment variables
    bru.setEnvVar("updated_permission_id", permissionId);
    bru.setEnvVar("updated_permission_code", permissionCode);
    bru.setEnvVar("updated_permission_group_id", groupId);
    
    console.log("Updated permission details saved:");
    console.log(`Permission ID: ${permissionId}`);
    console.log(`Permission Code: ${permissionCode}`);
    console.log(`Permission Name: ${permissionName}`);
    console.log(`Group ID: ${groupId}`);
  }
}

docs {
  title: "Cập nhật quyền"
  desc: "Cập nhật thông tin của một quyền"
}

