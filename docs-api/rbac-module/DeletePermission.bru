meta {
  name: DeletePermission
  type: http
  seq: 5
}

delete {
  url: {{base_url}}/api/v1/rbac/permissions/{{permission_id}}
  body: none
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true) {
    // Get the permission ID from the URL
    const url = req.url;
    const urlParts = url.split('/');
    const permissionId = urlParts[urlParts.length - 1];
    
    // Save to environment variables for confirmation
    bru.setEnvVar("deleted_permission_id", permissionId);
    
    console.log("Permission deletion confirmed:");
    console.log(`Permission ID: ${permissionId}`);
    console.log(`Status: ${responseJson.status.message}`);
  }
}

docs {
  title: "<PERSON>óa quyền"
  desc: "<PERSON><PERSON><PERSON> một quyền khỏi hệ thống"
}

