meta {
  name: GetUserRoles
  type: http
  seq: 14
}

get {
  url: {{base_url}}/api/v1/rbac/user-roles/users/{{user_id}}/roles
  body: none
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    // Check if there are user roles
    if (responseJson.data.user_roles && responseJson.data.user_roles.length > 0) {
      // Extract user details
      const userId = responseJson.data.user_roles[0].user_id;
      
      // Save to environment variables
      bru.setEnvVar("user_roles_count", responseJson.data.user_roles.length);
      
      // Optionally, save first role if needed
      if (responseJson.data.user_roles[0].role) {
        const firstRole = responseJson.data.user_roles[0].role;
        bru.setEnvVar("user_first_role_id", firstRole.role_id);
        bru.setEnvVar("user_first_role_code", firstRole.role_code);
      }
      
      console.log("User roles saved:");
      console.log(`User ID: ${userId}`);
      console.log(`Roles Count: ${responseJson.data.user_roles.length}`);
    }
  }
}

docs {
  title: "Lấy danh sách vai trò của người dùng"
  desc: "Lấy tất cả vai trò được gán cho một người dùng"
}

