meta {
  name: ListPermissionGroups
  type: http
  seq: 17
}

get {
  url: {{base_url}}/api/v1/rbac/permission-groups?limit=20&cursor=1&tenant_id=1
  body: none
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    // Check if there are permission groups
    if (responseJson.data.groups && responseJson.data.groups.length > 0) {
      // Extract details from the first group
      const firstGroup = responseJson.data.groups[0];
      const groupId = firstGroup.permission_group_id;
      const groupName = firstGroup.permission_group_name;
      
      // Save to environment variables
      bru.setEnvVar("first_group_id", groupId);
      bru.setEnvVar("first_group_name", groupName);
      
      console.log("Permission group list saved:");
      console.log(`First Group ID: ${groupId}`);
      console.log(`First Group Name: ${groupName}`);
      console.log(`Total Groups: ${responseJson.data.groups.length}`);
    }
    
    // Save pagination info if available
    if (responseJson.data.next_cursor) {
      bru.setEnvVar("groups_next_cursor", responseJson.data.next_cursor);
      console.log(`Next Cursor: ${responseJson.data.next_cursor}`);
    }
  }
}

docs {
  title: "Lấy danh sách nhóm quyền"
  desc: "Lấy danh sách nhóm quyền với phân trang"
}

