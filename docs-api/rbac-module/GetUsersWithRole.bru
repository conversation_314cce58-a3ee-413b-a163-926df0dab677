meta {
  name: GetUsersWithRole
  type: http
  seq: 16
}

get {
  url: {{base_url}}/api/v1/rbac/user-roles/roles/{{role_id}}/users
  body: none
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    // Check if there are users with role
    if (responseJson.data.user_roles && responseJson.data.user_roles.length > 0) {
      // Extract role details
      const roleId = responseJson.data.user_roles[0].role_id;
      
      // Save to environment variables
      bru.setEnvVar("role_users_count", responseJson.data.user_roles.length);
      
      // Optionally, save first user if needed
      if (responseJson.data.user_roles.length > 0) {
        const firstUserRole = responseJson.data.user_roles[0];
        bru.setEnvVar("role_first_user_id", firstUserRole.user_id);
      }
      
      console.log("Users with role saved:");
      console.log(`Role ID: ${roleId}`);
      console.log(`Users Count: ${responseJson.data.user_roles.length}`);
    }
  }
}

docs {
  title: "Lấy danh sách người dùng có vai trò"
  desc: "Lấy tất cả người dùng được gán một vai trò cụ thể"
}

