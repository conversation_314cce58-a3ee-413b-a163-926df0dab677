meta {
  name: ListRoles
  type: http
  seq: 6
}

get {
  url: {{base_url}}/api/v1/rbac/roles?limit=20&cursor=&with_permissions=true
  body: none
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    // Check if there are roles
    if (responseJson.data.roles && responseJson.data.roles.length > 0) {
      // Extract details from the first role
      const firstRole = responseJson.data.roles[0];
      const roleId = firstRole.role_id;
      const roleCode = firstRole.role_code;
      const roleName = firstRole.role_name;
      
      // Save to environment variables
      bru.setEnvVar("first_role_id", roleId);
      bru.setEnvVar("first_role_code", roleCode);
      
      console.log("Role list saved:");
      console.log(`First Role ID: ${roleId}`);
      console.log(`First Role Code: ${roleCode}`);
      console.log(`First Role Name: ${roleName}`);
      console.log(`Total Roles: ${responseJson.data.roles.length}`);
    }
    
    // Save pagination info if available
    if (responseJson.data.next_cursor) {
      bru.setEnvVar("roles_next_cursor", responseJson.data.next_cursor);
      console.log(`Next Cursor: ${responseJson.data.next_cursor}`);
    }
  }
}

docs {
  title: "Lấy danh sách vai trò"
  desc: "Lấy danh sách vai trò với phân trang và tùy chọn bao gồm quyền"
}

