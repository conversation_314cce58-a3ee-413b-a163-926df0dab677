meta {
  name: AssignUserRole
  type: http
  seq: 12
}

post {
  url: {{base_url}}/api/v1/rbac/user-roles/assign
  body: json
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

body:json {
  {
    "user_id": 5,
    "role_id": 2
  }
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    // Extract IDs
    const tenantId = responseJson.data.tenant_id;
    const userId = responseJson.data.user_id;
    const roleId = responseJson.data.role_id;
    const roleCode = responseJson.data.role?.role_code;
    
    // Save to environment variables
    bru.setEnvVar("last_tenant_id", tenantId);
    bru.setEnvVar("last_user_id", userId);
    bru.setEnvVar("last_role_id", roleId);
    if (roleCode) {
      bru.setEnvVar("last_role_code", roleCode);
    }
    
    console.log("User role assignment saved:");
    console.log(`User ID: ${userId}`);
    console.log(`Role ID: ${roleId}`);
    if (roleCode) {
      console.log(`Role Code: ${roleCode}`);
    }
  }
}

docs {
  title: "Gán vai trò cho người dùng"
  desc: "Gán một vai trò cho người dùng"
}

