meta {
  name: GetRole
  type: http
  seq: 8
}

get {
  url: {{base_url}}/api/v1/rbac/roles/{{role_id}}
  body: none
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    // Extract role details
    const roleId = responseJson.data.role_id;
    const roleCode = responseJson.data.role_code;
    const roleName = responseJson.data.role_name;
    const roleDescription = responseJson.data.role_description;
    const permissions = responseJson.data.permissions;
    
    // Save to environment variables
    bru.setEnvVar("role_id", roleId);
    bru.setEnvVar("role_code", roleCode);
    bru.setEnvVar("role_name", roleName);
    
    console.log("Role details saved:");
    console.log(`Role ID: ${roleId}`);
    console.log(`Role Code: ${roleCode}`);
    console.log(`Role Name: ${roleName}`);
    if (permissions) {
      console.log(`Permissions: ${permissions.join(', ')}`);
    }
  }
}

docs {
  title: "Lấy chi tiết vai trò"
  desc: "Lấy thông tin chi tiết của một vai trò theo ID"
}

