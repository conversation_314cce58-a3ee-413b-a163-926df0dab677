meta {
  name: RevokeUserRole
  type: http
  seq: 13
}

delete {
  url: {{base_url}}/api/v1/rbac/user-roles/users/{{user_id}}/roles/{{role_id}}
  body: none
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true) {
    // Get the user and role IDs from the URL
    const url = req.url;
    const urlParts = url.split('/');
    const userIdIndex = urlParts.indexOf('users') + 1;
    const roleIdIndex = urlParts.indexOf('roles') + 1;
    
    const userId = urlParts[userIdIndex];
    const roleId = urlParts[roleIdIndex];
    
    // Save to environment variables for confirmation
    bru.setEnvVar("revoked_user_id", userId);
    bru.setEnvVar("revoked_role_id", roleId);
    
    console.log("Role revocation confirmed:");
    console.log(`User ID: ${userId}`);
    console.log(`Role ID: ${roleId}`);
    console.log(`Status: ${responseJson.status.message}`);
  }
}

docs {
  title: "Thu hồi vai trò của người dùng"
  desc: "Thu hồi một vai trò từ người dùng"
}

