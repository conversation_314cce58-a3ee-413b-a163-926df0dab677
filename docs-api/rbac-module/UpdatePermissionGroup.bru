meta {
  name: UpdatePermissionGroup
  type: http
  seq: 20
}

put {
  url: {{base_url}}/api/v1/rbac/permission-groups/{{group_id}}
  body: json
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

body:json {
  {
    "permission_group_name": "Advanced User Management",
    "permission_group_description": "Quản lý người dùng và tài khoản nâng cao"
  }
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    // Extract updated permission group details
    const groupId = responseJson.data.permission_group_id;
    const groupName = responseJson.data.permission_group_name;
    const description = responseJson.data.permission_group_description;
    
    // Save to environment variables
    bru.setEnvVar("updated_permission_group_id", groupId);
    bru.setEnvVar("updated_permission_group_name", groupName);
    
    console.log("Updated permission group details saved:");
    console.log(`Group ID: ${groupId}`);
    console.log(`Group Name: ${groupName}`);
    console.log(`Description: ${description}`);
  }
}

docs {
  title: "Cập nhật nhóm quyền"
  desc: "Cập nhật thông tin của một nhóm quyền"
}

