meta {
  name: CreateRole
  type: http
  seq: 7
}

post {
  url: {{base_url}}/api/v1/rbac/roles
  body: json
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

body:json {
  {
    "role_code": "editor",
    "tenant_id": 1,
    "role_name": "Biên tập viên",
    "role_description": "Vai trò biên tập viên nội dung",
    "permissions": [1, 2, 3]
  }
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    // Extract role details
    const roleId = responseJson.data.role_id;
    const roleCode = responseJson.data.role_code;
    const roleName = responseJson.data.role_name;
    const permissions = responseJson.data.permissions;
    
    // Save to environment variables
    bru.setEnvVar("role_id", roleId);
    bru.setEnvVar("role_code", roleCode);
    bru.setEnvVar("role_name", roleName);
    
    console.log("Role creation saved:");
    console.log(`Role ID: ${roleId}`);
    console.log(`Role Code: ${roleCode}`);
    console.log(`Role Name: ${roleName}`);
    if (permissions) {
      console.log(`Permissions: ${permissions.join(', ')}`);
    }
  }
}

docs {
  title: "Tạo vai trò mới"
  desc: "Tạo một vai trò mới với danh sách quyền"
}

