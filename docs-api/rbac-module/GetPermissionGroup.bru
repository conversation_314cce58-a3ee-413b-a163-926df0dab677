meta {
  name: GetPermissionGroup
  type: http
  seq: 19
}

get {
  url: {{base_url}}/api/v1/rbac/permission-groups/{{group_id}}
  body: none
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    // Extract permission group details
    const groupId = responseJson.data.permission_group_id;
    const groupName = responseJson.data.permission_group_name;
    const tenantId = responseJson.data.tenant_id;
    
    // Save to environment variables
    bru.setEnvVar("permission_group_id", groupId);
    bru.setEnvVar("permission_group_name", groupName);
    
    console.log("Permission group details saved:");
    console.log(`Group ID: ${groupId}`);
    console.log(`Group Name: ${groupName}`);
    console.log(`Tenant ID: ${tenantId}`);
  }
}

docs {
  title: "<PERSON><PERSON><PERSON> chi tiết nhóm quyền"
  desc: "<PERSON><PERSON><PERSON> thông tin chi tiết của một nhóm quyền theo ID"
}

