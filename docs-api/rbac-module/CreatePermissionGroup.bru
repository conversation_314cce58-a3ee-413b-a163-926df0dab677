meta {
  name: CreatePermissionGroup
  type: http
  seq: 18
}

post {
  url: {{base_url}}/api/v1/rbac/permission-groups
  body: json
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

body:json {
  {
    "tenant_id": 1,
    "permission_group_name": "Content Management",
    "permission_group_description": "Quản lý nội dung và bài viết"
  }
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    // Extract permission group details
    const groupId = responseJson.data.permission_group_id;
    const groupName = responseJson.data.permission_group_name;
    const tenantId = responseJson.data.tenant_id;
    
    // Save to environment variables
    bru.setEnvVar("permission_group_id", groupId);
    bru.setEnvVar("permission_group_name", groupName);
    bru.setEnvVar("permission_group_tenant_id", tenantId);
    
    console.log("Permission group creation saved:");
    console.log(`Group ID: ${groupId}`);
    console.log(`Group Name: ${groupName}`);
    console.log(`Tenant ID: ${tenantId}`);
  }
}

docs {
  title: "Tạo nhóm quyền mới"
  desc: "Tạo một nhóm quyền mới trong hệ thống"
}

