meta {
  name: CheckUserRole
  type: http
  seq: 15
}

get {
  url: {{base_url}}/api/v1/rbac/user-roles/users/{{user_id}}/roles/{{role_id}}/check
  body: none
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    // Extract the check result
    const hasRole = responseJson.data.has_role;
    const userId = responseJson.data.user_id;
    const roleId = responseJson.data.role_id;
    
    // Save to environment variables
    bru.setEnvVar("check_user_id", userId);
    bru.setEnvVar("check_role_id", roleId);
    bru.setEnvVar("user_has_role", hasRole);
    
    console.log("Role check result saved:");
    console.log(`User ID: ${userId}`);
    console.log(`Role ID: ${roleId}`);
    console.log(`Has Role: ${hasRole}`);
  }
}

docs {
  title: "Kiểm tra vai trò của người dùng"
  desc: "Kiểm tra xem người dùng có vai trò cụ thể hay không"
}

