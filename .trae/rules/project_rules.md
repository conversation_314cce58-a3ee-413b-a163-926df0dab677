# Project Rules
- Base url: http://localhost:9200
- <PERSON><PERSON><PERSON> nhập bằng tài khoản: <EMAIL> và password : 12345678

## API - golang
- chạy server:
```
go run cmd/fx-server/main.go
```
. env dùng .env

- <PERSON><PERSON><PERSON> chạy go build để test khi chạy xong 1 task

## Docs
- Khi thực hiện task liên quan tới thêm/ sửa/ xóa 1 api, phải cập nhật lại docs bruno tại ./docs-api, luôn kiểm tra file/folder đã có
- bruno dùng {{api_url}} thay cho {{base_url}}
- Header set X-Tenant-Id
## Database
- Luôn dùng db migration để cập nhật database
