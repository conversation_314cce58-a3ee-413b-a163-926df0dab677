CREATE TABLE IF NOT EXISTS website_themes (
  theme_id INT UNSIGNED NOT NULL AUTO_INCREMENT,
  tenant_id INT UNSIGNED NOT NULL,
  website_id INT UNSIGNED NULL,
  name VARCHAR(100) NOT NULL,
  description TEXT NULL,
  thumbnail_url VARCHAR(255) NULL,
  is_public BOOLEAN NOT NULL DEFAULT 0,
  created_by INT UNSIGNED NULL,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (theme_id),
  INDEX idx_tenant_id (tenant_id),
  INDEX idx_website_id (website_id),
  INDEX idx_name (name),
  INDEX idx_is_public (is_public),
  INDEX idx_created_by (created_by)
)
ENGINE = InnoDB
CHARACTER SET = utf8mb4
COLLATE = utf8mb4_unicode_ci; 