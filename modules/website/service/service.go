package service

import (
	"wnapi/internal/pkg/logger"
	"wnapi/modules/website/internal"
)

// Service implements all the service interfaces for the website module
type Service struct {
	websiteService WebsiteService
	pageService    PageService
	// themeService    ThemeService // TODO: Fix theme service
	templateService TemplateService
	logger          logger.Logger
	config          internal.WebsiteConfig
}

// NewService creates a new service instance for the website module
func NewService(repo internal.Repository, config internal.WebsiteConfig, log logger.Logger) *Service {
	// Create underlying service implementations
	// websiteService := NewWebsiteService(repo) // TODO: Fix website service
	pageService := NewPageService(repo, repo)
	// themeService := NewThemeService(repo) // TODO: Fix theme service
	templateService := NewTemplateService(repo, repo)

	return &Service{
		// websiteService: websiteService, // TODO: Fix website service
		pageService: pageService,
		// themeService:    themeService, // TODO: Fix theme service
		templateService: templateService,
		logger:          log,
		config:          config,
	}
}

// Implement type assertion methods to make Service implement all service interfaces

// WebsiteService returns the website service implementation
func (s *Service) WebsiteService() WebsiteService {
	return s.websiteService
}

// PageService returns the page service implementation
func (s *Service) PageService() PageService {
	return s.pageService
}

// ThemeService returns the theme service implementation
// TODO: Fix theme service
// func (s *Service) ThemeService() ThemeService {
// 	return s.themeService
// }

// TemplateService returns the template service implementation
func (s *Service) TemplateService() TemplateService {
	return s.templateService
}
