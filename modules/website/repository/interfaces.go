package repository

import (
	"context"
	"wnapi/modules/website/dto/request"
	"wnapi/modules/website/models"
)

// WebsiteRepository defines methods for website data access
type WebsiteRepository interface {
	Create(ctx context.Context, website *models.Website) error
	GetByID(ctx context.Context, tenantID, websiteID int) (*models.Website, error)
	GetBySubdomain(ctx context.Context, subdomain string) (*models.Website, error)
	GetByCustomDomain(ctx context.Context, customDomain string) (*models.Website, error)
	Update(ctx context.Context, website *models.Website) error
	Delete(ctx context.Context, tenantID, websiteID int) error
	List(ctx context.Context, tenantID int, req request.ListWebsiteRequest) ([]*models.Website, string, bool, error)
}

// PageRepository defines methods for page data access
type PageRepository interface {
	Create(ctx context.Context, page *models.Page) error
	GetByID(ctx context.Context, tenantID, websiteID, pageID int) (*models.Page, error)
	GetBySlug(ctx context.Context, tenantID, websiteID int, slug string) (*models.Page, error)
	GetHomepage(ctx context.Context, tenantID, websiteID int) (*models.Page, error)
	Update(ctx context.Context, page *models.Page) error
	Delete(ctx context.Context, tenantID, websiteID, pageID int) error
	List(ctx context.Context, tenantID, websiteID int, req request.ListPageRequest) ([]*models.Page, string, bool, error)
}

// ThemeRepository defines methods for theme data access
type ThemeRepository interface {
	Create(ctx context.Context, theme *models.Theme) error
	GetByID(ctx context.Context, tenantID, themeID int) (*models.Theme, error)
	Update(ctx context.Context, theme *models.Theme) error
	Delete(ctx context.Context, tenantID, themeID int) error
	List(ctx context.Context, tenantID int, req request.ListThemeRequest) ([]*models.Theme, string, bool, error)
	GetPublicThemes(ctx context.Context, req request.ListThemeRequest) ([]*models.Theme, string, bool, error)
}

// TemplateRepository defines methods for template data access
type TemplateRepository interface {
	Create(ctx context.Context, template *models.Template) error
	GetByID(ctx context.Context, tenantID, templateID int) (*models.Template, error)
	GetByThemeID(ctx context.Context, tenantID, themeID int) ([]*models.Template, error)
	Update(ctx context.Context, template *models.Template) error
	Delete(ctx context.Context, tenantID, templateID int) error
	List(ctx context.Context, tenantID int, req request.ListTemplateRequest) ([]*models.Template, string, bool, error)
}
