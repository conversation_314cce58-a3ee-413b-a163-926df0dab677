package repository

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"strings"
	"time"

	"wnapi/internal/pkg/auth"
	"wnapi/internal/pkg/database"
	"wnapi/internal/pkg/logger"
	"wnapi/modules/website/dto"
	"wnapi/modules/website/internal"

	"github.com/gin-gonic/gin"
	"github.com/gosimple/slug"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

// mysqlRepository triển khai Repository interface sử dụng GORM
type mysqlRepository struct {
	db     *gorm.DB
	logger logger.Logger
}

// NewMySQLRepository tạo một repository mới
func NewMySQLRepository(dbManager *database.DBManager, logger logger.Logger) (internal.Repository, error) {
	if dbManager == nil {
		return nil, errors.New("database manager không được để trống")
	}

	sqlxDB := dbManager.GetDB()
	if sqlxDB == nil {
		return nil, errors.New("không thể kết nối tới cơ sở dữ liệu")
	}

	// Create GORM DB from sqlx DB
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn: sqlxDB.DB,
	}), &gorm.Config{})
	if err != nil {
		return nil, fmt.Errorf("failed to initialize GORM: %w", err)
	}

	return &mysqlRepository{
		db:     gormDB,
		logger: logger,
	}, nil
}

// Page methods
// ---------------------------------

// CreatePage tạo trang mới
func (r *mysqlRepository) CreatePage(ctx context.Context, page *internal.Page) error {

	// Tạo slug từ tiêu đề
	if page.Slug == "" {
		page.Slug = slug.Make(page.Title)
	}

	// Set tenant_id and website_id from context if not already set
	if page.TenantID == 0 {
		if ginCtx, ok := ctx.(*gin.Context); ok {
			page.TenantID = uint(auth.GetTenantID(ginCtx))
			page.WebsiteID = uint(auth.GetWebsiteID(ginCtx))
		}
	}

	result := r.db.WithContext(ctx).Create(page)
	if result.Error != nil {
		r.logger.Error("Không thể tạo trang", logger.String("error", result.Error.Error()))
		return internal.ErrDatabaseError
	}

	return nil
}

// GetPageByID lấy trang theo ID
func (r *mysqlRepository) GetPageByID(ctx context.Context, id int64) (*internal.Page, error) {

	var page internal.Page
	result := r.db.WithContext(ctx).First(&page, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, internal.ErrPageNotFound
		}
		r.logger.Error("Không thể lấy trang", logger.String("error", result.Error.Error()))
		return nil, internal.ErrDatabaseError
	}

	return &page, nil
}

// GetPageBySlug lấy trang theo slug
func (r *mysqlRepository) GetPageBySlug(ctx context.Context, slug string) (*internal.Page, error) {

	var page internal.Page
	result := r.db.WithContext(ctx).Where("slug = ?", slug).First(&page)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, internal.ErrPageNotFound
		}
		r.logger.Error("Không thể lấy trang theo slug", logger.String("error", result.Error.Error()))
		return nil, internal.ErrDatabaseError
	}

	return &page, nil
}

// UpdatePage cập nhật trang
func (r *mysqlRepository) UpdatePage(ctx context.Context, page *internal.Page) error {

	// Tạo slug từ tiêu đề nếu cần
	if page.Slug == "" && page.Title != "" {
		page.Slug = slug.Make(page.Title)
	}

	result := r.db.WithContext(ctx).Save(page)
	if result.Error != nil {
		r.logger.Error("Không thể cập nhật trang", logger.String("error", result.Error.Error()))
		return internal.ErrDatabaseError
	}

	if result.RowsAffected == 0 {
		return internal.ErrPageNotFound
	}

	return nil
}

// DeletePage xóa trang
func (r *mysqlRepository) DeletePage(ctx context.Context, id int64) error {

	result := r.db.WithContext(ctx).Delete(&internal.Page{}, id)
	if result.Error != nil {
		r.logger.Error("Không thể xóa trang", logger.String("error", result.Error.Error()))
		return internal.ErrDatabaseError
	}

	if result.RowsAffected == 0 {
		return internal.ErrPageNotFound
	}

	return nil
}

// ListPages lấy danh sách trang với phân trang sử dụng raw SQL
func (r *mysqlRepository) ListPages(ctx context.Context, params dto.ListPagesParams) ([]internal.Page, int64, error) {
	// Validate page size
	pageSize := params.PageSize
	if pageSize <= 0 {
		pageSize = 10
	}
	if pageSize > 100 {
		pageSize = 100
	}

	// Validate page number
	page := params.Page
	if page <= 0 {
		page = 1
	}

	// Build count query first
	countQuery := `SELECT COUNT(*) FROM website_pages WHERE 1=1`
	countArgs := []interface{}{}

	// Build base query
	baseQuery := `
		SELECT id, tenant_id, website_id, title, slug, content, description,
		       published, published_at, featured_image, created_at, updated_at
		FROM website_pages
		WHERE 1=1`

	args := []interface{}{}

	// Add search filter
	if params.Search != "" {
		searchCondition := ` AND (title LIKE ? OR content LIKE ?)`
		searchTerm := "%" + params.Search + "%"

		countQuery += searchCondition
		countArgs = append(countArgs, searchTerm, searchTerm)

		baseQuery += searchCondition
		args = append(args, searchTerm, searchTerm)
	}

	// Add published filter
	if params.PublishedOnly {
		publishedCondition := ` AND published = ?`

		countQuery += publishedCondition
		countArgs = append(countArgs, true)

		baseQuery += publishedCondition
		args = append(args, true)
	}

	// Get total count
	var total int64
	var sqlDB *sql.DB
	if gormDB := r.db.WithContext(ctx); gormDB != nil {
		sqlDB, _ = gormDB.DB()
	}

	err := sqlDB.QueryRowContext(ctx, countQuery, countArgs...).Scan(&total)
	if err != nil {
		r.logger.Error("Failed to count pages", logger.String("error", err.Error()))
		return nil, 0, internal.ErrDatabaseError
	}

	// Add ordering
	sortBy := params.SortBy
	if sortBy == "" {
		sortBy = "created_at"
	}
	sortOrder := params.SortOrder
	if sortOrder == "" {
		sortOrder = "desc"
	}

	baseQuery += fmt.Sprintf(" ORDER BY %s %s", sortBy, strings.ToUpper(sortOrder))

	// Add pagination
	offset := (page - 1) * pageSize
	baseQuery += " LIMIT ? OFFSET ?"
	args = append(args, pageSize, offset)

	// Execute query
	rows, err := sqlDB.QueryContext(ctx, baseQuery, args...)
	if err != nil {
		r.logger.Error("Failed to execute ListPages query", logger.String("error", err.Error()))
		return nil, 0, internal.ErrDatabaseError
	}
	defer rows.Close()

	var pages []internal.Page
	for rows.Next() {
		var page internal.Page
		var content sql.NullString
		var description sql.NullString
		var featuredImage sql.NullString
		var publishedAt sql.NullTime

		err := rows.Scan(
			&page.ID, &page.TenantID, &page.WebsiteID, &page.Title, &page.Slug,
			&content, &description, &page.Published, &publishedAt, &featuredImage,
			&page.CreatedAt, &page.UpdatedAt,
		)
		if err != nil {
			r.logger.Error("Failed to scan page row", logger.String("error", err.Error()))
			continue
		}

		// Handle nullable fields
		if content.Valid {
			page.Content = content.String
		}
		if description.Valid {
			page.Description = description.String
		}
		if featuredImage.Valid {
			page.FeaturedImage = featuredImage.String
		}
		if publishedAt.Valid {
			page.PublishedAt = &publishedAt.Time
		}

		pages = append(pages, page)
	}

	if err = rows.Err(); err != nil {
		r.logger.Error("Error iterating page rows", logger.String("error", err.Error()))
		return nil, 0, internal.ErrDatabaseError
	}

	return pages, total, nil
}

// Menu methods
// ---------------------------------

// CreateMenu tạo menu mới
func (r *mysqlRepository) CreateMenu(ctx context.Context, menu *internal.Menu) error {

	result := r.db.WithContext(ctx).Create(menu)
	if result.Error != nil {
		r.logger.Error("Không thể tạo menu", logger.String("error", result.Error.Error()))
		return internal.ErrDatabaseError
	}

	return nil
}

// GetMenuByID lấy menu theo ID
func (r *mysqlRepository) GetMenuByID(ctx context.Context, id int64) (*internal.Menu, error) {

	var menu internal.Menu
	result := r.db.WithContext(ctx).First(&menu, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, internal.ErrMenuNotFound
		}
		r.logger.Error("Không thể lấy menu", logger.String("error", result.Error.Error()))
		return nil, internal.ErrDatabaseError
	}

	// Lấy các menu items
	items, err := r.GetMenuItemsByMenuID(ctx, menu.ID)
	if err != nil {
		return nil, err
	}
	menu.Items = items

	return &menu, nil
}

// GetMenuByPosition lấy menu theo vị trí
func (r *mysqlRepository) GetMenuByPosition(ctx context.Context, position string) (*internal.Menu, error) {

	var menu internal.Menu
	result := r.db.WithContext(ctx).Where("position = ? AND is_active = ?", position, true).First(&menu)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, internal.ErrMenuNotFound
		}
		r.logger.Error("Không thể lấy menu theo vị trí", logger.String("error", result.Error.Error()))
		return nil, internal.ErrDatabaseError
	}

	// Lấy các menu items
	items, err := r.GetMenuItemsByMenuID(ctx, menu.ID)
	if err != nil {
		return nil, err
	}
	menu.Items = items

	return &menu, nil
}

// UpdateMenu cập nhật menu
func (r *mysqlRepository) UpdateMenu(ctx context.Context, menu *internal.Menu) error {

	result := r.db.WithContext(ctx).Save(menu)
	if result.Error != nil {
		r.logger.Error("Không thể cập nhật menu", logger.String("error", result.Error.Error()))
		return internal.ErrDatabaseError
	}

	if result.RowsAffected == 0 {
		return internal.ErrMenuNotFound
	}

	return nil
}

// DeleteMenu xóa menu
func (r *mysqlRepository) DeleteMenu(ctx context.Context, id int64) error {

	// Sử dụng transaction để đảm bảo xóa cả menu items
	tx := r.db.WithContext(ctx).Begin()
	if tx.Error != nil {
		r.logger.Error("Không thể bắt đầu transaction", logger.String("error", tx.Error.Error()))
		return internal.ErrDatabaseError
	}

	// Xóa menu items trước
	if err := tx.Where("menu_id = ?", id).Delete(&internal.MenuItem{}).Error; err != nil {
		tx.Rollback()
		r.logger.Error("Không thể xóa menu items", logger.String("error", err.Error()))
		return internal.ErrDatabaseError
	}

	// Xóa menu
	result := tx.Delete(&internal.Menu{}, id)
	if result.Error != nil {
		tx.Rollback()
		r.logger.Error("Không thể xóa menu", logger.String("error", result.Error.Error()))
		return internal.ErrDatabaseError
	}

	if result.RowsAffected == 0 {
		tx.Rollback()
		return internal.ErrMenuNotFound
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		r.logger.Error("Không thể commit transaction", logger.String("error", err.Error()))
		return internal.ErrDatabaseError
	}

	return nil
}

// ListMenus lấy danh sách menu với phân trang sử dụng raw SQL
func (r *mysqlRepository) ListMenus(ctx context.Context, params dto.ListMenusParams) ([]internal.Menu, int64, error) {
	// Validate page size
	pageSize := params.PageSize
	if pageSize <= 0 {
		pageSize = 10
	}
	if pageSize > 100 {
		pageSize = 100
	}

	// Validate page number
	page := params.Page
	if page <= 0 {
		page = 1
	}

	// Build count query first
	countQuery := `SELECT COUNT(*) FROM website_menus WHERE 1=1`
	countArgs := []interface{}{}

	// Build base query
	baseQuery := `
		SELECT id, tenant_id, website_id, name, position, is_active, created_at, updated_at
		FROM website_menus
		WHERE 1=1`

	args := []interface{}{}

	// Add active filter
	if params.ActiveOnly {
		activeCondition := ` AND is_active = ?`

		countQuery += activeCondition
		countArgs = append(countArgs, true)

		baseQuery += activeCondition
		args = append(args, true)
	}

	// Add position filter
	if params.Position != "" {
		positionCondition := ` AND position = ?`

		countQuery += positionCondition
		countArgs = append(countArgs, params.Position)

		baseQuery += positionCondition
		args = append(args, params.Position)
	}

	// Get total count
	var total int64
	var sqlDB *sql.DB
	if gormDB := r.db.WithContext(ctx); gormDB != nil {
		sqlDB, _ = gormDB.DB()
	}

	err := sqlDB.QueryRowContext(ctx, countQuery, countArgs...).Scan(&total)
	if err != nil {
		r.logger.Error("Failed to count menus", logger.String("error", err.Error()))
		return nil, 0, internal.ErrDatabaseError
	}

	// Add ordering
	sortBy := params.SortBy
	if sortBy == "" {
		sortBy = "created_at"
	}
	sortOrder := params.SortOrder
	if sortOrder == "" {
		sortOrder = "desc"
	}

	baseQuery += fmt.Sprintf(" ORDER BY %s %s", sortBy, strings.ToUpper(sortOrder))

	// Add pagination
	offset := (page - 1) * pageSize
	baseQuery += " LIMIT ? OFFSET ?"
	args = append(args, pageSize, offset)

	// Execute query
	rows, err := sqlDB.QueryContext(ctx, baseQuery, args...)
	if err != nil {
		r.logger.Error("Failed to execute ListMenus query", logger.String("error", err.Error()))
		return nil, 0, internal.ErrDatabaseError
	}
	defer rows.Close()

	var menus []internal.Menu
	for rows.Next() {
		var menu internal.Menu

		err := rows.Scan(
			&menu.ID, &menu.TenantID, &menu.WebsiteID, &menu.Name, &menu.Position,
			&menu.IsActive, &menu.CreatedAt, &menu.UpdatedAt,
		)
		if err != nil {
			r.logger.Error("Failed to scan menu row", logger.String("error", err.Error()))
			continue
		}

		menus = append(menus, menu)
	}

	if err = rows.Err(); err != nil {
		r.logger.Error("Error iterating menu rows", logger.String("error", err.Error()))
		return nil, 0, internal.ErrDatabaseError
	}

	// Load menu items for each menu using GORM (since it's more complex)
	for i := range menus {
		items, err := r.GetMenuItemsByMenuID(ctx, menus[i].ID)
		if err != nil {
			r.logger.Error("Failed to load menu items", logger.String("error", err.Error()))
			// Continue without items rather than failing completely
			menus[i].Items = []internal.MenuItem{}
		} else {
			menus[i].Items = items
		}
	}

	return menus, total, nil
}

// Menu item methods
// ---------------------------------

// CreateMenuItem tạo mục menu mới
func (r *mysqlRepository) CreateMenuItem(ctx context.Context, item *internal.MenuItem) error {

	result := r.db.WithContext(ctx).Create(item)
	if result.Error != nil {
		r.logger.Error("Không thể tạo mục menu", logger.String("error", result.Error.Error()))
		return internal.ErrDatabaseError
	}

	return nil
}

// GetMenuItemsByMenuID lấy các mục menu theo menu ID
func (r *mysqlRepository) GetMenuItemsByMenuID(ctx context.Context, menuID int64) ([]internal.MenuItem, error) {

	var items []internal.MenuItem
	result := r.db.WithContext(ctx).
		Where("menu_id = ?", menuID).
		Order("parent_id, `order`, id").
		Find(&items)

	if result.Error != nil {
		r.logger.Error("Không thể lấy các mục menu", logger.String("error", result.Error.Error()))
		return nil, internal.ErrDatabaseError
	}

	return items, nil
}

// UpdateMenuItem cập nhật mục menu
func (r *mysqlRepository) UpdateMenuItem(ctx context.Context, item *internal.MenuItem) error {

	result := r.db.WithContext(ctx).Save(item)
	if result.Error != nil {
		r.logger.Error("Không thể cập nhật mục menu", logger.String("error", result.Error.Error()))
		return internal.ErrDatabaseError
	}

	if result.RowsAffected == 0 {
		return internal.ErrMenuNotFound
	}

	return nil
}

// DeleteMenuItem xóa mục menu
func (r *mysqlRepository) DeleteMenuItem(ctx context.Context, id int64) error {

	// Kiểm tra xem có mục con không
	var count int64
	if err := r.db.WithContext(ctx).Model(&internal.MenuItem{}).Where("parent_id = ?", id).Count(&count).Error; err != nil {
		r.logger.Error("Không thể kiểm tra mục con", logger.String("error", err.Error()))
		return internal.ErrDatabaseError
	}

	// Nếu có mục con, cập nhật parent_id của chúng thành NULL
	if count > 0 {
		if err := r.db.WithContext(ctx).Model(&internal.MenuItem{}).Where("parent_id = ?", id).Update("parent_id", nil).Error; err != nil {
			r.logger.Error("Không thể cập nhật mục con", logger.String("error", err.Error()))
			return internal.ErrDatabaseError
		}
	}

	// Xóa mục menu
	result := r.db.WithContext(ctx).Delete(&internal.MenuItem{}, id)
	if result.Error != nil {
		r.logger.Error("Không thể xóa mục menu", logger.String("error", result.Error.Error()))
		return internal.ErrDatabaseError
	}

	if result.RowsAffected == 0 {
		return internal.ErrMenuNotFound
	}

	return nil
}

// Banner methods
// ---------------------------------

// CreateBanner tạo banner mới
func (r *mysqlRepository) CreateBanner(ctx context.Context, banner *internal.Banner) error {

	result := r.db.WithContext(ctx).Create(banner)
	if result.Error != nil {
		r.logger.Error("Không thể tạo banner", logger.String("error", result.Error.Error()))
		return internal.ErrDatabaseError
	}

	return nil
}

// GetBannerByID lấy banner theo ID
func (r *mysqlRepository) GetBannerByID(ctx context.Context, id int64) (*internal.Banner, error) {

	var banner internal.Banner
	result := r.db.WithContext(ctx).First(&banner, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, internal.ErrBannerNotFound
		}
		r.logger.Error("Không thể lấy banner", logger.String("error", result.Error.Error()))
		return nil, internal.ErrDatabaseError
	}

	return &banner, nil
}

// UpdateBanner cập nhật banner
func (r *mysqlRepository) UpdateBanner(ctx context.Context, banner *internal.Banner) error {

	result := r.db.WithContext(ctx).Save(banner)
	if result.Error != nil {
		r.logger.Error("Không thể cập nhật banner", logger.String("error", result.Error.Error()))
		return internal.ErrDatabaseError
	}

	if result.RowsAffected == 0 {
		return internal.ErrBannerNotFound
	}

	return nil
}

// DeleteBanner xóa banner
func (r *mysqlRepository) DeleteBanner(ctx context.Context, id int64) error {

	result := r.db.WithContext(ctx).Delete(&internal.Banner{}, id)
	if result.Error != nil {
		r.logger.Error("Không thể xóa banner", logger.String("error", result.Error.Error()))
		return internal.ErrDatabaseError
	}

	if result.RowsAffected == 0 {
		return internal.ErrBannerNotFound
	}

	return nil
}

// ListBanners lấy danh sách banner với phân trang sử dụng raw SQL
func (r *mysqlRepository) ListBanners(ctx context.Context, params dto.ListBannersParams) ([]internal.Banner, int64, error) {
	// Validate page size
	pageSize := params.PageSize
	if pageSize <= 0 {
		pageSize = 10
	}
	if pageSize > 100 {
		pageSize = 100
	}

	// Validate page number
	page := params.Page
	if page <= 0 {
		page = 1
	}

	// Build count query first
	countQuery := `SELECT COUNT(*) FROM website_banners WHERE 1=1`
	countArgs := []interface{}{}

	// Build base query
	baseQuery := `
		SELECT id, tenant_id, website_id, title, image_url, link_url, position,
		       ` + "`order`" + `, is_active, start_date, end_date, created_at, updated_at
		FROM website_banners
		WHERE 1=1`

	args := []interface{}{}

	// Add active filter
	if params.ActiveOnly {
		activeCondition := ` AND is_active = ?`

		countQuery += activeCondition
		countArgs = append(countArgs, true)

		baseQuery += activeCondition
		args = append(args, true)
	}

	// Add position filter
	if params.Position != "" {
		positionCondition := ` AND position = ?`

		countQuery += positionCondition
		countArgs = append(countArgs, params.Position)

		baseQuery += positionCondition
		args = append(args, params.Position)
	}

	// Get total count
	var total int64
	var sqlDB *sql.DB
	if gormDB := r.db.WithContext(ctx); gormDB != nil {
		sqlDB, _ = gormDB.DB()
	}

	err := sqlDB.QueryRowContext(ctx, countQuery, countArgs...).Scan(&total)
	if err != nil {
		r.logger.Error("Failed to count banners", logger.String("error", err.Error()))
		return nil, 0, internal.ErrDatabaseError
	}

	// Add ordering
	sortBy := params.SortBy
	if sortBy == "" {
		sortBy = "`order`"
	}
	sortOrder := params.SortOrder
	if sortOrder == "" {
		sortOrder = "asc"
	}

	baseQuery += fmt.Sprintf(" ORDER BY %s %s", sortBy, strings.ToUpper(sortOrder))

	// Add pagination
	offset := (page - 1) * pageSize
	baseQuery += " LIMIT ? OFFSET ?"
	args = append(args, pageSize, offset)

	// Execute query
	rows, err := sqlDB.QueryContext(ctx, baseQuery, args...)
	if err != nil {
		r.logger.Error("Failed to execute ListBanners query", logger.String("error", err.Error()))
		return nil, 0, internal.ErrDatabaseError
	}
	defer rows.Close()

	var banners []internal.Banner
	for rows.Next() {
		var banner internal.Banner
		var linkURL sql.NullString
		var startDate sql.NullTime
		var endDate sql.NullTime

		err := rows.Scan(
			&banner.ID, &banner.TenantID, &banner.WebsiteID, &banner.Title, &banner.ImageURL,
			&linkURL, &banner.Position, &banner.Order, &banner.IsActive, &startDate, &endDate,
			&banner.CreatedAt, &banner.UpdatedAt,
		)
		if err != nil {
			r.logger.Error("Failed to scan banner row", logger.String("error", err.Error()))
			continue
		}

		// Handle nullable fields
		if linkURL.Valid {
			banner.LinkURL = linkURL.String
		}
		if startDate.Valid {
			banner.StartDate = &startDate.Time
		}
		if endDate.Valid {
			banner.EndDate = &endDate.Time
		}

		banners = append(banners, banner)
	}

	if err = rows.Err(); err != nil {
		r.logger.Error("Error iterating banner rows", logger.String("error", err.Error()))
		return nil, 0, internal.ErrDatabaseError
	}

	return banners, total, nil
}

// GetActiveBannersByPosition lấy các banner đang hoạt động theo vị trí
func (r *mysqlRepository) GetActiveBannersByPosition(ctx context.Context, position string) ([]internal.Banner, error) {

	now := time.Now()

	var banners []internal.Banner
	result := r.db.WithContext(ctx).
		Where("position = ? AND is_active = ?", position, true).
		Where("(start_date IS NULL OR start_date <= ?)", now).
		Where("(end_date IS NULL OR end_date >= ?)", now).
		Order("`order` ASC").
		Find(&banners)

	if result.Error != nil {
		r.logger.Error("Không thể lấy banner theo vị trí", logger.String("error", result.Error.Error()))
		return nil, internal.ErrDatabaseError
	}

	return banners, nil
}
